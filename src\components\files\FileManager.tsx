'use client'

import React, { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { FileUpload } from './FileUpload'
import { FilePreview } from './FilePreview'
import { 
  Search, 
  FolderPlus, 
  Folder, 
  File, 
  Image, 
  FileText, 
  Archive,
  Download,
  Trash2,
  Eye,
  Grid3X3,
  List,
  Filter,
  Calendar,
  User,
  Tag
} from 'lucide-react'

interface FileItem {
  id: string
  name: string
  type: string
  size: number
  url: string
  uploadDate: string
  uploadedBy: string
  folderId?: string
  tags: string[]
  description?: string
}

interface Folder {
  id: string
  name: string
  parentId?: string
  createdDate: string
  itemCount: number
}

interface FileManagerProps {
  projectId?: string
  taskId?: string
  readonly?: boolean
  maxFiles?: number
  maxFileSize?: number
  acceptedTypes?: string[]
  onFilesChange?: (files: FileItem[]) => void
}

type ViewMode = 'grid' | 'list'
type SortBy = 'name' | 'date' | 'size' | 'type'
type SortOrder = 'asc' | 'desc'

export function FileManager({
  readonly = false,
  maxFiles = 50,
  maxFileSize = 25,
  acceptedTypes = ['image/*', 'application/pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt', '.zip', '.rar'],
  onFilesChange
}: FileManagerProps) {
  const [files, setFiles] = useState<FileItem[]>([])
  const [folders, setFolders] = useState<Folder[]>([])
  const [currentFolderId, setCurrentFolderId] = useState<string | undefined>()
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [sortBy, setSortBy] = useState<SortBy>('date')
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc')
  // const [selectedItems] = useState<string[]>([])
  const [showFilters, setShowFilters] = useState(false)
  const [newFolderName, setNewFolderName] = useState('')
  const [isCreatingFolder, setIsCreatingFolder] = useState(false)

  // بيانات وهمية للتطوير
  useEffect(() => {
    const mockFiles: FileItem[] = [
      {
        id: '1',
        name: 'تقرير المشروع النهائي.pdf',
        type: 'application/pdf',
        size: 2048000,
        url: '/api/files/1',
        uploadDate: '2024-03-15T10:30:00Z',
        uploadedBy: 'أحمد محمد',
        tags: ['تقرير', 'نهائي'],
        description: 'التقرير النهائي للمشروع مع جميع النتائج والتوصيات'
      },
      {
        id: '2',
        name: 'صورة المخطط.png',
        type: 'image/png',
        size: 1024000,
        url: '/api/files/2',
        uploadDate: '2024-03-14T14:20:00Z',
        uploadedBy: 'سارة أحمد',
        tags: ['مخطط', 'تصميم'],
        folderId: 'folder1'
      },
      {
        id: '3',
        name: 'جدول البيانات.xlsx',
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        size: 512000,
        url: '/api/files/3',
        uploadDate: '2024-03-13T09:15:00Z',
        uploadedBy: 'محمد علي',
        tags: ['بيانات', 'جدول']
      }
    ]

    const mockFolders: Folder[] = [
      {
        id: 'folder1',
        name: 'المرفقات',
        createdDate: '2024-03-01T00:00:00Z',
        itemCount: 5
      },
      {
        id: 'folder2',
        name: 'التقارير',
        createdDate: '2024-03-02T00:00:00Z',
        itemCount: 3
      }
    ]

    setFiles(mockFiles)
    setFolders(mockFolders)
  }, [])

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return <Image className="w-5 h-5 text-blue-600" />
    if (type.includes('pdf')) return <FileText className="w-5 h-5 text-red-600" />
    if (type.includes('word') || type.includes('document')) return <FileText className="w-5 h-5 text-blue-600" />
    if (type.includes('sheet') || type.includes('excel')) return <FileText className="w-5 h-5 text-green-600" />
    if (type.includes('zip') || type.includes('rar')) return <Archive className="w-5 h-5 text-orange-600" />
    return <File className="w-5 h-5 text-gray-600" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const filteredFiles = files
    .filter(file => file.folderId === currentFolderId)
    .filter(file => 
      file.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      file.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .sort((a, b) => {
      let comparison = 0
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name, 'ar')
          break
        case 'date':
          comparison = new Date(a.uploadDate).getTime() - new Date(b.uploadDate).getTime()
          break
        case 'size':
          comparison = a.size - b.size
          break
        case 'type':
          comparison = a.type.localeCompare(b.type)
          break
      }
      return sortOrder === 'asc' ? comparison : -comparison
    })

  const filteredFolders = folders.filter(folder => folder.parentId === currentFolderId)

  const handleFileUpload = async (uploadedFiles: File[]) => {
    // محاكاة رفع الملفات
    const newFiles: FileItem[] = uploadedFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      type: file.type,
      size: file.size,
      url: URL.createObjectURL(file),
      uploadDate: new Date().toISOString(),
      uploadedBy: 'المستخدم الحالي',
      folderId: currentFolderId,
      tags: []
    }))

    setFiles(prev => [...prev, ...newFiles])
    onFilesChange?.([...files, ...newFiles])
  }

  const handleFilePreview = (file: FileItem) => {
    setSelectedFile(file)
    setIsPreviewOpen(true)
  }

  const handleFileDownload = (file: FileItem) => {
    // محاكاة تحميل الملف
    const link = document.createElement('a')
    link.href = file.url
    link.download = file.name
    link.click()
  }

  const handleFileDelete = (file: FileItem) => {
    if (confirm('هل أنت متأكد من حذف هذا الملف؟')) {
      const updatedFiles = files.filter(f => f.id !== file.id)
      setFiles(updatedFiles)
      onFilesChange?.(updatedFiles)
    }
  }

  const handleCreateFolder = () => {
    if (!newFolderName.trim()) return

    const newFolder: Folder = {
      id: Math.random().toString(36).substr(2, 9),
      name: newFolderName.trim(),
      parentId: currentFolderId,
      createdDate: new Date().toISOString(),
      itemCount: 0
    }

    setFolders(prev => [...prev, newFolder])
    setNewFolderName('')
    setIsCreatingFolder(false)
  }

  const handleFolderClick = (folderId: string) => {
    setCurrentFolderId(folderId)
  }

  const handleBackClick = () => {
    const currentFolder = folders.find(f => f.id === currentFolderId)
    setCurrentFolderId(currentFolder?.parentId)
  }

  const toggleSort = (field: SortBy) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">إدارة الملفات</h3>
          <p className="text-sm text-gray-600">
            {filteredFiles.length} ملف • {filteredFolders.length} مجلد
          </p>
        </div>

        <div className="flex items-center gap-3">
          {/* Search */}
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="بحث في الملفات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-3 pr-10 w-64"
            />
          </div>

          {/* View Mode */}
          <div className="flex border border-gray-300 rounded-lg">
            <Button
              variant={viewMode === 'grid' ? 'primary' : 'ghost'}
              size="sm"
              icon={<Grid3X3 className="w-4 h-4" />}
              onClick={() => setViewMode('grid')}
            />
            <Button
              variant={viewMode === 'list' ? 'primary' : 'ghost'}
              size="sm"
              icon={<List className="w-4 h-4" />}
              onClick={() => setViewMode('list')}
            />
          </div>

          {/* Filters */}
          <Button
            variant="ghost"
            icon={<Filter className="w-4 h-4" />}
            onClick={() => setShowFilters(!showFilters)}
          >
            فلاتر
          </Button>

          {/* Create Folder */}
          {!readonly && (
            <Button
              variant="ghost"
              icon={<FolderPlus className="w-4 h-4" />}
              onClick={() => setIsCreatingFolder(true)}
            >
              مجلد جديد
            </Button>
          )}
        </div>
      </div>

      {/* Breadcrumb */}
      {currentFolderId && (
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBackClick}
          >
            الرئيسية
          </Button>
          <span>/</span>
          <span className="font-medium">
            {folders.find(f => f.id === currentFolderId)?.name}
          </span>
        </div>
      )}

      {/* Filters Panel */}
      {showFilters && (
        <Card className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ترتيب حسب
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as SortBy)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="name">الاسم</option>
                <option value="date">التاريخ</option>
                <option value="size">الحجم</option>
                <option value="type">النوع</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الترتيب
              </label>
              <select
                value={sortOrder}
                onChange={(e) => setSortOrder(e.target.value as SortOrder)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="asc">تصاعدي</option>
                <option value="desc">تنازلي</option>
              </select>
            </div>
          </div>
        </Card>
      )}

      {/* Create Folder Modal */}
      {isCreatingFolder && (
        <Card className="p-4 border-blue-200 bg-blue-50">
          <div className="flex items-center gap-3">
            <Input
              placeholder="اسم المجلد الجديد"
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleCreateFolder()}
              className="flex-1"
            />
            <Button
              onClick={handleCreateFolder}
              disabled={!newFolderName.trim()}
            >
              إنشاء
            </Button>
            <Button
              variant="ghost"
              onClick={() => {
                setIsCreatingFolder(false)
                setNewFolderName('')
              }}
            >
              إلغاء
            </Button>
          </div>
        </Card>
      )}

      {/* File Upload */}
      {!readonly && (
        <FileUpload
          maxFiles={maxFiles}
          maxFileSize={maxFileSize}
          acceptedTypes={acceptedTypes}
          onUpload={handleFileUpload}
          showPreview={false}
        />
      )}

      {/* Files and Folders Grid/List */}
      <Card className="p-4">
        {viewMode === 'grid' ? (
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {/* Folders */}
            {filteredFolders.map(folder => (
              <div
                key={folder.id}
                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => handleFolderClick(folder.id)}
              >
                <Folder className="w-12 h-12 text-blue-600 mb-2" />
                <p className="text-sm font-medium text-gray-900 text-center truncate w-full">
                  {folder.name}
                </p>
                <p className="text-xs text-gray-500">{folder.itemCount} عنصر</p>
              </div>
            ))}

            {/* Files */}
            {filteredFiles.map(file => (
              <div
                key={file.id}
                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors group"
                onClick={() => handleFilePreview(file)}
              >
                <div className="w-12 h-12 mb-2 flex items-center justify-center">
                  {file.type.startsWith('image/') ? (
                    <img
                      src={file.url}
                      alt={file.name}
                      className="w-12 h-12 object-cover rounded"
                    />
                  ) : (
                    getFileIcon(file.type)
                  )}
                </div>
                <p className="text-sm font-medium text-gray-900 text-center truncate w-full">
                  {file.name}
                </p>
                <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                
                {/* Actions */}
                <div className="flex items-center gap-1 mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    variant="ghost"
                    size="sm"
                    icon={<Eye className="w-3 h-3" />}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleFilePreview(file)
                    }}
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    icon={<Download className="w-3 h-3" />}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleFileDownload(file)
                    }}
                  />
                  {!readonly && (
                    <Button
                      variant="ghost"
                      size="sm"
                      icon={<Trash2 className="w-3 h-3" />}
                      onClick={(e) => {
                        e.stopPropagation()
                        handleFileDelete(file)
                      }}
                      className="text-red-600 hover:text-red-700"
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-2">
            {/* Folders */}
            {filteredFolders.map(folder => (
              <div
                key={folder.id}
                className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => handleFolderClick(folder.id)}
              >
                <Folder className="w-5 h-5 text-blue-600" />
                <div className="flex-1">
                  <p className="font-medium text-gray-900">{folder.name}</p>
                  <p className="text-sm text-gray-500">{folder.itemCount} عنصر</p>
                </div>
                <p className="text-sm text-gray-500">{formatDate(folder.createdDate)}</p>
              </div>
            ))}

            {/* Files */}
            {filteredFiles.map(file => (
              <div
                key={file.id}
                className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors group"
                onClick={() => handleFilePreview(file)}
              >
                <div className="flex-shrink-0">
                  {getFileIcon(file.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-gray-900 truncate">{file.name}</p>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span>{formatFileSize(file.size)}</span>
                    <span className="flex items-center gap-1">
                      <User className="w-3 h-3" />
                      {file.uploadedBy}
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {formatDate(file.uploadDate)}
                    </span>
                  </div>
                  {file.tags.length > 0 && (
                    <div className="flex items-center gap-1 mt-1">
                      <Tag className="w-3 h-3 text-gray-400" />
                      <div className="flex gap-1">
                        {file.tags.map((tag, index) => (
                          <span key={index} className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                
                {/* Actions */}
                <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    variant="ghost"
                    size="sm"
                    icon={<Eye className="w-4 h-4" />}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleFilePreview(file)
                    }}
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    icon={<Download className="w-4 h-4" />}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleFileDownload(file)
                    }}
                  />
                  {!readonly && (
                    <Button
                      variant="ghost"
                      size="sm"
                      icon={<Trash2 className="w-4 h-4" />}
                      onClick={(e) => {
                        e.stopPropagation()
                        handleFileDelete(file)
                      }}
                      className="text-red-600 hover:text-red-700"
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Empty State */}
        {filteredFiles.length === 0 && filteredFolders.length === 0 && (
          <div className="text-center py-12">
            <File className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد ملفات</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm ? 'لم يتم العثور على ملفات تطابق البحث' : 'ابدأ برفع الملفات أو إنشاء مجلدات'}
            </p>
            {!readonly && !searchTerm && (
              <Button
                icon={<FolderPlus className="w-4 h-4" />}
                onClick={() => setIsCreatingFolder(true)}
              >
                إنشاء مجلد جديد
              </Button>
            )}
          </div>
        )}
      </Card>

      {/* File Preview Modal */}
      <FilePreview
        file={selectedFile}
        isOpen={isPreviewOpen}
        onClose={() => {
          setIsPreviewOpen(false)
          setSelectedFile(null)
        }}
        onDownload={handleFileDownload}
        onDelete={!readonly ? handleFileDelete : undefined}
        showActions={!readonly}
      />
    </div>
  )
} 