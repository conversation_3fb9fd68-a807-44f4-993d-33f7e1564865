# نظام إدارة طلبات المشاريع واعتمادها

نظام متكامل لإدارة طلبات المشاريع واعتمادها مع التركيز على مشاريع التحسين باستخدام منهجية PDCA ونظام الموافقات المتدرج.

## 🌟 المميزات الرئيسية

### أنواع المشاريع
- **مقترح مشروع تحسين**: للمقترحات التي تحتاج دراسة وتقييم
- **طلب مشروع تحسين**: للمشاريع الكبيرة التي تحتاج موافقة الإدارة العليا
- **مشروع كويك وين**: للتحسينات السريعة

### نظام الموافقات المتدرج
- **كويك وين**: مدير مكتب المشاريع فقط
- **مقترح مشروع**: مدير المكتب + مدير التخطيط
- **طلب مشروع**: مدير المكتب + مدير التخطيط + المدير التنفيذي

### إدارة المشاريع بمنهجية PDCA
- **Plan (التخطيط)**: مرحلة التخطيط والتحليل
- **Do (التنفيذ)**: مرحلة التنفيذ والتطوير
- **Check (المراجعة)**: مرحلة الاختبار والمراجعة
- **Act (التحسين)**: مرحلة النشر والتحسين المستمر

## 🚀 التقنيات المستخدمة

- **Frontend**: Next.js 15.3, React 19, TypeScript
- **Styling**: Tailwind CSS
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **State Management**: Zustand
- **Icons**: Lucide React
- **Date Handling**: date-fns

## 📦 التثبيت والإعداد

### المتطلبات الأساسية
- Node.js 18+ 
- npm أو yarn
- حساب Supabase

### خطوات التثبيت

1. **استنساخ المشروع**
   ```bash
   git clone [repository-url]
   cd pmo-system
   ```

2. **تثبيت التبعيات**
   ```bash
   npm install
   ```

3. **إعداد متغيرات البيئة**
   ```bash
   cp .env.local.example .env.local
   ```
   
   أضف متغيرات Supabase:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **إعداد قاعدة البيانات**
   ```bash
   # تشغيل ملفات SQL في Supabase
   # 1. database/schema.sql
   # 2. database/rls-policies.sql
   ```

5. **تشغيل المشروع**
   ```bash
   npm run dev
   ```

6. **اختبار النظام**
   - زر صفحة الاختبار: `http://localhost:3000/supabase-test`
   - تأكد من نجاح جميع الاختبارات
   - استخدم بيانات الدخول: `<EMAIL>` / `admin123`

## 🏗️ هيكل المشروع

```
pmo-system/
├── src/
│   ├── app/                    # صفحات Next.js App Router
│   │   ├── auth/              # صفحات المصادقة
│   │   ├── dashboard/         # لوحة التحكم
│   │   ├── requests/          # إدارة الطلبات
│   │   ├── projects/          # إدارة المشاريع
│   │   ├── approvals/         # الموافقات
│   │   └── users/             # إدارة المستخدمين
│   ├── components/            # مكونات React
│   │   ├── ui/               # مكونات واجهة المستخدم
│   │   └── layout/           # مكونات التخطيط
│   ├── lib/                  # مكتبات ومساعدات
│   │   ├── supabase.ts       # إعداد Supabase
│   │   ├── auth.ts           # نظام المصادقة
│   │   └── utils.ts          # دوال مساعدة
│   └── types/                # تعريفات TypeScript
├── database/                 # ملفات قاعدة البيانات
│   ├── schema.sql           # هيكل قاعدة البيانات
│   └── rls-policies.sql     # سياسات الأمان
└── public/                  # الملفات العامة
```

## 👥 الأدوار والصلاحيات

### الأدوار المتاحة
1. **مدير النظام (Admin)**: صلاحيات كاملة
2. **مدير مكتب المشاريع (PMO Manager)**: إدارة الطلبات والمشاريع
3. **مدير إدارة التخطيط (Planning Manager)**: مراجعة واعتماد المقترحات
4. **المدير التنفيذي (Executive Manager)**: الموافقة النهائية
5. **مدير المشروع (Project Manager)**: إدارة المشاريع المعينة
6. **الموظف (Employee)**: تقديم الطلبات

### الصلاحيات
- **إنشاء الطلبات**: جميع المستخدمين
- **مراجعة الطلبات**: المدراء حسب المستوى
- **إدارة المشاريع**: مدراء المشاريع والأعلى
- **إدارة المستخدمين**: مدير النظام ومدير مكتب المشاريع

## 🗄️ قاعدة البيانات

### الجداول الرئيسية
- **users**: معلومات المستخدمين
- **roles**: الأدوار والصلاحيات
- **departments**: الأقسام
- **project_requests**: طلبات المشاريع
- **approvals**: الموافقات
- **projects**: المشاريع
- **tasks**: المهام
- **notifications**: الإشعارات

### الأمان
- Row Level Security (RLS) مفعل على جميع الجداول
- سياسات أمان مفصلة حسب الأدوار
- تشفير البيانات الحساسة

## 🎯 الاستخدام

### تسجيل الدخول
```
البريد الإلكتروني: <EMAIL>
كلمة المرور: admin123
```

### إنشاء طلب جديد
1. انتقل إلى "طلبات المشاريع"
2. اضغط على "طلب جديد"
3. اختر نوع المشروع
4. املأ المعلومات المطلوبة
5. أرسل الطلب للمراجعة

### إدارة المشاريع
1. انتقل إلى "إدارة المشاريع"
2. اختر عرض الكانبان أو القائمة
3. اسحب المشاريع بين المراحل
4. تابع التقدم والإحصائيات

## 🧪 الاختبار

```bash
# تشغيل الاختبارات
npm run test

# اختبارات التغطية
npm run test:coverage

# اختبارات E2E
npm run test:e2e
```

## 🚀 النشر

### Vercel (موصى به)
```bash
npm run build
vercel --prod
```

### Docker
```bash
docker build -t pmo-system .
docker run -p 3000:3000 pmo-system
```

## 📚 التوثيق الإضافي

- [دليل المستخدم](./docs/user-guide.md)
- [دليل المطور](./docs/developer-guide.md)
- [API Reference](./docs/api-reference.md)
- [دليل النشر](./docs/deployment-guide.md)

## 🤝 المساهمة

1. Fork المشروع
2. إنشاء فرع للميزة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +966-11-123-4567
- 🌐 الموقع: https://pmo-system.com

## 🔄 التحديثات

### الإصدار 1.0.0
- ✅ نظام المصادقة والصلاحيات
- ✅ إدارة طلبات المشاريع
- ✅ نظام الموافقات المتدرج
- ✅ لوحة إدارة المشاريع بنظام Kanban
- ✅ إدارة المستخدمين والأقسام

### قادم في الإصدار 1.1.0
- 🔄 نظام الإشعارات المتقدم
- 🔄 تقارير وتحليلات مفصلة
- 🔄 تطبيق الهاتف المحمول
- 🔄 تكامل مع أنظمة خارجية

---

**نظام إدارة المشاريع** - تطوير فريق إدارة المشاريع © 2024
