// إعداد المستخدمين الافتراضيين
import { supabase } from './supabase'

export interface DefaultUser {
  email: string
  password: string
  name: string
  role_name: string
  department_name: string
}

export const DEFAULT_USERS: DefaultUser[] = [
  {
    email: '<EMAIL>',
    password: 'admin123',
    name: 'مدير النظام',
    role_name: 'admin',
    department_name: 'مكتب إدارة المشاريع'
  },
  {
    email: '<EMAIL>',
    password: 'pmo123',
    name: 'مدير مكتب إدارة المشاريع',
    role_name: 'pmo_manager',
    department_name: 'مكتب إدارة المشاريع'
  },
  {
    email: '<EMAIL>',
    password: 'emp123',
    name: 'موظف',
    role_name: 'employee',
    department_name: 'قسم التطوير'
  }
]

/**
 * التحقق من وجود المستخدمين وإنشاؤهم إذا لم يكونوا موجودين
 */
export async function setupDefaultUsers() {
  const results = []
  
  for (const user of DEFAULT_USERS) {
    try {
      // التحقق من وجود المستخدم في auth.users
      const { data: authUser } = await supabase
        .from('auth.users')
        .select('id, email')
        .eq('email', user.email)
        .single()
      
      if (!authUser) {
        // إنشاء المستخدم في Supabase Auth
        const { data: newAuthUser, error: authError } = await supabase.auth.signUp({
          email: user.email,
          password: user.password,
          options: {
            data: {
              name: user.name
            }
          }
        })
        
        if (authError) {
          results.push({
            email: user.email,
            status: 'error',
            message: `فشل في إنشاء المستخدم في Auth: ${authError.message}`
          })
          continue
        }
        
        if (newAuthUser.user) {
          // الحصول على معرف الدور والقسم
          const { data: role } = await supabase
            .from('roles')
            .select('id')
            .eq('name', user.role_name)
            .single()
          
          const { data: department } = await supabase
            .from('departments')
            .select('id')
            .eq('name', user.department_name)
            .single()
          
          // إنشاء سجل المستخدم في جدول users
          const { error: userError } = await supabase
            .from('users')
            .insert({
              id: newAuthUser.user.id,
              email: user.email,
              name: user.name,
              role_id: role?.id,
              department_id: department?.id,
              is_active: true
            })
          
          if (userError) {
            results.push({
              email: user.email,
              status: 'error',
              message: `فشل في إنشاء سجل المستخدم: ${userError.message}`
            })
          } else {
            results.push({
              email: user.email,
              status: 'created',
              message: 'تم إنشاء المستخدم بنجاح'
            })
          }
        }
      } else {
        // التحقق من وجود المستخدم في جدول users
        const { data: userRecord } = await supabase
          .from('users')
          .select('id')
          .eq('email', user.email)
          .single()
        
        if (!userRecord) {
          // إنشاء سجل المستخدم فقط
          const { data: role } = await supabase
            .from('roles')
            .select('id')
            .eq('name', user.role_name)
            .single()
          
          const { data: department } = await supabase
            .from('departments')
            .select('id')
            .eq('name', user.department_name)
            .single()
          
          const { error: userError } = await supabase
            .from('users')
            .insert({
              id: authUser.id,
              email: user.email,
              name: user.name,
              role_id: role?.id,
              department_id: department?.id,
              is_active: true
            })
          
          if (userError) {
            results.push({
              email: user.email,
              status: 'error',
              message: `فشل في إنشاء سجل المستخدم: ${userError.message}`
            })
          } else {
            results.push({
              email: user.email,
              status: 'updated',
              message: 'تم إنشاء سجل المستخدم'
            })
          }
        } else {
          results.push({
            email: user.email,
            status: 'exists',
            message: 'المستخدم موجود بالفعل'
          })
        }
      }
    } catch (error: any) {
      results.push({
        email: user.email,
        status: 'error',
        message: `خطأ غير متوقع: ${error.message}`
      })
    }
  }
  
  return results
}

/**
 * إعادة تعيين كلمة مرور مستخدم
 */
export async function resetUserPassword(email: string, newPassword: string) {
  try {
    // هذا يتطلب صلاحيات admin - في الإنتاج استخدم Supabase Admin API
    console.log(`Password reset requested for ${email} to ${newPassword}`)
    
    return {
      success: true,
      message: `تم طلب إعادة تعيين كلمة المرور للمستخدم ${email}`
    }
  } catch (error: any) {
    return {
      success: false,
      message: `فشل في إعادة تعيين كلمة المرور: ${error.message}`
    }
  }
}

/**
 * فحص حالة المستخدمين
 */
export async function checkUsersStatus() {
  const status = {
    authUsers: 0,
    dbUsers: 0,
    missingInDb: [],
    missingInAuth: []
  }
  
  try {
    // عدد المستخدمين في Auth
    const { count: authCount } = await supabase
      .from('auth.users')
      .select('*', { count: 'exact', head: true })
    
    status.authUsers = authCount || 0
    
    // عدد المستخدمين في DB
    const { count: dbCount } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
    
    status.dbUsers = dbCount || 0
    
    return status
  } catch (error) {
    console.error('Error checking users status:', error)
    return status
  }
}
