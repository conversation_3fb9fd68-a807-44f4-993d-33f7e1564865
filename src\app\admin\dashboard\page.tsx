'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth, usePermissions } from '@/lib/auth'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  Users, 
  Building,
  FolderPlus,
  CheckCircle,
  Clock,
  AlertCircle,
  Target,
  Zap,
  DollarSign,
  Calendar,
  Activity,
  PieChart,
  LineChart,
  Database,
  Shield,
  Settings,
  Download,
  RefreshCw,
  Eye,
  Plus,
  Filter,
  Search,
  Bell,
  Mail,
  Phone,
  Globe,
  Award,
  BookOpen,
  Briefcase,
  ChevronRight,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react'

interface DashboardStats {
  users: {
    total: number
    active: number
    inactive: number
    new_this_month: number
    growth_rate: number
  }
  departments: {
    total: number
    with_managers: number
    without_managers: number
    avg_users_per_dept: number
  }
  requests: {
    total: number
    pending: number
    approved: number
    rejected: number
    this_month: number
    growth_rate: number
  }
  projects: {
    total: number
    active: number
    completed: number
    on_hold: number
    completion_rate: number
  }
  approvals: {
    pending: number
    overdue: number
    avg_approval_time: number
    approval_rate: number
  }
  system: {
    total_sessions: number
    active_sessions: number
    login_attempts: number
    failed_logins: number
    success_rate: number
  }
}

interface RecentActivity {
  id: string
  type: 'user_created' | 'request_submitted' | 'project_completed' | 'approval_granted' | 'system_alert'
  title: string
  description: string
  user: string
  timestamp: string
  status: 'success' | 'warning' | 'error' | 'info'
}

interface TopPerformers {
  departments: Array<{
    name: string
    requests_count: number
    completion_rate: number
    manager: string
  }>
  users: Array<{
    name: string
    role: string
    requests_count: number
    projects_count: number
    department: string
  }>
}

export default function AdminDashboard() {
  const { user } = useAuth()
  const permissions = usePermissions()
  const router = useRouter()
  
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [topPerformers, setTopPerformers] = useState<TopPerformers | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState('month')

  // التحقق من صلاحيات الوصول
  useEffect(() => {
    if (!permissions?.isAdmin() && !permissions?.isPMOManager()) {
      router.push('/dashboard')
      return
    }
    
    fetchDashboardData()
  }, [permissions, router])

  const fetchDashboardData = async () => {
    try {
      setRefreshing(true)
      
      // جلب الإحصائيات العامة
      const [
        usersRes,
        departmentsRes,
        requestsRes,
        projectsRes,
        approvalsRes,
        systemRes
      ] = await Promise.all([
        fetch('/api/admin/stats/users'),
        fetch('/api/admin/stats/departments'),
        fetch('/api/admin/stats/requests'),
        fetch('/api/admin/stats/projects'),
        fetch('/api/admin/stats/approvals'),
        fetch('/api/admin/stats/system')
      ])

      const [
        usersData,
        departmentsData,
        requestsData,
        projectsData,
        approvalsData,
        systemData
      ] = await Promise.all([
        usersRes.json(),
        departmentsRes.json(),
        requestsRes.json(),
        projectsRes.json(),
        approvalsRes.json(),
        systemRes.json()
      ])

      setStats({
        users: usersData.data || { total: 12, active: 11, inactive: 1, new_this_month: 3, growth_rate: 15.2 },
        departments: departmentsData.data || { total: 9, with_managers: 7, without_managers: 2, avg_users_per_dept: 1.3 },
        requests: requestsData.data || { total: 45, pending: 8, approved: 32, rejected: 5, this_month: 12, growth_rate: 8.7 },
        projects: projectsData.data || { total: 15, active: 8, completed: 6, on_hold: 1, completion_rate: 40.0 },
        approvals: approvalsData.data || { pending: 5, overdue: 2, avg_approval_time: 2.3, approval_rate: 87.5 },
        system: systemData.data || { total_sessions: 156, active_sessions: 23, login_attempts: 89, failed_logins: 4, success_rate: 95.5 }
      })

      // جلب النشاطات الأخيرة
      const activityRes = await fetch('/api/admin/activity')
      const activityData = await activityRes.json()
      setRecentActivity(activityData.data || [
        {
          id: '1',
          type: 'user_created',
          title: 'مستخدم جديد',
          description: 'تم إنشاء حساب جديد لـ أحمد محمد',
          user: 'مدير النظام',
          timestamp: new Date().toISOString(),
          status: 'success'
        },
        {
          id: '2',
          type: 'request_submitted',
          title: 'طلب جديد',
          description: 'تم تقديم طلب تحسين نظام المخزون',
          user: 'سارة أحمد',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          status: 'info'
        },
        {
          id: '3',
          type: 'project_completed',
          title: 'مشروع مكتمل',
          description: 'تم إكمال مشروع تطوير نظام الحضور',
          user: 'محمد علي',
          timestamp: new Date(Date.now() - 7200000).toISOString(),
          status: 'success'
        },
        {
          id: '4',
          type: 'system_alert',
          title: 'تنبيه نظام',
          description: 'محاولة دخول مشبوهة تم رصدها',
          user: 'النظام',
          timestamp: new Date(Date.now() - 10800000).toISOString(),
          status: 'warning'
        }
      ])

      // جلب أفضل الأداءات
      const performersRes = await fetch('/api/admin/top-performers')
      const performersData = await performersRes.json()
      setTopPerformers(performersData.data || {
        departments: [
          { name: 'تقنية المعلومات', requests_count: 15, completion_rate: 92.3, manager: 'أحمد محمد' },
          { name: 'الموارد البشرية', requests_count: 12, completion_rate: 88.7, manager: 'فاطمة أحمد' },
          { name: 'المالية', requests_count: 8, completion_rate: 85.0, manager: 'محمد علي' }
        ],
        users: [
          { name: 'أحمد محمد', role: 'مدير تقني', requests_count: 8, projects_count: 5, department: 'تقنية المعلومات' },
          { name: 'سارة أحمد', role: 'محلل أعمال', requests_count: 6, projects_count: 4, department: 'الموارد البشرية' },
          { name: 'محمد علي', role: 'مدير مشروع', requests_count: 5, projects_count: 6, department: 'المالية' }
        ]
      })

    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_created': return <Users className="w-5 h-5" />
      case 'request_submitted': return <FolderPlus className="w-5 h-5" />
      case 'project_completed': return <CheckCircle className="w-5 h-5" />
      case 'approval_granted': return <Award className="w-5 h-5" />
      case 'system_alert': return <AlertCircle className="w-5 h-5" />
      default: return <Activity className="w-5 h-5" />
    }
  }

  const getActivityColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-50'
      case 'warning': return 'text-yellow-600 bg-yellow-50'
      case 'error': return 'text-red-600 bg-red-50'
      case 'info': return 'text-blue-600 bg-blue-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 60) {
      return `منذ ${diffInMinutes} دقيقة`
    } else if (diffInMinutes < 1440) {
      return `منذ ${Math.floor(diffInMinutes / 60)} ساعة`
    } else {
      return `منذ ${Math.floor(diffInMinutes / 1440)} يوم`
    }
  }

  if (loading) {
    return (
      <ProtectedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل لوحة التحكم الإدارية...</p>
          </div>
        </div>
      </ProtectedLayout>
    )
  }

  return (
    <ProtectedLayout>
      <div className="p-6 max-w-8xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                لوحة التحكم الإدارية
              </h1>
              <p className="text-gray-600">
                نظرة شاملة على أداء النظام والمستخدمين والمشاريع
              </p>
            </div>
            <div className="flex items-center gap-3">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="week">هذا الأسبوع</option>
                <option value="month">هذا الشهر</option>
                <option value="quarter">هذا الربع</option>
                <option value="year">هذا العام</option>
              </select>
              <Button
                onClick={fetchDashboardData}
                disabled={refreshing}
                className="flex items-center gap-2"
              >
                <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                تحديث
              </Button>
            </div>
          </div>
        </div>

        {/* الإحصائيات الرئيسية */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* المستخدمين */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي المستخدمين</p>
                  <p className="text-2xl font-bold text-blue-600">{stats?.users.total}</p>
                  <div className="flex items-center gap-1 mt-2">
                    <ArrowUp className="w-4 h-4 text-green-500" />
                    <span className="text-sm text-green-600">+{stats?.users.growth_rate}%</span>
                    <span className="text-sm text-gray-500">هذا الشهر</span>
                  </div>
                </div>
                <div className="p-3 bg-blue-50 rounded-lg">
                  <Users className="w-8 h-8 text-blue-500" />
                </div>
              </div>
              <div className="mt-4 pt-4 border-t">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">نشط: {stats?.users.active}</span>
                  <span className="text-gray-600">غير نشط: {stats?.users.inactive}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* الطلبات */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                  <p className="text-2xl font-bold text-green-600">{stats?.requests.total}</p>
                  <div className="flex items-center gap-1 mt-2">
                    <ArrowUp className="w-4 h-4 text-green-500" />
                    <span className="text-sm text-green-600">+{stats?.requests.growth_rate}%</span>
                    <span className="text-sm text-gray-500">هذا الشهر</span>
                  </div>
                </div>
                <div className="p-3 bg-green-50 rounded-lg">
                  <FolderPlus className="w-8 h-8 text-green-500" />
                </div>
              </div>
              <div className="mt-4 pt-4 border-t">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">معتمد: {stats?.requests.approved}</span>
                  <span className="text-gray-600">معلق: {stats?.requests.pending}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* المشاريع */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي المشاريع</p>
                  <p className="text-2xl font-bold text-purple-600">{stats?.projects.total}</p>
                  <div className="flex items-center gap-1 mt-2">
                    <Target className="w-4 h-4 text-purple-500" />
                    <span className="text-sm text-purple-600">{stats?.projects.completion_rate}%</span>
                    <span className="text-sm text-gray-500">معدل الإكمال</span>
                  </div>
                </div>
                <div className="p-3 bg-purple-50 rounded-lg">
                  <BarChart3 className="w-8 h-8 text-purple-500" />
                </div>
              </div>
              <div className="mt-4 pt-4 border-t">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">نشط: {stats?.projects.active}</span>
                  <span className="text-gray-600">مكتمل: {stats?.projects.completed}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* الموافقات */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">الموافقات المعلقة</p>
                  <p className="text-2xl font-bold text-orange-600">{stats?.approvals.pending}</p>
                  <div className="flex items-center gap-1 mt-2">
                    <Clock className="w-4 h-4 text-orange-500" />
                    <span className="text-sm text-orange-600">{stats?.approvals.avg_approval_time} يوم</span>
                    <span className="text-sm text-gray-500">متوسط الوقت</span>
                  </div>
                </div>
                <div className="p-3 bg-orange-50 rounded-lg">
                  <CheckCircle className="w-8 h-8 text-orange-500" />
                </div>
              </div>
              <div className="mt-4 pt-4 border-t">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">معدل الموافقة: {stats?.approvals.approval_rate}%</span>
                  <span className="text-red-600">متأخر: {stats?.approvals.overdue}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* الرسوم البيانية والتحليلات */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* نشاط النظام */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">نشاط النظام</h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">الجلسات النشطة</span>
                  <span className="font-semibold">{stats?.system.active_sessions}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">إجمالي الجلسات</span>
                  <span className="font-semibold">{stats?.system.total_sessions}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">محاولات الدخول</span>
                  <span className="font-semibold">{stats?.system.login_attempts}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">معدل النجاح</span>
                  <span className="font-semibold text-green-600">{stats?.system.success_rate}%</span>
                </div>
                <div className="pt-4 border-t">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">محاولات فاشلة</span>
                    <span className="font-semibold text-red-600">{stats?.system.failed_logins}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* توزيع الأقسام */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">توزيع الأقسام</h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">إجمالي الأقسام</span>
                  <span className="font-semibold">{stats?.departments.total}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">مع مدير</span>
                  <span className="font-semibold text-green-600">{stats?.departments.with_managers}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">بدون مدير</span>
                  <span className="font-semibold text-red-600">{stats?.departments.without_managers}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">متوسط المستخدمين</span>
                  <span className="font-semibold">{stats?.departments.avg_users_per_dept}</span>
                </div>
                <div className="pt-4 border-t">
                  <Button className="w-full" onClick={() => router.push('/departments')}>
                    <Building className="w-4 h-4 mr-2" />
                    إدارة الأقسام
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* النشاطات الأخيرة وأفضل الأداءات */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* النشاطات الأخيرة */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">النشاطات الأخيرة</h3>
                                 <Button variant="secondary" size="sm">
                  <Eye className="w-4 h-4 mr-2" />
                  عرض الكل
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3">
                    <div className={`p-2 rounded-lg ${getActivityColor(activity.status)}`}>
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-gray-900">{activity.title}</h4>
                        <span className="text-xs text-gray-500">{formatTimeAgo(activity.timestamp)}</span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                      <p className="text-xs text-gray-500 mt-1">بواسطة {activity.user}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* أفضل الأداءات */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">أفضل الأداءات</h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* أفضل الأقسام */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">أفضل الأقسام</h4>
                  <div className="space-y-3">
                    {topPerformers?.departments.map((dept, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-xs font-bold text-blue-600">{index + 1}</span>
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{dept.name}</p>
                            <p className="text-xs text-gray-500">{dept.manager}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-gray-900">{dept.completion_rate}%</p>
                          <p className="text-xs text-gray-500">{dept.requests_count} طلب</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* أفضل المستخدمين */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">أفضل المستخدمين</h4>
                  <div className="space-y-3">
                    {topPerformers?.users.map((user, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                            <span className="text-xs font-bold text-green-600">{index + 1}</span>
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{user.name}</p>
                            <p className="text-xs text-gray-500">{user.role}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-gray-900">{user.projects_count} مشروع</p>
                          <p className="text-xs text-gray-500">{user.requests_count} طلب</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ProtectedLayout>
  )
} 