@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }
  
  html {
    font-family: 'Readex Pro', 'Cairo', sans-serif;
    direction: rtl;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }
  
  body {
    background: #ffffff !important;
    color: #1f2937 !important;
    font-size: 13px; /* النص الأساسي - محسن للداشبورد - مصغر بنسبة 10% */
    line-height: 1.5;
    min-height: 100vh;
  }
  
  /* منع تطبيق الوضع المظلم تلقائياً */
  @media (prefers-color-scheme: dark) {
    body {
      background: #ffffff !important;
      color: #1f2937 !important;
    }
    
    * {
      color-scheme: light !important;
    }
  }
  
  /* تحسين عرض النصوص على الشاشات عالية الدقة */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    body {
      -webkit-font-smoothing: subpixel-antialiased;
    }
    
    /* تحسين وضوح النصوص على الشاشات 2K و 4K */
    h1, h2, h3, h4, h5, h6 {
      font-weight: 600;
      -webkit-font-smoothing: antialiased;
    }
    
    label, .form-label {
      font-weight: 500;
      -webkit-font-smoothing: antialiased;
    }
  }
  
  /* تحسين عرض النصوص العربية */
  .font-arabic {
    font-family: 'Readex Pro', 'Cairo', sans-serif;
    font-feature-settings: 'liga' 1, 'calt' 1;
    direction: rtl;
    text-align: right;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* تحسين عرض النماذج */
  input, textarea, select {
    font-family: 'Readex Pro', 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* تحسين الألوان للنصوص - إصلاح مشكلة الألوان السوداء */
  .text-gray-900 {
    color: #111827 !important;
  }
  
  .text-gray-800 {
    color: #1f2937 !important;
  }
  
  .text-gray-700 {
    color: #374151 !important;
  }
  
  .text-gray-600 {
    color: #4b5563 !important;
  }
  
  .text-gray-500 {
    color: #6b7280 !important;
  }
  
  /* إصلاح مشكلة placeholder */
  ::placeholder {
    color: #9ca3af !important;
    opacity: 1;
  }
  
  /* تحسين الألوان للشاشات عالية الدقة */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .text-gray-900 {
      color: #0f172a !important;
    }
    
    .text-gray-800 {
      color: #1e293b !important;
    }
    
    .text-gray-700 {
      color: #334155 !important;
    }
  }
}

@layer components {
  .arabic-text {
    direction: rtl;
    text-align: right;
    font-family: 'Readex Pro', 'Cairo', sans-serif;
  }
  
  /* تحسين عرض النماذج */
  .form-input {
    width: 100%;
    padding: 0.625rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    background-color: #ffffff;
    color: #111827;
    font-family: 'Readex Pro', 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }
  
  .form-input::placeholder {
    color: #9ca3af;
  }
  
  .form-input:hover {
    border-color: #9ca3af;
  }
  
  .form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
  }
  
  .form-input.error {
    border-color: #fca5a5;
  }
  
  .form-input.error:focus {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1);
  }
  
  .form-label {
    display: block;
    font-size: 11px; /* حجم مناسب للتسميات - مصغر بنسبة 10% */
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 0.5rem;
    font-family: 'Readex Pro', 'Cairo', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  .form-error {
    font-size: 10px; /* حجم صغير للأخطاء - مصغر بنسبة 10% */
    color: #dc2626;
    font-family: 'Readex Pro', 'Cairo', sans-serif;
    -webkit-font-smoothing: antialiased;
  }
  
  .form-helper {
    font-size: 10px; /* حجم صغير للنصوص المساعدة - مصغر بنسبة 10% */
    color: #4b5563;
    font-family: 'Readex Pro', 'Cairo', sans-serif;
    -webkit-font-smoothing: antialiased;
  }
  
  /* تحسين الأزرار */
  .btn-primary {
    background-color: #2563eb;
    color: #ffffff;
    font-family: 'Readex Pro', 'Cairo', sans-serif;
    -webkit-font-smoothing: antialiased;
  }
  
  .btn-primary:hover {
    background-color: #1d4ed8;
  }
  
  .btn-secondary {
    background-color: #e5e7eb;
    color: #1f2937;
    font-family: 'IBM Plex Sans Arabic', 'Cairo', sans-serif;
    -webkit-font-smoothing: antialiased;
  }
  
  .btn-secondary:hover {
    background-color: #d1d5db;
  }
  
  /* تحسين الكروت */
  .card {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    -webkit-font-smoothing: antialiased;
  }
}

@layer utilities {
  .dir-rtl {
    direction: rtl;
  }
  
  .dir-ltr {
    direction: ltr;
  }
  
  .text-right-rtl {
    text-align: right;
    direction: rtl;
  }
  
  .text-left-ltr {
    text-align: left;
    direction: ltr;
  }
  
  /* تحسين الألوان للشاشات عالية الدقة */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .high-dpi-text {
      -webkit-font-smoothing: subpixel-antialiased;
      font-weight: 400;
    }
    
    .high-dpi-bold {
      font-weight: 600;
      -webkit-font-smoothing: antialiased;
    }
  }
  
  /* تحسين الألوان للنصوص المظلمة */
  .text-dark {
    color: #0f172a !important;
    -webkit-font-smoothing: antialiased;
  }
  
  .text-medium {
    color: #334155 !important;
    -webkit-font-smoothing: antialiased;
  }
  
  .text-light {
    color: #64748b !important;
    -webkit-font-smoothing: antialiased;
  }
  
  /* حماية إضافية ضد الوضع المظلم */
  html {
    color-scheme: light !important;
  }
  
  html.dark {
    color-scheme: light !important;
  }
  
  /* إعادة تعيين الألوان للعناصر المهمة */
  h1, h2, h3, h4, h5, h6 {
    color: inherit !important;
  }
  
  p, span, div {
    color: inherit !important;
  }
  
  /* التأكد من أن النصوص لا تأخذ اللون الأسود */
  .text-black {
    color: #1f2937 !important;
  }
  
  /* نظام أحجام الخطوط الموحد - مصغر بنسبة 10% */
  .text-micro {
    font-size: 9px !important;
    line-height: 13px !important;
  }
  
  .text-small {
    font-size: 11px !important;
    line-height: 15px !important;
  }
  
  .text-normal {
    font-size: 13px !important;
    line-height: 18px !important;
  }
  
  .text-medium {
    font-size: 14px !important;
    line-height: 22px !important;
  }
  
  .text-large {
    font-size: 16px !important;
    line-height: 25px !important;
  }
  
  /* أحجام العناوين - محسنة للداشبورد - مصغرة بنسبة 10% */
  .heading-main {
    font-size: 16px !important;
    line-height: 25px !important;
    font-weight: 600;
  }
  
  .heading-sub {
    font-size: 14px !important;
    line-height: 22px !important;
    font-weight: 600;
  }
  
  .heading-section {
    font-size: 13px !important;
    line-height: 18px !important;
    font-weight: 600;
  }
  
  /* أحجام النصوص - محسنة للداشبورد - مصغرة بنسبة 10% */
  .text-body {
    font-size: 13px !important;
    line-height: 18px !important;
  }
  
  .text-caption {
    font-size: 11px !important;
    line-height: 15px !important;
  }
  
  .text-label {
    font-size: 9px !important;
    line-height: 13px !important;
  }
} 