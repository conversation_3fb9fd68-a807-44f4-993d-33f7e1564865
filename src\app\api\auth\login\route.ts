import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { createHash } from 'crypto'

// واجهة بيانات تسجيل الدخول
interface LoginData {
  email: string
  password: string
}

// POST - تسجيل الدخول
export async function POST(request: NextRequest) {
  try {
    const body: LoginData = await request.json()
    
    // التحقق من صحة البيانات
    if (!body.email || !body.password) {
      return NextResponse.json(
        { error: 'البريد الإلكتروني وكلمة المرور مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من صحة تنسيق البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { error: 'تنسيق البريد الإلكتروني غير صحيح' },
        { status: 400 }
      )
    }

    // تشفير كلمة المرور
    const hashedPassword = createHash('sha256').update(body.password).digest('hex')

    // البحث عن المستخدم
    const { data: user, error } = await supabase
      .from('users')
      .select(`
        id,
        email,
        name,
        phone,
        avatar_url,
        is_active,
        department:departments!department_id(id, name),
        role:roles!role_id(id, name, display_name, permissions)
      `)
      .eq('email', body.email.toLowerCase())
      .eq('password_hash', hashedPassword)
      .eq('is_active', true)
      .single()

    if (error || !user) {
      return NextResponse.json(
        { error: 'بيانات تسجيل الدخول غير صحيحة' },
        { status: 401 }
      )
    }

    // إنشاء جلسة
    const sessionToken = generateSessionToken()
    const expiresAt = new Date()
    expiresAt.setHours(expiresAt.getHours() + 24) // انتهاء الجلسة بعد 24 ساعة

    // حفظ الجلسة في localStorage بدلاً من قاعدة البيانات مؤقتاً
    // TODO: سيتم تحديثها لاحقاً لاستخدام قاعدة البيانات
    
    // تسجيل محاولة تسجيل الدخول الناجحة
    // await logAuthAttempt(user.id, 'login', 'success', getClientIP(request))

    // إعداد response مع cookie
    const response = NextResponse.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        phone: user.phone,
        avatar_url: user.avatar_url,
        department: user.department,
        role: user.role
      }
    })

    // إعداد cookie للجلسة
    response.cookies.set('session_token', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000, // 24 ساعة
      path: '/'
    })

    return response

  } catch (error) {
    console.error('Login API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// دالة إنشاء رمز الجلسة
function generateSessionToken(): string {
  const randomBytes = crypto.getRandomValues(new Uint8Array(32))
  return Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('')
}

// دالة الحصول على IP العميل
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
} 