'use client'

import React, { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  BarChart3, 
  PieChart, 
  Calendar,
  Users,
  Award,
  AlertTriangle,
  Activity,
  Clock
} from 'lucide-react'

interface KpiAnalyticsProps {
  projectId?: string
  timeRange?: 'week' | 'month' | 'quarter' | 'year'
}

interface AnalyticsData {
  trend: {
    period: string
    value: number
    change: number
  }[]
  distribution: {
    type: string
    count: number
    percentage: number
  }[]
  performance: {
    category: string
    on_track: number
    at_risk: number
    behind: number
  }[]
  insights: {
    type: 'positive' | 'negative' | 'neutral'
    title: string
    description: string
    impact: 'high' | 'medium' | 'low'
  }[]
}

export const KpiAnalytics: React.FC<KpiAnalyticsProps> = ({
  projectId,
  timeRange = 'month'
}) => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)
  const [selectedMetric, setSelectedMetric] = useState<'improvement' | 'progress' | 'efficiency'>('improvement')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadAnalyticsData()
  }, [projectId, timeRange, selectedMetric])

  const loadAnalyticsData = async () => {
    try {
      setIsLoading(true)
      
      // TODO: استدعاء API لجلب بيانات التحليل
      const mockData: AnalyticsData = {
        trend: [
          { period: 'الأسبوع 1', value: 15.2, change: 0 },
          { period: 'الأسبوع 2', value: 18.7, change: 23.0 },
          { period: 'الأسبوع 3', value: 22.1, change: 18.2 },
          { period: 'الأسبوع 4', value: 25.8, change: 16.7 }
        ],
        distribution: [
          { type: 'على المسار', count: 12, percentage: 60 },
          { type: 'في خطر', count: 5, percentage: 25 },
          { type: 'متأخر', count: 3, percentage: 15 }
        ],
        performance: [
          { category: 'مؤشرات الوقت', on_track: 8, at_risk: 2, behind: 1 },
          { category: 'مؤشرات العدد', on_track: 3, at_risk: 2, behind: 1 },
          { category: 'مؤشرات النسبة', on_track: 1, at_risk: 1, behind: 1 }
        ],
        insights: [
          {
            type: 'positive',
            title: 'تحسن ملحوظ في مؤشرات الوقت',
            description: 'انخفض متوسط وقت انتظار المرضى بنسبة 28% خلال الشهر الماضي',
            impact: 'high'
          },
          {
            type: 'negative',
            title: 'تباطؤ في مؤشرات العدد',
            description: 'لم تحقق مؤشرات عدد المرضى المعالجين الأهداف المحددة',
            impact: 'medium'
          },
          {
            type: 'neutral',
            title: 'استقرار في مؤشرات النسبة',
            description: 'مؤشرات الرضا تحافظ على مستوى ثابت خلال الفترة الماضية',
            impact: 'low'
          }
        ]
      }
      
      setAnalyticsData(mockData)
    } catch (error) {
      console.error('Error loading analytics data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'positive': return <TrendingUp className="w-5 h-5 text-green-600" />
      case 'negative': return <TrendingDown className="w-5 h-5 text-red-600" />
      default: return <Activity className="w-5 h-5 text-blue-600" />
    }
  }

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'positive': return 'border-green-200 bg-green-50'
      case 'negative': return 'border-red-200 bg-red-50'
      default: return 'border-blue-200 bg-blue-50'
    }
  }

  const getImpactBadge = (impact: string) => {
    switch (impact) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-green-100 text-green-800'
    }
  }

  const getImpactText = (impact: string) => {
    switch (impact) {
      case 'high': return 'تأثير عالي'
      case 'medium': return 'تأثير متوسط'
      default: return 'تأثير منخفض'
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!analyticsData) {
    return (
      <Card className="p-8 text-center">
        <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          لا توجد بيانات تحليل
        </h3>
        <p className="text-gray-600">
          لا توجد بيانات كافية لعرض التحليل في الوقت الحالي
        </p>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* العنوان والفلاتر */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <BarChart3 className="w-8 h-8 text-blue-600" />
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              تحليل المؤشرات
            </h2>
            <p className="text-gray-600">
              تحليل متقدم لأداء المؤشرات والاتجاهات
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {[
            { key: 'improvement', label: 'التحسن', icon: TrendingUp },
            { key: 'progress', label: 'التقدم', icon: Target },
            { key: 'efficiency', label: 'الكفاءة', icon: Activity }
          ].map(metric => (
            <Button
              key={metric.key}
              variant={selectedMetric === metric.key ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setSelectedMetric(metric.key as any)}
              className="flex items-center gap-2"
            >
              <metric.icon className="w-4 h-4" />
              {metric.label}
            </Button>
          ))}
        </div>
      </div>

      {/* الاتجاهات */}
      <Card className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <TrendingUp className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            اتجاه الأداء
          </h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {analyticsData.trend.map((item, index) => (
            <div key={index} className="text-center">
              <div className="text-2xl font-bold text-blue-600 mb-1">
                {item.value.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600 mb-2">
                {item.period}
              </div>
              {item.change !== 0 && (
                <div className={`text-xs flex items-center justify-center gap-1 ${
                  item.change > 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {item.change > 0 ? (
                    <TrendingUp className="w-3 h-3" />
                  ) : (
                    <TrendingDown className="w-3 h-3" />
                  )}
                  {Math.abs(item.change).toFixed(1)}%
                </div>
              )}
            </div>
          ))}
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* توزيع الحالات */}
        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <PieChart className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">
              توزيع حالات المؤشرات
            </h3>
          </div>
          
          <div className="space-y-3">
            {analyticsData.distribution.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`w-4 h-4 rounded-full ${
                    item.type === 'على المسار' ? 'bg-green-500' :
                    item.type === 'في خطر' ? 'bg-yellow-500' : 'bg-red-500'
                  }`}></div>
                  <span className="text-gray-900">{item.type}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">{item.count}</span>
                  <span className="text-sm font-medium text-gray-900">
                    {item.percentage}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* الأداء حسب الفئة */}
        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <Users className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">
              الأداء حسب فئة المؤشر
            </h3>
          </div>
          
          <div className="space-y-4">
            {analyticsData.performance.map((item, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-900">
                    {item.category}
                  </span>
                  <span className="text-xs text-gray-600">
                    {item.on_track + item.at_risk + item.behind} مؤشر
                  </span>
                </div>
                
                <div className="flex h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div 
                    className="bg-green-500"
                    style={{ width: `${(item.on_track / (item.on_track + item.at_risk + item.behind)) * 100}%` }}
                  ></div>
                  <div 
                    className="bg-yellow-500"
                    style={{ width: `${(item.at_risk / (item.on_track + item.at_risk + item.behind)) * 100}%` }}
                  ></div>
                  <div 
                    className="bg-red-500"
                    style={{ width: `${(item.behind / (item.on_track + item.at_risk + item.behind)) * 100}%` }}
                  ></div>
                </div>
                
                <div className="flex items-center gap-4 text-xs text-gray-600">
                  <span className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    {item.on_track} على المسار
                  </span>
                  <span className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    {item.at_risk} في خطر
                  </span>
                  <span className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    {item.behind} متأخر
                  </span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* الرؤى والتوصيات */}
      <Card className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <Award className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            الرؤى والتوصيات
          </h3>
        </div>
        
        <div className="space-y-4">
          {analyticsData.insights.map((insight, index) => (
            <div key={index} className={`p-4 rounded-lg border-2 ${getInsightColor(insight.type)}`}>
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 mt-1">
                  {getInsightIcon(insight.type)}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900">
                      {insight.title}
                    </h4>
                    <span className={`px-2 py-1 text-xs rounded-full ${getImpactBadge(insight.impact)}`}>
                      {getImpactText(insight.impact)}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-700">
                    {insight.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  )
} 