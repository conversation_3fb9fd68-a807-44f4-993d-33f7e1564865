import React from 'react'
import { cn } from '@/lib/utils'

interface TypographyProps {
  children: React.ReactNode
  className?: string
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div'
}

// العناوين الرئيسية - 24px
export const MainHeading = ({ children, className, as: Component = 'h1' }: TypographyProps) => (
  <Component className={cn('heading-main', className)}>
    {children}
  </Component>
)

// العناوين الفرعية - 20px
export const SubHeading = ({ children, className, as: Component = 'h2' }: TypographyProps) => (
  <Component className={cn('heading-sub', className)}>
    {children}
  </Component>
)

// عناوين الأقسام - 16px
export const SectionHeading = ({ children, className, as: Component = 'h3' }: TypographyProps) => (
  <Component className={cn('heading-section', className)}>
    {children}
  </Component>
)

// النصوص الأساسية - 16px
export const BodyText = ({ children, className, as: Component = 'p' }: TypographyProps) => (
  <Component className={cn('text-body', className)}>
    {children}
  </Component>
)

// النصوص الثانوية - 12px
export const CaptionText = ({ children, className, as: Component = 'span' }: TypographyProps) => (
  <Component className={cn('text-caption', className)}>
    {children}
  </Component>
)

// النصوص الصغيرة - 8px
export const LabelText = ({ children, className, as: Component = 'span' }: TypographyProps) => (
  <Component className={cn('text-label', className)}>
    {children}
  </Component>
)

// مكون Typography عام
interface GeneralTypographyProps extends TypographyProps {
  variant?: 'main' | 'sub' | 'section' | 'body' | 'caption' | 'label'
}

export const Typography = ({ 
  children, 
  className, 
  variant = 'body', 
  as: Component = 'p' 
}: GeneralTypographyProps) => {
  const variantClasses = {
    main: 'heading-main',
    sub: 'heading-sub', 
    section: 'heading-section',
    body: 'text-body',
    caption: 'text-caption',
    label: 'text-label'
  }

  return (
    <Component className={cn(variantClasses[variant], className)}>
      {children}
    </Component>
  )
}

// مكونات مخصصة للاستخدامات الشائعة
export const PageTitle = ({ children, className }: Omit<TypographyProps, 'as'>) => (
  <MainHeading className={cn('text-gray-900 mb-4', className)}>
    {children}
  </MainHeading>
)

export const PageSubtitle = ({ children, className }: Omit<TypographyProps, 'as'>) => (
  <SubHeading className={cn('text-gray-600 mb-6', className)}>
    {children}
  </SubHeading>
)

export const CardTitle = ({ children, className }: Omit<TypographyProps, 'as'>) => (
  <SectionHeading className={cn('text-gray-900 mb-2', className)}>
    {children}
  </SectionHeading>
)

export const CardDescription = ({ children, className }: Omit<TypographyProps, 'as'>) => (
  <CaptionText className={cn('text-gray-600', className)}>
    {children}
  </CaptionText>
)

export const FormLabel = ({ children, className }: Omit<TypographyProps, 'as'>) => (
  <CaptionText className={cn('font-medium text-gray-700 block mb-2', className)}>
    {children}
  </CaptionText>
)

export const ErrorText = ({ children, className }: Omit<TypographyProps, 'as'>) => (
  <CaptionText className={cn('text-red-600', className)}>
    {children}
  </CaptionText>
)

export const HelperText = ({ children, className }: Omit<TypographyProps, 'as'>) => (
  <CaptionText className={cn('text-gray-500', className)}>
    {children}
  </CaptionText>
)

export const Badge = ({ children, className }: Omit<TypographyProps, 'as'>) => (
  <LabelText className={cn('px-2 py-1 rounded-full font-medium', className)}>
    {children}
  </LabelText>
) 