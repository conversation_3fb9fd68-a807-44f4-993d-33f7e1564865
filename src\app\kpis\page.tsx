'use client'

import React, { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { KpiDashboard } from '@/components/kpi/KpiDashboard'
import { 
  Target, 
  TrendingUp, 
  Activity, 
  Search, 
  Filter, 
  BarChart3,
  Users,
  Clock,
  AlertCircle
} from 'lucide-react'

interface ProjectKpiSummary {
  project_id: string
  project_name: string
  project_type: string
  total_kpis: number
  on_track: number
  at_risk: number
  behind: number
  avg_progress: number
  last_updated: string
}

interface KpiOverviewStats {
  total_projects: number
  total_kpis: number
  avg_improvement: number
  projects_on_track: number
  projects_at_risk: number
  projects_behind: number
}

export default function KpisPage() {
  const [projects, setProjects] = useState<ProjectKpiSummary[]>([])
  const [overviewStats, setOverviewStats] = useState<KpiOverviewStats | null>(null)
  const [selectedProject, setSelectedProject] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'on_track' | 'at_risk' | 'behind'>('all')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadKpiData()
  }, [])

  const loadKpiData = async () => {
    try {
      setIsLoading(true)
      
      // TODO: استدعاء API لجلب بيانات المؤشرات
      const mockProjects: ProjectKpiSummary[] = [
        {
          project_id: '1',
          project_name: 'تطوير نظام إدارة المرضى',
          project_type: 'تحسين العمليات',
          total_kpis: 3,
          on_track: 2,
          at_risk: 1,
          behind: 0,
          avg_progress: 78.5,
          last_updated: '2024-01-15T10:30:00Z'
        },
        {
          project_id: '2',
          project_name: 'تحسين نظام الطوارئ',
          project_type: 'كويك وين',
          total_kpis: 2,
          on_track: 1,
          at_risk: 0,
          behind: 1,
          avg_progress: 65.2,
          last_updated: '2024-01-14T16:45:00Z'
        },
        {
          project_id: '3',
          project_name: 'اقتراح تحسين خدمة العملاء',
          project_type: 'مقترح',
          total_kpis: 2,
          on_track: 2,
          at_risk: 0,
          behind: 0,
          avg_progress: 92.3,
          last_updated: '2024-01-13T14:20:00Z'
        }
      ]
      
      const mockStats: KpiOverviewStats = {
        total_projects: 3,
        total_kpis: 7,
        avg_improvement: 23.4,
        projects_on_track: 1,
        projects_at_risk: 1,
        projects_behind: 1
      }
      
      setProjects(mockProjects)
      setOverviewStats(mockStats)
    } catch (error) {
      console.error('Error loading KPI data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.project_name.toLowerCase().includes(searchTerm.toLowerCase())
    
    let matchesFilter = true
    if (filterType !== 'all') {
      matchesFilter = project[filterType] > 0
    }
    
    return matchesSearch && matchesFilter
  })

  const getProjectStatusColor = (project: ProjectKpiSummary) => {
    if (project.behind > 0) return 'text-red-600'
    if (project.at_risk > 0) return 'text-yellow-600'
    if (project.on_track > 0) return 'text-green-600'
    return 'text-gray-600'
  }

  const getProjectStatusText = (project: ProjectKpiSummary) => {
    if (project.behind > 0) return 'يحتاج متابعة'
    if (project.at_risk > 0) return 'في خطر'
    if (project.on_track > 0) return 'على المسار'
    return 'غير محدد'
  }

  if (selectedProject) {
    const project = projects.find(p => p.project_id === selectedProject)
    if (project) {
      return (
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              onClick={() => setSelectedProject(null)}
              className="flex items-center gap-2"
            >
              ← العودة للقائمة
            </Button>
            <div className="h-6 w-px bg-gray-300"></div>
            <h1 className="text-2xl font-bold text-gray-900">
              {project.project_name}
            </h1>
          </div>
          
          <KpiDashboard
            projectId={selectedProject}
            projectName={project.project_name}
            canEdit={true}
          />
        </div>
      )
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* العنوان */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Target className="w-8 h-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              مؤشرات الأداء
            </h1>
            <p className="text-gray-600">
              متابعة وتحليل مؤشرات الأداء لجميع المشاريع
            </p>
          </div>
        </div>
      </div>

      {/* الإحصائيات العامة */}
      {overviewStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="p-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {overviewStats.total_projects}
                </div>
                <div className="text-sm text-gray-600">إجمالي المشاريع</div>
                <div className="text-xs text-gray-500">
                  {overviewStats.total_kpis} مؤشر أداء
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">
                  +{overviewStats.avg_improvement.toFixed(1)}%
                </div>
                <div className="text-sm text-gray-600">متوسط التحسن</div>
                <div className="text-xs text-gray-500">
                  عبر جميع المؤشرات
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {overviewStats.projects_on_track}
                </div>
                <div className="text-sm text-gray-600">على المسار</div>
                <div className="text-xs text-gray-500">
                  مشاريع تسير بشكل جيد
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <AlertCircle className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-red-600">
                  {overviewStats.projects_at_risk + overviewStats.projects_behind}
                </div>
                <div className="text-sm text-gray-600">تحتاج متابعة</div>
                <div className="text-xs text-gray-500">
                  {overviewStats.projects_at_risk} في خطر، {overviewStats.projects_behind} متأخر
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* البحث والفلترة */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              placeholder="البحث في المشاريع..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Filter className="w-5 h-5 text-gray-600" />
          <div className="flex gap-2">
            {[
              { key: 'all', label: 'الكل', color: 'blue' },
              { key: 'on_track', label: 'على المسار', color: 'green' },
              { key: 'at_risk', label: 'في خطر', color: 'yellow' },
              { key: 'behind', label: 'متأخر', color: 'red' }
            ].map(filter => (
              <Button
                key={filter.key}
                variant={filterType === filter.key ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => setFilterType(filter.key as any)}
                className={filterType === filter.key ? `bg-${filter.color}-600` : ''}
              >
                {filter.label}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* قائمة المشاريع */}
      <div className="space-y-4">
        {filteredProjects.length === 0 ? (
          <Card className="p-8 text-center">
            <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              لا توجد مشاريع
            </h3>
            <p className="text-gray-600">
              {searchTerm 
                ? 'لا توجد مشاريع تطابق البحث المحدد'
                : 'لا توجد مشاريع تطابق الفلتر المحدد'}
            </p>
          </Card>
        ) : (
          filteredProjects.map(project => (
            <Card key={project.project_id} className="p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <BarChart3 className="w-6 h-6 text-blue-600" />
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {project.project_name}
                    </h3>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span>{project.project_type}</span>
                      <span>•</span>
                      <span>{project.total_kpis} مؤشر</span>
                      <span>•</span>
                      <span className={getProjectStatusColor(project)}>
                        {getProjectStatusText(project)}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {project.avg_progress.toFixed(1)}%
                    </div>
                    <div className="text-xs text-gray-600">متوسط التقدم</div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-600">{project.on_track}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <span className="text-sm text-gray-600">{project.at_risk}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <span className="text-sm text-gray-600">{project.behind}</span>
                    </div>
                  </div>
                  
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => setSelectedProject(project.project_id)}
                    className="flex items-center gap-2"
                  >
                    <Activity className="w-4 h-4" />
                    عرض التفاصيل
                  </Button>
                </div>
              </div>
              
              <div className="mt-4 pt-4 border-t">
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Clock className="w-4 h-4" />
                  آخر تحديث: {new Date(project.last_updated).toLocaleDateString('ar-SA')}
                </div>
              </div>
            </Card>
          ))
        )}
      </div>
    </div>
  )
} 