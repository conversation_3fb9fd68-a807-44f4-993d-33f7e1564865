'use client'

import React, { useState, useEffect } from 'react'
import { Search, Filter, UserCheck, Edit, Save, X, Users, Shield } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'

interface User {
  id: string
  name: string
  email: string
  department: string
  role_id: string
  role_name: string
  role_display_name: string
  is_active: boolean
  created_at: string
}

interface Role {
  id: string
  name: string
  display_name: string
}

interface Department {
  id: string
  name: string
}

export default function UsersRoleAssignment() {
  const [users, setUsers] = useState<User[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [departments, setDepartments] = useState<Department[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedDepartment, setSelectedDepartment] = useState('')
  const [selectedRole, setSelectedRole] = useState('')
  const [editingUser, setEditingUser] = useState<string | null>(null)
  const [newRoleId, setNewRoleId] = useState('')

  // بيانات تجريبية للأدوار
  const mockRoles: Role[] = [
    { id: '1', name: 'admin', display_name: 'مدير النظام' },
    { id: '2', name: 'pmo_manager', display_name: 'مدير مكتب المشاريع' },
    { id: '3', name: 'planning_manager', display_name: 'مدير إدارة التخطيط' },
    { id: '4', name: 'project_manager', display_name: 'مدير مشروع' },
    { id: '5', name: 'employee', display_name: 'موظف' }
  ]

  // بيانات تجريبية للأقسام
  const mockDepartments: Department[] = [
    { id: '1', name: 'مكتب إدارة المشاريع' },
    { id: '2', name: 'إدارة التخطيط' },
    { id: '3', name: 'الإدارة التنفيذية' },
    { id: '4', name: 'تقنية المعلومات' },
    { id: '5', name: 'الموارد البشرية' },
    { id: '6', name: 'المالية' }
  ]

  // بيانات تجريبية للمستخدمين
  const mockUsers: User[] = [
    {
      id: '1',
      name: 'أحمد محمد',
      email: '<EMAIL>',
      department: 'مكتب إدارة المشاريع',
      role_id: '1',
      role_name: 'admin',
      role_display_name: 'مدير النظام',
      is_active: true,
      created_at: '2024-01-15'
    },
    {
      id: '2',
      name: 'فاطمة أحمد',
      email: '<EMAIL>',
      department: 'مكتب إدارة المشاريع',
      role_id: '2',
      role_name: 'pmo_manager',
      role_display_name: 'مدير مكتب المشاريع',
      is_active: true,
      created_at: '2024-01-20'
    },
    {
      id: '3',
      name: 'خالد سعد',
      email: '<EMAIL>',
      department: 'إدارة التخطيط',
      role_id: '3',
      role_name: 'planning_manager',
      role_display_name: 'مدير إدارة التخطيط',
      is_active: true,
      created_at: '2024-02-01'
    },
    {
      id: '4',
      name: 'سارة علي',
      email: '<EMAIL>',
      department: 'تقنية المعلومات',
      role_id: '4',
      role_name: 'project_manager',
      role_display_name: 'مدير مشروع',
      is_active: true,
      created_at: '2024-02-10'
    },
    {
      id: '5',
      name: 'محمد عبدالله',
      email: '<EMAIL>',
      department: 'الموارد البشرية',
      role_id: '5',
      role_name: 'employee',
      role_display_name: 'موظف',
      is_active: true,
      created_at: '2024-02-15'
    },
    {
      id: '6',
      name: 'نورا حسن',
      email: '<EMAIL>',
      department: 'المالية',
      role_id: '5',
      role_name: 'employee',
      role_display_name: 'موظف',
      is_active: true,
      created_at: '2024-02-20'
    },
    {
      id: '7',
      name: 'عبدالرحمن إبراهيم',
      email: '<EMAIL>',
      department: 'تقنية المعلومات',
      role_id: '4',
      role_name: 'project_manager',
      role_display_name: 'مدير مشروع',
      is_active: false,
      created_at: '2024-03-01'
    }
  ]

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setUsers(mockUsers)
      setRoles(mockRoles)
      setDepartments(mockDepartments)
      setLoading(false)
    }, 1000)
  }, [])

  // تصفية المستخدمين
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesDepartment = !selectedDepartment || user.department === selectedDepartment
    const matchesRole = !selectedRole || user.role_id === selectedRole
    
    return matchesSearch && matchesDepartment && matchesRole
  })

  const handleEditRole = (userId: string) => {
    const user = users.find(u => u.id === userId)
    if (user) {
      setEditingUser(userId)
      setNewRoleId(user.role_id)
    }
  }

  const handleSaveRole = async (userId: string) => {
    try {
      const updatedUsers = users.map(user => {
        if (user.id === userId) {
          const newRole = roles.find(r => r.id === newRoleId)
          return {
            ...user,
            role_id: newRoleId,
            role_name: newRole?.name || user.role_name,
            role_display_name: newRole?.display_name || user.role_display_name
          }
        }
        return user
      })
      
      setUsers(updatedUsers)
      setEditingUser(null)
      setNewRoleId('')
    } catch (error) {
      console.error('Error updating user role:', error)
    }
  }

  const handleCancelEdit = () => {
    setEditingUser(null)
    setNewRoleId('')
  }

  const getRoleColor = (roleName: string) => {
    const colors: Record<string, string> = {
      admin: 'bg-red-100 text-red-800',
      pmo_manager: 'bg-blue-100 text-blue-800',
      planning_manager: 'bg-purple-100 text-purple-800',
      project_manager: 'bg-green-100 text-green-800',
      employee: 'bg-gray-100 text-gray-800'
    }
    return colors[roleName] || 'bg-gray-100 text-gray-800'
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">تعيين الأدوار للمستخدمين</h2>
          <p className="text-gray-600 mt-1">
            إدارة أدوار المستخدمين وصلاحياتهم
          </p>
        </div>
        
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Users className="w-4 h-4" />
          <span>{filteredUsers.length} من {users.length} مستخدم</span>
        </div>
      </div>

      {/* Filters */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">البحث والتصفية</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* البحث */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              البحث
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="البحث بالاسم أو البريد الإلكتروني..."
                className="pl-10"
              />
            </div>
          </div>

          {/* تصفية بالقسم */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              القسم
            </label>
            <select
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">جميع الأقسام</option>
              {departments.map((dept) => (
                <option key={dept.id} value={dept.name}>
                  {dept.name}
                </option>
              ))}
            </select>
          </div>

          {/* تصفية بالدور */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الدور
            </label>
            <select
              value={selectedRole}
              onChange={(e) => setSelectedRole(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">جميع الأدوار</option>
              {roles.map((role) => (
                <option key={role.id} value={role.id}>
                  {role.display_name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </Card>

      {/* Users List */}
      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المستخدم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  القسم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الدور الحالي
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                          <span className="text-blue-600 font-medium text-sm">
                            {user.name.charAt(0)}
                          </span>
                        </div>
                      </div>
                      <div className="mr-4">
                        <div className="text-sm font-medium text-gray-900">
                          {user.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {user.email}
                        </div>
                      </div>
                    </div>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {user.department}
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    {editingUser === user.id ? (
                      <select
                        value={newRoleId}
                        onChange={(e) => setNewRoleId(e.target.value)}
                        className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        {roles.map((role) => (
                          <option key={role.id} value={role.id}>
                            {role.display_name}
                          </option>
                        ))}
                      </select>
                    ) : (
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role_name)}`}>
                        <Shield className="w-3 h-3 mr-1" />
                        {user.role_display_name}
                      </span>
                    )}
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      user.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {user.is_active ? 'نشط' : 'غير نشط'}
                    </span>
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {editingUser === user.id ? (
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleSaveRole(user.id)}
                          className="text-green-600 hover:text-green-900"
                        >
                          <Save className="w-4 h-4" />
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          className="text-gray-600 hover:text-gray-900"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    ) : (
                      <button
                        onClick={() => handleEditRole(user.id)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredUsers.length === 0 && (
          <div className="text-center py-12">
            <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              لا توجد نتائج
            </h3>
            <p className="text-gray-600">
              جرب تغيير معايير البحث أو التصفية
            </p>
          </div>
        )}
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {roles.map((role) => {
          const roleUsers = users.filter(u => u.role_id === role.id)
          const activeUsers = roleUsers.filter(u => u.is_active)
          
          return (
            <Card key={role.id} className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {role.display_name}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {activeUsers.length} مستخدم نشط من {roleUsers.length}
                  </p>
                </div>
                <div className={`p-3 rounded-full ${getRoleColor(role.name).replace('text-', 'text-').replace('bg-', 'bg-')}`}>
                  <Shield className="w-6 h-6" />
                </div>
              </div>
              
              <div className="mt-4">
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>معدل النشاط</span>
                  <span>{roleUsers.length > 0 ? Math.round((activeUsers.length / roleUsers.length) * 100) : 0}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ 
                      width: `${roleUsers.length > 0 ? (activeUsers.length / roleUsers.length) * 100 : 0}%` 
                    }}
                  ></div>
                </div>
              </div>
            </Card>
          )
        })}
      </div>
    </div>
  )
} 