'use client'

import React from 'react'
import { LucideIcon } from 'lucide-react'
import { FormType } from '../unified/UnifiedProjectForm'

interface StepHeaderProps {
  icon: LucideIcon
  title: string
  description: string
  formType: FormType
  stepNumber?: number
  className?: string
}

export function StepHeader({ 
  icon: Icon, 
  title, 
  description, 
  formType, 
  stepNumber,
  className = '' 
}: StepHeaderProps) {
  
  const getFormColor = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'purple'
      case 'suggestion':
        return 'indigo'
      case 'quick_win':
        return 'yellow'
      default:
        return 'blue'
    }
  }

  const colorClasses = {
    purple: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      text: 'text-purple-900',
      icon: 'text-purple-600'
    },
    indigo: {
      bg: 'bg-indigo-50',
      border: 'border-indigo-200',
      text: 'text-indigo-900',
      icon: 'text-indigo-600'
    },
    yellow: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      text: 'text-yellow-900',
      icon: 'text-yellow-600'
    },
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      text: 'text-blue-900',
      icon: 'text-blue-600'
    },
    green: {
      bg: 'bg-green-50',
      border: 'border-green-200',
      text: 'text-green-900',
      icon: 'text-green-600'
    },
    orange: {
      bg: 'bg-orange-50',
      border: 'border-orange-200',
      text: 'text-orange-900',
      icon: 'text-orange-600'
    }
  }

  const formColor = getFormColor()
  const colors = colorClasses[formColor]

  return (
    <div className={`${colors.bg} border ${colors.border} rounded-lg p-4 mb-6 ${className}`}>
      <div className="flex items-center gap-3 mb-2">
        {stepNumber && (
          <div className={`w-8 h-8 rounded-full ${colors.bg} border-2 ${colors.border} flex items-center justify-center`}>
            <span className={`text-caption font-bold ${colors.text}`}>{stepNumber}</span>
          </div>
        )}
        <Icon className={`w-5 h-5 ${colors.icon}`} />
        <h3 className={`heading-section ${colors.text}`}>{title}</h3>
      </div>
      <p className={`${colors.text} text-caption leading-relaxed`}>
        {description}
      </p>
    </div>
  )
}

// دالة مساعدة للحصول على ألوان المراحل حسب النوع
export function getStepColorByType(formType: FormType, stepName: string) {
  const stepColors: Record<string, Record<FormType, string>> = {
    'Find': {
      'enhanced_improvement': 'orange',
      'suggestion': 'indigo', 
      'quick_win': 'yellow'
    },
    'Organize': {
      'enhanced_improvement': 'green',
      'suggestion': 'indigo',
      'quick_win': 'yellow'
    },
    'Clarify': {
      'enhanced_improvement': 'blue',
      'suggestion': 'indigo',
      'quick_win': 'yellow'
    },
    'Understand': {
      'enhanced_improvement': 'purple',
      'suggestion': 'indigo',
      'quick_win': 'yellow'
    },
    'Select': {
      'enhanced_improvement': 'green',
      'suggestion': 'indigo',
      'quick_win': 'yellow'
    },
    'Planning': {
      'enhanced_improvement': 'blue',
      'suggestion': 'indigo',
      'quick_win': 'yellow'
    },
    'RiskManagement': {
      'enhanced_improvement': 'orange',
      'suggestion': 'indigo',
      'quick_win': 'yellow'
    },
    'Review': {
      'enhanced_improvement': 'purple',
      'suggestion': 'indigo',
      'quick_win': 'yellow'
    }
  }

  return stepColors[stepName]?.[formType] || 'blue'
} 