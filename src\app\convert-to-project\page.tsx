'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth, usePermissions } from '@/lib/auth'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { 
  CheckCircle, 
  Clock, 
  User, 
  Calendar,
  DollarSign,
  Building,
  ArrowRight,
  Plus,
  Search,
  Filter,
  RefreshCw,
  Zap,
  Target,
  Lightbulb,
  AlertCircle,
  CheckCircle2
} from 'lucide-react'

interface ApprovedRequest {
  id: string
  title: string
  description: string
  main_type: string
  sub_type: string
  priority: string
  estimated_budget: number
  expected_start_date: string
  expected_end_date: string
  requester: {
    name: string
    email: string
  }
  department: {
    name: string
  }
  approved_at: string
  business_case: string
  expected_benefits: string
  risks_and_challenges: string
}

interface User {
  id: string
  name: string
  email: string
  role: string
  department: string
}

export default function ConvertToProjectPage() {
  const { user } = useAuth()
  const permissions = usePermissions()
  const router = useRouter()
  
  const [approvedRequests, setApprovedRequests] = useState<ApprovedRequest[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [converting, setConverting] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')
  const [selectedRequest, setSelectedRequest] = useState<ApprovedRequest | null>(null)
  const [assigneeId, setAssigneeId] = useState('')
  const [estimatedHours, setEstimatedHours] = useState(0)
  const [tags, setTags] = useState('')
  const [showConvertModal, setShowConvertModal] = useState(false)

  // جلب الطلبات المعتمدة
  useEffect(() => {
    fetchApprovedRequests()
    fetchUsers()
  }, [])

  const fetchApprovedRequests = async () => {
    try {
      const response = await fetch('/api/requests?status=approved')
      const data = await response.json()
      if (data.success) {
        setApprovedRequests(data.data)
      }
    } catch (error) {
      console.error('Error fetching approved requests:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users')
      const data = await response.json()
      if (data.success) {
        setUsers(data.data)
      }
    } catch (error) {
      console.error('Error fetching users:', error)
    }
  }

  // تحويل الطلب إلى مشروع
  const handleConvertToProject = async () => {
    if (!selectedRequest || !assigneeId) return

    setConverting(selectedRequest.id)
    
    try {
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          request_id: selectedRequest.id,
          assignee_id: assigneeId,
          estimated_hours: estimatedHours,
          tags: tags.split(',').map(tag => tag.trim()).filter(Boolean)
        })
      })

      const data = await response.json()
      
      if (data.success) {
        // إزالة الطلب من القائمة
        setApprovedRequests(prev => prev.filter(req => req.id !== selectedRequest.id))
        setShowConvertModal(false)
        setSelectedRequest(null)
        setAssigneeId('')
        setEstimatedHours(0)
        setTags('')
        
        // إشعار بالنجاح
        alert('✅ تم تحويل الطلب إلى مشروع بنجاح!')
      } else {
        alert('❌ فشل في تحويل الطلب: ' + data.error)
      }
    } catch (error) {
      console.error('Error converting request:', error)
      alert('❌ حدث خطأ أثناء تحويل الطلب')
    } finally {
      setConverting(null)
    }
  }

  // فلترة الطلبات
  const filteredRequests = approvedRequests.filter(request => {
    const matchesSearch = request.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = filterType === 'all' || request.main_type === filterType
    return matchesSearch && matchesType
  })

  // تحديد نوع المشروع
  const getProjectTypeIcon = (mainType: string, subType: string) => {
    if (mainType === 'improvement_project') {
      if (subType === 'quick_win') return <Zap className="w-5 h-5 text-green-500" />
      if (subType === 'suggestion') return <Lightbulb className="w-5 h-5 text-yellow-500" />
      return <Target className="w-5 h-5 text-blue-500" />
    }
    return <Target className="w-5 h-5 text-purple-500" />
  }

  const getProjectTypeLabel = (mainType: string, subType: string) => {
    if (mainType === 'improvement_project') {
      if (subType === 'quick_win') return 'إنجاز سريع'
      if (subType === 'suggestion') return 'اقتراح تحسين'
      return 'مشروع تحسين'
    }
    return 'مشروع'
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-50'
      case 'high': return 'text-orange-600 bg-orange-50'
      case 'medium': return 'text-yellow-600 bg-yellow-50'
      case 'low': return 'text-green-600 bg-green-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'عاجل'
      case 'high': return 'عالي'
      case 'medium': return 'متوسط'
      case 'low': return 'منخفض'
      default: return 'غير محدد'
    }
  }

  return (
    <ProtectedLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            تحويل الطلبات إلى مشاريع
          </h1>
          <p className="text-gray-600">
            قم بتحويل الطلبات المعتمدة إلى مشاريع في لوحة Kanban لبدء التنفيذ
          </p>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">الطلبات المعتمدة</p>
                  <p className="text-2xl font-bold text-blue-600">{approvedRequests.length}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">مشاريع التحسين</p>
                  <p className="text-2xl font-bold text-green-600">
                    {approvedRequests.filter(r => r.main_type === 'improvement_project').length}
                  </p>
                </div>
                <Target className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">الإنجازات السريعة</p>
                  <p className="text-2xl font-bold text-yellow-600">
                    {approvedRequests.filter(r => r.sub_type === 'quick_win').length}
                  </p>
                </div>
                <Zap className="w-8 h-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">اقتراحات التحسين</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {approvedRequests.filter(r => r.sub_type === 'suggestion').length}
                  </p>
                </div>
                <Lightbulb className="w-8 h-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* أدوات البحث والفلترة */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  placeholder="البحث في الطلبات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">جميع الأنواع</option>
                <option value="improvement_project">مشاريع التحسين</option>
                <option value="development_project">مشاريع التطوير</option>
                <option value="maintenance_project">مشاريع الصيانة</option>
              </select>
              
              <Button
                onClick={fetchApprovedRequests}
                className="flex items-center gap-2"
                disabled={loading}
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                تحديث
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* قائمة الطلبات المعتمدة */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل الطلبات...</p>
          </div>
        ) : filteredRequests.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد طلبات معتمدة</h3>
              <p className="text-gray-600">لا توجد طلبات معتمدة متاحة للتحويل إلى مشاريع حالياً</p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredRequests.map((request) => (
              <Card key={request.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      {getProjectTypeIcon(request.main_type, request.sub_type)}
                      <div>
                        <h3 className="font-semibold text-gray-900">{request.title}</h3>
                        <p className="text-sm text-gray-600">
                          {getProjectTypeLabel(request.main_type, request.sub_type)}
                        </p>
                      </div>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getPriorityColor(request.priority)}`}>
                      {getPriorityLabel(request.priority)}
                    </span>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <p className="text-gray-700 text-sm line-clamp-2">{request.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600">{request.requester.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Building className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600">{request.department.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600">
                        {new Date(request.expected_end_date).toLocaleDateString('ar-SA')}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <DollarSign className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600">
                        {request.estimated_budget?.toLocaleString()} ريال
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center pt-4 border-t">
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <CheckCircle2 className="w-4 h-4 text-green-500" />
                      <span>معتمد في {new Date(request.approved_at).toLocaleDateString('ar-SA')}</span>
                    </div>
                    
                    <Button
                      onClick={() => {
                        setSelectedRequest(request)
                        setShowConvertModal(true)
                      }}
                      className="flex items-center gap-2"
                      disabled={converting === request.id}
                    >
                      {converting === request.id ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          جاري التحويل...
                        </>
                      ) : (
                        <>
                          <ArrowRight className="w-4 h-4" />
                          تحويل إلى مشروع
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Modal تحويل الطلب إلى مشروع */}
        {showConvertModal && selectedRequest && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <h2 className="text-2xl font-bold mb-4">تحويل الطلب إلى مشروع</h2>
              
              <div className="space-y-6">
                {/* معلومات الطلب */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-semibold mb-2">{selectedRequest.title}</h3>
                  <p className="text-sm text-gray-600 mb-2">{selectedRequest.description}</p>
                  <div className="flex items-center gap-4 text-sm">
                    <span className="flex items-center gap-1">
                      <User className="w-4 h-4" />
                      {selectedRequest.requester.name}
                    </span>
                    <span className="flex items-center gap-1">
                      <Building className="w-4 h-4" />
                      {selectedRequest.department.name}
                    </span>
                  </div>
                </div>

                {/* إعدادات المشروع */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">مدير المشروع</label>
                    <select
                      value={assigneeId}
                      onChange={(e) => setAssigneeId(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    >
                      <option value="">اختر مدير المشروع</option>
                      {users.map(user => (
                        <option key={user.id} value={user.id}>
                          {user.name} - {user.role}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">الساعات المقدرة</label>
                    <Input
                      type="number"
                      value={estimatedHours}
                      onChange={(e) => setEstimatedHours(Number(e.target.value))}
                      placeholder="عدد الساعات المتوقعة"
                      min="0"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">العلامات (مفصولة بفواصل)</label>
                    <Input
                      value={tags}
                      onChange={(e) => setTags(e.target.value)}
                      placeholder="مثال: تحسين, تطوير, صيانة"
                    />
                  </div>
                </div>

                {/* أزرار التحكم */}
                <div className="flex justify-end gap-3 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowConvertModal(false)
                      setSelectedRequest(null)
                      setAssigneeId('')
                      setEstimatedHours(0)
                      setTags('')
                    }}
                  >
                    إلغاء
                  </Button>
                  <Button
                    onClick={handleConvertToProject}
                    disabled={!assigneeId || converting === selectedRequest.id}
                    className="flex items-center gap-2"
                  >
                    {converting === selectedRequest.id ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        جاري التحويل...
                      </>
                    ) : (
                      <>
                        <Plus className="w-4 h-4" />
                        إنشاء المشروع
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </ProtectedLayout>
  )
} 