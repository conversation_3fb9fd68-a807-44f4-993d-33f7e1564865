'use client'

import { useState } from 'react'
import { useAuth } from '@/lib/core/auth'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { ErrorMessage } from '@/components/ui/ErrorMessage'
import Link from 'next/link'
import { ArrowLeft, FileText, Play, CheckCircle, AlertCircle } from 'lucide-react'

type FormType = 'quick_win' | 'suggestion' | 'improvement_full'

export default function FormsTestPage() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<{[key: string]: any}>({})
  const [errors, setErrors] = useState<{[key: string]: string}>({})
  
  const { user } = useAuth()

  // بيانات اختبار لكويك وين
  const quickWinData = {
    request_number: `QW-${Date.now()}`,
    form_type: 'quick_win',
    form_data: {
      projectTitle: 'مشروع كويك وين - تحسين سرعة النظام',
      section: 'قسم تطوير النظم',
      projectExecutor: {
        name: 'مطور النظام',
        phone: '0501234567',
        email: '<EMAIL>'
      },
      problemDescription: 'بطء في استجابة النظام يؤثر على تجربة المستخدم',
      indicatorName: 'سرعة الاستجابة',
      currentValue: 5,
      targetValue: 2,
      unit: 'ثانية',
      improvementDirection: 'decrease',
      dataSource: 'مراقبة النظام',
      measurementMethod: 'قياس متوسط وقت الاستجابة',
      solution: {
        description: 'تحسين استعلامات قاعدة البيانات وتحسين الكاش',
        tasks: [
          { title: 'تحسين الاستعلامات', assignee: 'مطور قاعدة البيانات' },
          { title: 'تحسين نظام التخزين المؤقت', assignee: 'مطور الواجهة الخلفية' }
        ],
        implementationWeeks: 2,
        estimatedCost: 3000
      },
      teamLeader: {
        name: 'قائد فريق التطوير',
        phone: '0501234568',
        email: '<EMAIL>'
      }
    }
  }

  // بيانات اختبار للاقتراح
  const suggestionData = {
    request_number: `SG-${Date.now()}`,
    form_type: 'suggestion',
    form_data: {
      projectTitle: 'اقتراح تحسين - نظام إشعارات محسن',
      section: 'قسم تجربة المستخدم',
      projectExecutor: {
        name: 'مصمم التجربة',
        phone: '0501234569',
        email: '<EMAIL>'
      },
      problemDescription: 'نظام الإشعارات الحالي غير فعال ولا يجذب انتباه المستخدمين',
      indicatorName: 'معدل قراءة الإشعارات',
      currentValue: 30,
      targetValue: 80,
      unit: '%',
      improvementDirection: 'increase',
      dataSource: 'تحليلات النظام',
      measurementMethod: 'نسبة الإشعارات المقروءة إلى المرسلة'
    }
  }

  const testForm = async (formType: FormType, data: any) => {
    setLoading(true)
    setErrors({})
    
    try {
      console.log(`Testing ${formType} form with data:`, data)
      
      const response = await fetch('/api/requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      const result = await response.json()
      
      if (response.ok) {
        setResults(prev => ({
          ...prev,
          [formType]: {
            success: true,
            data: result,
            timestamp: new Date().toISOString()
          }
        }))
      } else {
        setErrors(prev => ({
          ...prev,
          [formType]: result.error || 'حدث خطأ غير متوقع'
        }))
      }
    } catch (error) {
      console.error(`Error testing ${formType}:`, error)
      setErrors(prev => ({
        ...prev,
        [formType]: 'فشل في الاتصال بالخادم'
      }))
    } finally {
      setLoading(false)
    }
  }

  const getResultIcon = (formType: string) => {
    if (results[formType]?.success) {
      return <CheckCircle className="w-5 h-5 text-green-500" />
    }
    if (errors[formType]) {
      return <AlertCircle className="w-5 h-5 text-red-500" />
    }
    return null
  }

  return (
    <ProtectedLayout>
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-6xl mx-auto">
          {/* Navigation */}
          <div className="mb-6">
            <Link 
              href="/testing" 
              className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              العودة إلى لوحة الاختبارات
            </Link>
          </div>

          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-3 mb-4">
              <FileText className="w-8 h-8 text-purple-600" />
              <h1 className="text-3xl font-bold text-gray-900">
                اختبار النماذج
              </h1>
            </div>
            <p className="text-gray-600 max-w-2xl mx-auto">
              اختبار شامل لجميع أنواع النماذج وعمليات الإرسال
            </p>
          </div>

          {/* User Info */}
          {user && (
            <Card className="mb-6 p-4 bg-blue-50 border-blue-200">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                  {user.name?.charAt(0) || 'U'}
                </div>
                <div>
                  <p className="font-medium text-gray-900">{user.name}</p>
                  <p className="text-sm text-gray-600">{user.email}</p>
                </div>
              </div>
            </Card>
          )}

          {/* Test Forms */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Quick Win Test */}
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  اختبار نموذج كويك وين
                </h3>
                {getResultIcon('quick_win')}
              </div>
              
              <p className="text-gray-600 mb-4">
                اختبار نموذج مشروع التحسين السريع (كويك وين)
              </p>

              <div className="space-y-3 mb-4">
                <div className="text-sm">
                  <span className="font-medium">العنوان:</span> {quickWinData.form_data.projectTitle}
                </div>
                <div className="text-sm">
                  <span className="font-medium">القسم:</span> {quickWinData.form_data.section}
                </div>
                <div className="text-sm">
                  <span className="font-medium">المؤشر:</span> {quickWinData.form_data.indicatorName}
                </div>
              </div>

              <Button
                onClick={() => testForm('quick_win', quickWinData)}
                disabled={loading}
                className="w-full"
              >
                {loading ? 'جاري الاختبار...' : 'اختبار النموذج'}
                <Play className="w-4 h-4 mr-2" />
              </Button>

              {errors.quick_win && (
                <ErrorMessage message={errors.quick_win} className="mt-3" />
              )}

              {results.quick_win && (
                <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-green-800 font-medium">تم الإرسال بنجاح!</p>
                  <p className="text-sm text-green-600">
                    رقم الطلب: {results.quick_win.data?.request_number}
                  </p>
                </div>
              )}
            </Card>

            {/* Suggestion Test */}
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  اختبار نموذج الاقتراح
                </h3>
                {getResultIcon('suggestion')}
              </div>
              
              <p className="text-gray-600 mb-4">
                اختبار نموذج مقترح التحسين
              </p>

              <div className="space-y-3 mb-4">
                <div className="text-sm">
                  <span className="font-medium">العنوان:</span> {suggestionData.form_data.projectTitle}
                </div>
                <div className="text-sm">
                  <span className="font-medium">القسم:</span> {suggestionData.form_data.section}
                </div>
                <div className="text-sm">
                  <span className="font-medium">المؤشر:</span> {suggestionData.form_data.indicatorName}
                </div>
              </div>

              <Button
                onClick={() => testForm('suggestion', suggestionData)}
                disabled={loading}
                className="w-full"
                variant="secondary"
              >
                {loading ? 'جاري الاختبار...' : 'اختبار النموذج'}
                <Play className="w-4 h-4 mr-2" />
              </Button>

              {errors.suggestion && (
                <ErrorMessage message={errors.suggestion} className="mt-3" />
              )}

              {results.suggestion && (
                <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-green-800 font-medium">تم الإرسال بنجاح!</p>
                  <p className="text-sm text-green-600">
                    رقم الطلب: {results.suggestion.data?.request_number}
                  </p>
                </div>
              )}
            </Card>
          </div>

          {/* Quick Links */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              href="/testing/forms/submission"
              className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow"
            >
              <h4 className="font-medium text-gray-900 mb-2">اختبار الإرسال</h4>
              <p className="text-sm text-gray-600">اختبار عملية إرسال النماذج</p>
            </Link>

            <Link
              href="/testing/forms/unified"
              className="p-4 bg-white rounded-lg border hover:shadow-md transition-shadow"
            >
              <h4 className="font-medium text-gray-900 mb-2">الاختبار الموحد</h4>
              <p className="text-sm text-gray-600">اختبار شامل لجميع النماذج</p>
            </Link>

            <Link
              href="/requests/create"
              className="p-4 bg-blue-50 rounded-lg border border-blue-200 hover:shadow-md transition-shadow"
            >
              <h4 className="font-medium text-blue-900 mb-2">إنشاء طلب حقيقي</h4>
              <p className="text-sm text-blue-600">انتقل لإنشاء طلب فعلي</p>
            </Link>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  )
}
