// مكتبة تشفير الملفات والأمان
import CryptoJS from 'crypto-js'

// مفتاح التشفير (في الإنتاج يجب أن يكون في متغيرات البيئة)
const ENCRYPTION_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'default-key-for-development'

export interface FileMetadata {
  originalName: string
  size: number
  type: string
  uploadDate: string
  uploadedBy: string
  checksum: string
  encrypted: boolean
}

export interface EncryptedFile {
  encryptedData: string
  metadata: FileMetadata
  iv: string
}

export interface AuditLog {
  id: string
  action: 'upload' | 'download' | 'delete' | 'view' | 'share'
  fileId: string
  fileName: string
  userId: string
  userName: string
  timestamp: string
  ipAddress?: string
  userAgent?: string
  success: boolean
  details?: string
}

/**
 * تشفير ملف
 */
export async function encryptFile(file: File, userId: string): Promise<EncryptedFile> {
  try {
    // قراءة الملف كـ ArrayBuffer
    const arrayBuffer = await file.arrayBuffer()
    const wordArray = CryptoJS.lib.WordArray.create(arrayBuffer)
    
    // إنشاء IV عشوائي
    const iv = CryptoJS.lib.WordArray.random(16)
    
    // تشفير البيانات
    const encrypted = CryptoJS.AES.encrypt(wordArray, ENCRYPTION_KEY, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })
    
    // حساب checksum للتحقق من سلامة البيانات
    const checksum = CryptoJS.SHA256(wordArray).toString()
    
    const metadata: FileMetadata = {
      originalName: file.name,
      size: file.size,
      type: file.type,
      uploadDate: new Date().toISOString(),
      uploadedBy: userId,
      checksum,
      encrypted: true
    }
    
    return {
      encryptedData: encrypted.toString(),
      metadata,
      iv: iv.toString()
    }
  } catch (error) {
    console.error('Error encrypting file:', error)
    throw new Error('فشل في تشفير الملف')
  }
}

/**
 * فك تشفير ملف
 */
export function decryptFile(encryptedFile: EncryptedFile): Promise<Blob> {
  try {
    const iv = CryptoJS.enc.Hex.parse(encryptedFile.iv)
    
    // فك التشفير
    const decrypted = CryptoJS.AES.decrypt(encryptedFile.encryptedData, ENCRYPTION_KEY, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })
    
    // تحويل إلى ArrayBuffer
    const arrayBuffer = new ArrayBuffer(decrypted.sigBytes)
    const uint8Array = new Uint8Array(arrayBuffer)
    
    for (let i = 0; i < decrypted.sigBytes; i++) {
      uint8Array[i] = (decrypted.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff
    }
    
    // التحقق من checksum
    const wordArray = CryptoJS.lib.WordArray.create(arrayBuffer)
    const checksum = CryptoJS.SHA256(wordArray).toString()
    
    if (checksum !== encryptedFile.metadata.checksum) {
      throw new Error('فشل في التحقق من سلامة البيانات')
    }
    
    return Promise.resolve(new Blob([arrayBuffer], { type: encryptedFile.metadata.type }))
  } catch (error) {
    console.error('Error decrypting file:', error)
    throw new Error('فشل في فك تشفير الملف')
  }
}

/**
 * فحص أمان الملف
 */
export function scanFileForSecurity(file: File): Promise<{ safe: boolean; threats: string[] }> {
  return new Promise((resolve) => {
    // محاكاة فحص الأمان
    setTimeout(() => {
      const threats: string[] = []
      
      // فحص امتداد الملف
      const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.vbs', '.js']
      const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
      
      if (dangerousExtensions.includes(extension)) {
        threats.push('امتداد ملف خطير')
      }
      
      // فحص حجم الملف (ملفات كبيرة جداً قد تكون مشبوهة)
      if (file.size > 100 * 1024 * 1024) { // 100MB
        threats.push('حجم ملف مشبوه')
      }
      
      // فحص اسم الملف
      const suspiciousPatterns = [/virus/i, /malware/i, /trojan/i, /hack/i]
      if (suspiciousPatterns.some(pattern => pattern.test(file.name))) {
        threats.push('اسم ملف مشبوه')
      }
      
      resolve({
        safe: threats.length === 0,
        threats
      })
    }, 1000) // محاكاة وقت الفحص
  })
}

/**
 * إنشاء سجل تدقيق
 */
export function createAuditLog(
  action: AuditLog['action'],
  fileId: string,
  fileName: string,
  userId: string,
  userName: string,
  success: boolean = true,
  details?: string
): AuditLog {
  return {
    id: Math.random().toString(36).substr(2, 9),
    action,
    fileId,
    fileName,
    userId,
    userName,
    timestamp: new Date().toISOString(),
    success,
    details
  }
}

/**
 * حفظ سجل التدقيق
 */
export async function saveAuditLog(log: AuditLog): Promise<void> {
  try {
    // في الإنتاج، يتم حفظ السجل في قاعدة البيانات
    console.log('Audit Log:', log)
    
    // محاكاة حفظ السجل
    const existingLogs = JSON.parse(localStorage.getItem('auditLogs') || '[]')
    existingLogs.push(log)
    localStorage.setItem('auditLogs', JSON.stringify(existingLogs))
  } catch (error) {
    console.error('Error saving audit log:', error)
  }
}

/**
 * استرجاع سجلات التدقيق
 */
export async function getAuditLogs(fileId?: string): Promise<AuditLog[]> {
  try {
    const logs = JSON.parse(localStorage.getItem('auditLogs') || '[]')
    
    if (fileId) {
      return logs.filter((log: AuditLog) => log.fileId === fileId)
    }
    
    return logs
  } catch (error) {
    console.error('Error getting audit logs:', error)
    return []
  }
}

/**
 * التحقق من صلاحيات الوصول للملف
 */
export function checkFilePermissions(
  fileId: string,
  userId: string,
  action: 'read' | 'write' | 'delete'
): boolean {
  // في الإنتاج، يتم التحقق من صلاحيات المستخدم من قاعدة البيانات
  
  // محاكاة فحص الصلاحيات
  const userPermissions = JSON.parse(localStorage.getItem('userPermissions') || '{}')
  const filePermissions = userPermissions[fileId] || []
  
  return filePermissions.includes(action) || filePermissions.includes('admin')
}

/**
 * إنشاء رابط آمن مؤقت للملف
 */
export function generateSecureFileLink(
  fileId: string,
  expirationMinutes: number = 60
): string {
  const expiration = Date.now() + (expirationMinutes * 60 * 1000)
  const token = CryptoJS.HmacSHA256(`${fileId}:${expiration}`, ENCRYPTION_KEY).toString()
  
  return `${window.location.origin}/api/files/${fileId}?token=${token}&expires=${expiration}`
}

/**
 * التحقق من صحة الرابط الآمن
 */
export function validateSecureFileLink(
  fileId: string,
  token: string,
  expires: string
): boolean {
  try {
    const expirationTime = parseInt(expires)
    
    // التحقق من انتهاء الصلاحية
    if (Date.now() > expirationTime) {
      return false
    }
    
    // التحقق من صحة التوقيع
    const expectedToken = CryptoJS.HmacSHA256(`${fileId}:${expires}`, ENCRYPTION_KEY).toString()
    
    return token === expectedToken
  } catch (error) {
    return false
  }
}

/**
 * ضغط الملف قبل التشفير (اختياري)
 */
export async function compressFile(file: File): Promise<File> {
  // محاكاة ضغط الملف
  // في الإنتاج، يمكن استخدام مكتبات الضغط
  return file
}

/**
 * إنشاء نسخة احتياطية من الملف
 */
export async function createFileBackup(
  encryptedFile: EncryptedFile,
  backupLocation: string
): Promise<boolean> {
  try {
    // محاكاة إنشاء نسخة احتياطية
    console.log(`Creating backup for file at: ${backupLocation}`)
    
    // في الإنتاج، يتم رفع النسخة الاحتياطية إلى التخزين السحابي
    return true
  } catch (error) {
    console.error('Error creating backup:', error)
    return false
  }
} 
import CryptoJS from 'crypto-js'

// مفتاح التشفير (في الإنتاج يجب أن يكون في متغيرات البيئة)
const ENCRYPTION_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'default-key-for-development'

export interface FileMetadata {
  originalName: string
  size: number
  type: string
  uploadDate: string
  uploadedBy: string
  checksum: string
  encrypted: boolean
}

export interface EncryptedFile {
  encryptedData: string
  metadata: FileMetadata
  iv: string
}

export interface AuditLog {
  id: string
  action: 'upload' | 'download' | 'delete' | 'view' | 'share'
  fileId: string
  fileName: string
  userId: string
  userName: string
  timestamp: string
  ipAddress?: string
  userAgent?: string
  success: boolean
  details?: string
}

/**
 * تشفير ملف
 */
export async function encryptFile(file: File, userId: string): Promise<EncryptedFile> {
  try {
    // قراءة الملف كـ ArrayBuffer
    const arrayBuffer = await file.arrayBuffer()
    const wordArray = CryptoJS.lib.WordArray.create(arrayBuffer)
    
    // إنشاء IV عشوائي
    const iv = CryptoJS.lib.WordArray.random(16)
    
    // تشفير البيانات
    const encrypted = CryptoJS.AES.encrypt(wordArray, ENCRYPTION_KEY, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })
    
    // حساب checksum للتحقق من سلامة البيانات
    const checksum = CryptoJS.SHA256(wordArray).toString()
    
    const metadata: FileMetadata = {
      originalName: file.name,
      size: file.size,
      type: file.type,
      uploadDate: new Date().toISOString(),
      uploadedBy: userId,
      checksum,
      encrypted: true
    }
    
    return {
      encryptedData: encrypted.toString(),
      metadata,
      iv: iv.toString()
    }
  } catch (error) {
    console.error('Error encrypting file:', error)
    throw new Error('فشل في تشفير الملف')
  }
}

/**
 * فك تشفير ملف
 */
export function decryptFile(encryptedFile: EncryptedFile): Promise<Blob> {
  try {
    const iv = CryptoJS.enc.Hex.parse(encryptedFile.iv)
    
    // فك التشفير
    const decrypted = CryptoJS.AES.decrypt(encryptedFile.encryptedData, ENCRYPTION_KEY, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })
    
    // تحويل إلى ArrayBuffer
    const arrayBuffer = new ArrayBuffer(decrypted.sigBytes)
    const uint8Array = new Uint8Array(arrayBuffer)
    
    for (let i = 0; i < decrypted.sigBytes; i++) {
      uint8Array[i] = (decrypted.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff
    }
    
    // التحقق من checksum
    const wordArray = CryptoJS.lib.WordArray.create(arrayBuffer)
    const checksum = CryptoJS.SHA256(wordArray).toString()
    
    if (checksum !== encryptedFile.metadata.checksum) {
      throw new Error('فشل في التحقق من سلامة البيانات')
    }
    
    return Promise.resolve(new Blob([arrayBuffer], { type: encryptedFile.metadata.type }))
  } catch (error) {
    console.error('Error decrypting file:', error)
    throw new Error('فشل في فك تشفير الملف')
  }
}

/**
 * فحص أمان الملف
 */
export function scanFileForSecurity(file: File): Promise<{ safe: boolean; threats: string[] }> {
  return new Promise((resolve) => {
    // محاكاة فحص الأمان
    setTimeout(() => {
      const threats: string[] = []
      
      // فحص امتداد الملف
      const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.vbs', '.js']
      const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
      
      if (dangerousExtensions.includes(extension)) {
        threats.push('امتداد ملف خطير')
      }
      
      // فحص حجم الملف (ملفات كبيرة جداً قد تكون مشبوهة)
      if (file.size > 100 * 1024 * 1024) { // 100MB
        threats.push('حجم ملف مشبوه')
      }
      
      // فحص اسم الملف
      const suspiciousPatterns = [/virus/i, /malware/i, /trojan/i, /hack/i]
      if (suspiciousPatterns.some(pattern => pattern.test(file.name))) {
        threats.push('اسم ملف مشبوه')
      }
      
      resolve({
        safe: threats.length === 0,
        threats
      })
    }, 1000) // محاكاة وقت الفحص
  })
}

/**
 * إنشاء سجل تدقيق
 */
export function createAuditLog(
  action: AuditLog['action'],
  fileId: string,
  fileName: string,
  userId: string,
  userName: string,
  success: boolean = true,
  details?: string
): AuditLog {
  return {
    id: Math.random().toString(36).substr(2, 9),
    action,
    fileId,
    fileName,
    userId,
    userName,
    timestamp: new Date().toISOString(),
    success,
    details
  }
}

/**
 * حفظ سجل التدقيق
 */
export async function saveAuditLog(log: AuditLog): Promise<void> {
  try {
    // في الإنتاج، يتم حفظ السجل في قاعدة البيانات
    console.log('Audit Log:', log)
    
    // محاكاة حفظ السجل
    const existingLogs = JSON.parse(localStorage.getItem('auditLogs') || '[]')
    existingLogs.push(log)
    localStorage.setItem('auditLogs', JSON.stringify(existingLogs))
  } catch (error) {
    console.error('Error saving audit log:', error)
  }
}

/**
 * استرجاع سجلات التدقيق
 */
export async function getAuditLogs(fileId?: string): Promise<AuditLog[]> {
  try {
    const logs = JSON.parse(localStorage.getItem('auditLogs') || '[]')
    
    if (fileId) {
      return logs.filter((log: AuditLog) => log.fileId === fileId)
    }
    
    return logs
  } catch (error) {
    console.error('Error getting audit logs:', error)
    return []
  }
}

/**
 * التحقق من صلاحيات الوصول للملف
 */
export function checkFilePermissions(
  fileId: string,
  userId: string,
  action: 'read' | 'write' | 'delete'
): boolean {
  // في الإنتاج، يتم التحقق من صلاحيات المستخدم من قاعدة البيانات
  
  // محاكاة فحص الصلاحيات
  const userPermissions = JSON.parse(localStorage.getItem('userPermissions') || '{}')
  const filePermissions = userPermissions[fileId] || []
  
  return filePermissions.includes(action) || filePermissions.includes('admin')
}

/**
 * إنشاء رابط آمن مؤقت للملف
 */
export function generateSecureFileLink(
  fileId: string,
  expirationMinutes: number = 60
): string {
  const expiration = Date.now() + (expirationMinutes * 60 * 1000)
  const token = CryptoJS.HmacSHA256(`${fileId}:${expiration}`, ENCRYPTION_KEY).toString()
  
  return `${window.location.origin}/api/files/${fileId}?token=${token}&expires=${expiration}`
}

/**
 * التحقق من صحة الرابط الآمن
 */
export function validateSecureFileLink(
  fileId: string,
  token: string,
  expires: string
): boolean {
  try {
    const expirationTime = parseInt(expires)
    
    // التحقق من انتهاء الصلاحية
    if (Date.now() > expirationTime) {
      return false
    }
    
    // التحقق من صحة التوقيع
    const expectedToken = CryptoJS.HmacSHA256(`${fileId}:${expires}`, ENCRYPTION_KEY).toString()
    
    return token === expectedToken
  } catch (error) {
    return false
  }
}

/**
 * ضغط الملف قبل التشفير (اختياري)
 */
export async function compressFile(file: File): Promise<File> {
  // محاكاة ضغط الملف
  // في الإنتاج، يمكن استخدام مكتبات الضغط
  return file
}

/**
 * إنشاء نسخة احتياطية من الملف
 */
export async function createFileBackup(
  encryptedFile: EncryptedFile,
  backupLocation: string
): Promise<boolean> {
  try {
    // محاكاة إنشاء نسخة احتياطية
    console.log(`Creating backup for file at: ${backupLocation}`)
    
    // في الإنتاج، يتم رفع النسخة الاحتياطية إلى التخزين السحابي
    return true
  } catch (error) {
    console.error('Error creating backup:', error)
    return false
  }
} 