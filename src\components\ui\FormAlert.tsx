'use client'

import React from 'react'
import { AlertCircle, CheckCircle, AlertTriangle, Info, X } from 'lucide-react'

export type AlertType = 'success' | 'error' | 'warning' | 'info'

interface FormAlertProps {
  type: AlertType
  title?: string
  message: string
  onClose?: () => void
  showIcon?: boolean
  className?: string
  children?: React.ReactNode
}

export function FormAlert({
  type,
  title,
  message,
  onClose,
  showIcon = true,
  className = '',
  children
}: FormAlertProps) {
  const getAlertStyles = () => {
    const baseStyles = 'flex items-start gap-3 p-4 rounded-lg border-2 transition-all duration-200'
    
    switch (type) {
      case 'success':
        return `${baseStyles} bg-green-50 border-green-200 text-green-800`
      case 'error':
        return `${baseStyles} bg-red-50 border-red-200 text-red-800`
      case 'warning':
        return `${baseStyles} bg-yellow-50 border-yellow-200 text-yellow-800`
      case 'info':
        return `${baseStyles} bg-blue-50 border-blue-200 text-blue-800`
      default:
        return `${baseStyles} bg-gray-50 border-gray-200 text-gray-800`
    }
  }

  const getIcon = () => {
    if (!showIcon) return null
    
    const iconClass = 'w-5 h-5 flex-shrink-0 mt-0.5'
    
    switch (type) {
      case 'success':
        return <CheckCircle className={`${iconClass} text-green-600`} />
      case 'error':
        return <AlertCircle className={`${iconClass} text-red-600`} />
      case 'warning':
        return <AlertTriangle className={`${iconClass} text-yellow-600`} />
      case 'info':
        return <Info className={`${iconClass} text-blue-600`} />
      default:
        return <Info className={`${iconClass} text-gray-600`} />
    }
  }

  return (
    <div className={`${getAlertStyles()} ${className}`}>
      {getIcon()}
      
      <div className="flex-1 min-w-0">
        {title && (
          <h4 className="font-medium text-sm leading-tight mb-1">
            {title}
          </h4>
        )}
        <p className="text-sm leading-relaxed">
          {message}
        </p>
        {children && (
          <div className="mt-2">
            {children}
          </div>
        )}
      </div>
      
      {onClose && (
        <button
          onClick={onClose}
          className="mr-2 p-1 rounded-full hover:bg-black hover:bg-opacity-10 transition-colors"
          aria-label="إغلاق التنبيه"
        >
          <X className="w-4 h-4" />
        </button>
      )}
    </div>
  )
}

// مكونات مساعدة للاستخدام السريع
export function SuccessAlert({ message, title, onClose, className, children }: Omit<FormAlertProps, 'type'>) {
  return (
    <FormAlert
      type="success"
      title={title}
      message={message}
      onClose={onClose}
      className={className}
    >
      {children}
    </FormAlert>
  )
}

export function ErrorAlert({ message, title, onClose, className, children }: Omit<FormAlertProps, 'type'>) {
  return (
    <FormAlert
      type="error"
      title={title}
      message={message}
      onClose={onClose}
      className={className}
    >
      {children}
    </FormAlert>
  )
}

export function WarningAlert({ message, title, onClose, className, children }: Omit<FormAlertProps, 'type'>) {
  return (
    <FormAlert
      type="warning"
      title={title}
      message={message}
      onClose={onClose}
      className={className}
    >
      {children}
    </FormAlert>
  )
}

export function InfoAlert({ message, title, onClose, className, children }: Omit<FormAlertProps, 'type'>) {
  return (
    <FormAlert
      type="info"
      title={title}
      message={message}
      onClose={onClose}
      className={className}
    >
      {children}
    </FormAlert>
  )
}

// مكون تنبيهات التحقق من صحة النموذج
interface ValidationAlertProps {
  errors: string[]
  onClose?: () => void
  className?: string
}

export function ValidationAlert({ errors, onClose, className = '' }: ValidationAlertProps) {
  if (errors.length === 0) return null

  return (
    <FormAlert
      type="error"
      title="يرجى تصحيح الأخطاء التالية:"
      message=""
      onClose={onClose}
      className={className}
    >
      <ul className="mt-2 space-y-1">
        {errors.map((error, index) => (
          <li key={index} className="text-sm text-red-700 flex items-start gap-2">
            <span className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2 flex-shrink-0"></span>
            {error}
          </li>
        ))}
      </ul>
    </FormAlert>
  )
}

// مكون تنبيهات الحالة
interface StatusAlertProps {
  status: 'loading' | 'success' | 'error' | 'idle'
  loadingMessage?: string
  successMessage?: string
  errorMessage?: string
  onRetry?: () => void
  className?: string
}

export function StatusAlert({
  status,
  loadingMessage = 'جاري المعالجة...',
  successMessage = 'تم بنجاح',
  errorMessage = 'حدث خطأ',
  onRetry,
  className = ''
}: StatusAlertProps) {
  if (status === 'idle') return null

  const getStatusProps = () => {
    switch (status) {
      case 'loading':
        return {
          type: 'info' as AlertType,
          message: loadingMessage,
          showIcon: false
        }
      case 'success':
        return {
          type: 'success' as AlertType,
          message: successMessage
        }
      case 'error':
        return {
          type: 'error' as AlertType,
          message: errorMessage
        }
      default:
        return {
          type: 'info' as AlertType,
          message: ''
        }
    }
  }

  const statusProps = getStatusProps()

  return (
    <FormAlert
      {...statusProps}
      className={className}
    >
      {status === 'loading' && (
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-sm text-blue-700">{loadingMessage}</span>
        </div>
      )}
      
      {status === 'error' && onRetry && (
        <button
          onClick={onRetry}
          className="mt-2 text-sm font-medium text-red-800 underline hover:no-underline transition-all"
        >
          إعادة المحاولة
        </button>
      )}
    </FormAlert>
  )
} 