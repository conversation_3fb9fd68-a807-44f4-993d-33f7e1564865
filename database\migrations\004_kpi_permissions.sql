-- Migration: صلاحيات نظام المؤشرات
-- التاريخ: 2024-01-26
-- الوصف: إعداد Row Level Security وصلاحيات المؤشرات

-- =============================================
-- تفعيل RLS للجداول الجديدة
-- =============================================
ALTER TABLE kpi_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_kpis ENABLE ROW LEVEL SECURITY;
ALTER TABLE kpi_measurements ENABLE ROW LEVEL SECURITY;
ALTER TABLE kpi_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE kpi_analysis ENABLE ROW LEVEL SECURITY;

-- =============================================
-- سياسات جدول أنواع المؤشرات (kpi_types)
-- =============================================

-- الجميع يمكنهم قراءة أنواع المؤشرات النشطة
CREATE POLICY "Everyone can view active KPI types" ON kpi_types
  FOR SELECT USING (is_active = true);

-- المدراء يمكنهم إدارة أنواع المؤشرات
CREATE POLICY "Managers can manage KPI types" ON kpi_types
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN roles r ON u.role_id = r.id 
      WHERE u.id::text = auth.uid()::text 
      AND r.name IN ('admin', 'pmo_manager')
    )
  );

-- =============================================
-- سياسات جدول المؤشرات (project_kpis)
-- =============================================

-- مقدمو الطلبات يمكنهم قراءة وتعديل مؤشرات مشاريعهم
CREATE POLICY "Requesters can manage their project KPIs" ON project_kpis
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM project_requests pr 
      WHERE pr.id = project_kpis.project_request_id 
      AND pr.requester_id::text = auth.uid()::text
    )
  );

-- قسم الجودة يمكنه مراجعة واعتماد جميع المؤشرات
CREATE POLICY "Quality team can review all KPIs" ON project_kpis
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN roles r ON u.role_id = r.id 
      JOIN departments d ON u.department_id = d.id
      WHERE u.id::text = auth.uid()::text 
      AND (r.name IN ('admin', 'pmo_manager') OR d.name = 'الجودة')
    )
  );

-- المدراء يمكنهم قراءة جميع المؤشرات
CREATE POLICY "Managers can view all KPIs" ON project_kpis
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN roles r ON u.role_id = r.id 
      WHERE u.id::text = auth.uid()::text 
      AND r.name IN ('admin', 'pmo_manager', 'planning_manager', 'executive_manager')
    )
  );

-- أعضاء الفريق يمكنهم قراءة مؤشرات مشاريعهم
CREATE POLICY "Team members can view their project KPIs" ON project_kpis
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM project_requests pr 
      JOIN users u ON pr.department_id = u.department_id
      WHERE pr.id = project_kpis.project_request_id 
      AND u.id::text = auth.uid()::text
    )
  );

-- =============================================
-- سياسات جدول قياسات المؤشرات (kpi_measurements)
-- =============================================

-- مقدمو الطلبات يمكنهم إضافة قياسات لمؤشرات مشاريعهم
CREATE POLICY "Requesters can add measurements to their KPIs" ON kpi_measurements
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM project_kpis pk
      JOIN project_requests pr ON pk.project_request_id = pr.id
      WHERE pk.id = kpi_measurements.kpi_id 
      AND pr.requester_id::text = auth.uid()::text
    )
  );

-- المستخدمون يمكنهم قراءة قياسات مؤشرات أقسامهم
CREATE POLICY "Users can view department KPI measurements" ON kpi_measurements
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM project_kpis pk
      JOIN project_requests pr ON pk.project_request_id = pr.id
      JOIN users u ON pr.department_id = u.department_id
      WHERE pk.id = kpi_measurements.kpi_id 
      AND u.id::text = auth.uid()::text
    )
  );

-- قسم الجودة يمكنه التحقق من جميع القياسات
CREATE POLICY "Quality team can verify all measurements" ON kpi_measurements
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN roles r ON u.role_id = r.id 
      JOIN departments d ON u.department_id = d.id
      WHERE u.id::text = auth.uid()::text 
      AND (r.name IN ('admin', 'pmo_manager') OR d.name = 'الجودة')
    )
  );

-- المدراء يمكنهم قراءة جميع القياسات
CREATE POLICY "Managers can view all measurements" ON kpi_measurements
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN roles r ON u.role_id = r.id 
      WHERE u.id::text = auth.uid()::text 
      AND r.name IN ('admin', 'pmo_manager', 'planning_manager', 'executive_manager')
    )
  );

-- =============================================
-- سياسات جدول قوالب المؤشرات (kpi_templates)
-- =============================================

-- الجميع يمكنهم قراءة القوالب النشطة
CREATE POLICY "Everyone can view active KPI templates" ON kpi_templates
  FOR SELECT USING (is_active = true);

-- المدراء وقسم الجودة يمكنهم إدارة القوالب
CREATE POLICY "Managers can manage KPI templates" ON kpi_templates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN roles r ON u.role_id = r.id 
      JOIN departments d ON u.department_id = d.id
      WHERE u.id::text = auth.uid()::text 
      AND (r.name IN ('admin', 'pmo_manager') OR d.name = 'الجودة')
    )
  );

-- =============================================
-- سياسات جدول تحليل المؤشرات (kpi_analysis)
-- =============================================

-- المحللون يمكنهم إنشاء تحليلات
CREATE POLICY "Analysts can create KPI analysis" ON kpi_analysis
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN roles r ON u.role_id = r.id 
      WHERE u.id::text = auth.uid()::text 
      AND r.name IN ('admin', 'pmo_manager', 'project_manager')
    )
  );

-- المستخدمون يمكنهم قراءة تحليلات مؤشرات أقسامهم
CREATE POLICY "Users can view department KPI analysis" ON kpi_analysis
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM project_kpis pk
      JOIN project_requests pr ON pk.project_request_id = pr.id
      JOIN users u ON pr.department_id = u.department_id
      WHERE pk.id = kpi_analysis.kpi_id 
      AND u.id::text = auth.uid()::text
    )
  );

-- المدراء يمكنهم قراءة جميع التحليلات
CREATE POLICY "Managers can view all KPI analysis" ON kpi_analysis
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN roles r ON u.role_id = r.id 
      WHERE u.id::text = auth.uid()::text 
      AND r.name IN ('admin', 'pmo_manager', 'planning_manager', 'executive_manager')
    )
  );

-- =============================================
-- تحديث صلاحيات الأدوار
-- =============================================

-- إضافة صلاحيات المؤشرات للأدوار الموجودة
UPDATE roles SET permissions = permissions || '{
  "kpis": ["read", "write", "approve", "analyze"]
}'::jsonb WHERE name = 'admin';

UPDATE roles SET permissions = permissions || '{
  "kpis": ["read", "write", "approve", "analyze"]
}'::jsonb WHERE name = 'pmo_manager';

UPDATE roles SET permissions = permissions || '{
  "kpis": ["read", "approve", "analyze"]
}'::jsonb WHERE name = 'planning_manager';

UPDATE roles SET permissions = permissions || '{
  "kpis": ["read"]
}'::jsonb WHERE name = 'executive_manager';

UPDATE roles SET permissions = permissions || '{
  "kpis": ["read", "write"]
}'::jsonb WHERE name = 'project_manager';

UPDATE roles SET permissions = permissions || '{
  "kpis": ["read", "write"]
}'::jsonb WHERE name = 'employee';

-- =============================================
-- إنشاء دور خاص لقسم الجودة
-- =============================================
INSERT INTO roles (name, display_name, description, permissions) VALUES
('quality_manager', 'مدير الجودة', 'مسؤول عن مراجعة واعتماد مؤشرات الأداء', '{
  "projects": ["read", "write"],
  "requests": ["read", "write"],
  "kpis": ["read", "write", "approve", "analyze", "verify"],
  "reports": ["read", "write"]
}')
ON CONFLICT (name) DO UPDATE SET
  permissions = EXCLUDED.permissions,
  description = EXCLUDED.description;

-- =============================================
-- إنشاء دالة للتحقق من صلاحيات المؤشرات
-- =============================================
CREATE OR REPLACE FUNCTION check_kpi_permission(
  user_id UUID,
  permission_type TEXT,
  kpi_id UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  user_role TEXT;
  user_dept TEXT;
  kpi_dept TEXT;
BEGIN
  -- الحصول على دور المستخدم وقسمه
  SELECT r.name, d.name INTO user_role, user_dept
  FROM users u
  JOIN roles r ON u.role_id = r.id
  JOIN departments d ON u.department_id = d.id
  WHERE u.id = user_id;
  
  -- التحقق من الصلاحيات العامة
  IF user_role IN ('admin', 'pmo_manager') THEN
    RETURN TRUE;
  END IF;
  
  -- التحقق من صلاحيات قسم الجودة
  IF user_dept = 'الجودة' AND permission_type IN ('approve', 'verify') THEN
    RETURN TRUE;
  END IF;
  
  -- التحقق من صلاحيات المؤشر المحدد
  IF kpi_id IS NOT NULL THEN
    SELECT d.name INTO kpi_dept
    FROM project_kpis pk
    JOIN project_requests pr ON pk.project_request_id = pr.id
    JOIN departments d ON pr.department_id = d.id
    WHERE pk.id = kpi_id;
    
    -- نفس القسم يمكنه القراءة والكتابة
    IF user_dept = kpi_dept AND permission_type IN ('read', 'write') THEN
      RETURN TRUE;
    END IF;
  END IF;
  
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- تعليقات على السياسات
-- =============================================
COMMENT ON POLICY "Everyone can view active KPI types" ON kpi_types IS 'جميع المستخدمين يمكنهم عرض أنواع المؤشرات النشطة';
COMMENT ON POLICY "Requesters can manage their project KPIs" ON project_kpis IS 'مقدمو الطلبات يمكنهم إدارة مؤشرات مشاريعهم';
COMMENT ON POLICY "Quality team can review all KPIs" ON project_kpis IS 'قسم الجودة يمكنه مراجعة جميع المؤشرات';

-- =============================================
-- إنشاء دالة لحساب معدل التحسن
-- =============================================
CREATE OR REPLACE FUNCTION calculate_improvement_rate(
  baseline_value DECIMAL,
  current_value DECIMAL,
  kpi_type TEXT
) RETURNS DECIMAL AS $$
BEGIN
  -- التحقق من القيم الصحيحة
  IF baseline_value IS NULL OR current_value IS NULL OR baseline_value = 0 THEN
    RETURN NULL;
  END IF;
  
  -- حساب معدل التحسن حسب نوع المؤشر
  CASE kpi_type
    WHEN 'time', 'cost', 'count' THEN
      -- للمؤشرات التي نريد تقليلها
      RETURN ROUND(((baseline_value - current_value) / baseline_value) * 100, 2);
    ELSE
      -- للمؤشرات التي نريد زيادتها
      RETURN ROUND(((current_value - baseline_value) / baseline_value) * 100, 2);
  END CASE;
END;
$$ LANGUAGE plpgsql; 