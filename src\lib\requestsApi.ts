import { supabase } from './supabase'

// واجهات البيانات
export interface RequestSubmissionData {
  request_number: string
  form_type: 'enhanced_improvement' | 'suggestion' | 'quick_win'
  form_data: any
  status: string
  created_by: string
  created_at: string
}

export interface DraftSubmissionData {
  form_type: 'enhanced_improvement' | 'suggestion' | 'quick_win'
  form_data: any
  user_id: string
  draft_name?: string
}

export interface RequestFilters {
  user_id?: string
  status?: string
  type?: string
  limit?: number
  offset?: number
}

// دوال API للطلبات
export class RequestsAPI {
  
  // إرسال طلب جديد
  static async submitRequest(data: RequestSubmissionData) {
    try {
      const response = await fetch('/api/requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في إرسال الطلب')
      }

      return await response.json()
    } catch (error) {
      console.error('Error submitting request:', error)
      throw error
    }
  }

  // حفظ مسودة
  static async saveDraft(data: DraftSubmissionData) {
    try {
      const response = await fetch('/api/requests/drafts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في حفظ المسودة')
      }

      return await response.json()
    } catch (error) {
      console.error('Error saving draft:', error)
      throw error
    }
  }

  // استرجاع الطلبات
  static async getRequests(filters: RequestFilters = {}) {
    try {
      const params = new URLSearchParams()
      
      if (filters.user_id) params.append('user_id', filters.user_id)
      if (filters.status) params.append('status', filters.status)
      if (filters.type) params.append('type', filters.type)

      const response = await fetch(`/api/requests?${params.toString()}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في استرجاع الطلبات')
      }

      return await response.json()
    } catch (error) {
      console.error('Error fetching requests:', error)
      throw error
    }
  }

  // استرجاع المسودات
  static async getDrafts(userId: string, formType?: string) {
    try {
      const params = new URLSearchParams({ user_id: userId })
      if (formType) params.append('form_type', formType)

      const response = await fetch(`/api/requests/drafts?${params.toString()}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في استرجاع المسودات')
      }

      return await response.json()
    } catch (error) {
      console.error('Error fetching drafts:', error)
      throw error
    }
  }

  // حذف مسودة
  static async deleteDraft(draftId: string, userId: string) {
    try {
      const params = new URLSearchParams({ draft_id: draftId, user_id: userId })
      
      const response = await fetch(`/api/requests/drafts?${params.toString()}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في حذف المسودة')
      }

      return await response.json()
    } catch (error) {
      console.error('Error deleting draft:', error)
      throw error
    }
  }

  // استرجاع طلب محدد
  static async getRequestById(requestId: string) {
    try {
      const { data, error } = await supabase
        .from('project_requests')
        .select(`
          *,
          requester:users!requester_id(name, email, department:departments(name)),
          department:departments!department_id(name),
          approvals(
            id,
            status,
            notes,
            approved_at,
            approver:users!approver_id(name, email)
          )
        `)
        .eq('id', requestId)
        .single()

      if (error) {
        throw new Error('حدث خطأ في استرجاع الطلب')
      }

      return { success: true, data }
    } catch (error) {
      console.error('Error fetching request:', error)
      throw error
    }
  }

  // تحديث حالة الطلب
  static async updateRequestStatus(requestId: string, status: string, notes?: string) {
    try {
      const { data, error } = await supabase
        .from('project_requests')
        .update({ status, updated_at: new Date().toISOString() })
        .eq('id', requestId)
        .select()
        .single()

      if (error) {
        throw new Error('حدث خطأ في تحديث حالة الطلب')
      }

      return { success: true, data }
    } catch (error) {
      console.error('Error updating request status:', error)
      throw error
    }
  }

  // إنشاء رقم طلب جديد
  static generateRequestNumber(formType: string): string {
    const year = new Date().getFullYear()
    const month = String(new Date().getMonth() + 1).padStart(2, '0')
    const day = String(new Date().getDate()).padStart(2, '0')
    const time = String(Date.now()).slice(-4)
    
    const prefix = formType === 'suggestion' ? 'SUG' : 'REQ'
    return `${prefix}-${year}${month}${day}-${time}`
  }

  // التحقق من صحة البيانات
  static validateFormData(formType: string, formData: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    // التحقق من الحقول الأساسية المشتركة
    if (!formData.problemDescription || formData.problemDescription.trim().length < 20) {
      errors.push('وصف المشكلة يجب أن يكون 20 حرف على الأقل')
    }

    if (!formData.indicatorName || formData.indicatorName.trim().length < 3) {
      errors.push('اسم المؤشر مطلوب')
    }

    if (!formData.currentValue && formData.currentValue !== 0) {
      errors.push('القيمة الحالية مطلوبة')
    }

    if (!formData.targetValue && formData.targetValue !== 0) {
      errors.push('القيمة المستهدفة مطلوبة')
    }

    if (!formData.improvementDirection) {
      errors.push('اتجاه التحسن مطلوب')
    }

    if (!formData.unit || formData.unit.trim().length < 1) {
      errors.push('وحدة القياس مطلوبة')
    }

    if (!formData.responsibleDepartment) {
      errors.push('القسم المسؤول مطلوب')
    }

    if (!formData.teamLeader?.name || formData.teamLeader.name.trim().length < 2) {
      errors.push('اسم قائد الفريق مطلوب')
    }

    if (!formData.teamLeader?.phone || formData.teamLeader.phone.trim().length < 10) {
      errors.push('رقم جوال قائد الفريق مطلوب')
    }

    if (!formData.teamLeader?.email || !formData.teamLeader.email.includes('@')) {
      errors.push('بريد إلكتروني صحيح لقائد الفريق مطلوب')
    }

    // التحقق من الحقول الخاصة بكل نوع
    switch (formType) {
      case 'quick_win':
        if (!formData.solution?.description || formData.solution.description.trim().length < 10) {
          errors.push('وصف الحل مطلوب (10 أحرف على الأقل)')
        }
        break

      case 'suggestion':
        if (!formData.suggestedSolutions || formData.suggestedSolutions.length === 0) {
          errors.push('حل واحد على الأقل مطلوب')
        }
        break

      case 'enhanced_improvement':
        if (!formData.selectedSolution?.title || formData.selectedSolution.title.trim().length < 5) {
          errors.push('عنوان الحل المختار مطلوب')
        }
        if (!formData.projectTasks || formData.projectTasks.length === 0) {
          errors.push('مهمة واحدة على الأقل مطلوبة')
        }
        break
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

// دوال مساعدة للتعامل مع البيانات
export class RequestDataHelpers {
  
  // تحويل بيانات النموذج إلى تنسيق قاعدة البيانات
  static formatForDatabase(formData: any, formType: string) {
    return {
      title: this.extractTitle(formData, formType),
      description: this.extractDescription(formData),
      business_case: this.extractBusinessCase(formData, formType),
      expected_benefits: this.extractBenefits(formData),
      risks_and_challenges: this.extractRisks(formData),
      estimated_budget: this.extractBudget(formData),
      expected_start_date: this.extractStartDate(formData),
      expected_end_date: this.extractEndDate(formData)
    }
  }

  private static extractTitle(formData: any, formType: string): string {
    const projectName = formData.projectName || ''
    switch (formType) {
      case 'quick_win':
        return projectName || 'مشروع كويك وين'
      case 'suggestion':
        return projectName || 'مقترح تحسين'
      case 'enhanced_improvement':
        return projectName || 'مشروع تحسين شامل'
      default:
        return projectName || 'طلب مشروع'
    }
  }

  private static extractDescription(formData: any): string {
    return formData.problemDescription || formData.projectDescription || ''
  }

  private static extractBusinessCase(formData: any, formType: string): string {
    const problem = formData.problemDescription || ''
    switch (formType) {
      case 'quick_win':
        return `المشكلة: ${problem}\nالحل: ${formData.solution?.description || ''}`
      case 'suggestion':
        const solutions = formData.suggestedSolutions?.map((s: any) => s.title).join(', ') || ''
        return `المشكلة: ${problem}\nالحلول المقترحة: ${solutions}`
      case 'enhanced_improvement':
        return `المشكلة: ${problem}\nالحل المختار: ${formData.selectedSolution?.title || ''}`
      default:
        return problem
    }
  }

  private static extractBenefits(formData: any): string {
    const indicator = formData.indicatorName || ''
    const current = formData.currentValue || 0
    const target = formData.targetValue || 0
    return `تحسين ${indicator} من ${current} إلى ${target}`
  }

  private static extractRisks(formData: any): string {
    if (formData.risks && formData.risks.length > 0) {
      return formData.risks.map((risk: any) => 
        `${risk.description} (احتمالية: ${risk.probability}, تأثير: ${risk.impact})`
      ).join('\n')
    }
    return 'لا توجد مخاطر محددة'
  }

  private static extractBudget(formData: any): number | null {
    return formData.estimatedCost || formData.budget || null
  }

  private static extractStartDate(formData: any): string | null {
    return formData.startDate || formData.expectedStartDate || null
  }

  private static extractEndDate(formData: any): string | null {
    return formData.endDate || formData.expectedEndDate || null
  }
} 