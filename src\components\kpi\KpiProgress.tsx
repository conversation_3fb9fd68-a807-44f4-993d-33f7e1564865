'use client'

import React from 'react'
import { Card } from '@/components/ui/Card'
import { KpiProgress as KpiProgressType } from '@/types/kpi.types'
import { 
  TrendingUp, 
  TrendingDown, 
  Minus, 
  Target,
  Calendar,
  Activity
} from 'lucide-react'

interface KpiProgressProps {
  kpiProgress: KpiProgressType
  showDetails?: boolean
  compact?: boolean
}

export function KpiProgress({ 
  kpiProgress, 
  showDetails = false, 
  compact = false 
}: KpiProgressProps) {
  const {
    kpi_name,
    current_value,
    target_value,
    baseline_value,
    unit,
    improvement_percentage,
    target_percentage,
    trend,
    status,
    last_updated
  } = kpiProgress

  const getTrendIcon = () => {
    switch (trend) {
      case 'improving':
        return <TrendingUp className="w-4 h-4 text-green-600" />
      case 'declining':
        return <TrendingDown className="w-4 h-4 text-red-600" />
      default:
        return <Minus className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'on_track':
        return 'bg-green-100 text-green-800'
      case 'at_risk':
        return 'bg-yellow-100 text-yellow-800'
      case 'behind':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = () => {
    switch (status) {
      case 'on_track':
        return 'على المسار الصحيح'
      case 'at_risk':
        return 'في خطر'
      case 'behind':
        return 'متأخر عن الهدف'
      default:
        return 'غير محدد'
    }
  }

  const formatValue = (value: number | null | undefined) => {
    if (value === null || value === undefined) return 'غير محدد'
    return `${value} ${unit || ''}`
  }

  const formatPercentage = (percentage: number | null | undefined) => {
    if (percentage === null || percentage === undefined) return '0%'
    return `${percentage > 0 ? '+' : ''}${percentage.toFixed(1)}%`
  }

  if (compact) {
    return (
      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Target className="w-4 h-4 text-gray-600" />
            <span className="font-medium text-gray-900">{kpi_name}</span>
          </div>
          <div className="flex items-center gap-1">
            {getTrendIcon()}
            <span className={`text-sm ${
              improvement_percentage && improvement_percentage > 0 
                ? 'text-green-600' 
                : improvement_percentage && improvement_percentage < 0 
                  ? 'text-red-600' 
                  : 'text-gray-600'
            }`}>
              {formatPercentage(improvement_percentage)}
            </span>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="text-sm text-gray-600">
            {formatValue(current_value)} / {formatValue(target_value)}
          </div>
          <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>
      </div>
    )
  }

  return (
    <Card className="p-6">
      <div className="space-y-4">
        {/* العنوان والحالة */}
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <Target className="w-5 h-5 text-blue-600" />
            <div>
              <h3 className="font-semibold text-gray-900">{kpi_name}</h3>
              {last_updated && (
                <div className="flex items-center gap-1 text-sm text-gray-500 mt-1">
                  <Calendar className="w-4 h-4" />
                  آخر تحديث: {new Date(last_updated).toLocaleDateString('ar-SA')}
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {getTrendIcon()}
            <span className={`px-3 py-1 text-sm rounded-full ${getStatusColor()}`}>
              {getStatusText()}
            </span>
          </div>
        </div>

        {/* القيم الرئيسية */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {formatValue(current_value)}
            </div>
            <div className="text-sm text-gray-600">القيمة الحالية</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {formatValue(target_value)}
            </div>
            <div className="text-sm text-gray-600">الهدف</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-500">
              {formatValue(baseline_value)}
            </div>
            <div className="text-sm text-gray-600">القيمة الأساسية</div>
          </div>
        </div>

        {/* شريط التقدم */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">التقدم نحو الهدف</span>
            <span className="font-medium">{target_percentage?.toFixed(1)}%</span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                (target_percentage || 0) >= 100 
                  ? 'bg-green-500' 
                  : (target_percentage || 0) >= 75 
                    ? 'bg-blue-500' 
                    : (target_percentage || 0) >= 50 
                      ? 'bg-yellow-500' 
                      : 'bg-red-500'
              }`}
              style={{ width: `${Math.min(target_percentage || 0, 100)}%` }}
            />
          </div>
        </div>

        {/* التحسن */}
        {improvement_percentage !== null && improvement_percentage !== undefined && (
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              <Activity className="w-4 h-4 text-gray-600" />
              <span className="text-sm text-gray-600">التحسن من القيمة الأساسية</span>
            </div>
            
            <div className="flex items-center gap-2">
              {getTrendIcon()}
              <span className={`font-medium ${
                improvement_percentage > 0 
                  ? 'text-green-600' 
                  : improvement_percentage < 0 
                    ? 'text-red-600' 
                    : 'text-gray-600'
              }`}>
                {formatPercentage(improvement_percentage)}
              </span>
            </div>
          </div>
        )}

        {/* التفاصيل الإضافية */}
        {showDetails && (
          <div className="border-t pt-4 space-y-3">
            <h4 className="font-medium text-gray-900">تفاصيل إضافية</h4>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">نوع الاتجاه:</span>
                <span className="font-medium mr-2">
                  {trend === 'improving' ? 'تحسن' : 
                   trend === 'declining' ? 'تراجع' : 'ثابت'}
                </span>
              </div>
              
              <div>
                <span className="text-gray-600">الفرق عن الهدف:</span>
                <span className="font-medium mr-2">
                  {current_value && target_value 
                    ? `${(current_value - target_value).toFixed(1)} ${unit || ''}`
                    : 'غير محدد'}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </Card>
  )
} 