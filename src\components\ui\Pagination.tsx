'use client'

import React from 'react'
import { But<PERSON> } from './Button'
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react'

interface PaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  showingFrom: number
  showingTo: number
  totalItems: number
  itemsPerPage?: number
  className?: string
}

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  showingFrom,
  showingTo,
  totalItems,
  itemsPerPage = 10,
  className = ''
}: PaginationProps) {
  // حساب الصفحات المرئية
  const getVisiblePages = () => {
    const visiblePages: (number | string)[] = []
    const maxVisiblePages = 7

    if (totalPages <= maxVisiblePages) {
      // إذا كان العدد الإجمالي للصفحات قليل، اعرض جميع الصفحات
      for (let i = 1; i <= totalPages; i++) {
        visiblePages.push(i)
      }
    } else {
      // إذا كان العدد كبير، اعرض الصفحات بذكاء
      if (currentPage <= 4) {
        // في البداية
        for (let i = 1; i <= 5; i++) {
          visiblePages.push(i)
        }
        visiblePages.push('...')
        visiblePages.push(totalPages)
      } else if (currentPage >= totalPages - 3) {
        // في النهاية
        visiblePages.push(1)
        visiblePages.push('...')
        for (let i = totalPages - 4; i <= totalPages; i++) {
          visiblePages.push(i)
        }
      } else {
        // في المنتصف
        visiblePages.push(1)
        visiblePages.push('...')
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          visiblePages.push(i)
        }
        visiblePages.push('...')
        visiblePages.push(totalPages)
      }
    }

    return visiblePages
  }

  const visiblePages = getVisiblePages()

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page)
    }
  }

  if (totalPages <= 1) {
    return (
      <div className={`flex justify-center items-center mt-6 ${className}`}>
        <p className="text-sm text-gray-600">
          عرض {totalItems} عنصر
        </p>
      </div>
    )
  }

  return (
    <div className={`flex flex-col items-center gap-4 mt-6 ${className}`}>
      {/* معلومات العرض */}
      <div className="text-sm text-gray-600">
        عرض {showingFrom} إلى {showingTo} من أصل {totalItems} عنصر
      </div>

      {/* أزرار الترقيم */}
      <div className="flex items-center gap-1">
        {/* زر الصفحة السابقة */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          icon={<ChevronRight className="w-4 h-4" />}
          className="px-3 py-2"
        >
          السابق
        </Button>

        {/* أرقام الصفحات */}
        <div className="flex items-center gap-1">
          {visiblePages.map((page, index) => {
            if (page === '...') {
              return (
                <div
                  key={`ellipsis-${index}`}
                  className="px-3 py-2 text-gray-500"
                >
                  <MoreHorizontal className="w-4 h-4" />
                </div>
              )
            }

            const pageNumber = page as number
            const isActive = pageNumber === currentPage

            return (
              <button
                key={pageNumber}
                onClick={() => handlePageChange(pageNumber)}
                className={`
                  px-3 py-2 min-w-[40px] h-10 text-sm font-medium rounded-lg
                  transition-all duration-200 ease-in-out
                  ${isActive
                    ? 'bg-blue-600 text-white shadow-md transform scale-105'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:border-gray-400'
                  }
                  ${!isActive && 'hover:shadow-sm'}
                  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
                `}
              >
                {pageNumber}
              </button>
            )
          })}
        </div>

        {/* زر الصفحة التالية */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          icon={<ChevronLeft className="w-4 h-4" />}
          className="px-3 py-2"
        >
          التالي
        </Button>
      </div>

      {/* معلومات إضافية */}
      <div className="text-xs text-gray-500">
        الصفحة {currentPage} من {totalPages}
      </div>
    </div>
  )
}

// Hook مساعد لإدارة الترقيم
export function usePagination<T>(
  data: T[],
  itemsPerPage: number = 10
) {
  const [currentPage, setCurrentPage] = React.useState(1)

  // حساب البيانات المرئية
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedData = data.slice(startIndex, endIndex)

  // حساب معلومات الترقيم
  const totalPages = Math.ceil(data.length / itemsPerPage)
  const showingFrom = data.length === 0 ? 0 : startIndex + 1
  const showingTo = Math.min(endIndex, data.length)
  const totalItems = data.length

  // إعادة تعيين الصفحة عند تغيير البيانات
  React.useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(1)
    }
  }, [totalPages, currentPage])

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page)
    }
  }

  const goToFirstPage = () => setCurrentPage(1)
  const goToLastPage = () => setCurrentPage(totalPages)
  const goToPreviousPage = () => setCurrentPage(Math.max(1, currentPage - 1))
  const goToNextPage = () => setCurrentPage(Math.min(totalPages, currentPage + 1))

  return {
    // البيانات
    paginatedData,
    
    // معلومات الترقيم
    currentPage,
    totalPages,
    showingFrom,
    showingTo,
    totalItems,
    itemsPerPage,
    
    // وظائف التنقل
    goToPage,
    goToFirstPage,
    goToLastPage,
    goToPreviousPage,
    goToNextPage,
    
    // حالة الترقيم
    isFirstPage: currentPage === 1,
    isLastPage: currentPage === totalPages,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1
  }
} 