'use client'

import React from 'react'
import { Input, Select } from '@/components/ui/Input'
import { Button } from '@/components/ui/Button'
import { FieldHelp } from '@/components/ui/HelpSystem'
import { Plus, Trash2, User, Phone, Building } from 'lucide-react'

interface TeamMember {
  name: string
  role: string
  phone: string
  department: string
}

interface TeamManagementProps {
  teamMembers: TeamMember[]
  onAddMember: () => void
  onRemoveMember: (index: number) => void
  onUpdateMember: (index: number, field: string, value: string) => void
  errors: Record<string, string>
  departments?: string[]
  className?: string
}

const defaultDepartments = [
  'إدارة الجودة', 'الشؤون الطبية', 'التمريض', 'المختبرات',
  'الأشعة', 'الصيدلة', 'خدمة العملاء', 'الموارد البشرية',
  'تقنية المعلومات', 'الشؤون المالية', 'الخدمات اللوجستية'
]

export function TeamManagement({
  teamMembers,
  onAddMember,
  onRemoveMember,
  onUpdateMember,
  errors,
  departments = defaultDepartments,
  className = ''
}: TeamManagementProps) {
  
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <User className="w-5 h-5 text-blue-600" />
          <h4 className="font-semibold text-gray-900">أعضاء الفريق</h4>
          <FieldHelp 
            content="أضف أعضاء الفريق المشاركين في المشروع"
            field="teamMembers"
            step={3}
          />
        </div>
        <Button onClick={onAddMember} variant="default" size="sm">
          <Plus className="w-4 h-4 ml-2" />
          إضافة عضو
        </Button>
      </div>

      <div className="space-y-4">
        {teamMembers.map((member, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h5 className="font-medium text-gray-900">عضو الفريق {index + 1}</h5>
              {teamMembers.length > 1 && (
                <Button
                  onClick={() => onRemoveMember(index)}
                  variant="danger"
                  size="sm"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <User className="w-4 h-4 inline ml-1" />
                  الاسم *
                </label>
                <Input
                  value={member.name}
                  onChange={(e) => onUpdateMember(index, 'name', e.target.value)}
                  placeholder="اسم العضو"
                  error={errors[`teamMembers.${index}.name`]}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Phone className="w-4 h-4 inline ml-1" />
                  رقم الجوال *
                </label>
                <Input
                  value={member.phone}
                  onChange={(e) => onUpdateMember(index, 'phone', e.target.value)}
                  placeholder="05xxxxxxxx"
                  error={errors[`teamMembers.${index}.phone`]}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المسمى الوظيفي *
                </label>
                <Input
                  value={member.role}
                  onChange={(e) => onUpdateMember(index, 'role', e.target.value)}
                  placeholder="مثال: مشرف جودة"
                  error={errors[`teamMembers.${index}.role`]}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Building className="w-4 h-4 inline ml-1" />
                  القسم *
                </label>
                <Select
                  value={member.department}
                  onChange={(e) => onUpdateMember(index, 'department', e.target.value)}
                  error={errors[`teamMembers.${index}.department`]}
                >
                  <option value="">اختر القسم</option>
                  {departments.map(dept => (
                    <option key={dept} value={dept}>{dept}</option>
                  ))}
                </Select>
              </div>
            </div>
          </div>
        ))}
      </div>

      {teamMembers.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <User className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p>لم يتم إضافة أعضاء فريق بعد</p>
          <p className="text-sm">انقر على &quot;إضافة عضو&quot; لبدء إضافة أعضاء الفريق</p>
        </div>
      )}
    </div>
  )
}

// مكون مبسط لقائد الفريق
interface TeamLeaderProps {
  teamLeader: {
    name: string
    phone: string
    email: string
    department: string
  }
  onUpdate: (field: string, value: string) => void
  errors: Record<string, string>
  departments?: string[]
  className?: string
}

export function TeamLeader({
  teamLeader,
  onUpdate,
  errors,
  departments = defaultDepartments,
  className = ''
}: TeamLeaderProps) {
  
  return (
    <div className={`bg-gray-50 border rounded-lg p-4 ${className}`}>
      <div className="flex items-center gap-2 mb-4">
        <User className="w-5 h-5 text-blue-600" />
        <h4 className="font-semibold text-gray-900">قائد الفريق</h4>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <label className="block text-sm font-medium text-gray-700">
              الاسم *
            </label>
            <FieldHelp 
              content="اسم الشخص المسؤول عن قيادة الفريق"
              field="teamLeaderName"
              step={3}
            />
          </div>
          <Input
            value={teamLeader.name}
            onChange={(e) => onUpdate('name', e.target.value)}
            placeholder="اسم قائد الفريق"
            error={errors['teamLeader.name']}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            رقم الجوال *
          </label>
          <Input
            value={teamLeader.phone}
            onChange={(e) => onUpdate('phone', e.target.value)}
            placeholder="05xxxxxxxx"
            error={errors['teamLeader.phone']}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            البريد الإلكتروني *
          </label>
          <Input
            value={teamLeader.email}
            onChange={(e) => onUpdate('email', e.target.value)}
            placeholder="<EMAIL>"
            type="email"
            error={errors['teamLeader.email']}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            القسم *
          </label>
          <Select
            value={teamLeader.department}
            onChange={(e) => onUpdate('department', e.target.value)}
            error={errors['teamLeader.department']}
          >
            <option value="">اختر القسم</option>
            {departments.map(dept => (
              <option key={dept} value={dept}>{dept}</option>
            ))}
          </Select>
        </div>
      </div>
    </div>
  )
} 