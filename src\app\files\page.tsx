'use client'

import { useState } from 'react'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { FileManager } from '@/components/files/FileManager'
import { AuditLogger } from '@/components/files/AuditLogger'
import { FolderOrganizer } from '@/components/files/FolderOrganizer'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  Files, 
  HardDrive, 
  Upload, 
  Download,
  Settings,
  Shield,
  BarChart3
} from 'lucide-react'

export default function FilesPage() {
  const [showAuditLogs, setShowAuditLogs] = useState(false)
  const [showFolderOrganizer, setShowFolderOrganizer] = useState(false)

  // إحصائيات وهمية
  const stats = {
    totalFiles: 156,
    totalSize: '2.4 GB',
    totalFolders: 12,
    recentUploads: 8
  }

  const handleFilesChange = (files: any[]) => {
    console.log('Files updated:', files)
  }

  return (
    <ProtectedLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Page Header */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">إدارة الملفات</h1>
            <p className="text-gray-600 mt-1">
              إدارة وتنظيم جميع ملفات ومرفقات المشاريع
            </p>
          </div>
          
          <div className="flex flex-wrap gap-3">
            <Button
              variant="ghost"
              icon={<Shield className="w-4 h-4" />}
              onClick={() => setShowAuditLogs(!showAuditLogs)}
            >
              {showAuditLogs ? 'إخفاء السجلات' : 'سجل الأمان'}
            </Button>
            
            <Button
              variant="ghost"
              icon={<Files className="w-4 h-4" />}
              onClick={() => setShowFolderOrganizer(!showFolderOrganizer)}
            >
              {showFolderOrganizer ? 'إخفاء المجلدات' : 'تنظيم المجلدات'}
            </Button>
            
            <Button
              variant="ghost"
              icon={<BarChart3 className="w-4 h-4" />}
            >
              الإحصائيات
            </Button>
            
            <Button
              variant="ghost"
              icon={<Download className="w-4 h-4" />}
            >
              تصدير
            </Button>
            
            <Button
              variant="ghost"
              icon={<Settings className="w-4 h-4" />}
            >
              الإعدادات
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي الملفات</p>
                  <p className="text-3xl font-bold text-blue-600">{stats.totalFiles}</p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Files className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">المساحة المستخدمة</p>
                  <p className="text-3xl font-bold text-green-600">{stats.totalSize}</p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <HardDrive className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">المجلدات</p>
                  <p className="text-3xl font-bold text-purple-600">{stats.totalFolders}</p>
                </div>
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Files className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">رفع حديث</p>
                  <p className="text-3xl font-bold text-orange-600">{stats.recentUploads}</p>
                </div>
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Upload className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Security Notice */}
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900">أمان الملفات</h4>
                <p className="text-sm text-blue-700 mt-1">
                  جميع الملفات محمية بالتشفير ومراقبة الوصول. يتم حفظ سجل لجميع العمليات.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Folder Organizer */}
        {showFolderOrganizer && (
          <Card>
            <CardContent className="p-6">
              <FolderOrganizer
                onFolderSelect={(folderId) => console.log('Selected folder:', folderId)}
                onFolderCreate={(name, parentId) => console.log('Create folder:', name, parentId)}
                onFolderDelete={(folderId) => console.log('Delete folder:', folderId)}
                onFolderMove={(folderId, targetId) => console.log('Move folder:', folderId, targetId)}
              />
            </CardContent>
          </Card>
        )}

        {/* Audit Logs */}
        {showAuditLogs && (
          <AuditLogger maxLogs={50} />
        )}

        {/* File Manager Component */}
        <Card>
          <CardContent className="p-6">
            <FileManager
              maxFiles={100}
              maxFileSize={50}
              acceptedTypes={[
                'image/*',
                'application/pdf',
                '.doc',
                '.docx',
                '.xls',
                '.xlsx',
                '.ppt',
                '.pptx',
                '.txt',
                '.zip',
                '.rar'
              ]}
              onFilesChange={handleFilesChange}
            />
          </CardContent>
        </Card>

        {/* Usage Guidelines */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">إرشادات الاستخدام</h3>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">أنواع الملفات المدعومة</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• الصور: JPG, PNG, GIF, SVG</li>
                  <li>• المستندات: PDF, DOC, DOCX, XLS, XLSX</li>
                  <li>• العروض التقديمية: PPT, PPTX</li>
                  <li>• الأرشيف: ZIP, RAR</li>
                  <li>• النصوص: TXT, CSV</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">قيود الرفع</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• الحد الأقصى لحجم الملف: 50 MB</li>
                  <li>• الحد الأقصى لعدد الملفات: 100 ملف</li>
                  <li>• يتم فحص الملفات تلقائياً للفيروسات</li>
                  <li>• يحفظ سجل لجميع عمليات الرفع والتحميل</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ProtectedLayout>
  )
} 