# إعدادات Supabase لمشروع إرادة PMO 2025
# انسخ هذا الملف إلى .env.local وحدث القيم

# معلومات المشروع الأساسية
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here

# مفتاح الخدمة (للعمليات الإدارية فقط - لا تعرضه في العميل)
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# إعدادات قاعدة البيانات
DATABASE_URL=postgresql://postgres:[password]@db.your-project-id.supabase.co:5432/postgres

# إعدادات المصادقة
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=http://localhost:3000

# إعدادات البريد الإلكتروني (اختيارية)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# إعدادات التطوير
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# إعدادات الأمان
JWT_SECRET=your-jwt-secret-here
ENCRYPTION_KEY=your-encryption-key-here

# إعدادات التخزين
NEXT_PUBLIC_SUPABASE_STORAGE_BUCKET=eradah-files

# إعدادات الإشعارات
ENABLE_NOTIFICATIONS=true
NOTIFICATION_EMAIL=<EMAIL>

# إعدادات المراقبة
ENABLE_ANALYTICS=true
SENTRY_DSN=your-sentry-dsn-here

# إعدادات الأداء
ENABLE_CACHING=true
CACHE_TTL=3600

# معلومات المؤسسة
ORGANIZATION_NAME=وزارة الداخلية
ORGANIZATION_DOMAIN=eradah.gov.sa
SUPPORT_EMAIL=<EMAIL>
