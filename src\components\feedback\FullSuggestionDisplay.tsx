'use client';

import React from 'react';
import { Card } from '@/components/ui/Card';

interface SubmitterInfo {
  name: string;
  department: string;
  position: string;
  email?: string;
  phone?: string;
  submissionDate: string;
}

interface FullSuggestionData {
  // المراحل السبع
  find: {
    problemDescription: string;
    currentSituation: string;
    targetAudience: string;
  };
  
  organize: {
    responsibleDepartment: {
      name: string;
      manager: string;
      managerPhone: string;
    };
    teamLeader: {
      name: string;
      phone: string;
      email?: string;
    };
    participatingDepartments: Array<{
      name: string;
      manager: {
        name: string;
        phone: string;
        email?: string;
      };
      involvement: string;
    }>;
  };
  
  clarify: {
    processDescription: string;
    processMap?: string; // رابط خريطة العملية
  };
  
  understand: {
    analysisMethod: 'whys_5' | 'fishbone' | 'root_cause_analysis';
    rootCause: string;
    analysisDetails?: string;
  };
  
  select: {
    proposedSolutions: Array<{
      id: string;
      title: string;
      description: string;
      expectedBenefits: string[];
      implementationSteps: string[];
      feasibilityScore?: number;
    }>;
  };
  
  recommendations: {
    finalRecommendations: string;
    implementationTips: string[];
  };
  
  // المؤشرات
  kpi: {
    name: string;
    currentValue: number;
    targetValue: number;
    unit: string;
    improvementDirection: 'increase' | 'decrease';
    improvementPercentage: number;
  };
}

interface FullSuggestionDisplayProps {
  suggestionData: FullSuggestionData;
  suggestionId: string;
  suggestionTitle: string;
  suggestionDescription: string;
  submitterInfo: SubmitterInfo;
}

export default function FullSuggestionDisplay({
  suggestionData,
  suggestionId,
  suggestionTitle,
  suggestionDescription,
  submitterInfo
}: FullSuggestionDisplayProps) {
  
  const getAnalysisMethodLabel = (method: string) => {
    const labels = {
      'whys_5': 'خمسة أسئلة لماذا - Whys 5',
      'fishbone': 'مخطط عظمة السمك - Fishbone',
      'root_cause_analysis': 'تحليل السبب الجذري - Root Cause Analysis'
    };
    return labels[method as keyof typeof labels] || method;
  };

  const getImprovementDirection = (direction: string) => {
    return direction === 'increase' ? 'زيادة' : 'تقليل';
  };

  return (
    <div className="space-y-6">
      {/* معلومات مقدم المقترح */}
      <Card className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              {suggestionTitle}
            </h2>
            <p className="text-gray-600 text-lg">
              {suggestionDescription}
            </p>
          </div>
          <div className="text-right">
            <span className="inline-block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
              مقترح تحسين
            </span>
            <div className="text-sm text-gray-500 mt-1">
              رقم المقترح: {suggestionId}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6 pt-6 border-t border-blue-200">
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <div>
              <div className="font-semibold text-gray-900">{submitterInfo.name}</div>
              <div className="text-sm text-gray-600">{submitterInfo.position}</div>
              <div className="text-sm text-gray-500">{submitterInfo.department}</div>
            </div>
          </div>

          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <div>
              <div className="text-sm text-gray-600">البريد الإلكتروني</div>
              <div className="font-medium text-gray-900">{submitterInfo.email || 'غير محدد'}</div>
              <div className="text-sm text-gray-500">{submitterInfo.phone || 'غير محدد'}</div>
            </div>
          </div>

          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <div>
              <div className="text-sm text-gray-600">تاريخ التقديم</div>
              <div className="font-medium text-gray-900">{submitterInfo.submissionDate}</div>
            </div>
          </div>
        </div>
      </Card>

      {/* المراحل السبع */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* المرحلة 1: العثور على المشكلة - Find */}
        <Card className="p-6">
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center ml-3">
              <span className="text-red-600 font-bold text-sm">1</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900">العثور على المشكلة - Find</h3>
          </div>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-800 mb-2">وصف المشكلة:</h4>
              <p className="text-gray-600 text-sm leading-relaxed">
                {suggestionData.find.problemDescription}
              </p>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-800 mb-2">الوضع الحالي:</h4>
              <p className="text-gray-600 text-sm leading-relaxed">
                {suggestionData.find.currentSituation}
              </p>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-800 mb-2">الجمهور المستهدف:</h4>
              <p className="text-gray-600 text-sm leading-relaxed">
                {suggestionData.find.targetAudience}
              </p>
            </div>
          </div>
        </Card>

        {/* المرحلة 2: تنظيم الفريق - Organize */}
        <Card className="p-6">
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center ml-3">
              <span className="text-orange-600 font-bold text-sm">2</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900">تنظيم الفريق - Organize</h3>
          </div>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-800 mb-2">القسم المسؤول:</h4>
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="font-medium text-gray-900">{suggestionData.organize.responsibleDepartment.name}</p>
                <p className="text-sm text-gray-600">المدير: {suggestionData.organize.responsibleDepartment.manager}</p>
                <p className="text-sm text-gray-500">هاتف: {suggestionData.organize.responsibleDepartment.managerPhone}</p>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-800 mb-2">قائد الفريق:</h4>
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="font-medium text-gray-900">{suggestionData.organize.teamLeader.name}</p>
                <p className="text-sm text-gray-600">هاتف: {suggestionData.organize.teamLeader.phone}</p>
                {suggestionData.organize.teamLeader.email && (
                  <p className="text-sm text-gray-500">بريد: {suggestionData.organize.teamLeader.email}</p>
                )}
              </div>
            </div>
            
            {suggestionData.organize.participatingDepartments.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-800 mb-2">الأقسام المشاركة:</h4>
                <div className="space-y-2">
                  {suggestionData.organize.participatingDepartments.map((dept, index) => (
                    <div key={index} className="bg-blue-50 p-3 rounded-lg">
                      <p className="font-medium text-gray-900">{dept.name}</p>
                      <p className="text-sm text-gray-600">المدير: {dept.manager.name}</p>
                      <p className="text-sm text-gray-500">مستوى المشاركة: {dept.involvement}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* المرحلة 3: توضيح العمليات - Clarify */}
        <Card className="p-6">
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center ml-3">
              <span className="text-yellow-600 font-bold text-sm">3</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900">توضيح العمليات - Clarify</h3>
          </div>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-800 mb-2">وصف العملية الحالية:</h4>
              <p className="text-gray-600 text-sm leading-relaxed">
                {suggestionData.clarify.processDescription}
              </p>
            </div>
            
            {suggestionData.clarify.processMap && (
              <div>
                <h4 className="font-medium text-gray-800 mb-2">خريطة العملية:</h4>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                  <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <p className="text-sm text-gray-500">تم إرفاق خريطة العملية</p>
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* المرحلة 4: فهم أسباب المشكلة - Understand */}
        <Card className="p-6">
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center ml-3">
              <span className="text-green-600 font-bold text-sm">4</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900">فهم أسباب المشكلة - Understand</h3>
          </div>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-800 mb-2">طريقة التحليل:</h4>
              <div className="bg-green-50 p-3 rounded-lg">
                <p className="font-medium text-green-800">
                  {getAnalysisMethodLabel(suggestionData.understand.analysisMethod)}
                </p>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-800 mb-2">السبب الجذري المحدد:</h4>
              <p className="text-gray-600 text-sm leading-relaxed">
                {suggestionData.understand.rootCause}
              </p>
            </div>
            
            {suggestionData.understand.analysisDetails && (
              <div>
                <h4 className="font-medium text-gray-800 mb-2">تفاصيل التحليل:</h4>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {suggestionData.understand.analysisDetails}
                </p>
              </div>
            )}
          </div>
        </Card>

        {/* المرحلة 5: اقتراح الحلول - Select */}
        <Card className="p-6 lg:col-span-2">
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center ml-3">
              <span className="text-blue-600 font-bold text-sm">5</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900">اقتراح الحلول - Select</h3>
          </div>
          
          <div className="space-y-4">
            <h4 className="font-medium text-gray-800 mb-3">الحلول المقترحة:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {suggestionData.select.proposedSolutions.map((solution, index) => (
                <div key={solution.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h5 className="font-semibold text-gray-900">{solution.title}</h5>
                    {solution.feasibilityScore && (
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">
                        جدوى: {solution.feasibilityScore}%
                      </span>
                    )}
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-3 leading-relaxed">
                    {solution.description}
                  </p>
                  
                  <div className="mb-3">
                    <h6 className="font-medium text-gray-800 text-sm mb-2">الفوائد المتوقعة:</h6>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {solution.expectedBenefits.map((benefit, idx) => (
                        <li key={idx} className="flex items-start">
                          <span className="text-green-500 ml-2">•</span>
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <h6 className="font-medium text-gray-800 text-sm mb-2">خطوات التنفيذ:</h6>
                    <ol className="text-sm text-gray-600 space-y-1">
                      {solution.implementationSteps.map((step, idx) => (
                        <li key={idx} className="flex items-start">
                          <span className="text-blue-500 ml-2 font-medium">{idx + 1}.</span>
                          {step}
                        </li>
                      ))}
                    </ol>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>

        {/* المرحلة 6: التوصيات النهائية - Recommendations */}
        <Card className="p-6">
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center ml-3">
              <span className="text-purple-600 font-bold text-sm">6</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900">التوصيات النهائية - Recommendations</h3>
          </div>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-800 mb-2">التوصيات النهائية:</h4>
              <p className="text-gray-600 text-sm leading-relaxed">
                {suggestionData.recommendations.finalRecommendations}
              </p>
            </div>
            
            {suggestionData.recommendations.implementationTips.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-800 mb-2">نصائح التنفيذ:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {suggestionData.recommendations.implementationTips.map((tip, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-purple-500 ml-2">•</span>
                      {tip}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </Card>

        {/* المؤشر المستهدف */}
        <Card className="p-6">
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center ml-3">
              <svg className="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900">المؤشر المستهدف</h3>
          </div>
          
          <div className="space-y-4">
            <div className="bg-indigo-50 p-4 rounded-lg">
              <h4 className="font-semibold text-indigo-900 mb-3">{suggestionData.kpi.name}</h4>
              
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600 mb-1">
                    {suggestionData.kpi.currentValue}{suggestionData.kpi.unit}
                  </div>
                  <div className="text-sm text-gray-600">القيمة الحالية</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600 mb-1">
                    {suggestionData.kpi.targetValue}{suggestionData.kpi.unit}
                  </div>
                  <div className="text-sm text-gray-600">القيمة المستهدفة</div>
                </div>
              </div>
              
              <div className="text-center p-3 bg-white rounded-lg">
                <div className="text-lg font-bold text-indigo-600 mb-1">
                  {suggestionData.kpi.improvementPercentage}%
                </div>
                <div className="text-sm text-gray-600">
                  نسبة التحسن المتوقعة ({getImprovementDirection(suggestionData.kpi.improvementDirection)})
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
} 