import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

// واجهة بيانات الإشعار الجماعي
interface BulkNotificationData {
  user_ids: string[]
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  action_url?: string
  data?: any
}

// POST - إرسال إشعارات جماعية
export async function POST(request: NextRequest) {
  try {
    const body: BulkNotificationData = await request.json()
    
    // التحقق من صحة البيانات
    if (!body.user_ids || !Array.isArray(body.user_ids) || body.user_ids.length === 0) {
      return NextResponse.json(
        { error: 'قائمة المستخدمين مطلوبة' },
        { status: 400 }
      )
    }

    if (!body.title || !body.message || !body.type) {
      return NextResponse.json(
        { error: 'العنوان والرسالة والنوع مطلوبان' },
        { status: 400 }
      )
    }

    // إنشاء الإشعارات لجميع المستخدمين
    const notifications = body.user_ids.map(userId => ({
      user_id: userId,
      title: body.title,
      message: body.message,
      type: body.type,
      action_url: body.action_url,
      data: body.data
    }))

    const { data, error } = await supabase
      .from('notifications')
      .insert(notifications)
      .select()

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في إرسال الإشعارات' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: `تم إرسال ${data.length} إشعار بنجاح`,
      data: {
        sent_count: data.length,
        notifications: data
      }
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// PUT - تحديث حالة إشعارات متعددة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { notification_ids, is_read, user_id } = body

    if (!notification_ids || !Array.isArray(notification_ids) || notification_ids.length === 0) {
      return NextResponse.json(
        { error: 'قائمة معرفات الإشعارات مطلوبة' },
        { status: 400 }
      )
    }

    if (is_read === undefined) {
      return NextResponse.json(
        { error: 'حالة القراءة مطلوبة' },
        { status: 400 }
      )
    }

    // تحديث حالة الإشعارات
    let query = supabase
      .from('notifications')
      .update({ 
        is_read,
        read_at: is_read ? new Date().toISOString() : null 
      })
      .in('id', notification_ids)

    // إضافة شرط المستخدم للأمان
    if (user_id) {
      query = query.eq('user_id', user_id)
    }

    const { data, error } = await query.select()

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في تحديث الإشعارات' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: `تم تحديث ${data.length} إشعار بنجاح`,
      data: {
        updated_count: data.length,
        notifications: data
      }
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// DELETE - حذف إشعارات متعددة
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json()
    const { notification_ids, user_id } = body

    if (!notification_ids || !Array.isArray(notification_ids) || notification_ids.length === 0) {
      return NextResponse.json(
        { error: 'قائمة معرفات الإشعارات مطلوبة' },
        { status: 400 }
      )
    }

    // حذف الإشعارات
    let query = supabase
      .from('notifications')
      .delete()
      .in('id', notification_ids)

    // إضافة شرط المستخدم للأمان
    if (user_id) {
      query = query.eq('user_id', user_id)
    }

    const { error } = await query

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في حذف الإشعارات' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الإشعارات بنجاح'
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
} 