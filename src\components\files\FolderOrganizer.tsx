'use client'

import { useState, useEffect } from 'react'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { 
  Folder, 
  FolderPlus, 
  Edit3, 
  Trash2, 
  Move,
  ChevronRight,
  ChevronDown,
  MoreVertical,
  Search,
  Filter
} from 'lucide-react'

interface FolderItem {
  id: string
  name: string
  parentId: string | null
  children: FolderItem[]
  fileCount: number
  size: string
  createdAt: string
  updatedAt: string
}

interface FolderOrganizerProps {
  onFolderSelect?: (folderId: string) => void
  onFolderCreate?: (name: string, parentId: string | null) => void
  onFolderDelete?: (folderId: string) => void
  onFolderMove?: (folderId: string, targetParentId: string | null) => void
}

export function FolderOrganizer({
  onFolderSelect,
  onFolderCreate,
  onFolderDelete,
  onFolderMove
}: FolderOrganizerProps) {
  const [folders, setFolders] = useState<FolderItem[]>([])
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set())
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [newFolderName, setNewFolderName] = useState('')
  const [newFolderParent, setNewFolderParent] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [showActions, setShowActions] = useState<string | null>(null)

  useEffect(() => {
    loadFolders()
  }, [])

  const loadFolders = async () => {
    // محاكاة تحميل البيانات من API
    const mockFolders: FolderItem[] = [
      {
        id: '1',
        name: 'مشاريع 2024',
        parentId: null,
        children: [
          {
            id: '2',
            name: 'مشروع التطوير الرقمي',
            parentId: '1',
            children: [],
            fileCount: 15,
            size: '45.2 MB',
            createdAt: '2024-01-15',
            updatedAt: '2024-01-20'
          },
          {
            id: '3',
            name: 'مشروع التحسين التشغيلي',
            parentId: '1',
            children: [],
            fileCount: 8,
            size: '23.1 MB',
            createdAt: '2024-01-18',
            updatedAt: '2024-01-22'
          }
        ],
        fileCount: 23,
        size: '68.3 MB',
        createdAt: '2024-01-01',
        updatedAt: '2024-01-22'
      },
      {
        id: '4',
        name: 'أرشيف المشاريع',
        parentId: null,
        children: [],
        fileCount: 156,
        size: '2.1 GB',
        createdAt: '2023-01-01',
        updatedAt: '2023-12-31'
      }
    ]

    setFolders(mockFolders)
  }

  const toggleFolder = (folderId: string) => {
    const newExpanded = new Set(expandedFolders)
    if (newExpanded.has(folderId)) {
      newExpanded.delete(folderId)
    } else {
      newExpanded.add(folderId)
    }
    setExpandedFolders(newExpanded)
  }

  const handleFolderSelect = (folderId: string) => {
    setSelectedFolder(folderId)
    onFolderSelect?.(folderId)
  }

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) return

    // محاكاة إنشاء مجلد جديد
    const newFolder: FolderItem = {
      id: Date.now().toString(),
      name: newFolderName,
      parentId: newFolderParent,
      children: [],
      fileCount: 0,
      size: '0 KB',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    onFolderCreate?.(newFolderName, newFolderParent)
    
    setNewFolderName('')
    setNewFolderParent(null)
    setIsCreating(false)
    
    // تحديث القائمة
    await loadFolders()
  }

  const handleDeleteFolder = async (folderId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المجلد؟ سيتم حذف جميع الملفات والمجلدات الفرعية.')) {
      onFolderDelete?.(folderId)
      await loadFolders()
    }
  }

  const renderFolder = (folder: FolderItem, level: number = 0) => {
    const isExpanded = expandedFolders.has(folder.id)
    const isSelected = selectedFolder === folder.id
    const hasChildren = folder.children.length > 0

    return (
      <div key={folder.id} className="select-none">
        <div
          className={`
            flex items-center gap-2 p-2 rounded-lg cursor-pointer transition-colors
            ${isSelected ? 'bg-blue-100 border border-blue-300' : 'hover:bg-gray-50'}
          `}
          style={{ paddingRight: `${level * 20 + 8}px` }}
          onClick={() => handleFolderSelect(folder.id)}
        >
          {/* Expand/Collapse Button */}
          {hasChildren ? (
            <button
              onClick={(e) => {
                e.stopPropagation()
                toggleFolder(folder.id)
              }}
              className="p-1 hover:bg-gray-200 rounded"
            >
              {isExpanded ? (
                <ChevronDown className="w-4 h-4 text-gray-600" />
              ) : (
                <ChevronRight className="w-4 h-4 text-gray-600" />
              )}
            </button>
          ) : (
            <div className="w-6 h-6" />
          )}

          {/* Folder Icon */}
          <Folder className="w-5 h-5 text-blue-600 flex-shrink-0" />

          {/* Folder Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <span className="font-medium text-gray-900 truncate">{folder.name}</span>
              
              {/* Actions Menu */}
              <div className="relative">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    setShowActions(showActions === folder.id ? null : folder.id)
                  }}
                  className="p-1 hover:bg-gray-200 rounded opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <MoreVertical className="w-4 h-4 text-gray-600" />
                </button>

                {showActions === folder.id && (
                  <div className="absolute left-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-32">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        setNewFolderParent(folder.id)
                        setIsCreating(true)
                        setShowActions(null)
                      }}
                      className="w-full text-right px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                    >
                      <FolderPlus className="w-4 h-4" />
                      إنشاء مجلد فرعي
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        // إضافة منطق إعادة التسمية
                        setShowActions(null)
                      }}
                      className="w-full text-right px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                    >
                      <Edit3 className="w-4 h-4" />
                      إعادة تسمية
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleDeleteFolder(folder.id)
                        setShowActions(null)
                      }}
                      className="w-full text-right px-3 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                    >
                      <Trash2 className="w-4 h-4" />
                      حذف
                    </button>
                  </div>
                )}
              </div>
            </div>
            
            <div className="text-xs text-gray-500 mt-1">
              {folder.fileCount} ملف • {folder.size}
            </div>
          </div>
        </div>

        {/* Children */}
        {isExpanded && hasChildren && (
          <div>
            {folder.children.map(child => renderFolder(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  const filteredFolders = folders.filter(folder =>
    folder.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">تنظيم المجلدات</h3>
        <Button
          variant="primary"
          size="sm"
          icon={<FolderPlus className="w-4 h-4" />}
          onClick={() => {
            setNewFolderParent(null)
            setIsCreating(true)
          }}
        >
          إنشاء مجلد جديد
        </Button>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <Input
          placeholder="بحث في المجلدات..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-3 pr-10"
        />
      </div>

      {/* Create Folder Form */}
      {isCreating && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  اسم المجلد الجديد
                </label>
                <Input
                  value={newFolderName}
                  onChange={(e) => setNewFolderName(e.target.value)}
                  placeholder="أدخل اسم المجلد..."
                  autoFocus
                />
              </div>
              
              {newFolderParent && (
                <div className="text-sm text-gray-600">
                  سيتم إنشاء المجلد داخل: {folders.find(f => f.id === newFolderParent)?.name}
                </div>
              )}
              
              <div className="flex gap-2">
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleCreateFolder}
                  disabled={!newFolderName.trim()}
                >
                  إنشاء
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setIsCreating(false)
                    setNewFolderName('')
                    setNewFolderParent(null)
                  }}
                >
                  إلغاء
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Folders Tree */}
      <Card>
        <CardContent className="p-4">
          {filteredFolders.length === 0 ? (
            <div className="text-center py-8">
              <Folder className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مجلدات</h3>
              <p className="text-gray-600 mb-4">
                ابدأ بإنشاء مجلد جديد لتنظيم ملفاتك
              </p>
              <Button
                variant="primary"
                icon={<FolderPlus className="w-4 h-4" />}
                onClick={() => {
                  setNewFolderParent(null)
                  setIsCreating(true)
                }}
              >
                إنشاء مجلد جديد
              </Button>
            </div>
          ) : (
            <div className="space-y-1 group">
              {filteredFolders.map(folder => renderFolder(folder))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Click outside to close actions menu */}
      {showActions && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowActions(null)}
        />
      )}
    </div>
  )
} 