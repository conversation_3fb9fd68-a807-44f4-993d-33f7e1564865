import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'

// واجهة بيانات الموافقة
interface ApprovalData {
  request_id: string
  approver_id: string
  decision: 'approved' | 'rejected'
  notes?: string
}

// POST - معالجة موافقة
export async function POST(request: NextRequest) {
  try {
    const body: ApprovalData = await request.json()
    
    // التحقق من صحة البيانات
    if (!body.request_id || !body.approver_id || !body.decision) {
      return NextResponse.json(
        { error: 'البيانات المطلوبة غير مكتملة' },
        { status: 400 }
      )
    }

    // التحقق من وجود الطلب
    const { data: projectRequest, error: requestError } = await supabaseAdmin
      .from('project_requests')
      .select('*')
      .eq('id', body.request_id)
      .single()

    if (requestError || !projectRequest) {
      return NextResponse.json(
        { error: 'الطلب غير موجود' },
        { status: 404 }
      )
    }

    // إنشاء موافقة جديدة أو تحديث موافقة موجودة
    const { data: existingApproval, error: approvalError } = await supabaseAdmin
      .from('approvals')
      .select('*')
      .eq('request_id', body.request_id)
      .eq('approver_id', body.approver_id)
      .maybeSingle()

    let approvalResult
    if (existingApproval) {
      // تحديث الموافقة الموجودة
      approvalResult = await supabaseAdmin
        .from('approvals')
        .update({
          status: body.decision,
          notes: body.notes,
          approved_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', existingApproval.id)
    } else {
      // إنشاء موافقة جديدة
      approvalResult = await supabaseAdmin
        .from('approvals')
        .insert({
          request_id: body.request_id,
          approver_id: body.approver_id,
          status: body.decision,
          notes: body.notes,
          approved_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
    }

    if (approvalResult.error) {
      console.error('Error updating/creating approval:', approvalResult.error)
      return NextResponse.json(
        { error: 'حدث خطأ في معالجة الموافقة' },
        { status: 500 }
      )
    }

    // معالجة الخطوة التالية في سير العمل
    const workflowResult = await processWorkflowStep(
      body.request_id,
      body.decision,
      projectRequest
    )

    if (!workflowResult.success) {
      return NextResponse.json(
        { error: workflowResult.error },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: body.decision === 'approved' ? 'تم اعتماد الطلب بنجاح' : 'تم رفض الطلب',
      next_step: workflowResult.nextStep
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// GET - استرجاع الموافقات
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const approverId = searchParams.get('approver_id')
    const status = searchParams.get('status')
    const requestId = searchParams.get('request_id')

    let query = supabaseAdmin
      .from('approvals')
      .select(`
        *,
        request:project_requests(
          id,
          title,
          description,
          main_type,
          sub_type,
          status,
          priority,
          estimated_budget,
          created_at,
          requester:users!requester_id(name, email),
          department:departments!department_id(name)
        ),
        approver:users!approver_id(name, email, role:roles(display_name))
      `)

    // تطبيق الفلاتر
    if (approverId) {
      query = query.eq('approver_id', approverId)
    }
    if (status) {
      query = query.eq('status', status)
    }
    if (requestId) {
      query = query.eq('request_id', requestId)
    }

    const { data, error } = await query.order('created_at', { ascending: false })

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في استرجاع البيانات' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: data || []
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// دالة معالجة خطوات سير العمل
async function processWorkflowStep(
  requestId: string,
  decision: 'approved' | 'rejected',
  projectRequest: any
): Promise<{ success: boolean; error?: string; nextStep?: string }> {
  try {
    if (decision === 'rejected') {
      // رفض الطلب
      const { error } = await supabaseAdmin
        .from('project_requests')
        .update({ 
          status: 'rejected',
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId)

      if (error) throw error

      // إرسال إشعار للمقدم
      await sendNotification(
        projectRequest.requester_id,
        'تم رفض الطلب',
        `تم رفض طلب المشروع: ${projectRequest.title}`,
        'error',
        `/requests/${requestId}`
      )

      return { success: true, nextStep: 'rejected' }
    }

    // الموافقة - تحديد الخطوة التالية
    const requiredLevels = getRequiredApprovalLevels(
      projectRequest.main_type,
      projectRequest.sub_type
    )
    const currentLevel = projectRequest.approval_level || 1
    const nextLevel = currentLevel + 1

    if (nextLevel > requiredLevels) {
      // اعتماد نهائي
      const { error } = await supabaseAdmin
        .from('project_requests')
        .update({ 
          status: 'approved',
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId)

      if (error) throw error

      // إرسال إشعار للمقدم
      await sendNotification(
        projectRequest.requester_id,
        'تم اعتماد الطلب',
        `تم اعتماد طلب المشروع: ${projectRequest.title}`,
        'success',
        `/requests/${requestId}`
      )

      return { success: true, nextStep: 'approved' }
    } else {
      // الانتقال للمستوى التالي
      const nextApproverId = await getApproverByLevel(nextLevel)
      
      if (!nextApproverId) {
        return { success: false, error: 'لم يتم العثور على معتمد للمستوى التالي' }
      }

      // تحديث مستوى الموافقة
      const { error: updateError } = await supabaseAdmin
        .from('project_requests')
        .update({ 
          approval_level: nextLevel,
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId)

      if (updateError) throw updateError

      // إنشاء موافقة للمستوى التالي
      const { error: approvalError } = await supabaseAdmin
        .from('approvals')
        .insert({
          request_id: requestId,
          approver_id: nextApproverId,
          status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (approvalError) throw approvalError

      // إرسال إشعار للمعتمد التالي
      await sendNotification(
        nextApproverId,
        'طلب جديد للاعتماد',
        `يوجد طلب جديد يحتاج لاعتمادك: ${projectRequest.title}`,
        'info',
        `/approvals/${requestId}`
      )

      return { success: true, nextStep: `level_${nextLevel}` }
    }

  } catch (error) {
    console.error('Workflow processing error:', error)
    return { success: false, error: 'حدث خطأ في معالجة سير العمل' }
  }
}

// دالة تحديد مستويات الموافقة المطلوبة
function getRequiredApprovalLevels(mainType: string, subType: string | null): number {
  if (mainType === 'improvement_project') {
    if (subType === 'quick_win') return 1 // مدير مكتب المشاريع فقط
    if (subType === 'suggestion') return 2 // مكتب المشاريع + التخطيط
    if (subType === 'comprehensive') return 3 // مكتب المشاريع + التخطيط + التنفيذي
  }
  
  if (mainType === 'general_project') {
    return 3 // جميع المستويات
  }
  
  return 1 // افتراضي
}

// دالة الحصول على معتمد حسب المستوى
async function getApproverByLevel(level: number): Promise<string | null> {
  try {
    let roleName = ''
    
    switch (level) {
      case 1:
        roleName = 'مدير مكتب المشاريع'
        break
      case 2:
        roleName = 'مدير إدارة التخطيط'
        break
      case 3:
        roleName = 'المدير التنفيذي'
        break
      default:
        return null
    }

    const roleId = await getRoleId(roleName)
    if (!roleId) return null

    // البحث عن مستخدم بهذا الدور
    const { data: user, error } = await supabaseAdmin
      .from('users')
      .select('id')
      .eq('role_id', roleId)
      .eq('is_active', true)
      .limit(1)
      .single()

    if (error || !user) return null

    return user.id
  } catch (error) {
    console.error('Error getting approver by level:', error)
    return null
  }
}

// دالة مساعدة للحصول على معرف الدور
async function getRoleId(roleName: string): Promise<string | null> {
  try {
    const { data: role, error } = await supabaseAdmin
      .from('roles')
      .select('id')
      .eq('display_name', roleName)
      .single()

    if (error || !role) return null

    return role.id
  } catch (error) {
    console.error('Error getting role ID:', error)
    return null
  }
}

// دالة إرسال الإشعارات
async function sendNotification(
  userId: string,
  title: string,
  message: string,
  type: 'info' | 'success' | 'warning' | 'error',
  actionUrl?: string
): Promise<void> {
  try {
    await supabaseAdmin
      .from('notifications')
      .insert({
        user_id: userId,
        title,
        message,
        type,
        action_url: actionUrl,
        is_read: false,
        created_at: new Date().toISOString()
      })
  } catch (error) {
    console.error('Error sending notification:', error)
    // لا نرمي خطأ هنا لأن الإشعار ليس أساسياً
  }
} 