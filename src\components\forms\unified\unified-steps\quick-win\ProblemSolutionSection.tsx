'use client'

import React from 'react'
import { Input, Textarea } from '@/components/ui/Input'
import { Card } from '@/components/ui/Card'
import { FieldHelp } from '@/components/ui/HelpSystem'
import { AlertTriangle, Lightbulb } from 'lucide-react'

interface ProblemSolutionSectionProps {
  data: any
  updateData: (field: string, value: any) => void
  errors: Record<string, string>
}

export function ProblemSolutionSection({ data, updateData, errors }: ProblemSolutionSectionProps) {
  return (
    <div className="space-y-6">
      {/* وصف المشكلة */}
      <Card className="p-6 border-red-200 bg-red-50">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center font-bold">
            <AlertTriangle className="w-4 h-4" />
          </div>
          <h3 className="text-lg font-bold text-red-800">وصف المشكلة بالتفصيل</h3>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            وصف المشكلة *
          </label>
          <FieldHelp 
            field="problemDescription" 
            step={1} 
            content="اكتب وصفاً دقيقاً للمشكلة مع ذكر الأرقام والحقائق المحددة" 
          />
          <Textarea
            value={data.problemDescription || ''}
            onChange={(e) => updateData('problemDescription', e.target.value)}
            placeholder="مثال: تأخير في تسليم التقارير يصل إلى 3 أيام في 40% من الحالات، مما يؤثر على رضا العملاء ويزيد الشكاوى بنسبة 25%"
            rows={4}
            error={errors.problemDescription}
            className="mt-1"
          />
        </div>
      </Card>

      {/* الحل المقترح */}
      <Card className="p-6 border-green-200 bg-green-50">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center font-bold">
            <Lightbulb className="w-4 h-4" />
          </div>
          <h3 className="text-lg font-bold text-green-800">الحل المقترح</h3>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            وصف الحل *
          </label>
          <FieldHelp 
            field="proposedSolution" 
            step={1} 
            content="اشرح الحل المقترح بوضوح مع ذكر الخطوات الأساسية" 
          />
          <Textarea
            value={data.solution?.description || ''}
            onChange={(e) => updateData('solution.description', e.target.value)}
            placeholder="مثال: تطبيق نظام تتبع إلكتروني للتقارير مع تنبيهات تلقائية للمسؤولين عند اقتراب موعد التسليم"
            rows={4}
            error={errors['solution.description']}
            className="mt-1"
          />
        </div>
      </Card>

      {/* المهام والمدة */}
      <Card className="p-6 border-blue-200 bg-blue-50">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold">
            📋
          </div>
          <h3 className="text-lg font-bold text-blue-800">المهام والتنفيذ</h3>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            المهام المطلوبة *
          </label>
          <FieldHelp 
            field="requiredTasks" 
            step={1} 
            content="اذكر المهام الأساسية المطلوبة لتنفيذ الحل (كل مهمة في سطر منفصل)" 
          />
          <Textarea
            value={data.solution?.tasks?.join('\n') || ''}
            onChange={(e) => updateData('solution.tasks', e.target.value.split('\n').filter(task => task.trim()))}
            placeholder="1. تحليل النظام الحالي
2. تصميم النظام الجديد
3. برمجة وتطوير النظام
4. اختبار النظام
5. تدريب المستخدمين"
            rows={5}
            error={errors['solution.tasks']}
            className="mt-1"
          />
          <p className="text-xs text-gray-500 mt-1">
            💡 اكتب كل مهمة في سطر منفصل لتنظيم أفضل
          </p>
        </div>
      </Card>
    </div>
  )
} 