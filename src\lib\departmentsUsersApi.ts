import { supabase } from './supabase'

// واجهات البيانات للأقسام
export interface Department {
  id: string
  name: string
  description?: string
  parent_id?: string
  manager_id?: string
  created_at: string
  updated_at: string
  parent?: {
    id: string
    name: string
  }
  manager?: {
    id: string
    name: string
    email: string
  }
  children?: Department[]
  users?: User[]
}

export interface DepartmentData {
  name: string
  description?: string
  parent_id?: string
  manager_id?: string
}

// واجهات البيانات للمستخدمين
export interface User {
  id: string
  email: string
  name: string
  department_id: string
  role_id: string
  phone?: string
  avatar_url?: string
  is_active: boolean
  created_at: string
  updated_at: string
  department?: {
    id: string
    name: string
    parent?: {
      id: string
      name: string
    }
  }
  role?: {
    id: string
    name: string
    display_name: string
    permissions: any
  }
}

export interface UserData {
  email: string
  name: string
  department_id: string
  role_id: string
  phone?: string
  password?: string
  is_active?: boolean
}

// API للأقسام
export class DepartmentsAPI {
  
  // إنشاء قسم جديد
  static async createDepartment(data: DepartmentData) {
    try {
      const response = await fetch('/api/departments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في إنشاء القسم')
      }

      return await response.json()
    } catch (error) {
      console.error('Error creating department:', error)
      throw error
    }
  }

  // استرجاع الأقسام
  static async getDepartments(filters?: {
    parent_id?: string | null
    include_children?: boolean
    include_users?: boolean
    hierarchy?: boolean
  }) {
    try {
      const params = new URLSearchParams()
      
      if (filters?.parent_id !== undefined) {
        params.append('parent_id', filters.parent_id === null ? 'null' : filters.parent_id)
      }
      if (filters?.include_children) {
        params.append('include_children', 'true')
      }
      if (filters?.include_users) {
        params.append('include_users', 'true')
      }
      if (filters?.hierarchy) {
        params.append('hierarchy', 'true')
      }

      const response = await fetch(`/api/departments?${params.toString()}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في استرجاع الأقسام')
      }

      return await response.json()
    } catch (error) {
      console.error('Error fetching departments:', error)
      throw error
    }
  }

  // تحديث قسم
  static async updateDepartment(id: string, data: Partial<DepartmentData>) {
    try {
      const response = await fetch('/api/departments', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id, ...data })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في تحديث القسم')
      }

      return await response.json()
    } catch (error) {
      console.error('Error updating department:', error)
      throw error
    }
  }

  // حذف قسم
  static async deleteDepartment(id: string) {
    try {
      const response = await fetch(`/api/departments?id=${id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في حذف القسم')
      }

      return await response.json()
    } catch (error) {
      console.error('Error deleting department:', error)
      throw error
    }
  }

  // الحصول على الهيكل الهرمي للأقسام
  static async getDepartmentHierarchy() {
    try {
      return await this.getDepartments({ hierarchy: true })
    } catch (error) {
      console.error('Error fetching department hierarchy:', error)
      throw error
    }
  }

  // الحصول على جميع الأقسام الفرعية
  static async getChildDepartments(parentId: string, includeGrandChildren: boolean = true) {
    try {
      const allChildren: Department[] = []
      
      const getChildren = async (id: string) => {
        const result = await this.getDepartments({ parent_id: id })
        const children = result.data || []
        
        allChildren.push(...children)
        
        if (includeGrandChildren) {
          for (const child of children) {
            await getChildren(child.id)
          }
        }
      }

      await getChildren(parentId)
      return allChildren
    } catch (error) {
      console.error('Error fetching child departments:', error)
      throw error
    }
  }

  // الحصول على مسار القسم (القسم الأب -> القسم الحالي)
  static async getDepartmentPath(departmentId: string): Promise<Department[]> {
    try {
      const path: Department[] = []
      let currentId = departmentId

      while (currentId) {
        const { data: dept, error } = await supabase
          .from('departments')
          .select(`
            *,
            parent:departments!parent_id(id, name)
          `)
          .eq('id', currentId)
          .single()

        if (error || !dept) break

        path.unshift(dept)
        currentId = dept.parent_id
      }

      return path
    } catch (error) {
      console.error('Error fetching department path:', error)
      return []
    }
  }
}

// API للمستخدمين
export class UsersAPI {
  
  // إنشاء مستخدم جديد
  static async createUser(data: UserData) {
    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في إنشاء المستخدم')
      }

      return await response.json()
    } catch (error) {
      console.error('Error creating user:', error)
      throw error
    }
  }

  // استرجاع المستخدمين
  static async getUsers(filters?: {
    department_id?: string
    role_id?: string
    is_active?: boolean
    search?: string
    include_hierarchy?: boolean
  }) {
    try {
      const params = new URLSearchParams()
      
      if (filters?.department_id) {
        params.append('department_id', filters.department_id)
      }
      if (filters?.role_id) {
        params.append('role_id', filters.role_id)
      }
      if (filters?.is_active !== undefined) {
        params.append('is_active', filters.is_active.toString())
      }
      if (filters?.search) {
        params.append('search', filters.search)
      }
      if (filters?.include_hierarchy) {
        params.append('include_hierarchy', 'true')
      }

      const response = await fetch(`/api/users?${params.toString()}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في استرجاع المستخدمين')
      }

      return await response.json()
    } catch (error) {
      console.error('Error fetching users:', error)
      throw error
    }
  }

  // تحديث مستخدم
  static async updateUser(id: string, data: Partial<UserData>) {
    try {
      const response = await fetch('/api/users', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id, ...data })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في تحديث المستخدم')
      }

      return await response.json()
    } catch (error) {
      console.error('Error updating user:', error)
      throw error
    }
  }

  // حذف/تعطيل مستخدم
  static async deleteUser(id: string, permanent: boolean = false) {
    try {
      const params = new URLSearchParams()
      params.append('id', id)
      if (permanent) {
        params.append('permanent', 'true')
      }

      const response = await fetch(`/api/users?${params.toString()}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في حذف المستخدم')
      }

      return await response.json()
    } catch (error) {
      console.error('Error deleting user:', error)
      throw error
    }
  }

  // الحصول على مستخدمي القسم مع الأقسام الفرعية
  static async getDepartmentUsers(departmentId: string, includeSubDepartments: boolean = true) {
    try {
      return await this.getUsers({
        department_id: departmentId,
        include_hierarchy: includeSubDepartments
      })
    } catch (error) {
      console.error('Error fetching department users:', error)
      throw error
    }
  }

  // تغيير كلمة المرور
  static async changePassword(id: string, newPassword: string) {
    try {
      return await this.updateUser(id, { password: newPassword })
    } catch (error) {
      console.error('Error changing password:', error)
      throw error
    }
  }

  // تفعيل/تعطيل مستخدم
  static async toggleUserStatus(id: string, isActive: boolean) {
    try {
      return await this.updateUser(id, { is_active: isActive })
    } catch (error) {
      console.error('Error toggling user status:', error)
      throw error
    }
  }
}

// دوال مساعدة للصلاحيات العمودية
export class PermissionsHelper {
  
  // التحقق من صلاحية المستخدم على قسم معين
  static async canAccessDepartment(userId: string, targetDepartmentId: string): Promise<boolean> {
    try {
      // الحصول على بيانات المستخدم
      const { data: user, error } = await supabase
        .from('users')
        .select(`
          *,
          department:departments!department_id(
            id,
            name,
            parent_id
          ),
          role:roles!role_id(
            id,
            name,
            permissions
          )
        `)
        .eq('id', userId)
        .single()

      if (error || !user) return false

      // مدير النظام له صلاحية على جميع الأقسام
      if (user.role?.permissions?.all === true) return true

      // المستخدم له صلاحية على قسمه
      if (user.department_id === targetDepartmentId) return true

      // التحقق من الصلاحية على الأقسام الفرعية
      if (user.role?.permissions?.departments?.includes('manage_sub_departments')) {
        const isSubDepartment = await this.isSubDepartment(user.department_id, targetDepartmentId)
        return isSubDepartment
      }

      return false
    } catch (error) {
      console.error('Error checking department access:', error)
      return false
    }
  }

  // التحقق من كون قسم فرعي لقسم آخر
  static async isSubDepartment(parentDepartmentId: string, childDepartmentId: string): Promise<boolean> {
    try {
      let currentParentId = childDepartmentId

      while (currentParentId) {
        const { data: dept, error } = await supabase
          .from('departments')
          .select('parent_id')
          .eq('id', currentParentId)
          .single()

        if (error || !dept) break

        if (dept.parent_id === parentDepartmentId) return true

        currentParentId = dept.parent_id
      }

      return false
    } catch (error) {
      console.error('Error checking sub-department:', error)
      return false
    }
  }

  // الحصول على الأقسام التي يمكن للمستخدم الوصول إليها
  static async getAccessibleDepartments(userId: string): Promise<string[]> {
    try {
      const { data: user, error } = await supabase
        .from('users')
        .select(`
          *,
          department:departments!department_id(id, name),
          role:roles!role_id(id, name, permissions)
        `)
        .eq('id', userId)
        .single()

      if (error || !user) return []

      // مدير النظام له صلاحية على جميع الأقسام
      if (user.role?.permissions?.all === true) {
        const { data: allDepts } = await supabase
          .from('departments')
          .select('id')
        
        return allDepts?.map(d => d.id) || []
      }

      const accessibleDepartments: string[] = []

      // إضافة قسم المستخدم
      if (user.department_id) {
        accessibleDepartments.push(user.department_id)
      }

      // إضافة الأقسام الفرعية إذا كان له صلاحية
      if (user.role?.permissions?.departments?.includes('manage_sub_departments')) {
        const childDepartments = await DepartmentsAPI.getChildDepartments(user.department_id)
        accessibleDepartments.push(...childDepartments.map(d => d.id))
      }

      return accessibleDepartments
    } catch (error) {
      console.error('Error getting accessible departments:', error)
      return []
    }
  }

  // التحقق من صلاحية إدارة المستخدمين
  static async canManageUsers(userId: string, targetDepartmentId?: string): Promise<boolean> {
    try {
      const { data: user, error } = await supabase
        .from('users')
        .select(`
          *,
          role:roles!role_id(id, name, permissions)
        `)
        .eq('id', userId)
        .single()

      if (error || !user) return false

      // مدير النظام له صلاحية كاملة
      if (user.role?.permissions?.all === true) return true

      // التحقق من صلاحية إدارة المستخدمين
      if (!user.role?.permissions?.users?.includes('manage')) return false

      // إذا لم يحدد قسم، التحقق من الصلاحية العامة
      if (!targetDepartmentId) return true

      // التحقق من الصلاحية على القسم المحدد
      return await this.canAccessDepartment(userId, targetDepartmentId)
    } catch (error) {
      console.error('Error checking user management permission:', error)
      return false
    }
  }

  // الحصول على المستخدمين الذين يمكن إدارتهم
  static async getManageableUsers(managerId: string): Promise<User[]> {
    try {
      const accessibleDepartments = await this.getAccessibleDepartments(managerId)
      
      if (accessibleDepartments.length === 0) return []

      const { data: users, error } = await supabase
        .from('users')
        .select(`
          *,
          department:departments!department_id(id, name),
          role:roles!role_id(id, name, display_name)
        `)
        .in('department_id', accessibleDepartments)

      if (error) {
        console.error('Error fetching manageable users:', error)
        return []
      }

      return users || []
    } catch (error) {
      console.error('Error getting manageable users:', error)
      return []
    }
  }
}

// دوال مساعدة للعرض
export class DisplayHelpers {
  
  // تنسيق مسار القسم
  static formatDepartmentPath(departments: Department[]): string {
    return departments.map(dept => dept.name).join(' > ')
  }

  // تحديد لون حالة المستخدم
  static getUserStatusColor(isActive: boolean): string {
    return isActive 
      ? 'bg-green-100 text-green-800' 
      : 'bg-red-100 text-red-800'
  }

  // تحديد نص حالة المستخدم
  static getUserStatusText(isActive: boolean): string {
    return isActive ? 'نشط' : 'معطل'
  }

  // تحديد أيقونة حالة المستخدم
  static getUserStatusIcon(isActive: boolean): string {
    return isActive ? '✅' : '❌'
  }

  // تنسيق عدد المستخدمين
  static formatUserCount(count: number): string {
    if (count === 0) return 'لا يوجد مستخدمين'
    if (count === 1) return 'مستخدم واحد'
    if (count === 2) return 'مستخدمان'
    if (count <= 10) return `${count} مستخدمين`
    return `${count} مستخدم`
  }

  // تنسيق عدد الأقسام
  static formatDepartmentCount(count: number): string {
    if (count === 0) return 'لا يوجد أقسام'
    if (count === 1) return 'قسم واحد'
    if (count === 2) return 'قسمان'
    if (count <= 10) return `${count} أقسام`
    return `${count} قسم`
  }

  // تحديد مستوى القسم في الهيكل الهرمي
  static getDepartmentLevel(department: Department, allDepartments: Department[]): number {
    let level = 0
    let currentParentId = department.parent_id

    while (currentParentId) {
      level++
      const parent = allDepartments.find(d => d.id === currentParentId)
      currentParentId = parent?.parent_id
    }

    return level
  }

  // إنشاء فراغات للمستوى في الهيكل الهرمي
  static getDepartmentIndent(level: number): string {
    return '  '.repeat(level)
  }
} 