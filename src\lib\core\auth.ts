'use client'

import { create<PERSON>ontext, useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '../supabase'

// ==================== TYPES & INTERFACES ====================

export interface LoginData {
  email: string
  password: string
}

export interface User {
  id: string
  email: string
  name: string
  phone?: string
  avatar_url?: string
  department: {
    id: string
    name: string
  }
  role: {
    id: string
    name: string
    display_name: string
    permissions: any
  }
}

export interface ExtendedUser extends User {
  created_at?: string
  updated_at?: string
  is_active?: boolean
}

export interface UserRole {
  id: string
  name: string
  display_name: string
  permissions: Record<string, any>
}

export interface UserDepartment {
  id: string
  name: string
  description?: string
  parent_id?: string | null
  manager_id?: string | null
}

export interface Session {
  access_token: string
  refresh_token: string
  expires_at: number
  user: User
}

export interface AuthResponse {
  success: boolean
  message?: string
  user?: User
  error?: string
}

export interface UserProfile {
  id: string
  email: string
  name: string
  role?: {
    id: string
    name: string
    display_name: string
  }
  department?: {
    id: string
    name: string
  }
  is_active: boolean
}

export interface LoginAttempt {
  id: string
  email: string
  ip_address?: string
  user_agent?: string
  success: boolean
  failure_reason?: string
  attempted_at: string
}

export interface UserSession {
  id: string
  user_id: string
  session_token: string
  ip_address?: string
  user_agent?: string
  device_info?: any
  login_at: string
  last_activity: string
  logout_at?: string
  is_active: boolean
  expires_at: string
}

export interface AuditLogEntry {
  id: string
  user_id: string
  action: string
  table_name: string
  record_id: string
  old_values?: any
  new_values?: any
  ip_address?: string
  user_agent?: string
  session_id?: string
  created_at: string
}

// ==================== CONSTANTS ====================

export const DEFAULT_PASSWORDS = {
  '<EMAIL>': 'admin123',
  '<EMAIL>': 'pmo123',
  '<EMAIL>': 'emp123'
} as const

// ==================== AUTH CONTEXT ====================

interface AuthContextType {
  user: ExtendedUser | null
  session: Session | null
  userRole: UserRole | null
  userDepartment: UserDepartment | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>
  signOut: () => Promise<{ error: any }>
  signUp: (email: string, password: string, userData: any) => Promise<{ data: any; error: any }>
  resetPassword: (email: string) => Promise<{ data: any; error: any }>
  updatePassword: (password: string) => Promise<{ data: any; error: any }>
  fetchUserDetails: (userId: string) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// ==================== AUTH PROVIDER ====================

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<ExtendedUser | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [userRole, setUserRole] = useState<UserRole | null>(null)
  const [userDepartment, setUserDepartment] = useState<UserDepartment | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  // تهيئة المصادقة
  useEffect(() => {
    initializeAuth()
  }, [])

  const initializeAuth = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (session?.user) {
        setSession(session)
        setUser(session.user as ExtendedUser)
        await fetchUserDetails(session.user.id)
      } else {
        // التحقق من الجلسة المحفوظة محلياً
        const storedSession = localStorage.getItem('demo_session')
        if (storedSession) {
          try {
            const parsedSession = JSON.parse(storedSession)
            if (parsedSession && parsedSession.user && parsedSession.expires_at > Date.now()) {
              setSession(parsedSession)
              setUser(parsedSession.user as ExtendedUser)
              await fetchUserDetails(parsedSession.user.id)
            } else {
              localStorage.removeItem('demo_session')
            }
          } catch (error) {
            console.error('Error parsing stored session:', error)
            localStorage.removeItem('demo_session')
          }
        }
      }
    } catch (error) {
      console.error('Auth initialization error:', error)
    } finally {
      setLoading(false)
    }
  }

  // جلب تفاصيل المستخدم
  const fetchUserDetails = async (userId: string) => {
    try {
      // جلب دور المستخدم
      const { data: roleData } = await supabase
        .from('user_roles')
        .select(`
          role:roles(*)
        `)
        .eq('user_id', userId)
        .single()

      if (roleData?.role) {
        setUserRole(roleData.role)
      } else {
        // دور افتراضي
        setUserRole({
          id: '1',
          name: 'employee',
          display_name: 'موظف',
          permissions: { requests: ['create', 'view_own'] }
        })
      }

      // جلب قسم المستخدم
      const { data: deptData } = await supabase
        .from('user_departments')
        .select(`
          department:departments(*)
        `)
        .eq('user_id', userId)
        .single()

      if (deptData?.department) {
        setUserDepartment(deptData.department)
      } else {
        // قسم افتراضي
        setUserDepartment({
          id: '1',
          name: 'قسم عام',
          description: 'قسم افتراضي',
          parent_id: null,
          manager_id: null
        })
      }
    } catch (error) {
      console.error('Error fetching user details:', error)
      // تعيين قيم افتراضية في حالة الخطأ
      setUserRole({
        id: '1',
        name: 'employee',
        display_name: 'موظف',
        permissions: { requests: ['create', 'view_own'] }
      })
      setUserDepartment({
        id: '1',
        name: 'قسم عام',
        description: 'قسم افتراضي',
        parent_id: null,
        manager_id: null
      })
    }
  }

  // تسجيل الدخول
  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await AuthCore.signIn(email, password)
      
      if (data?.user && !error) {
        setUser(data.user as ExtendedUser)
        setSession(data.session as Session)
        await fetchUserDetails(data.user.id)
        return { data, error: null }
      }
      
      return { data, error }
    } catch (error) {
      console.error('Sign in error:', error)
      return { data: null, error: { message: 'حدث خطأ أثناء تسجيل الدخول' } }
    }
  }

  // تسجيل الخروج
  const signOut = async () => {
    const { error } = await AuthCore.signOut()
    
    if (!error) {
      setUser(null)
      setSession(null)
      setUserRole(null)
      setUserDepartment(null)
      router.push('/auth/login')
    }
    
    return { error }
  }

  // تسجيل مستخدم جديد
  const signUp = async (email: string, password: string, userData: any) => {
    const { data, error } = await AuthCore.signUp(email, password, userData)
    return { data, error }
  }

  // إعادة تعيين كلمة المرور
  const resetPassword = async (email: string) => {
    return { data: null, error: null }
  }

  // تحديث كلمة المرور
  const updatePassword = async (password: string) => {
    return { data: null, error: null }
  }

  return (
    <AuthContext.Provider value={{
      user,
      session,
      userRole,
      userDepartment,
      loading,
      signIn,
      signOut,
      signUp,
      resetPassword,
      updatePassword,
      fetchUserDetails,
    }}>
      {children}
    </AuthContext.Provider>
  )
}

// ==================== AUTH HOOK ====================

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// ==================== AUTH CORE CLASS ====================

export class AuthCore {
  // تسجيل الدخول الأساسي
  static async signIn(email: string, password: string): Promise<{
    data: { user: User | null; session: Session | null }
    error: any
  }> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })
      
      return { data, error }
    } catch (error) {
      console.error('Auth sign in error:', error)
      return { 
        data: { user: null, session: null }, 
        error: { message: 'حدث خطأ أثناء تسجيل الدخول' } 
      }
    }
  }

  // تسجيل الخروج
  static async signOut(): Promise<{ error: any }> {
    try {
      const { error } = await supabase.auth.signOut()
      localStorage.removeItem('demo_session')
      return { error }
    } catch (error) {
      console.error('Auth sign out error:', error)
      return { error: { message: 'حدث خطأ أثناء تسجيل الخروج' } }
    }
  }

  // تسجيل مستخدم جديد
  static async signUp(email: string, password: string, userData: any): Promise<{
    data: any
    error: any
  }> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData
        }
      })
      
      return { data, error }
    } catch (error) {
      console.error('Auth sign up error:', error)
      return { 
        data: null, 
        error: { message: 'حدث خطأ أثناء إنشاء الحساب' } 
      }
    }
  }

  // التحقق من الجلسة
  static async verifySession(): Promise<AuthResponse> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        return { success: false, error: error.message }
      }
      
      if (session?.user) {
        return { 
          success: true, 
          user: session.user as User 
        }
      }
      
      return { success: false, error: 'لا توجد جلسة نشطة' }
    } catch (error) {
      console.error('Session verification error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'حدث خطأ في التحقق من الجلسة'
      }
    }
  }
}

// ==================== PERMISSION MANAGER ====================

export class PermissionManager {
  private userRole: UserRole | null

  constructor(userRole: UserRole | null) {
    this.userRole = userRole
  }

  // التحقق من صلاحية عامة
  hasPermission(resource: string, action: string): boolean {
    if (!this.userRole) return false

    // المدير العام له صلاحيات كاملة
    if (this.userRole.name === 'admin') return true
    if (this.userRole.permissions?.all === true) return true

    const resourcePermissions = this.userRole.permissions?.[resource]
    if (!resourcePermissions) return false

    // إذا كانت الصلاحية مصفوفة من الأعمال
    if (Array.isArray(resourcePermissions)) {
      return resourcePermissions.includes(action)
    }

    // إذا كانت الصلاحية boolean
    if (typeof resourcePermissions === 'boolean') {
      return resourcePermissions
    }

    return false
  }

  // التحقق من الدور
  hasRole(role: string): boolean {
    if (!this.userRole) return false
    return this.userRole.name === role
  }

  // التحقق من أي من الأدوار
  hasAnyRole(roles: string[]): boolean {
    if (!this.userRole) return false
    return roles.includes(this.userRole.name)
  }

  // التحقق من صلاحيات المستخدم
  static checkUserPermission(userRole: string, requiredPermission: string): boolean {
    const rolePermissions: Record<string, string[]> = {
      'admin': ['*'], // جميع الصلاحيات
      'pmo_manager': [
        'view_all_requests',
        'approve_requests',
        'manage_projects',
        'view_reports'
      ],
      'planning_manager': [
        'view_requests',
        'approve_planning',
        'view_reports'
      ],
      'executive_manager': [
        'view_requests',
        'final_approval',
        'view_reports'
      ],
      'project_manager': [
        'view_assigned_projects',
        'manage_tasks',
        'update_progress'
      ],
      'employee': [
        'create_requests',
        'view_own_requests'
      ]
    }

    const permissions = rolePermissions[userRole] || []
    return permissions.includes('*') || permissions.includes(requiredPermission)
  }

  // تحديد اسم الدور بالعربية
  static getRoleDisplayName(roleName: string): string {
    const roleNames: { [key: string]: string } = {
      admin: 'مدير النظام',
      pmo_manager: 'مدير مكتب المشاريع',
      planning_manager: 'مدير إدارة التخطيط',
      executive_manager: 'المدير التنفيذي',
      project_manager: 'مدير مشروع',
      employee: 'موظف'
    }

    return roleNames[roleName] || roleName
  }
}

// ==================== SESSION MANAGER ====================

export class SessionManager {
  // تسجيل محاولة دخول
  static async logLoginAttempt(attempt: Omit<LoginAttempt, 'id' | 'attempted_at'>): Promise<void> {
    try {
      await supabase
        .from('login_attempts')
        .insert({
          ...attempt,
          attempted_at: new Date().toISOString()
        })
    } catch (error) {
      console.error('Error logging login attempt:', error)
    }
  }

  // إنشاء جلسة مستخدم
  static async createUserSession(sessionData: Omit<UserSession, 'id' | 'login_at' | 'last_activity' | 'is_active' | 'expires_at'>): Promise<void> {
    try {
      const now = new Date()
      const expiresAt = new Date(now.getTime() + 24 * 60 * 60 * 1000) // 24 ساعة

      await supabase
        .from('user_sessions')
        .insert({
          ...sessionData,
          login_at: now.toISOString(),
          last_activity: now.toISOString(),
          is_active: true,
          expires_at: expiresAt.toISOString()
        })
    } catch (error) {
      console.error('Error creating user session:', error)
    }
  }

  // إلغاء تفعيل جلسة
  static async deactivateUserSession(sessionToken: string): Promise<void> {
    try {
      await supabase
        .from('user_sessions')
        .update({
          is_active: false,
          logout_at: new Date().toISOString()
        })
        .eq('session_token', sessionToken)
    } catch (error) {
      console.error('Error deactivating session:', error)
    }
  }

  // تسجيل عملية في سجل التدقيق
  static async logAuditEvent(event: Omit<AuditLogEntry, 'id' | 'created_at'>): Promise<void> {
    try {
      await supabase
        .from('audit_logs')
        .insert({
          ...event,
          created_at: new Date().toISOString()
        })
    } catch (error) {
      console.error('Error logging audit event:', error)
    }
  }

  // فحص الأمان للمستخدم
  static async performSecurityCheck(userId: string): Promise<{
    suspicious_activity: boolean
    failed_attempts: number
    active_sessions: number
    last_login?: string
  }> {
    try {
      // فحص محاولات تسجيل الدخول الفاشلة في آخر ساعة
      const { data: failedAttempts } = await supabase
        .from('login_attempts')
        .select('*')
        .eq('success', false)
        .gte('attempted_at', new Date(Date.now() - 60 * 60 * 1000).toISOString())

      // فحص الجلسات النشطة
      const { data: activeSessions } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)

      // فحص آخر تسجيل دخول ناجح
      const { data: lastLogin } = await supabase
        .from('login_attempts')
        .select('*')
        .eq('success', true)
        .order('attempted_at', { ascending: false })
        .limit(1)

      return {
        suspicious_activity: (failedAttempts?.length || 0) > 5,
        failed_attempts: failedAttempts?.length || 0,
        active_sessions: activeSessions?.length || 0,
        last_login: lastLogin?.[0]?.attempted_at
      }
    } catch (error) {
      console.error('Error performing security check:', error)
      return {
        suspicious_activity: false,
        failed_attempts: 0,
        active_sessions: 0
      }
    }
  }
}

// ==================== APPROVAL WORKFLOW ====================

export class ApprovalWorkflow {
  // تحديد المستويات المطلوبة للاعتماد
  getRequiredApprovalLevels(mainType: string, subType: string | null): number {
    // قواعد الاعتماد حسب نوع الطلب
    if (mainType === 'general_project') {
      if (subType === 'quick_win') {
        return 1 // مدير مكتب المشاريع فقط
      }
      return 3 // جميع المستويات
    }

    if (mainType === 'improvement_project') {
      if (subType === 'suggestion') {
        return 2 // مدير مكتب المشاريع + مدير التخطيط
      }
      return 3 // جميع المستويات
    }

    return 1 // افتراضي
  }

  // تحديد المعتمد للمستوى التالي
  getNextApprover(mainType: string, subType: string | null, currentLevel: number): string | null {
    const requiredLevels = this.getRequiredApprovalLevels(mainType, subType)

    if (currentLevel >= requiredLevels) {
      return null // لا يوجد معتمد آخر
    }

    const nextLevel = currentLevel + 1

    switch (nextLevel) {
      case 1:
        return 'pmo_manager'
      case 2:
        return 'planning_manager'
      case 3:
        return 'executive_manager'
      default:
        return null
    }
  }

  // التحقق من صلاحية الاعتماد
  canApprove(userRole: string, approvalLevel: number): boolean {
    const approvalRoles = {
      1: ['pmo_manager', 'admin'],
      2: ['planning_manager', 'admin'],
      3: ['executive_manager', 'admin']
    }

    return approvalRoles[approvalLevel as keyof typeof approvalRoles]?.includes(userRole) || false
  }
}

// ==================== USER MANAGEMENT ====================

export class UserManager {
  // الحصول على قائمة المستخدمين النشطين
  static async getActiveUsers() {
    try {
      const { data, error } = await supabase
        .from('users')
        .select(`
          *,
          role:roles(*),
          department:departments(*)
        `)
        .eq('is_active', true)
        .order('name')

      return { data, error }
    } catch (error) {
      console.error('Error fetching active users:', error)
      return { data: null, error }
    }
  }

  // إنشاء مستخدم جديد
  static async createUser(userData: {
    email: string
    name: string
    role_id: string
    department_id?: string
    password?: string
  }) {
    try {
      // أولاً، إنشاء المستخدم في Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password || 'temp123456',
        options: {
          data: {
            name: userData.name
          }
        }
      })

      if (authError) {
        return { data: null, error: authError }
      }

      if (authData.user) {
        // إضافة المستخدم إلى جدول المستخدمين
        const { data: userRecord, error: userError } = await supabase
          .from('users')
          .insert({
            id: authData.user.id,
            email: userData.email,
            name: userData.name,
            is_active: true
          })
          .select()
          .single()

        if (userError) {
          return { data: null, error: userError }
        }

        // ربط المستخدم بالدور
        await supabase
          .from('user_roles')
          .insert({
            user_id: authData.user.id,
            role_id: userData.role_id
          })

        // ربط المستخدم بالقسم إذا تم تحديده
        if (userData.department_id) {
          await supabase
            .from('user_departments')
            .insert({
              user_id: authData.user.id,
              department_id: userData.department_id
            })
        }

        return { data: userRecord, error: null }
      }

      return { data: null, error: { message: 'فشل في إنشاء المستخدم' } }
    } catch (error) {
      console.error('Error creating user:', error)
      return { data: null, error }
    }
  }
}

// ==================== HOOKS ====================

// خطاف للتحقق من الصلاحيات
export function usePermissions() {
  const { userRole } = useAuth()
  return new PermissionManager(userRole)
}

// خطاف لإدارة تدفق الاعتماد
export function useApprovalWorkflow() {
  return new ApprovalWorkflow()
}
