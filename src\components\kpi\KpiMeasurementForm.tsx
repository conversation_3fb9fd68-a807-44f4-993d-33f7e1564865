'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { Input, Select, Textarea } from '@/components/ui/Input'
import { KpiMeasurementFormData } from '@/types/kpi.types'
import { 
  Save, 
  Calendar, 
  TrendingUp, 
  FileText, 
  AlertCircle,
  CheckCircle
} from 'lucide-react'

interface KpiMeasurementFormProps {
  kpiId: string
  kpiName: string
  currentValue?: number
  targetValue: number
  unit: string
  onSubmit: (data: KpiMeasurementFormData) => Promise<void>
  onCancel?: () => void
  isLoading?: boolean
}

export function KpiMeasurementForm({
  kpiId,
  kpiName,
  currentValue,
  targetValue,
  unit,
  onSubmit,
  onCancel,
  isLoading = false
}: KpiMeasurementFormProps) {
  const [formData, setFormData] = useState<KpiMeasurementFormData>({
    kpi_id: kpiId,
    value: currentValue || 0,
    measurement_date: new Date().toISOString().split('T')[0],
    notes: '',
    data_source: '',
    verified_by: '',
    measurement_method: '',
    context: ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [showSuccess, setShowSuccess] = useState(false)

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.value && formData.value !== 0) {
      newErrors.value = 'القيمة مطلوبة'
    }

    if (!formData.measurement_date) {
      newErrors.measurement_date = 'تاريخ القياس مطلوب'
    }

    if (!formData.data_source) {
      newErrors.data_source = 'مصدر البيانات مطلوب'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      await onSubmit(formData)
      setShowSuccess(true)
      setTimeout(() => setShowSuccess(false), 3000)
    } catch (error) {
      console.error('Error submitting measurement:', error)
    }
  }

  const calculateImprovement = () => {
    if (!currentValue || !formData.value) return null
    
    const improvement = ((formData.value - currentValue) / currentValue) * 100
    return improvement
  }

  const calculateTargetProgress = () => {
    if (!targetValue || !formData.value) return null
    
    // حساب التقدم نحو الهدف
    const progress = (formData.value / targetValue) * 100
    return Math.min(progress, 100)
  }

  const improvement = calculateImprovement()
  const targetProgress = calculateTargetProgress()

  return (
    <Card className="p-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* العنوان */}
        <div className="flex items-center gap-3">
          <TrendingUp className="w-6 h-6 text-blue-600" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              تسجيل قياس جديد
            </h3>
            <p className="text-sm text-gray-600">{kpiName}</p>
          </div>
        </div>

        {/* رسالة النجاح */}
        {showSuccess && (
          <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="text-green-800">تم حفظ القياس بنجاح</span>
          </div>
        )}

        {/* البيانات الأساسية */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Input
              label="القيمة الجديدة *"
              type="number"
              step="0.01"
              value={formData.value}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                value: Number(e.target.value) 
              }))}
              placeholder={`القيمة الجديدة بـ ${unit}`}
              error={errors.value}
              required
            />
            {currentValue && (
              <div className="mt-1 text-sm text-gray-500">
                القيمة الحالية: {currentValue} {unit}
              </div>
            )}
          </div>

          <div>
            <Input
              label="تاريخ القياس *"
              type="date"
              value={formData.measurement_date}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                measurement_date: e.target.value 
              }))}
              error={errors.measurement_date}
              required
            />
          </div>

          <div>
            <Input
              label="مصدر البيانات *"
              value={formData.data_source}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                data_source: e.target.value 
              }))}
              placeholder="مثال: نظام المستشفى، تقرير يدوي"
              error={errors.data_source}
              required
            />
          </div>

          <div>
            <Input
              label="تم التحقق بواسطة"
              value={formData.verified_by}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                verified_by: e.target.value 
              }))}
              placeholder="اسم الشخص المسؤول عن التحقق"
            />
          </div>
        </div>

        {/* طريقة القياس */}
        <div>
          <Textarea
            label="طريقة القياس"
            value={formData.measurement_method}
            onChange={(e) => setFormData(prev => ({ 
              ...prev, 
              measurement_method: e.target.value 
            }))}
            placeholder="كيف تم قياس هذه القيمة؟"
            rows={3}
          />
        </div>

        {/* السياق والملاحظات */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Textarea
              label="السياق"
              value={formData.context}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                context: e.target.value 
              }))}
              placeholder="الظروف المحيطة بالقياس..."
              rows={3}
            />
          </div>

          <div>
            <Textarea
              label="ملاحظات إضافية"
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                notes: e.target.value 
              }))}
              placeholder="أي ملاحظات أو تفاصيل إضافية..."
              rows={3}
            />
          </div>
        </div>

        {/* معاينة النتائج */}
        {formData.value && (
          <div className="bg-gray-50 rounded-lg p-4 space-y-3">
            <h4 className="font-medium text-gray-900 flex items-center gap-2">
              <FileText className="w-4 h-4" />
              معاينة النتائج
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {formData.value} {unit}
                </div>
                <div className="text-sm text-gray-600">القيمة الجديدة</div>
              </div>
              
              {improvement !== null && (
                <div className="text-center">
                  <div className={`text-2xl font-bold ${
                    improvement > 0 ? 'text-green-600' : 
                    improvement < 0 ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    {improvement > 0 ? '+' : ''}{improvement.toFixed(1)}%
                  </div>
                  <div className="text-sm text-gray-600">التحسن</div>
                </div>
              )}
              
              {targetProgress !== null && (
                <div className="text-center">
                  <div className={`text-2xl font-bold ${
                    targetProgress >= 100 ? 'text-green-600' : 
                    targetProgress >= 75 ? 'text-blue-600' : 
                    targetProgress >= 50 ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {targetProgress.toFixed(1)}%
                  </div>
                  <div className="text-sm text-gray-600">تقدم الهدف</div>
                </div>
              )}
            </div>

            {/* تحذيرات */}
            {formData.value && targetValue && formData.value > targetValue * 1.2 && (
              <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <AlertCircle className="w-5 h-5 text-yellow-600" />
                <span className="text-yellow-800 text-sm">
                  القيمة أعلى من الهدف بنسبة كبيرة، يرجى التحقق من صحة البيانات
                </span>
              </div>
            )}
          </div>
        )}

        {/* أزرار التحكم */}
        <div className="flex justify-end gap-3">
          {onCancel && (
            <Button
              type="button"
              variant="ghost"
              onClick={onCancel}
              disabled={isLoading}
            >
              إلغاء
            </Button>
          )}
          
          <Button
            type="submit"
            variant="primary"
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Save className="w-4 h-4" />
            {isLoading ? 'جاري الحفظ...' : 'حفظ القياس'}
          </Button>
        </div>
      </form>
    </Card>
  )
} 