'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { 
  SuggestionFeedback, 
  SolutionEvaluation, 
  FeedbackType,
  FeedbackPriority 
} from '@/types/feedback.types';

interface SolutionReviewCardProps {
  solution: {
    id: string;
    title: string;
    description: string;
    feasibilityScore: number;
    expectedBenefits: string[];
    implementationSteps: string[];
  };
  evaluation: SolutionEvaluation;
  canAddFeedback: boolean;
  onAddFeedback: (solutionId: string) => void;
  onViewDetails: (solutionId: string) => void;
}

export default function SolutionReviewCard({
  solution,
  evaluation,
  canAddFeedback,
  onAddFeedback,
  onViewDetails
}: SolutionReviewCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  // حساب إحصائيات الملاحظات
  const feedbackStats = {
    total: evaluation.feedbacks.length,
    positive: evaluation.feedbacks.filter(f => 
      f.feedbackType === 'approval' || f.feedbackType === 'suggestion'
    ).length,
    negative: evaluation.feedbacks.filter(f => 
      f.feedbackType === 'rejection' || f.feedbackType === 'concern'
    ).length,
    questions: evaluation.feedbacks.filter(f => 
      f.feedbackType === 'question'
    ).length
  };

  // ألوان مستوى التوصية
  const recommendationColors = {
    'highly_recommended': 'bg-green-100 text-green-800 border-green-300',
    'recommended': 'bg-blue-100 text-blue-800 border-blue-300',
    'neutral': 'bg-gray-100 text-gray-800 border-gray-300',
    'not_recommended': 'bg-red-100 text-red-800 border-red-300'
  };

  // ترجمة مستوى التوصية
  const recommendationLabels = {
    'highly_recommended': 'موصى به بشدة',
    'recommended': 'موصى به',
    'neutral': 'محايد',
    'not_recommended': 'غير موصى به'
  };

  // ألوان مستوى الإجماع
  const consensusColors = {
    'high': 'text-green-600',
    'medium': 'text-yellow-600',
    'low': 'text-red-600'
  };

  const consensusLabels = {
    'high': 'إجماع عالي',
    'medium': 'إجماع متوسط',
    'low': 'إجماع منخفض'
  };

  return (
    <Card className="mb-6 hover:shadow-lg transition-shadow">
      <div className="p-6">
        {/* رأس البطاقة */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            <h3 className="text-xl font-bold text-gray-900 mb-2">
              {solution.title}
            </h3>
            <p className="text-gray-600 text-sm mb-3">
              {solution.description}
            </p>
          </div>
          
          {/* مؤشر الجدوى */}
          <div className="ml-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {solution.feasibilityScore}%
            </div>
            <div className="text-xs text-gray-500">درجة الجدوى</div>
          </div>
        </div>

        {/* مؤشرات التقييم */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          {/* مستوى التوصية */}
          <div className={`p-3 rounded-lg border ${recommendationColors[evaluation.recommendationLevel]}`}>
            <div className="text-center">
              <div className="font-semibold">
                {recommendationLabels[evaluation.recommendationLevel]}
              </div>
              <div className="text-xs mt-1">مستوى التوصية</div>
            </div>
          </div>

          {/* مستوى الإجماع */}
          <div className="p-3 rounded-lg border border-gray-200 bg-gray-50">
            <div className="text-center">
              <div className={`font-semibold ${consensusColors[evaluation.consensusLevel]}`}>
                {consensusLabels[evaluation.consensusLevel]}
              </div>
              <div className="text-xs mt-1 text-gray-600">بين أصحاب المصلحة</div>
            </div>
          </div>

          {/* إحصائيات الملاحظات */}
          <div className="p-3 rounded-lg border border-gray-200 bg-gray-50">
            <div className="text-center">
              <div className="font-semibold text-gray-800">
                {feedbackStats.total} ملاحظة
              </div>
              <div className="text-xs mt-1 text-gray-600">
                {feedbackStats.positive} إيجابية | {feedbackStats.negative} سلبية
              </div>
            </div>
          </div>
        </div>

        {/* الفوائد المتوقعة */}
        {solution.expectedBenefits.length > 0 && (
          <div className="mb-4">
            <h4 className="font-semibold text-gray-800 mb-2">الفوائد المتوقعة:</h4>
            <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
              {solution.expectedBenefits.slice(0, 3).map((benefit, index) => (
                <li key={index}>{benefit}</li>
              ))}
              {solution.expectedBenefits.length > 3 && !isExpanded && (
                <li className="text-blue-600 cursor-pointer" onClick={() => setIsExpanded(true)}>
                  ... و {solution.expectedBenefits.length - 3} فوائد أخرى
                </li>
              )}
              {isExpanded && solution.expectedBenefits.slice(3).map((benefit, index) => (
                <li key={index + 3}>{benefit}</li>
              ))}
            </ul>
          </div>
        )}

        {/* عرض الملاحظات الأخيرة */}
        {evaluation.feedbacks.length > 0 && (
          <div className="mb-4">
            <h4 className="font-semibold text-gray-800 mb-2">آخر الملاحظات:</h4>
            <div className="space-y-2">
              {evaluation.feedbacks.slice(0, 2).map((feedback) => (
                <div key={feedback.id} className="p-3 bg-gray-50 rounded-lg border">
                  <div className="flex justify-between items-start mb-1">
                    <span className="font-medium text-sm text-gray-800">
                      {feedback.reviewerName}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      feedback.feedbackType === 'approval' ? 'bg-green-100 text-green-800' :
                      feedback.feedbackType === 'rejection' ? 'bg-red-100 text-red-800' :
                      feedback.feedbackType === 'concern' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {feedback.feedbackType === 'approval' ? 'موافقة' :
                       feedback.feedbackType === 'rejection' ? 'رفض' :
                       feedback.feedbackType === 'concern' ? 'قلق' :
                       feedback.feedbackType === 'suggestion' ? 'اقتراح' :
                       feedback.feedbackType === 'question' ? 'سؤال' : 'تحسين'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">{feedback.comment}</p>
                </div>
              ))}
              {evaluation.feedbacks.length > 2 && (
                                 <div className="text-center">
                   <Button 
                     variant="ghost" 
                     size="sm"
                     onClick={() => onViewDetails(solution.id)}
                   >
                     عرض جميع الملاحظات ({evaluation.feedbacks.length})
                   </Button>
                 </div>
              )}
            </div>
          </div>
        )}

        {/* أزرار الإجراءات */}
        <div className="flex justify-between items-center pt-4 border-t border-gray-200">
          <div className="flex space-x-2 space-x-reverse">
            {canAddFeedback && (
              <Button 
                variant="ghost"
                size="sm"
                onClick={() => onAddFeedback(solution.id)}
              >
                إضافة ملاحظة
              </Button>
            )}
            <Button 
              variant="ghost"
              size="sm"
              onClick={() => onViewDetails(solution.id)}
            >
              عرض التفاصيل
            </Button>
          </div>

          {/* مؤشر سريع للحالة */}
          <div className="flex items-center space-x-2 space-x-reverse">
            {feedbackStats.positive > feedbackStats.negative ? (
              <div className="flex items-center text-green-600">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-sm">ملاحظات إيجابية</span>
              </div>
            ) : feedbackStats.negative > feedbackStats.positive ? (
              <div className="flex items-center text-red-600">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <span className="text-sm">يحتاج مراجعة</span>
              </div>
            ) : (
              <div className="flex items-center text-gray-600">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <span className="text-sm">قيد المراجعة</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
} 