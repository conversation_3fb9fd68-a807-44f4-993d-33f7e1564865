'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { supabase, systemInfo } from '@/lib/supabase'
import { 
  FileText, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Play,
  Download,
  AlertTriangle
} from 'lucide-react'

interface Migration {
  id: string
  name: string
  description: string
  sql: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  createdAt: string
  executedAt?: string
  error?: string
}

export function MigrationManager() {
  const [migrations, setMigrations] = useState<Migration[]>([
    {
      id: '001',
      name: 'update_project_requests_schema',
      description: 'تحديث مخطط جدول طلبات المشاريع',
      sql: `-- تحديث جدول طلبات المشاريع
ALTER TABLE project_requests 
ADD COLUMN IF NOT EXISTS main_type VARCHAR(30) CHECK (main_type IN ('general_project', 'improvement_project'));

ALTER TABLE project_requests 
ADD COLUMN IF NOT EXISTS sub_type VARCHAR(30) CHECK (sub_type IN ('quick_win', 'improvement_full', 'suggestion'));`,
      status: 'pending',
      createdAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '002',
      name: 'create_kpi_system',
      description: 'إنشاء نظام مؤشرات الأداء',
      sql: `-- إنشاء جدول مؤشرات الأداء
CREATE TABLE IF NOT EXISTS kpis (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  category VARCHAR(50) NOT NULL,
  unit VARCHAR(20) NOT NULL,
  target_value DECIMAL(10,2),
  current_value DECIMAL(10,2) DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);`,
      status: 'pending',
      createdAt: '2024-01-02T00:00:00Z'
    },
    {
      id: '003',
      name: 'insert_kpi_templates',
      description: 'إدراج قوالب مؤشرات الأداء',
      sql: `-- إدراج قوالب مؤشرات الأداء الأساسية
INSERT INTO kpis (name, description, category, unit, target_value) VALUES
('معدل إنجاز المشاريع', 'نسبة المشاريع المكتملة في الوقت المحدد', 'projects', 'percentage', 90.00),
('رضا العملاء', 'مستوى رضا العملاء عن المشاريع', 'customer', 'rating', 4.50),
('توفير التكلفة', 'نسبة توفير التكلفة في المشاريع', 'finance', 'percentage', 15.00);`,
      status: 'pending',
      createdAt: '2024-01-03T00:00:00Z'
    }
  ])

  const [isExecuting, setIsExecuting] = useState(false)
  const [selectedMigration, setSelectedMigration] = useState<Migration | null>(null)

  // تنفيذ هجرة واحدة
  const executeMigration = async (migration: Migration) => {
    if (systemInfo.isDevelopmentMode) {
      // محاكاة تنفيذ الهجرة في وضع التطوير
      setIsExecuting(true)
      
      setMigrations(prev => 
        prev.map(m => 
          m.id === migration.id 
            ? { ...m, status: 'running' }
            : m
        )
      )

      // محاكاة تأخير التنفيذ
      await new Promise(resolve => setTimeout(resolve, 2000))

      // محاكاة نجاح أو فشل عشوائي
      const success = Math.random() > 0.2 // 80% نجاح

      setMigrations(prev => 
        prev.map(m => 
          m.id === migration.id 
            ? { 
                ...m, 
                status: success ? 'completed' : 'failed',
                executedAt: new Date().toISOString(),
                error: success ? undefined : 'خطأ في تنفيذ الهجرة (محاكاة)'
              }
            : m
        )
      )

      setIsExecuting(false)
      return
    }

    // تنفيذ حقيقي مع Supabase
    setIsExecuting(true)
    
    try {
      setMigrations(prev => 
        prev.map(m => 
          m.id === migration.id 
            ? { ...m, status: 'running' }
            : m
        )
      )

      const { error } = await supabase.rpc('execute_migration', {
        migration_sql: migration.sql,
        migration_name: migration.name
      })

      if (error) {
        throw error
      }

      setMigrations(prev => 
        prev.map(m => 
          m.id === migration.id 
            ? { 
                ...m, 
                status: 'completed',
                executedAt: new Date().toISOString()
              }
            : m
        )
      )

    } catch (error) {
      setMigrations(prev => 
        prev.map(m => 
          m.id === migration.id 
            ? { 
                ...m, 
                status: 'failed',
                executedAt: new Date().toISOString(),
                error: error instanceof Error ? error.message : 'خطأ غير معروف'
              }
            : m
        )
      )
    } finally {
      setIsExecuting(false)
    }
  }

  // تنفيذ جميع الهجرات المعلقة
  const executeAllPendingMigrations = async () => {
    const pendingMigrations = migrations.filter(m => m.status === 'pending')
    
    for (const migration of pendingMigrations) {
      await executeMigration(migration)
    }
  }

  // تصدير مخطط قاعدة البيانات
  const exportSchema = async () => {
    if (systemInfo.isDevelopmentMode) {
      // في وضع التطوير، نصدر مخطط وهمي
      const schema = `-- مخطط قاعدة البيانات (وضع التطوير)
-- تم إنشاؤه في: ${new Date().toISOString()}

-- الجداول الأساسية
CREATE TABLE users (...);
CREATE TABLE project_requests (...);
CREATE TABLE projects (...);
-- إلخ...`

      const blob = new Blob([schema], { type: 'text/sql' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `schema_${new Date().toISOString().split('T')[0]}.sql`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      return
    }

    // تصدير حقيقي من Supabase
    try {
      const { data, error } = await supabase.rpc('export_schema')
      
      if (error) throw error

      const blob = new Blob([data], { type: 'text/sql' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `schema_${new Date().toISOString().split('T')[0]}.sql`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('خطأ في تصدير المخطط:', error)
    }
  }

  const getStatusIcon = (status: Migration['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-600" />
      case 'running':
        return <Clock className="h-5 w-5 text-blue-600 animate-spin" />
      default:
        return <FileText className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: Migration['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-50 border-green-200 text-green-800'
      case 'failed':
        return 'bg-red-50 border-red-200 text-red-800'
      case 'running':
        return 'bg-blue-50 border-blue-200 text-blue-800'
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      {/* إحصائيات الهجرات */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي الهجرات</p>
              <p className="text-2xl font-bold">{migrations.length}</p>
            </div>
            <FileText className="h-8 w-8 text-gray-400" />
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">مكتملة</p>
              <p className="text-2xl font-bold text-green-600">
                {migrations.filter(m => m.status === 'completed').length}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-400" />
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">معلقة</p>
              <p className="text-2xl font-bold text-yellow-600">
                {migrations.filter(m => m.status === 'pending').length}
              </p>
            </div>
            <Clock className="h-8 w-8 text-yellow-400" />
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">فاشلة</p>
              <p className="text-2xl font-bold text-red-600">
                {migrations.filter(m => m.status === 'failed').length}
              </p>
            </div>
            <XCircle className="h-8 w-8 text-red-400" />
          </div>
        </Card>
      </div>

      {/* أدوات الإدارة */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">أدوات إدارة الهجرات</h3>
          <div className="flex gap-2">
            <Button
              variant="primary"
              onClick={executeAllPendingMigrations}
              disabled={isExecuting || migrations.filter(m => m.status === 'pending').length === 0}
              className="flex items-center gap-2"
            >
              <Play className="h-4 w-4" />
              تنفيذ جميع المعلقة
            </Button>
            
            <Button
              variant="default"
              onClick={exportSchema}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              تصدير المخطط
            </Button>
          </div>
        </div>

        {systemInfo.isDevelopmentMode && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
            <div className="flex items-center gap-2 text-yellow-700">
              <AlertTriangle className="h-4 w-4" />
              <span className="font-medium">وضع التطوير</span>
            </div>
            <p className="text-sm text-yellow-600 mt-1">
              الهجرات في وضع التطوير محاكاة فقط. لتنفيذ هجرات حقيقية، قم بإعداد قاعدة بيانات Supabase.
            </p>
          </div>
        )}
      </Card>

      {/* قائمة الهجرات */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">الهجرات المتاحة</h3>
        
        <div className="space-y-4">
          {migrations.map((migration) => (
            <div
              key={migration.id}
              className={`border rounded-lg p-4 ${getStatusColor(migration.status)}`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3">
                  {getStatusIcon(migration.status)}
                  <div>
                    <h4 className="font-medium">{migration.name}</h4>
                    <p className="text-sm opacity-75 mt-1">{migration.description}</p>
                    
                    {migration.executedAt && (
                      <p className="text-xs opacity-60 mt-2">
                        تم التنفيذ: {new Date(migration.executedAt).toLocaleString('ar-SA')}
                      </p>
                    )}
                    
                    {migration.error && (
                      <div className="mt-2 p-2 bg-red-100 border border-red-200 rounded text-sm text-red-700">
                        <strong>خطأ:</strong> {migration.error}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedMigration(migration)}
                  >
                    عرض SQL
                  </Button>
                  
                  {migration.status === 'pending' && (
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => executeMigration(migration)}
                      disabled={isExecuting}
                      className="flex items-center gap-1"
                    >
                      <Play className="h-3 w-3" />
                      تنفيذ
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* مودال عرض SQL */}
      {selectedMigration && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="max-w-4xl w-full max-h-[80vh] overflow-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">{selectedMigration.name}</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedMigration(null)}
                >
                  إغلاق
                </Button>
              </div>
              
              <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
                <pre className="text-sm">
                  <code>{selectedMigration.sql}</code>
                </pre>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  )
} 