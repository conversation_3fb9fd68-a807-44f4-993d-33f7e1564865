import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'

export async function GET(request: NextRequest) {
  try {
    const { data: projects, error: projectsError } = await supabaseAdmin
      .from('projects')
      .select('id, status')

    if (projectsError) {
      console.error('Error fetching projects:', projectsError)
      return NextResponse.json(
        { success: false, error: 'فشل في جلب إحصائيات المشاريع' },
        { status: 500 }
      )
    }

    const total = projects?.length || 0
    const active = projects?.filter(p => p.status === 'active').length || 0
    const completed = projects?.filter(p => p.status === 'completed').length || 0
    const onHold = projects?.filter(p => p.status === 'on_hold').length || 0
    const completionRate = total > 0 ? Math.round((completed / total) * 100 * 10) / 10 : 0

    const stats = {
      total,
      active,
      completed,
      on_hold: onHold,
      completion_rate: completionRate
    }

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('Error in projects stats API:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في الخادم' },
      { status: 500 }
    )
  }
} 