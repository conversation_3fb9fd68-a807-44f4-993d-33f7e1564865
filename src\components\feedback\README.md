# نظام المراجعة التفاعلية للمقترحات

## نظرة عامة

نظام المراجعة التفاعلية هو منصة شاملة لمراجعة مقترحات التحسين وتحويلها إلى مشاريع قابلة للتنفيذ. يتيح النظام لأصحاب المصلحة المشاركة في مراجعة الحلول المقترحة وتقديم الملاحظات قبل اتخاذ قرار نهائي بشأن الحل الأمثل.

## المكونات الرئيسية

### 1. InteractiveSolutionReview.tsx
**الغرض**: الصفحة الرئيسية للمراجعة التفاعلية
**المميزات**:
- عرض معلومات أصحاب المصلحة
- إحصائيات سريعة للملاحظات
- عرض الحلول المقترحة مع التقييمات
- نموذج إضافة الملاحظات
- حساب تلقائي لمستوى التوصية والإجماع

### 2. SolutionReviewCard.tsx
**الغرض**: عرض الحل المقترح مع مؤشرات التقييم
**المميزات**:
- مؤشرات بصرية للتقييم (مستوى التوصية، الإجماع)
- عرض الفوائد المتوقعة
- إحصائيات الملاحظات
- أزرار الإجراءات (إضافة ملاحظة، عرض التفاصيل)

### 3. FeedbackForm.tsx
**الغرض**: نموذج تفاعلي لإضافة الملاحظات
**المميزات**:
- 6 أنواع ملاحظات (اقتراح، موافقة، قلق، رفض، سؤال، تحسين)
- 4 مستويات أولوية (منخفضة، متوسطة، عالية، حرجة)
- تحقق شامل من البيانات
- واجهة تفاعلية مع ألوان مميزة

### 4. SolutionSelection.tsx
**الغرض**: اختيار الحل الأمثل مع موافقة أصحاب المصلحة
**المميزات**:
- عرض جميع الحلول المقترحة مع التقييمات
- نظام موافقة أصحاب المصلحة (موافق، موافق بشروط، محايد، مرفوض)
- مبرر اختيار الحل
- ملخص الموافقات
- التحقق من الإجماع المطلوب

### 5. ConversionToProject.tsx
**الغرض**: تحويل المقترح إلى مشروع مع إكمال البيانات الناقصة
**المميزات**:
- 4 مراحل: المعلومات الأساسية، المهام، الموارد، إدارة المخاطر
- تحويل خطوات التنفيذ إلى مهام قابلة للتتبع
- إدارة الموارد المطلوبة (بشرية، مادية، معدات، برمجيات)
- نظام إدارة المخاطر المبسط
- مؤشر تقدم تفاعلي

## الأدوات المساعدة

### 1. StakeholderExtractor.ts
**الغرض**: استخراج أصحاب المصلحة من بيانات النموذج
**الوظائف**:
- `extractStakeholders()`: استخراج جميع أصحاب المصلحة
- `extractTeamLeader()`: استخراج قائد الفريق
- `extractDepartmentManager()`: استخراج مدير القسم
- `validateStakeholderData()`: التحقق من صحة البيانات

### 2. FeedbackManager.ts
**الغرض**: إدارة العمليات الخلفية للملاحظات والمراجعة
**الوظائف**:
- `sendSuggestionForReview()`: إرسال المقترح للمراجعة
- `getInteractiveReviewData()`: الحصول على بيانات المراجعة
- `addFeedback()`: إضافة ملاحظة جديدة
- `saveSolutionSelection()`: حفظ اختيار الحل الأمثل
- `convertSuggestionToProject()`: تحويل المقترح إلى مشروع
- `getReviewStatistics()`: الحصول على إحصائيات المراجعة

## قاعدة البيانات

### الجداول الجديدة

#### suggestion_feedback
- تخزين الملاحظات والتعليقات من أصحاب المصلحة
- أنواع الملاحظات: suggestion, approval, concern, rejection, question, improvement
- مستويات الأولوية: low, medium, high, critical
- حالات المعالجة: pending, addressed, resolved, dismissed

#### solution_selection
- تخزين اختيار الحل الأمثل
- مبرر الاختيار
- موافقات أصحاب المصلحة
- الطابع الزمني للقرار

#### suggestion_conversion
- تتبع تحويل المقترحات إلى مشاريع
- ربط الطلب الأصلي بالمشروع الجديد
- بيانات التحويل الكاملة

### الحالات الجديدة
- `under_feedback`: المقترح قيد المراجعة
- `feedback_completed`: اكتملت المراجعة واختير الحل
- `converting_to_project`: جاري التحويل إلى مشروع

## سير العمل

### 1. مرحلة المراجعة التفاعلية
1. إرسال المقترح للمراجعة (تحديث الحالة إلى `under_feedback`)
2. إشعار أصحاب المصلحة
3. جمع الملاحظات والتعليقات
4. حساب تقييمات الحلول تلقائياً
5. عرض الإحصائيات والتقدم

### 2. مرحلة اختيار الحل
1. عرض جميع الحلول مع التقييمات
2. جمع موافقات أصحاب المصلحة
3. كتابة مبرر اختيار الحل
4. التحقق من الإجماع المطلوب
5. حفظ القرار النهائي

### 3. مرحلة التحويل إلى مشروع
1. استكمال المعلومات الأساسية للمشروع
2. تحويل خطوات التنفيذ إلى مهام مفصلة
3. تحديد الموارد المطلوبة والتكاليف
4. إعداد خطة إدارة المخاطر
5. إنشاء طلب مشروع جديد

## أصحاب المصلحة

### الأنواع المدعومة
- **قائد الفريق** (`team_leader`): من بيانات `teamLeader`
- **مدير القسم** (`department_manager`): من `responsibleDepartment`
- **أعضاء الفريق** (`team_member`): من `teamMembers`
- **مكتب المشاريع** (`pmo_stakeholder`): المديرون والمشرفون

### الصلاحيات
- **أصحاب المصلحة**: إضافة ملاحظات، عرض التقييمات
- **مكتب المشاريع**: جميع الصلاحيات + اختيار الحل + التحويل

## الحسابات التلقائية

### مستوى التوصية
- **موصى به بشدة**: موافقات ≥ 70% من الملاحظات
- **موصى به**: موافقات > مخاوف + رفض
- **محايد**: متوازن أو غير محدد
- **غير موصى به**: رفض أو مخاوف > موافقات

### مستوى الإجماع
- **عالي**: مشاركة ≥ 80% من أصحاب المصلحة
- **متوسط**: مشاركة ≥ 50% من أصحاب المصلحة
- **منخفض**: مشاركة < 50% من أصحاب المصلحة

## التكامل

### مع النماذج الموجودة
- يستخرج البيانات من `form_data` في `project_requests`
- يتكامل مع نظام الصلاحيات الحالي
- يستخدم مكونات UI الموحدة

### مع سير العمل
- ينتقل بسلاسة من المقترح إلى المشروع
- يحافظ على تاريخ القرارات والتبريرات
- يدعم التتبع والمراجعة اللاحقة

## الاستخدام

### بدء المراجعة التفاعلية
```typescript
// إرسال المقترح للمراجعة
const success = await FeedbackManager.sendSuggestionForReview(requestId);

// الانتقال إلى صفحة المراجعة
router.push(`/suggestions/${requestId}/review`);
```

### إضافة ملاحظة
```typescript
const feedback: NewFeedbackForm = {
  solutionId: 'solution-1',
  comment: 'ملاحظة مفصلة...',
  feedbackType: 'suggestion',
  priority: 'medium'
};

await FeedbackManager.addFeedback(requestId, feedback, reviewerInfo);
```

### اختيار الحل وتحويله
```typescript
// اختيار الحل
const selection: SolutionSelectionForm = {
  selectedSolutionId: 'solution-1',
  selectionRationale: 'مبرر الاختيار...',
  stakeholderApprovals: [...]
};

await FeedbackManager.saveSolutionSelection(requestId, selection, userId);

// التحويل إلى مشروع
const conversionData: SuggestionConversionData = {
  projectName: 'اسم المشروع',
  projectDescription: 'وصف المشروع',
  // ... بقية البيانات
};

const result = await FeedbackManager.convertSuggestionToProject(
  requestId, 
  conversionData, 
  userId
);
```

## الصفحات

### `/suggestions/[id]/review`
الصفحة الرئيسية للمراجعة التفاعلية مع جميع المراحل:
- مراجعة الحلول وإضافة الملاحظات
- اختيار الحل الأمثل مع موافقات أصحاب المصلحة
- تحويل المقترح إلى مشروع كامل

## التطوير المستقبلي

### مميزات محتملة
- نظام إشعارات في الوقت الفعلي
- تقارير تفصيلية للمراجعات
- تصدير ميثاق المشروع
- تكامل مع أنظمة إدارة المشاريع الخارجية
- نظام تقييم أداء المراجعين

### تحسينات تقنية
- حساب متوسط وقت الاستجابة
- تحليلات متقدمة للملاحظات
- نظام تنبيهات ذكي
- دعم المرفقات في الملاحظات 