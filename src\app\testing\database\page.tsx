import SupabaseConnectionTest from '@/components/testing/SupabaseConnectionTest'
import Link from 'next/link'
import { ArrowLeft, Database } from 'lucide-react'

export default function DatabaseTestPage() {
  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto py-8">
        {/* Navigation */}
        <div className="mb-6">
          <Link 
            href="/testing" 
            className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            العودة إلى لوحة الاختبارات
          </Link>
        </div>

        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Database className="w-8 h-8 text-green-600" />
            <h1 className="text-3xl font-bold text-gray-900">
              اختبار اتصال Supabase
            </h1>
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            هذه الصفحة تختبر جميع جوانب الاتصال مع Supabase بما في ذلك قاعدة البيانات والمصادقة والجداول
          </p>
        </div>
        
        <SupabaseConnectionTest />
      </div>
    </div>
  )
}
