'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { Input, Select, Textarea } from '@/components/ui/Input'
import { KpiType, KpiTemplate, KpiFormData } from '@/types/kpi.types'
import { 
  Target, 
  Clock, 
  Hash, 
  Percent, 
  DollarSign, 
  Star, 
  Heart, 
  Zap, 
  Shield,
  Plus,
  Search,
  Filter,
  X
} from 'lucide-react'

interface KpiSelectorProps {
  selectedKpis: KpiFormData[]
  onKpiAdd: (kpi: KpiFormData) => void
  onKpiRemove: (index: number) => void
  onKpiUpdate: (index: number, kpi: KpiFormData) => void
  maxKpis?: number
  showTemplates?: boolean
  projectType?: 'general_project' | 'improvement_project'
  subType?: 'quick_win' | 'improvement_full' | 'suggestion'
}

export function KpiSelector({
  selectedKpis,
  onKpiAdd,
  onKpiRemove,
  onKpiUpdate,
  maxKpis = 5,
  showTemplates = true,
  projectType = 'general_project',
  subType
}: KpiSelectorProps) {
  const [kpiTypes, setKpiTypes] = useState<KpiType[]>([])
  const [kpiTemplates, setKpiTemplates] = useState<KpiTemplate[]>([])
  const [showAddForm, setShowAddForm] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('')
  const [newKpi, setNewKpi] = useState<KpiFormData>({
    kpi_type_id: '',
    name: '',
    description: '',
    measurement_method: '',
    data_source: '',
    baseline_value: undefined,
    target_value: 0,
    unit: '',
    priority: 'medium',
    is_primary: false,
    target_date: ''
  })

  useEffect(() => {
    loadKpiTypes()
    loadKpiTemplates()
  }, [])

  const loadKpiTypes = async () => {
    // TODO: استدعاء API لجلب أنواع المؤشرات
    const mockTypes: KpiType[] = [
      {
        id: '1',
        name: 'time',
        display_name: 'مؤشرات الوقت',
        description: 'قياس الزمن والمدة',
        default_unit: 'minutes',
        icon: 'Clock',
        color: '#3B82F6',
        is_active: true,
        created_at: '',
        updated_at: ''
      },
      {
        id: '2',
        name: 'count',
        display_name: 'مؤشرات العدد',
        description: 'قياس الكمية والعدد',
        default_unit: 'count',
        icon: 'Hash',
        color: '#10B981',
        is_active: true,
        created_at: '',
        updated_at: ''
      },
      {
        id: '3',
        name: 'percentage',
        display_name: 'مؤشرات النسبة',
        description: 'قياس النسب المئوية',
        default_unit: 'percentage',
        icon: 'Percent',
        color: '#F59E0B',
        is_active: true,
        created_at: '',
        updated_at: ''
      },
      {
        id: '4',
        name: 'cost',
        display_name: 'مؤشرات التكلفة',
        description: 'قياس التكلفة والوفورات',
        default_unit: 'currency',
        icon: 'DollarSign',
        color: '#EF4444',
        is_active: true,
        created_at: '',
        updated_at: ''
      }
    ]
    setKpiTypes(mockTypes)
  }

  const loadKpiTemplates = async () => {
    // TODO: استدعاء API لجلب قوالب المؤشرات
    const mockTemplates: KpiTemplate[] = [
      {
        id: '1',
        kpi_type_id: '1',
        name: 'وقت انتظار المريض في الطوارئ',
        description: 'قياس متوسط وقت انتظار المريض من الوصول حتى بدء الفحص',
        category: 'healthcare',
        template_data: {},
        measurement_method: 'حساب متوسط الوقت من تسجيل الوصول إلى بدء الفحص',
        typical_baseline: 45,
        typical_target: 20,
        is_active: true,
        usage_count: 0,
        created_by: '',
        created_at: '',
        updated_at: ''
      }
    ]
    setKpiTemplates(mockTemplates)
  }

  const getIcon = (iconName: string) => {
    const icons = {
      Clock,
      Hash,
      Percent,
      DollarSign,
      Star,
      Heart,
      Zap,
      Shield
    }
    return icons[iconName as keyof typeof icons] || Target
  }

  const filteredTemplates = kpiTemplates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = !selectedType || template.kpi_type_id === selectedType
    return matchesSearch && matchesType
  })

  const handleTemplateSelect = (template: KpiTemplate) => {
    const kpiType = kpiTypes.find(type => type.id === template.kpi_type_id)
    const newKpiFromTemplate: KpiFormData = {
      kpi_type_id: template.kpi_type_id,
      name: template.name,
      description: template.description || '',
      measurement_method: template.measurement_method || '',
      data_source: '',
      baseline_value: template.typical_baseline || undefined,
      target_value: template.typical_target || 0,
      unit: kpiType?.default_unit || '',
      priority: 'medium',
      is_primary: selectedKpis.length === 0,
      target_date: ''
    }
    onKpiAdd(newKpiFromTemplate)
  }

  const handleAddCustomKpi = () => {
    if (newKpi.name && newKpi.kpi_type_id && newKpi.target_value > 0) {
      const kpiType = kpiTypes.find(type => type.id === newKpi.kpi_type_id)
      const kpiToAdd: KpiFormData = {
        ...newKpi,
        unit: newKpi.unit || kpiType?.default_unit || '',
        is_primary: selectedKpis.length === 0
      }
      onKpiAdd(kpiToAdd)
      setNewKpi({
        kpi_type_id: '',
        name: '',
        description: '',
        measurement_method: '',
        data_source: '',
        baseline_value: undefined,
        target_value: 0,
        unit: '',
        priority: 'medium',
        is_primary: false,
        target_date: ''
      })
      setShowAddForm(false)
    }
  }

  const handleKpiTypeChange = (typeId: string) => {
    const kpiType = kpiTypes.find(type => type.id === typeId)
    setNewKpi(prev => ({
      ...prev,
      kpi_type_id: typeId,
      unit: kpiType?.default_unit || ''
    }))
  }

  return (
    <div className="space-y-6">
      {/* المؤشرات المحددة */}
      {selectedKpis.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Target className="w-5 h-5" />
            المؤشرات المحددة ({selectedKpis.length})
          </h3>
          
          <div className="grid gap-4">
            {selectedKpis.map((kpi, index) => {
              const kpiType = kpiTypes.find(type => type.id === kpi.kpi_type_id)
              const IconComponent = getIcon(kpiType?.icon || 'Target')
              
              return (
                <Card key={index} className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      <div 
                        className="w-10 h-10 rounded-lg flex items-center justify-center"
                        style={{ backgroundColor: `${kpiType?.color || '#6B7280'}20` }}
                      >
                        <IconComponent 
                          className="w-5 h-5" 
                          style={{ color: kpiType?.color || '#6B7280' }}
                        />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-medium text-gray-900">{kpi.name}</h4>
                          {kpi.is_primary && (
                            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                              رئيسي
                            </span>
                          )}
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">القيمة الحالية:</span>
                            <span className="font-medium mr-2">
                              {kpi.baseline_value || 'غير محدد'} {kpi.unit}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600">الهدف:</span>
                            <span className="font-medium mr-2">
                              {kpi.target_value} {kpi.unit}
                            </span>
                          </div>
                        </div>
                        
                        {kpi.description && (
                          <p className="text-sm text-gray-600 mt-2">{kpi.description}</p>
                        )}
                      </div>
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onKpiRemove(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </Card>
              )
            })}
          </div>
        </div>
      )}

      {/* إضافة مؤشر جديد */}
      {selectedKpis.length < maxKpis && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              إضافة مؤشر جديد
            </h3>
            {!showAddForm && (
              <Button
                variant="primary"
                onClick={() => setShowAddForm(true)}
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                إضافة مؤشر مخصص
              </Button>
            )}
          </div>

          {/* البحث والفلترة */}
          {showTemplates && (
            <div className="flex gap-4">
              <div className="flex-1">
                                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="البحث في قوالب المؤشرات..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
              </div>
              <Select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-48"
              >
                <option value="">جميع الأنواع</option>
                {kpiTypes.map(type => (
                  <option key={type.id} value={type.id}>
                    {type.display_name}
                  </option>
                ))}
              </Select>
            </div>
          )}

          {/* قوالب المؤشرات */}
          {showTemplates && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredTemplates.map(template => {
                const kpiType = kpiTypes.find(type => type.id === template.kpi_type_id)
                const IconComponent = getIcon(kpiType?.icon || 'Target')
                
                return (
                  <Card key={template.id} className="p-4 hover:shadow-md transition-shadow cursor-pointer">
                    <div 
                      className="flex items-start gap-3"
                      onClick={() => handleTemplateSelect(template)}
                    >
                      <div 
                        className="w-10 h-10 rounded-lg flex items-center justify-center"
                        style={{ backgroundColor: `${kpiType?.color || '#6B7280'}20` }}
                      >
                        <IconComponent 
                          className="w-5 h-5" 
                          style={{ color: kpiType?.color || '#6B7280' }}
                        />
                      </div>
                      
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 mb-1">
                          {template.name}
                        </h4>
                        <p className="text-sm text-gray-600 mb-2">
                          {template.description}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>النوع: {kpiType?.display_name}</span>
                          {template.typical_baseline && template.typical_target && (
                            <span>
                              {template.typical_baseline} → {template.typical_target} {kpiType?.default_unit}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </Card>
                )
              })}
            </div>
          )}

          {/* نموذج إضافة مؤشر مخصص */}
          {showAddForm && (
            <Card className="p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-medium text-gray-900">
                    إضافة مؤشر مخصص
                  </h4>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowAddForm(false)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Select
                    label="نوع المؤشر *"
                    value={newKpi.kpi_type_id}
                    onChange={(e) => handleKpiTypeChange(e.target.value)}
                    required
                  >
                    <option value="">اختر نوع المؤشر</option>
                    {kpiTypes.map(type => (
                      <option key={type.id} value={type.id}>
                        {type.display_name}
                      </option>
                    ))}
                  </Select>

                  <Input
                    label="اسم المؤشر *"
                    value={newKpi.name}
                    onChange={(e) => setNewKpi(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="مثال: وقت انتظار المريض"
                    required
                  />

                  <Input
                    label="القيمة الحالية"
                    type="number"
                    value={newKpi.baseline_value || ''}
                    onChange={(e) => setNewKpi(prev => ({ 
                      ...prev, 
                      baseline_value: e.target.value ? Number(e.target.value) : undefined 
                    }))}
                    placeholder="القيمة قبل المشروع"
                  />

                  <Input
                    label="القيمة المستهدفة *"
                    type="number"
                    value={newKpi.target_value}
                    onChange={(e) => setNewKpi(prev => ({ 
                      ...prev, 
                      target_value: Number(e.target.value) 
                    }))}
                    placeholder="الهدف المطلوب تحقيقه"
                    required
                  />

                  <Input
                    label="وحدة القياس"
                    value={newKpi.unit}
                    onChange={(e) => setNewKpi(prev => ({ ...prev, unit: e.target.value }))}
                    placeholder="دقيقة، عدد، نسبة مئوية..."
                  />

                  <Select
                    label="الأولوية"
                    value={newKpi.priority}
                    onChange={(e) => setNewKpi(prev => ({ 
                      ...prev, 
                      priority: e.target.value as 'low' | 'medium' | 'high' 
                    }))}
                  >
                    <option value="low">منخفضة</option>
                    <option value="medium">متوسطة</option>
                    <option value="high">عالية</option>
                  </Select>
                </div>

                <Textarea
                  label="وصف المؤشر"
                  value={newKpi.description}
                  onChange={(e) => setNewKpi(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="وصف تفصيلي للمؤشر وأهميته..."
                  rows={3}
                />

                <Textarea
                  label="طريقة القياس"
                  value={newKpi.measurement_method}
                  onChange={(e) => setNewKpi(prev => ({ ...prev, measurement_method: e.target.value }))}
                  placeholder="كيفية قياس وحساب هذا المؤشر..."
                  rows={3}
                />

                <div className="flex justify-end gap-3">
                  <Button
                    variant="ghost"
                    onClick={() => setShowAddForm(false)}
                  >
                    إلغاء
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleAddCustomKpi}
                    disabled={!newKpi.name || !newKpi.kpi_type_id || newKpi.target_value <= 0}
                  >
                    إضافة المؤشر
                  </Button>
                </div>
              </div>
            </Card>
          )}
        </div>
      )}

      {/* رسالة الحد الأقصى */}
      {selectedKpis.length >= maxKpis && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
          <p className="text-amber-800 text-sm">
            تم الوصول للحد الأقصى من المؤشرات ({maxKpis}). يمكنك حذف مؤشر لإضافة آخر.
          </p>
        </div>
      )}
    </div>
  )
} 