// أنواع الإشعارات الموحدة
export type NotificationType = 'success' | 'error' | 'warning' | 'info' | 'loading'
export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent'
export type NotificationPosition = 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'

// واجهة الإشعار الأساسية
export interface BaseNotification {
  id: string
  type: NotificationType
  title: string
  message?: string
  timestamp?: string
  isRead?: boolean
  priority?: NotificationPriority
  duration?: number
  persistent?: boolean
  action?: {
    label: string
    onClick: () => void
  }
  actionUrl?: string
  actionLabel?: string
  metadata?: Record<string, any>
}

// إشعار Toast
export interface ToastNotification extends BaseNotification {
  duration: number
}

// إشعار النظام
export interface SystemNotification extends BaseNotification {
  priority: NotificationPriority
  isSticky?: boolean
  userId?: string
  expiresAt?: string
}

// إشعار منبثق
export interface PopupNotification extends BaseNotification {
  position?: NotificationPosition
}

// إعدادات الإشعارات
export interface NotificationSettings {
  maxVisible?: number
  defaultDuration?: number
  autoRefresh?: boolean
  refreshInterval?: number
  showFilters?: boolean
  position?: NotificationPosition
  enableSound?: boolean
  enableVibration?: boolean
} 