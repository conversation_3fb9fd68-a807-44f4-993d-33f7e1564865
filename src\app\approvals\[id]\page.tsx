'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useAuth, usePermissions } from '@/lib/auth'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { WorkflowTracker } from '@/components/workflow/WorkflowTracker'
import { 
  ArrowLeft, 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertCircle,
  FileText,
  Lightbulb,
  Target,
  Briefcase,
  Settings,
  Zap,
  User,
  Calendar,
  DollarSign,
  Building,
  Flag,
  MessageSquare,
  Eye,
  ThumbsUp,
  ThumbsDown,
  Download,
  Paperclip
} from 'lucide-react'

interface RequestData {
  id: string
  title: string
  description: string
  main_type: string
  sub_type: string | null
  status: string
  priority: string
  requester_id: string
  department_id: string
  expected_start_date: string | null
  expected_end_date: string | null
  estimated_budget: number | null
  business_case: string | null
  expected_benefits: string | null
  risks_and_challenges: string | null
  form_data: any
  attachments: any
  approval_level: number
  created_at: string
  updated_at: string
  requester: {
    id: string
    name: string
    email: string
  }
  department: {
    id: string
    name: string
    manager_name: string
  }
}

interface ApprovalData {
  id: string
  request_id: string
  approver_id: string
  status: string
  notes: string | null
  approved_at: string | null
  created_at: string
  approver: {
    id: string
    name: string
    role: {
      display_name: string
    }
  }
}

const statusConfig = {
  pending: { icon: Clock, color: 'text-yellow-600', bg: 'bg-yellow-100', label: 'في انتظار الاعتماد' },
  under_review: { icon: Eye, color: 'text-blue-600', bg: 'bg-blue-100', label: 'قيد المراجعة' },
  approved: { icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-100', label: 'معتمد' },
  rejected: { icon: XCircle, color: 'text-red-600', bg: 'bg-red-100', label: 'مرفوض' },
  converted_to_project: { icon: Target, color: 'text-purple-600', bg: 'bg-purple-100', label: 'تم تحويله لمشروع' }
}

const typeConfig = {
  general_project: { icon: Briefcase, label: 'مشروع عام', color: 'text-blue-600' },
  improvement_project: { icon: Target, label: 'مشروع تحسين', color: 'text-green-600' }
}

const subTypeConfig = {
  comprehensive: { icon: Settings, label: 'تحسين شامل', color: 'text-purple-600' },
  suggestion: { icon: Lightbulb, label: 'مقترح تحسين', color: 'text-yellow-600' },
  quick_win: { icon: Zap, label: 'كويك وين', color: 'text-orange-600' }
}

const priorityConfig = {
  low: { color: 'text-green-600', bg: 'bg-green-100', label: 'منخفض' },
  medium: { color: 'text-yellow-600', bg: 'bg-yellow-100', label: 'متوسط' },
  high: { color: 'text-orange-600', bg: 'bg-orange-100', label: 'عالي' },
  urgent: { color: 'text-red-600', bg: 'bg-red-100', label: 'عاجل' }
}

export default function ApprovalDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const permissions = usePermissions()
  
  const [request, setRequest] = useState<RequestData | null>(null)
  const [approvals, setApprovals] = useState<ApprovalData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('details')
  const [showApprovalModal, setShowApprovalModal] = useState(false)
  const [approvalDecision, setApprovalDecision] = useState<'approved' | 'rejected'>('approved')
  const [approvalNotes, setApprovalNotes] = useState('')
  const [processing, setProcessing] = useState(false)

  useEffect(() => {
    loadRequestData()
  }, [params.id])

  const loadRequestData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // تحميل بيانات الطلب
      const requestResponse = await fetch(`/api/requests/${params.id}`)
      if (!requestResponse.ok) {
        throw new Error('فشل في تحميل بيانات الطلب')
      }
      
      const requestResult = await requestResponse.json()
      if (!requestResult.success) {
        throw new Error(requestResult.error || 'فشل في تحميل بيانات الطلب')
      }
      
      setRequest(requestResult.data)
      
      // تحميل بيانات الموافقات
      const approvalsResponse = await fetch(`/api/approvals?request_id=${params.id}`)
      if (approvalsResponse.ok) {
        const approvalsResult = await approvalsResponse.json()
        if (approvalsResult.success) {
          setApprovals(approvalsResult.data || [])
        }
      }
      
    } catch (err) {
      console.error('Error loading request data:', err)
      setError(err instanceof Error ? err.message : 'فشل في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  const handleApproval = async () => {
    if (!request || !user) return
    
    setProcessing(true)
    try {
      const response = await fetch('/api/approvals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          request_id: request.id,
          approver_id: user.id,
          decision: approvalDecision,
          notes: approvalNotes
        })
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في معالجة الاعتماد')
      }
      
      setShowApprovalModal(false)
      setApprovalNotes('')
      await loadRequestData()
      
    } catch (err) {
      console.error('Error processing approval:', err)
      setError(err instanceof Error ? err.message : 'فشل في معالجة الاعتماد')
    } finally {
      setProcessing(false)
    }
  }

  const canApprove = () => {
    if (!request || !user || !permissions) return false
    
    // التحقق من أن الطلب في حالة انتظار الموافقة
    if (request.status !== 'pending' && request.status !== 'under_review') return false
    
    // التحقق من صلاحيات المستخدم
    return permissions.canApproveRequestType(
      request.main_type,
      request.sub_type,
      request.approval_level
    )
  }

  const getStatusIcon = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig]
    if (!config) return <Clock className="w-5 h-5 text-gray-500" />
    
    const Icon = config.icon
    return <Icon className={`w-5 h-5 ${config.color}`} />
  }

  const getTypeIcon = (mainType: string, subType?: string | null) => {
    if (mainType === 'improvement_project' && subType) {
      const config = subTypeConfig[subType as keyof typeof subTypeConfig]
      if (config) {
        const Icon = config.icon
        return <Icon className={`w-5 h-5 ${config.color}`} />
      }
    }
    
    const config = typeConfig[mainType as keyof typeof typeConfig]
    if (config) {
      const Icon = config.icon
      return <Icon className={`w-5 h-5 ${config.color}`} />
    }
    
    return <FileText className="w-5 h-5 text-gray-500" />
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  const renderFormData = () => {
    if (!request?.form_data) return null
    
    const formData = request.form_data
    
    return (
      <div className="space-y-6">
        {/* المعلومات الأساسية */}
        {formData.basicInfo && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">المعلومات الأساسية</h3>
            </CardHeader>
            <CardContent className="space-y-4">
              {formData.basicInfo.projectLeader && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">قائد المشروع</label>
                  <p className="text-gray-900">{formData.basicInfo.projectLeader}</p>
                </div>
              )}
              {formData.basicInfo.teamMembers && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">أعضاء الفريق</label>
                  <p className="text-gray-900">{formData.basicInfo.teamMembers}</p>
                </div>
              )}
              {formData.basicInfo.duration && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">المدة المتوقعة</label>
                  <p className="text-gray-900">{formData.basicInfo.duration}</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* وصف المشكلة */}
        {formData.problemDescription && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">وصف المشكلة</h3>
            </CardHeader>
            <CardContent>
              <p className="text-gray-900 whitespace-pre-wrap">{formData.problemDescription}</p>
            </CardContent>
          </Card>
        )}

        {/* الحل المقترح */}
        {formData.proposedSolution && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">الحل المقترح</h3>
            </CardHeader>
            <CardContent>
              <p className="text-gray-900 whitespace-pre-wrap">{formData.proposedSolution}</p>
            </CardContent>
          </Card>
        )}

        {/* المؤشرات */}
        {formData.indicators && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">المؤشرات</h3>
            </CardHeader>
            <CardContent className="space-y-4">
              {formData.indicators.map((indicator: any, index: number) => (
                <div key={index} className="border rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">{indicator.name}</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">الوضع الحالي: </span>
                      <span className="font-medium">{indicator.currentValue} {indicator.unit}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">الهدف: </span>
                      <span className="font-medium">{indicator.targetValue} {indicator.unit}</span>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* المهام */}
        {formData.tasks && formData.tasks.length > 0 && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">المهام</h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {formData.tasks.map((task: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{task.title}</p>
                      <p className="text-sm text-gray-600">المسؤول: {task.assignee}</p>
                    </div>
                    <div className="text-sm text-gray-500">
                      {task.dueDate && formatDate(task.dueDate)}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* المخاطر */}
        {formData.risks && formData.risks.length > 0 && (
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">المخاطر</h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {formData.risks.map((risk: any, index: number) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{risk.description}</h4>
                      <span className={`px-2 py-1 text-xs rounded ${
                        risk.level === 'high' ? 'bg-red-100 text-red-800' :
                        risk.level === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {risk.level === 'high' ? 'عالي' : risk.level === 'medium' ? 'متوسط' : 'منخفض'}
                      </span>
                    </div>
                    {risk.mitigation && (
                      <p className="text-sm text-gray-600">خطة التخفيف: {risk.mitigation}</p>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    )
  }

  if (loading) {
    return (
      <ProtectedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </ProtectedLayout>
    )
  }

  if (error || !request) {
    return (
      <ProtectedLayout>
        <div className="flex flex-col items-center justify-center min-h-screen">
          <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
          <p className="text-red-600 text-lg mb-4">{error || 'لم يتم العثور على الطلب'}</p>
          <Button onClick={() => router.push('/approvals')} variant="ghost">
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة للموافقات
          </Button>
        </div>
      </ProtectedLayout>
    )
  }

  return (
    <ProtectedLayout>
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              onClick={() => router.push('/approvals')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              العودة للموافقات
            </Button>
            <div className="h-6 w-px bg-gray-300" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{request.title}</h1>
              <div className="flex items-center gap-4 mt-2">
                <div className="flex items-center gap-2">
                  {getStatusIcon(request.status)}
                  <span className="text-sm text-gray-600">
                    {statusConfig[request.status as keyof typeof statusConfig]?.label || request.status}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  {getTypeIcon(request.main_type, request.sub_type)}
                  <span className="text-sm text-gray-600">
                    {typeConfig[request.main_type as keyof typeof typeConfig]?.label || request.main_type}
                    {request.sub_type && ` - ${subTypeConfig[request.sub_type as keyof typeof subTypeConfig]?.label || request.sub_type}`}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Flag className="w-4 h-4 text-gray-500" />
                  <span className={`text-sm px-2 py-1 rounded ${priorityConfig[request.priority as keyof typeof priorityConfig]?.bg || 'bg-gray-100'} ${priorityConfig[request.priority as keyof typeof priorityConfig]?.color || 'text-gray-800'}`}>
                    {priorityConfig[request.priority as keyof typeof priorityConfig]?.label || request.priority}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-3">
            {canApprove() && (
              <>
                <Button
                  variant="primary"
                  onClick={() => {
                    setApprovalDecision('approved')
                    setShowApprovalModal(true)
                  }}
                  className="flex items-center gap-2"
                >
                  <ThumbsUp className="w-4 h-4" />
                  اعتماد
                </Button>
                <Button
                  variant="danger"
                  onClick={() => {
                    setApprovalDecision('rejected')
                    setShowApprovalModal(true)
                  }}
                  className="flex items-center gap-2"
                >
                  <ThumbsDown className="w-4 h-4" />
                  رفض
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="flex space-x-8">
            {[
              { id: 'details', label: 'تفاصيل الطلب', icon: FileText },
              { id: 'workflow', label: 'سير الموافقة', icon: Clock },
              { id: 'history', label: 'سجل الأنشطة', icon: MessageSquare }
            ].map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === 'details' && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Main Content */}
              <div className="lg:col-span-2 space-y-6">
                {/* Basic Info */}
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">معلومات الطلب</h3>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">العنوان</label>
                      <p className="text-gray-900">{request.title}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
                      <p className="text-gray-900 whitespace-pre-wrap">{request.description}</p>
                    </div>
                    {request.business_case && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">المبرر التجاري</label>
                        <p className="text-gray-900 whitespace-pre-wrap">{request.business_case}</p>
                      </div>
                    )}
                    {request.expected_benefits && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">الفوائد المتوقعة</label>
                        <p className="text-gray-900 whitespace-pre-wrap">{request.expected_benefits}</p>
                      </div>
                    )}
                    {request.risks_and_challenges && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">المخاطر والتحديات</label>
                        <p className="text-gray-900 whitespace-pre-wrap">{request.risks_and_challenges}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Form Data */}
                {renderFormData()}

                {/* Attachments */}
                {request.attachments && request.attachments.length > 0 && (
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold">المرفقات</h3>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {request.attachments.map((attachment: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center gap-3">
                              <Paperclip className="w-4 h-4 text-gray-500" />
                              <div>
                                <p className="font-medium text-gray-900">{attachment.name}</p>
                                <p className="text-sm text-gray-500">{attachment.size}</p>
                              </div>
                            </div>
                            <Button variant="ghost" size="sm">
                              <Download className="w-4 h-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Request Info */}
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">معلومات الطلب</h3>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center gap-3">
                      <User className="w-5 h-5 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-600">مقدم الطلب</p>
                        <p className="font-medium">{request.requester.name}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Building className="w-5 h-5 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-600">القسم</p>
                        <p className="font-medium">{request.department.name}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Calendar className="w-5 h-5 text-gray-500" />
                      <div>
                        <p className="text-sm text-gray-600">تاريخ الإنشاء</p>
                        <p className="font-medium">{formatDate(request.created_at)}</p>
                      </div>
                    </div>
                    {request.estimated_budget && (
                      <div className="flex items-center gap-3">
                        <DollarSign className="w-5 h-5 text-gray-500" />
                        <div>
                          <p className="text-sm text-gray-600">الميزانية المتوقعة</p>
                          <p className="font-medium">{formatCurrency(request.estimated_budget)}</p>
                        </div>
                      </div>
                    )}
                    {request.expected_start_date && (
                      <div className="flex items-center gap-3">
                        <Calendar className="w-5 h-5 text-gray-500" />
                        <div>
                          <p className="text-sm text-gray-600">تاريخ البدء المتوقع</p>
                          <p className="font-medium">{formatDate(request.expected_start_date)}</p>
                        </div>
                      </div>
                    )}
                    {request.expected_end_date && (
                      <div className="flex items-center gap-3">
                        <Calendar className="w-5 h-5 text-gray-500" />
                        <div>
                          <p className="text-sm text-gray-600">تاريخ الانتهاء المتوقع</p>
                          <p className="font-medium">{formatDate(request.expected_end_date)}</p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Quick Actions */}
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">إجراءات سريعة</h3>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => router.push(`/requests/${request.id}`)}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      عرض الطلب الأصلي
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full justify-start"
                      onClick={() => window.print()}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      طباعة / تصدير
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {activeTab === 'workflow' && (
            <WorkflowTracker
              requestId={request.id}
              mainType={request.main_type}
              subType={request.sub_type}
              currentStatus={request.status}
              onApprovalUpdate={loadRequestData}
            />
          )}

          {activeTab === 'history' && (
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">سجل الأنشطة</h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Request Creation */}
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <FileText className="w-4 h-4 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="font-medium text-gray-900">تم إنشاء الطلب</p>
                        <span className="text-sm text-gray-500">{formatDate(request.created_at)}</span>
                      </div>
                      <p className="text-sm text-gray-600">
                        قام {request.requester.name} بإنشاء طلب جديد
                      </p>
                    </div>
                  </div>

                  {/* Approvals History */}
                  {approvals.map((approval) => (
                    <div key={approval.id} className="flex items-start gap-4">
                      <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                        approval.status === 'approved' ? 'bg-green-100' : 
                        approval.status === 'rejected' ? 'bg-red-100' : 'bg-yellow-100'
                      }`}>
                        {approval.status === 'approved' ? (
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        ) : approval.status === 'rejected' ? (
                          <XCircle className="w-4 h-4 text-red-600" />
                        ) : (
                          <Clock className="w-4 h-4 text-yellow-600" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <p className="font-medium text-gray-900">
                            {approval.status === 'approved' ? 'تم الاعتماد' : 
                             approval.status === 'rejected' ? 'تم الرفض' : 'في انتظار الموافقة'}
                          </p>
                          {approval.approved_at && (
                            <span className="text-sm text-gray-500">
                              {formatDate(approval.approved_at)}
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-1">
                          {approval.approver.name} - {approval.approver.role.display_name}
                        </p>
                        {approval.notes && (
                          <p className="text-sm text-gray-700 bg-gray-50 p-2 rounded">
                            {approval.notes}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Approval Modal */}
        {showApprovalModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">
                  {approvalDecision === 'approved' ? 'اعتماد الطلب' : 'رفض الطلب'}
                </h3>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {approvalDecision === 'approved' ? 'ملاحظات الاعتماد' : 'سبب الرفض'}
                  </label>
                  <textarea
                    value={approvalNotes}
                    onChange={(e) => setApprovalNotes(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={4}
                    placeholder={approvalDecision === 'approved' ? 'أضف ملاحظات الاعتماد...' : 'أدخل سبب الرفض...'}
                  />
                </div>
                <div className="flex justify-end gap-3">
                  <Button
                    variant="ghost"
                    onClick={() => setShowApprovalModal(false)}
                    disabled={processing}
                  >
                    إلغاء
                  </Button>
                  <Button
                    variant={approvalDecision === 'approved' ? 'primary' : 'danger'}
                    onClick={handleApproval}
                    disabled={processing}
                  >
                    {processing ? 'جاري المعالجة...' : (approvalDecision === 'approved' ? 'اعتماد' : 'رفض')}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </ProtectedLayout>
  )
} 