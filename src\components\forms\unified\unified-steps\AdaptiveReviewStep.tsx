'use client'

import React from 'react'
import { Card } from '@/components/ui/Card'
import { CheckCircle, FileText, Users, Target, Lightbulb, Calendar, DollarSign, AlertTriangle } from 'lucide-react'
import { FormType, UnifiedFormData, EnhancedImprovementData, SuggestionData, QuickWinData } from '../UnifiedProjectForm'

interface AdaptiveReviewStepProps {
  formType: FormType
  data: UnifiedFormData
  updateData: (field: string, value: any) => void
  errors: Record<string, string>
}

export function AdaptiveReviewStep({ formType, data }: AdaptiveReviewStepProps) {
  const getFormTypeTitle = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'مشروع التحسين الشامل'
      case 'suggestion':
        return 'مقترح تحسيني'
      case 'quick_win':
        return 'حل سريع (Quick Win)'
      default:
        return 'مشروع'
    }
  }

  const getFormTypeColor = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'red'
      case 'suggestion':
        return 'indigo'
      case 'quick_win':
        return 'yellow'
      default:
        return 'gray'
    }
  }

  const colorClasses = {
    red: {
      bg: 'bg-red-50',
      border: 'border-red-200',
      text: 'text-red-900',
      icon: 'text-red-600'
    },
    indigo: {
      bg: 'bg-indigo-50',
      border: 'border-indigo-200',
      text: 'text-indigo-900',
      icon: 'text-indigo-600'
    },
    yellow: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      text: 'text-yellow-900',
      icon: 'text-yellow-600'
    },
    gray: {
      bg: 'bg-gray-50',
      border: 'border-gray-200',
      text: 'text-gray-900',
      icon: 'text-gray-600'
    }
  }

  const formColor = getFormTypeColor()
  const colors = colorClasses[formColor]

  const renderBasicInfo = () => {
    if (formType === 'enhanced_improvement') {
      const enhancedData = data as EnhancedImprovementData
      return (
        <Card className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <FileText className="w-5 h-5 text-gray-600" />
            <h4 className="font-semibold text-gray-900">المعلومات الأساسية</h4>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">اسم المشروع:</span>
              <span className="mr-2">{enhancedData.projectName || 'غير محدد'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">الأولوية:</span>
              <span className="mr-2">{enhancedData.priority === 'urgent' ? 'عاجل' : enhancedData.priority === 'high' ? 'عالية' : enhancedData.priority === 'medium' ? 'متوسطة' : 'منخفضة'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">تاريخ البداية:</span>
              <span className="mr-2">{enhancedData.startDate || 'غير محدد'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">تاريخ النهاية:</span>
              <span className="mr-2">{enhancedData.endDate || 'غير محدد'}</span>
            </div>
          </div>
          {enhancedData.projectDescription && (
            <div className="mt-3">
              <span className="font-medium text-gray-700">وصف المشروع:</span>
              <p className="mt-1 text-gray-600">{enhancedData.projectDescription}</p>
            </div>
          )}
        </Card>
      )
    } else if (formType === 'quick_win') {
      const quickWinData = data as QuickWinData
      return (
        <Card className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <FileText className="w-5 h-5 text-gray-600" />
            <h4 className="font-semibold text-gray-900">المعلومات الأساسية</h4>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">عنوان المشروع:</span>
              <span className="mr-2">{quickWinData.projectTitle || 'غير محدد'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">القسم:</span>
              <span className="mr-2">{quickWinData.section || 'غير محدد'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">منفذ المشروع:</span>
              <span className="mr-2">{quickWinData.projectExecutor?.name || 'غير محدد'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">الجوال:</span>
              <span className="mr-2">{quickWinData.projectExecutor?.phone || 'غير محدد'}</span>
            </div>
          </div>
        </Card>
      )
    }
    return null
  }

  const renderProblemInfo = () => (
    <Card className="p-4">
      <div className="flex items-center gap-2 mb-3">
        <Target className="w-5 h-5 text-gray-600" />
        <h4 className="font-semibold text-gray-900">تحديد المشكلة</h4>
      </div>
      <div className="space-y-3">
        <div>
          <span className="font-medium text-gray-700">وصف المشكلة:</span>
          <p className="mt-1 text-gray-600">{data.problemDescription || 'غير محدد'}</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-700">المؤشر:</span>
            <span className="mr-2">{data.indicatorName || 'غير محدد'}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">القيمة الحالية:</span>
            <span className="mr-2">{data.currentValue} {data.unit}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">القيمة المستهدفة:</span>
            <span className="mr-2">{data.targetValue} {data.unit}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">الفجوة:</span>
            <span className="mr-2">{Math.abs(data.targetValue - data.currentValue)} {data.unit}</span>
          </div>
        </div>
      </div>
    </Card>
  )

  const renderTeamInfo = () => (
    <Card className="p-4">
      <div className="flex items-center gap-2 mb-3">
        <Users className="w-5 h-5 text-gray-600" />
        <h4 className="font-semibold text-gray-900">الفريق والتنظيم</h4>
      </div>
      <div className="space-y-3">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-700">القسم المسؤول:</span>
            <span className="mr-2">{data.responsibleDepartment || 'غير محدد'}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">قائد الفريق:</span>
            <span className="mr-2">{data.teamLeader.name || 'غير محدد'}</span>
          </div>
        </div>
        {data.teamMembers.length > 0 && (
          <div>
            <span className="font-medium text-gray-700">أعضاء الفريق:</span>
            <div className="mt-1 space-y-1">
              {data.teamMembers.map((member, index) => (
                <div key={index} className="text-sm text-gray-600">
                  {member.name} - {member.role} ({member.department})
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </Card>
  )

  const renderSolutionInfo = () => {
    if (formType === 'enhanced_improvement') {
      const enhancedData = data as EnhancedImprovementData
      return (
        <Card className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <Lightbulb className="w-5 h-5 text-gray-600" />
            <h4 className="font-semibold text-gray-900">الحل المختار</h4>
          </div>
          <div className="space-y-3">
            <div>
              <span className="font-medium text-gray-700">وصف الحل:</span>
              <p className="mt-1 text-gray-600">{enhancedData.selectedSolution.description || 'غير محدد'}</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">التكلفة المقدرة:</span>
                <span className="mr-2">{enhancedData.selectedSolution.estimatedCost?.toLocaleString()} ريال</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">مدة التنفيذ:</span>
                <span className="mr-2">{enhancedData.selectedSolution.implementationTime || 'غير محدد'}</span>
              </div>
            </div>
          </div>
        </Card>
      )
    } else if (formType === 'suggestion') {
      const suggestionData = data as SuggestionData
      return (
        <Card className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <Lightbulb className="w-5 h-5 text-gray-600" />
            <h4 className="font-semibold text-gray-900">الحلول المقترحة</h4>
          </div>
          <div className="space-y-3">
            {suggestionData.suggestedSolutions.map((solution, index) => (
              <div key={index} className="border rounded-lg p-3">
                <h5 className="font-medium text-gray-900">{solution.title}</h5>
                <p className="text-sm text-gray-600 mt-1">{solution.description}</p>
                <div className="grid grid-cols-3 gap-4 mt-2 text-xs">
                  <div>
                    <span className="font-medium">الجدوى:</span> {solution.feasibilityScore}/10
                  </div>
                  <div>
                    <span className="font-medium">التأثير:</span> {solution.impactScore}/10
                  </div>
                  <div>
                    <span className="font-medium">الأولوية:</span> {solution.priority === 'high' ? 'عالية' : solution.priority === 'medium' ? 'متوسطة' : 'منخفضة'}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )
    } else if (formType === 'quick_win') {
      const quickWinData = data as QuickWinData
      return (
        <Card className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <Lightbulb className="w-5 h-5 text-gray-600" />
            <h4 className="font-semibold text-gray-900">الحل السريع</h4>
          </div>
          <div className="space-y-3">
            <div>
              <span className="font-medium text-gray-700">وصف الحل:</span>
              <p className="mt-1 text-gray-600">{quickWinData.solution.description || 'غير محدد'}</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">مدة التنفيذ:</span>
                <span className="mr-2">{quickWinData.solution.implementationWeeks} أسابيع</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">التكلفة المقدرة:</span>
                <span className="mr-2">{quickWinData.solution.estimatedCost?.toLocaleString()} ريال</span>
              </div>
            </div>
            {quickWinData.solution.tasks.length > 0 && (
              <div>
                <span className="font-medium text-gray-700">المهام:</span>
                <ul className="mt-1 space-y-1">
                  {quickWinData.solution.tasks.map((task, index) => (
                    <li key={index} className="text-sm text-gray-600">• {task}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </Card>
      )
    }
    return null
  }

  const renderProjectPlanningInfo = () => {
    if (formType === 'enhanced_improvement') {
      const enhancedData = data as EnhancedImprovementData
      return (
        <Card className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <Calendar className="w-5 h-5 text-gray-600" />
            <h4 className="font-semibold text-gray-900">تخطيط المشروع</h4>
          </div>
          <div className="space-y-4">
            {enhancedData.projectTasks.length > 0 && (
              <div>
                <span className="font-medium text-gray-700">المهام ({enhancedData.projectTasks.length}):</span>
                <div className="mt-2 space-y-2">
                  {enhancedData.projectTasks.slice(0, 3).map((task, index) => (
                    <div key={index} className="text-sm bg-gray-50 rounded p-2">
                      <div className="font-medium">{task.title}</div>
                      <div className="text-gray-600">المسؤول: {task.assignee}</div>
                    </div>
                  ))}
                  {enhancedData.projectTasks.length > 3 && (
                    <div className="text-sm text-gray-500">
                      و {enhancedData.projectTasks.length - 3} مهام أخرى...
                    </div>
                  )}
                </div>
              </div>
            )}

            {enhancedData.requiredResources.length > 0 && (
              <div>
                <span className="font-medium text-gray-700">الموارد المطلوبة:</span>
                <div className="mt-2">
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4 text-green-600" />
                    <span className="font-medium text-green-900">
                      إجمالي التكلفة: {enhancedData.requiredResources.reduce((sum, resource) => 
                        sum + (resource.quantity * resource.cost), 0
                      ).toLocaleString()} ريال
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </Card>
      )
    }
    return null
  }

  const renderRiskInfo = () => {
    if (formType === 'enhanced_improvement') {
      const enhancedData = data as EnhancedImprovementData
      const totalRisks = enhancedData.risks?.length || 0
      const totalAssumptions = enhancedData.assumptions?.length || 0
      const totalIssues = enhancedData.issues?.length || 0
      const totalDependencies = enhancedData.dependencies?.length || 0

      if (totalRisks + totalAssumptions + totalIssues + totalDependencies > 0) {
        return (
          <Card className="p-4">
            <div className="flex items-center gap-2 mb-3">
              <AlertTriangle className="w-5 h-5 text-gray-600" />
              <h4 className="font-semibold text-gray-900">إدارة المخاطر (RAID)</h4>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className="font-bold text-red-600">{totalRisks}</div>
                <div className="text-gray-600">مخاطر</div>
              </div>
              <div className="text-center">
                <div className="font-bold text-blue-600">{totalAssumptions}</div>
                <div className="text-gray-600">افتراضات</div>
              </div>
              <div className="text-center">
                <div className="font-bold text-orange-600">{totalIssues}</div>
                <div className="text-gray-600">قضايا</div>
              </div>
              <div className="text-center">
                <div className="font-bold text-purple-600">{totalDependencies}</div>
                <div className="text-gray-600">تبعيات</div>
              </div>
            </div>
          </Card>
        )
      }
    }
    return null
  }

  const renderRecommendations = () => {
    if (formType === 'suggestion') {
      const suggestionData = data as SuggestionData
      return (
        <Card className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <CheckCircle className="w-5 h-5 text-gray-600" />
            <h4 className="font-semibold text-gray-900">التوصيات والخطوات التالية</h4>
          </div>
          <div className="space-y-3">
            {suggestionData.recommendations && (
              <div>
                <span className="font-medium text-gray-700">التوصيات:</span>
                <p className="mt-1 text-gray-600">{suggestionData.recommendations}</p>
              </div>
            )}
            {suggestionData.nextSteps.length > 0 && (
              <div>
                <span className="font-medium text-gray-700">الخطوات التالية:</span>
                <ul className="mt-1 space-y-1">
                  {suggestionData.nextSteps.map((step, index) => (
                    <li key={index} className="text-sm text-gray-600">• {step}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </Card>
      )
    }
    return null
  }

  return (
    <div className="space-y-6">
      <div className={`${colors.bg} border ${colors.border} rounded-lg p-4 mb-6`}>
        <div className="flex items-center gap-2 mb-2">
          <CheckCircle className={`w-5 h-5 ${colors.icon}`} />
          <h3 className={`font-semibold ${colors.text}`}>المراجعة النهائية</h3>
        </div>
        <p className={`${colors.text} text-sm`}>
          راجع جميع المعلومات المدخلة للتأكد من اكتمالها وصحتها قبل الإرسال
        </p>
        <div className="mt-3 flex items-center gap-2">
          <span className={`text-xs px-2 py-1 rounded ${colors.bg} ${colors.border} border ${colors.text}`}>
            {getFormTypeTitle()}
          </span>
        </div>
      </div>

      <div className="space-y-4">
        {renderBasicInfo()}
        {renderProblemInfo()}
        {renderTeamInfo()}
        {renderSolutionInfo()}
        {renderProjectPlanningInfo()}
        {renderRiskInfo()}
        {renderRecommendations()}
      </div>

      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-2">
          <CheckCircle className="w-5 h-5 text-green-600" />
          <h4 className="font-semibold text-green-900">جاهز للإرسال</h4>
        </div>
        <p className="text-green-800 text-sm">
          تم مراجعة جميع المعلومات. يمكنك الآن إرسال النموذج أو حفظه كمسودة للمراجعة لاحقاً.
        </p>
      </div>
    </div>
  )
} 