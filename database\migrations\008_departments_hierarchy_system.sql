-- Migration: إنشاء نظام الأقسام الهرمي والصلاحيات العمودية
-- Date: 2024-01-15
-- Description: إضافة جداول الأقسام مع الهيكل الهرمي وتحديث نظام الصلاحيات

-- إن<PERSON>اء جدول الأقسام
CREATE TABLE IF NOT EXISTS departments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES departments(id) ON DELETE RESTRICT,
    manager_id UUID REFERENCES users(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- إضا<PERSON>ة فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_departments_parent_id ON departments(parent_id);
CREATE INDEX IF NOT EXISTS idx_departments_manager_id ON departments(manager_id);
CREATE INDEX IF NOT EXISTS idx_departments_name ON departments(name);
CREATE INDEX IF NOT EXISTS idx_departments_is_active ON departments(is_active);

-- إضافة قيود فريدة
CREATE UNIQUE INDEX IF NOT EXISTS idx_departments_name_parent_unique 
ON departments(name, COALESCE(parent_id, '00000000-0000-0000-0000-000000000000'));

-- إضافة عمود department_id لجدول المستخدمين إذا لم يكن موجود
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS department_id UUID REFERENCES departments(id) ON DELETE RESTRICT;

-- إضافة فهرس للعمود الجديد
CREATE INDEX IF NOT EXISTS idx_users_department_id ON users(department_id);

-- تحديث جدول الأدوار لإضافة صلاحيات الأقسام
ALTER TABLE roles 
ADD COLUMN IF NOT EXISTS permissions JSONB DEFAULT '{}';

-- إضافة trigger لتحديث updated_at في جدول الأقسام
CREATE OR REPLACE FUNCTION update_departments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_departments_updated_at
    BEFORE UPDATE ON departments
    FOR EACH ROW
    EXECUTE FUNCTION update_departments_updated_at();

-- إضافة trigger للتحقق من المراجع الدائرية في الأقسام
CREATE OR REPLACE FUNCTION check_department_circular_reference()
RETURNS TRIGGER AS $$
DECLARE
    current_parent_id UUID;
    visited_ids UUID[];
BEGIN
    -- إذا لم يكن هناك قسم أب، لا حاجة للتحقق
    IF NEW.parent_id IS NULL THEN
        RETURN NEW;
    END IF;

    -- إذا كان القسم الأب هو نفس القسم
    IF NEW.parent_id = NEW.id THEN
        RAISE EXCEPTION 'لا يمكن جعل القسم أب لنفسه';
    END IF;

    -- التحقق من المراجع الدائرية
    current_parent_id := NEW.parent_id;
    visited_ids := ARRAY[NEW.id];

    WHILE current_parent_id IS NOT NULL LOOP
        -- إذا وجدنا القسم الحالي في المسار، فهناك حلقة
        IF current_parent_id = NEW.id THEN
            RAISE EXCEPTION 'لا يمكن إنشاء حلقة في الهيكل الهرمي للأقسام';
        END IF;

        -- إذا زرنا هذا القسم من قبل، فهناك حلقة
        IF current_parent_id = ANY(visited_ids) THEN
            RAISE EXCEPTION 'تم اكتشاف حلقة في الهيكل الهرمي للأقسام';
        END IF;

        -- إضافة القسم الحالي للمزارة
        visited_ids := visited_ids || current_parent_id;

        -- الانتقال للقسم الأب التالي
        SELECT parent_id INTO current_parent_id 
        FROM departments 
        WHERE id = current_parent_id;
    END LOOP;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_check_department_circular_reference
    BEFORE INSERT OR UPDATE ON departments
    FOR EACH ROW
    EXECUTE FUNCTION check_department_circular_reference();

-- إضافة دالة للحصول على جميع الأقسام الفرعية
CREATE OR REPLACE FUNCTION get_child_departments(department_id UUID)
RETURNS TABLE(id UUID, name VARCHAR, level INTEGER) AS $$
WITH RECURSIVE department_tree AS (
    -- القسم الأساسي
    SELECT d.id, d.name, d.parent_id, 0 as level
    FROM departments d
    WHERE d.id = department_id
    
    UNION ALL
    
    -- الأقسام الفرعية
    SELECT d.id, d.name, d.parent_id, dt.level + 1
    FROM departments d
    INNER JOIN department_tree dt ON d.parent_id = dt.id
)
SELECT dt.id, dt.name, dt.level
FROM department_tree dt
WHERE dt.level > 0
ORDER BY dt.level, dt.name;
$$ LANGUAGE SQL;

-- إضافة دالة للحصول على مسار القسم
CREATE OR REPLACE FUNCTION get_department_path(department_id UUID)
RETURNS TABLE(id UUID, name VARCHAR, level INTEGER) AS $$
WITH RECURSIVE department_path AS (
    -- القسم الحالي
    SELECT d.id, d.name, d.parent_id, 0 as level
    FROM departments d
    WHERE d.id = department_id
    
    UNION ALL
    
    -- الأقسام الأب
    SELECT d.id, d.name, d.parent_id, dp.level + 1
    FROM departments d
    INNER JOIN department_path dp ON d.id = dp.parent_id
)
SELECT dp.id, dp.name, dp.level
FROM department_path dp
ORDER BY dp.level DESC;
$$ LANGUAGE SQL;

-- إضافة دالة للتحقق من الصلاحية على القسم
CREATE OR REPLACE FUNCTION can_access_department(user_id UUID, target_department_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    user_dept_id UUID;
    user_permissions JSONB;
    is_sub_department BOOLEAN := false;
BEGIN
    -- الحصول على بيانات المستخدم
    SELECT u.department_id, r.permissions
    INTO user_dept_id, user_permissions
    FROM users u
    JOIN roles r ON u.role_id = r.id
    WHERE u.id = user_id AND u.is_active = true;

    -- إذا لم يوجد المستخدم
    IF user_dept_id IS NULL THEN
        RETURN false;
    END IF;

    -- مدير النظام له صلاحية على جميع الأقسام
    IF user_permissions->>'all' = 'true' THEN
        RETURN true;
    END IF;

    -- المستخدم له صلاحية على قسمه
    IF user_dept_id = target_department_id THEN
        RETURN true;
    END IF;

    -- التحقق من الصلاحية على الأقسام الفرعية
    IF user_permissions->'departments' ? 'manage_sub_departments' THEN
        SELECT EXISTS(
            SELECT 1 FROM get_child_departments(user_dept_id)
            WHERE id = target_department_id
        ) INTO is_sub_department;
        
        RETURN is_sub_department;
    END IF;

    RETURN false;
END;
$$ LANGUAGE plpgsql;

-- إدراج أقسام تجريبية
INSERT INTO departments (id, name, description, parent_id, manager_id) VALUES
    ('11111111-1111-1111-1111-111111111111', 'الإدارة العامة', 'الإدارة العامة للمؤسسة', NULL, NULL),
    ('22222222-2222-2222-2222-222222222222', 'تقنية المعلومات', 'قسم تقنية المعلومات', '11111111-1111-1111-1111-111111111111', NULL),
    ('33333333-3333-3333-3333-333333333333', 'الموارد البشرية', 'قسم الموارد البشرية', '11111111-1111-1111-1111-111111111111', NULL),
    ('44444444-4444-4444-4444-444444444444', 'المالية', 'قسم المالية والمحاسبة', '11111111-1111-1111-1111-111111111111', NULL),
    ('55555555-5555-5555-5555-555555555555', 'التطوير', 'قسم تطوير البرمجيات', '22222222-2222-2222-2222-222222222222', NULL),
    ('66666666-6666-6666-6666-666666666666', 'الدعم التقني', 'قسم الدعم التقني', '22222222-2222-2222-2222-222222222222', NULL),
    ('77777777-7777-7777-7777-777777777777', 'الأمن السيبراني', 'قسم الأمن السيبراني', '22222222-2222-2222-2222-222222222222', NULL)
ON CONFLICT (id) DO NOTHING;

-- تحديث المستخدمين الموجودين لربطهم بالأقسام
UPDATE users SET department_id = '22222222-2222-2222-2222-222222222222' WHERE email = '<EMAIL>';
UPDATE users SET department_id = '55555555-5555-5555-5555-555555555555' WHERE email = '<EMAIL>';
UPDATE users SET department_id = '66666666-6666-6666-6666-666666666666' WHERE email = '<EMAIL>';

-- تحديث صلاحيات الأدوار
UPDATE roles SET permissions = '{
    "all": true,
    "departments": ["manage", "create", "delete", "manage_sub_departments"],
    "users": ["manage", "create", "delete", "view_all"],
    "projects": ["manage", "approve", "view_all"],
    "reports": ["view_all", "export"]
}' WHERE name = 'admin';

UPDATE roles SET permissions = '{
    "departments": ["view", "manage_sub_departments"],
    "users": ["manage", "create", "view_department"],
    "projects": ["manage", "approve", "view_department"],
    "reports": ["view_department", "export"]
}' WHERE name = 'manager';

UPDATE roles SET permissions = '{
    "departments": ["view"],
    "users": ["view_own"],
    "projects": ["create", "view_own"],
    "reports": ["view_own"]
}' WHERE name = 'user';

-- إضافة view للأقسام مع المعلومات الإضافية
CREATE OR REPLACE VIEW departments_with_stats AS
SELECT 
    d.*,
    parent.name as parent_name,
    manager.name as manager_name,
    manager.email as manager_email,
    (SELECT COUNT(*) FROM users u WHERE u.department_id = d.id AND u.is_active = true) as active_users_count,
    (SELECT COUNT(*) FROM users u WHERE u.department_id = d.id) as total_users_count,
    (SELECT COUNT(*) FROM departments child WHERE child.parent_id = d.id) as child_departments_count
FROM departments d
LEFT JOIN departments parent ON d.parent_id = parent.id
LEFT JOIN users manager ON d.manager_id = manager.id
ORDER BY d.name;

-- إضافة view للمستخدمين مع معلومات الأقسام
CREATE OR REPLACE VIEW users_with_department_info AS
SELECT 
    u.*,
    d.name as department_name,
    parent_dept.name as parent_department_name,
    r.display_name as role_display_name,
    r.permissions as role_permissions
FROM users u
LEFT JOIN departments d ON u.department_id = d.id
LEFT JOIN departments parent_dept ON d.parent_id = parent_dept.id
LEFT JOIN roles r ON u.role_id = r.id
ORDER BY u.name;

-- إضافة RLS policies للأقسام
ALTER TABLE departments ENABLE ROW LEVEL SECURITY;

-- سياسة للقراءة: يمكن للمستخدمين رؤية الأقسام التي لهم صلاحية عليها
CREATE POLICY departments_select_policy ON departments
    FOR SELECT
    USING (
        -- مدير النظام يرى جميع الأقسام
        EXISTS (
            SELECT 1 FROM users u 
            JOIN roles r ON u.role_id = r.id 
            WHERE u.id = auth.uid() 
            AND r.permissions->>'all' = 'true'
        )
        OR
        -- المستخدم يرى قسمه والأقسام الفرعية إذا كان له صلاحية
        can_access_department(auth.uid(), id)
    );

-- سياسة للإدراج: فقط المستخدمين المخولين يمكنهم إنشاء أقسام
CREATE POLICY departments_insert_policy ON departments
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM users u 
            JOIN roles r ON u.role_id = r.id 
            WHERE u.id = auth.uid() 
            AND (
                r.permissions->>'all' = 'true'
                OR r.permissions->'departments' ? 'create'
            )
        )
    );

-- سياسة للتحديث: فقط المستخدمين المخولين يمكنهم تحديث الأقسام
CREATE POLICY departments_update_policy ON departments
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM users u 
            JOIN roles r ON u.role_id = r.id 
            WHERE u.id = auth.uid() 
            AND (
                r.permissions->>'all' = 'true'
                OR (r.permissions->'departments' ? 'manage' AND can_access_department(auth.uid(), id))
            )
        )
    );

-- سياسة للحذف: فقط المستخدمين المخولين يمكنهم حذف الأقسام
CREATE POLICY departments_delete_policy ON departments
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM users u 
            JOIN roles r ON u.role_id = r.id 
            WHERE u.id = auth.uid() 
            AND (
                r.permissions->>'all' = 'true'
                OR (r.permissions->'departments' ? 'delete' AND can_access_department(auth.uid(), id))
            )
        )
    );

-- تحديث سياسات RLS للمستخدمين
DROP POLICY IF EXISTS users_select_policy ON users;
CREATE POLICY users_select_policy ON users
    FOR SELECT
    USING (
        -- مدير النظام يرى جميع المستخدمين
        EXISTS (
            SELECT 1 FROM users u 
            JOIN roles r ON u.role_id = r.id 
            WHERE u.id = auth.uid() 
            AND r.permissions->>'all' = 'true'
        )
        OR
        -- المستخدم يرى نفسه
        id = auth.uid()
        OR
        -- المستخدم يرى مستخدمي الأقسام التي له صلاحية عليها
        (
            EXISTS (
                SELECT 1 FROM users u 
                JOIN roles r ON u.role_id = r.id 
                WHERE u.id = auth.uid() 
                AND r.permissions->'users' ? 'view_department'
            )
            AND can_access_department(auth.uid(), department_id)
        )
    );

-- إضافة تعليقات للتوثيق
COMMENT ON TABLE departments IS 'جدول الأقسام مع الهيكل الهرمي';
COMMENT ON COLUMN departments.parent_id IS 'معرف القسم الأب للهيكل الهرمي';
COMMENT ON COLUMN departments.manager_id IS 'معرف مدير القسم';
COMMENT ON FUNCTION get_child_departments(UUID) IS 'دالة للحصول على جميع الأقسام الفرعية';
COMMENT ON FUNCTION get_department_path(UUID) IS 'دالة للحصول على مسار القسم في الهيكل الهرمي';
COMMENT ON FUNCTION can_access_department(UUID, UUID) IS 'دالة للتحقق من صلاحية الوصول للقسم'; 