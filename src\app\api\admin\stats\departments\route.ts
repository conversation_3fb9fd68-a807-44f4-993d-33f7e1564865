import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'

export async function GET(request: NextRequest) {
  try {
    // جلب إحصائيات الأقسام
    const { data: departments, error: departmentsError } = await supabaseAdmin
      .from('departments')
      .select('id, name, manager_id')

    if (departmentsError) {
      console.error('Error fetching departments:', departmentsError)
      return NextResponse.json(
        { success: false, error: 'فشل في جلب إحصائيات الأقسام' },
        { status: 500 }
      )
    }

    // جلب عدد المستخدمين في كل قسم
    const { data: users, error: usersError } = await supabaseAdmin
      .from('users')
      .select('id, department_id')

    if (usersError) {
      console.error('Error fetching users:', usersError)
      return NextResponse.json(
        { success: false, error: 'فشل في جلب بيانات المستخدمين' },
        { status: 500 }
      )
    }

    // حساب الإحصائيات
    const total = departments?.length || 0
    const withManagers = departments?.filter(dept => dept.manager_id).length || 0
    const withoutManagers = total - withManagers

    // حساب متوسط المستخدمين لكل قسم
    const totalUsers = users?.length || 0
    const avgUsersPerDept = total > 0 ? Math.round((totalUsers / total) * 10) / 10 : 0

    const stats = {
      total,
      with_managers: withManagers,
      without_managers: withoutManagers,
      avg_users_per_dept: avgUsersPerDept
    }

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('Error in departments stats API:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في الخادم' },
      { status: 500 }
    )
  }
} 