'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { SuggestionConversionData } from '@/types/feedback.types';

interface ConversionToProjectProps {
  selectedSolution: {
    id: string;
    title: string;
    description: string;
    expectedBenefits: string[];
    implementationSteps: string[];
  };
  originalSuggestionData: any; // البيانات الأصلية من المقترح
  onSubmit: (conversionData: SuggestionConversionData) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

export default function ConversionToProject({
  selectedSolution,
  originalSuggestionData,
  onSubmit,
  onCancel,
  isSubmitting = false
}: ConversionToProjectProps) {
  const [formData, setFormData] = useState<SuggestionConversionData>({
    // البيانات الأساسية
    projectName: '',
    projectDescription: '',
    startDate: '',
    endDate: '',
    priority: 'medium',
    
    // تفاصيل الحل المختار
    selectedSolution: {
      id: selectedSolution.id,
      title: selectedSolution.title,
      description: selectedSolution.description,
      expectedBenefits: selectedSolution.expectedBenefits,
      implementationSteps: selectedSolution.implementationSteps
    },
    
    // التخطيط
    projectTasks: [],
    requiredResources: [],
    
    // إدارة المخاطر
    risks: []
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [currentStep, setCurrentStep] = useState(1);

  // تهيئة البيانات من المقترح الأصلي
  useEffect(() => {
    if (originalSuggestionData) {
      setFormData(prev => ({
        ...prev,
        projectName: originalSuggestionData.projectName || `مشروع تحسين: ${selectedSolution.title}`,
        projectDescription: originalSuggestionData.projectDescription || selectedSolution.description,
        // تحويل خطوات التنفيذ إلى مهام أولية
        projectTasks: selectedSolution.implementationSteps.map((step, index) => ({
          id: `task_${index + 1}`,
          title: step,
          description: `تنفيذ: ${step}`,
          assignedTo: originalSuggestionData.teamLeader?.name || 'قائد المشروع',
          dueDate: '',
          priority: 'medium'
        }))
      }));
    }
  }, [originalSuggestionData, selectedSolution]);

  // التحقق من صحة البيانات
  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    if (step === 1) {
      // معلومات أساسية
      if (!formData.projectName.trim()) {
        newErrors.projectName = 'اسم المشروع مطلوب';
      }
      if (!formData.projectDescription.trim()) {
        newErrors.projectDescription = 'وصف المشروع مطلوب';
      }
      if (!formData.startDate) {
        newErrors.startDate = 'تاريخ البداية مطلوب';
      }
      if (!formData.endDate) {
        newErrors.endDate = 'تاريخ النهاية مطلوب';
      } else if (formData.startDate && new Date(formData.endDate) <= new Date(formData.startDate)) {
        newErrors.endDate = 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية';
      }
    } else if (step === 2) {
      // المهام
      if (formData.projectTasks.length === 0) {
        newErrors.tasks = 'يجب إضافة مهمة واحدة على الأقل';
      }
      formData.projectTasks.forEach((task, index) => {
        if (!task.title.trim()) {
          newErrors[`task_title_${index}`] = 'عنوان المهمة مطلوب';
        }
        if (!task.assignedTo.trim()) {
          newErrors[`task_assigned_${index}`] = 'المسؤول عن المهمة مطلوب';
        }
      });
    } else if (step === 3) {
      // الموارد
      formData.requiredResources.forEach((resource, index) => {
        if (!resource.description.trim()) {
          newErrors[`resource_desc_${index}`] = 'وصف المورد مطلوب';
        }
        if (resource.cost < 0) {
          newErrors[`resource_cost_${index}`] = 'التكلفة يجب أن تكون رقم موجب';
        }
      });
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // إضافة مهمة جديدة
  const addTask = () => {
    const newTask = {
      id: `task_${formData.projectTasks.length + 1}`,
      title: '',
      description: '',
      assignedTo: originalSuggestionData?.teamLeader?.name || '',
      dueDate: '',
      priority: 'medium' as const
    };
    setFormData(prev => ({
      ...prev,
      projectTasks: [...prev.projectTasks, newTask]
    }));
  };

  // حذف مهمة
  const removeTask = (index: number) => {
    setFormData(prev => ({
      ...prev,
      projectTasks: prev.projectTasks.filter((_, i) => i !== index)
    }));
  };

  // تحديث مهمة
  const updateTask = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      projectTasks: prev.projectTasks.map((task, i) => 
        i === index ? { ...task, [field]: value } : task
      )
    }));
  };

  // إضافة مورد جديد
  const addResource = () => {
    const newResource = {
      id: `resource_${formData.requiredResources.length + 1}`,
      type: 'human',
      description: '',
      quantity: 1,
      cost: 0
    };
    setFormData(prev => ({
      ...prev,
      requiredResources: [...prev.requiredResources, newResource]
    }));
  };

  // حذف مورد
  const removeResource = (index: number) => {
    setFormData(prev => ({
      ...prev,
      requiredResources: prev.requiredResources.filter((_, i) => i !== index)
    }));
  };

  // تحديث مورد
  const updateResource = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      requiredResources: prev.requiredResources.map((resource, i) => 
        i === index ? { ...resource, [field]: value } : resource
      )
    }));
  };

  // إضافة مخاطرة جديدة
  const addRisk = () => {
    const newRisk = {
      id: `risk_${formData.risks.length + 1}`,
      description: '',
      probability: 'medium' as const,
      impact: 'medium' as const,
      mitigation: ''
    };
    setFormData(prev => ({
      ...prev,
      risks: [...prev.risks, newRisk]
    }));
  };

  // حذف مخاطرة
  const removeRisk = (index: number) => {
    setFormData(prev => ({
      ...prev,
      risks: prev.risks.filter((_, i) => i !== index)
    }));
  };

  // تحديث مخاطرة
  const updateRisk = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      risks: prev.risks.map((risk, i) => 
        i === index ? { ...risk, [field]: value } : risk
      )
    }));
  };

  // الانتقال للخطوة التالية
  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 4));
    }
  };

  // الرجوع للخطوة السابقة
  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // إرسال النموذج
  const handleSubmit = async () => {
    if (validateStep(currentStep)) {
      try {
        await onSubmit(formData);
      } catch (error) {
        console.error('خطأ في تحويل المقترح:', error);
        setErrors({ submit: 'حدث خطأ في التحويل. يرجى المحاولة مرة أخرى.' });
      }
    }
  };

  // عناوين الخطوات
  const stepTitles = [
    'المعلومات الأساسية',
    'مهام المشروع',
    'الموارد المطلوبة',
    'إدارة المخاطر'
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      <Card className="p-6">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            تحويل المقترح إلى مشروع
          </h2>
          <p className="text-gray-600 mb-4">
            الحل المختار: <span className="font-medium">{selectedSolution.title}</span>
          </p>
          
          {/* مؤشر التقدم */}
          <div className="flex items-center justify-between mb-6">
            {stepTitles.map((title, index) => (
              <div key={index} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  index + 1 <= currentStep 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {index + 1}
                </div>
                <span className={`ml-2 text-sm ${
                  index + 1 <= currentStep ? 'text-blue-600 font-medium' : 'text-gray-500'
                }`}>
                  {title}
                </span>
                {index < stepTitles.length - 1 && (
                  <div className={`mx-4 h-0.5 w-12 ${
                    index + 1 < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* محتوى الخطوات */}
        <div className="min-h-[400px]">
          {/* الخطوة 1: المعلومات الأساسية */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">المعلومات الأساسية للمشروع</h3>
              
              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    اسم المشروع *
                  </label>
                  <Input
                    value={formData.projectName}
                    onChange={(e) => setFormData(prev => ({ ...prev, projectName: e.target.value }))}
                    placeholder="أدخل اسم المشروع..."
                    error={errors.projectName}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    وصف المشروع *
                  </label>
                  <textarea
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={formData.projectDescription}
                    onChange={(e) => setFormData(prev => ({ ...prev, projectDescription: e.target.value }))}
                    placeholder="وصف شامل للمشروع وأهدافه..."
                  />
                  {errors.projectDescription && (
                    <p className="mt-1 text-sm text-red-600">{errors.projectDescription}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      تاريخ البداية *
                    </label>
                    <Input
                      type="date"
                      value={formData.startDate}
                      onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                      error={errors.startDate}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      تاريخ النهاية *
                    </label>
                    <Input
                      type="date"
                      value={formData.endDate}
                      onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                      error={errors.endDate}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    أولوية المشروع
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={formData.priority}
                    onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as any }))}
                  >
                    <option value="low">منخفضة</option>
                    <option value="medium">متوسطة</option>
                    <option value="high">عالية</option>
                    <option value="critical">حرجة</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {/* الخطوة 2: مهام المشروع */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-900">مهام المشروع</h3>
                <Button variant="secondary" onClick={addTask}>
                  إضافة مهمة
                </Button>
              </div>

              {formData.projectTasks.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  لا توجد مهام. اضغط "إضافة مهمة" لبدء إضافة المهام.
                </div>
              ) : (
                <div className="space-y-4">
                  {formData.projectTasks.map((task, index) => (
                    <Card key={task.id} className="p-4">
                      <div className="flex justify-between items-start mb-4">
                        <h4 className="font-medium text-gray-900">مهمة {index + 1}</h4>
                        <Button
                          variant="danger"
                          size="sm"
                          onClick={() => removeTask(index)}
                        >
                          حذف
                        </Button>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            عنوان المهمة *
                          </label>
                          <Input
                            value={task.title}
                            onChange={(e) => updateTask(index, 'title', e.target.value)}
                            placeholder="عنوان المهمة..."
                            error={errors[`task_title_${index}`]}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            المسؤول *
                          </label>
                          <Input
                            value={task.assignedTo}
                            onChange={(e) => updateTask(index, 'assignedTo', e.target.value)}
                            placeholder="اسم المسؤول..."
                            error={errors[`task_assigned_${index}`]}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            تاريخ الاستحقاق
                          </label>
                          <Input
                            type="date"
                            value={task.dueDate}
                            onChange={(e) => updateTask(index, 'dueDate', e.target.value)}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            الأولوية
                          </label>
                          <select
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            value={task.priority}
                            onChange={(e) => updateTask(index, 'priority', e.target.value)}
                          >
                            <option value="low">منخفضة</option>
                            <option value="medium">متوسطة</option>
                            <option value="high">عالية</option>
                          </select>
                        </div>
                      </div>

                      <div className="mt-4">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          وصف المهمة
                        </label>
                        <textarea
                          rows={2}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          value={task.description}
                          onChange={(e) => updateTask(index, 'description', e.target.value)}
                          placeholder="وصف تفصيلي للمهمة..."
                        />
                      </div>
                    </Card>
                  ))}
                </div>
              )}
              
              {errors.tasks && (
                <p className="text-sm text-red-600">{errors.tasks}</p>
              )}
            </div>
          )}

          {/* الخطوة 3: الموارد المطلوبة */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-900">الموارد المطلوبة</h3>
                <Button variant="secondary" onClick={addResource}>
                  إضافة مورد
                </Button>
              </div>

              {formData.requiredResources.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  لا توجد موارد محددة. اضغط "إضافة مورد" لبدء إضافة الموارد.
                </div>
              ) : (
                <div className="space-y-4">
                  {formData.requiredResources.map((resource, index) => (
                    <Card key={resource.id} className="p-4">
                      <div className="flex justify-between items-start mb-4">
                        <h4 className="font-medium text-gray-900">مورد {index + 1}</h4>
                        <Button
                          variant="danger"
                          size="sm"
                          onClick={() => removeResource(index)}
                        >
                          حذف
                        </Button>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            نوع المورد
                          </label>
                          <select
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            value={resource.type}
                            onChange={(e) => updateResource(index, 'type', e.target.value)}
                          >
                            <option value="human">موارد بشرية</option>
                            <option value="material">مواد</option>
                            <option value="equipment">معدات</option>
                            <option value="software">برمجيات</option>
                            <option value="other">أخرى</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            الكمية
                          </label>
                          <Input
                            type="number"
                            min="1"
                            value={resource.quantity}
                            onChange={(e) => updateResource(index, 'quantity', parseInt(e.target.value) || 1)}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            التكلفة (ريال)
                          </label>
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            value={resource.cost}
                            onChange={(e) => updateResource(index, 'cost', parseFloat(e.target.value) || 0)}
                            error={errors[`resource_cost_${index}`]}
                          />
                        </div>

                        <div className="md:col-span-2 lg:col-span-1">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            وصف المورد *
                          </label>
                          <Input
                            value={resource.description}
                            onChange={(e) => updateResource(index, 'description', e.target.value)}
                            placeholder="وصف المورد..."
                            error={errors[`resource_desc_${index}`]}
                          />
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* الخطوة 4: إدارة المخاطر */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-900">إدارة المخاطر</h3>
                <Button variant="secondary" onClick={addRisk}>
                  إضافة مخاطرة
                </Button>
              </div>

              {formData.risks.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  لا توجد مخاطر محددة. اضغط "إضافة مخاطرة" لبدء إضافة المخاطر.
                </div>
              ) : (
                <div className="space-y-4">
                  {formData.risks.map((risk, index) => (
                    <Card key={risk.id} className="p-4">
                      <div className="flex justify-between items-start mb-4">
                        <h4 className="font-medium text-gray-900">مخاطرة {index + 1}</h4>
                        <Button
                          variant="danger"
                          size="sm"
                          onClick={() => removeRisk(index)}
                        >
                          حذف
                        </Button>
                      </div>
                      
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            وصف المخاطرة *
                          </label>
                          <textarea
                            rows={2}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            value={risk.description}
                            onChange={(e) => updateRisk(index, 'description', e.target.value)}
                            placeholder="وصف المخاطرة المحتملة..."
                          />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              احتمالية الحدوث
                            </label>
                            <select
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              value={risk.probability}
                              onChange={(e) => updateRisk(index, 'probability', e.target.value)}
                            >
                              <option value="low">منخفض</option>
                              <option value="medium">متوسط</option>
                              <option value="high">عالي</option>
                            </select>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              درجة التأثير
                            </label>
                            <select
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              value={risk.impact}
                              onChange={(e) => updateRisk(index, 'impact', e.target.value)}
                            >
                              <option value="low">منخفض</option>
                              <option value="medium">متوسط</option>
                              <option value="high">عالي</option>
                            </select>
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            خطة التخفيف
                          </label>
                          <textarea
                            rows={2}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            value={risk.mitigation}
                            onChange={(e) => updateRisk(index, 'mitigation', e.target.value)}
                            placeholder="الإجراءات المطلوبة لتقليل أو تجنب المخاطرة..."
                          />
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* رسالة خطأ عامة */}
        {errors.submit && (
          <div className="mt-6 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">{errors.submit}</p>
          </div>
        )}

        {/* أزرار التنقل */}
        <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
          <div>
            {currentStep > 1 && (
              <Button variant="ghost" onClick={prevStep}>
                السابق
              </Button>
            )}
          </div>

          <div className="flex space-x-3 space-x-reverse">
            <Button variant="ghost" onClick={onCancel} disabled={isSubmitting}>
              إلغاء
            </Button>
            
            {currentStep < 4 ? (
              <Button variant="primary" onClick={nextStep}>
                التالي
              </Button>
            ) : (
              <Button
                variant="primary"
                onClick={handleSubmit}
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'جاري التحويل...' : 'تحويل إلى مشروع'}
              </Button>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
} 