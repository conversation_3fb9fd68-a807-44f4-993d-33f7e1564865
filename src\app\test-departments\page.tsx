'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

export default function TestDepartmentsPage() {
  const [testResults, setTestResults] = useState<string[]>([])
  const [loading, setLoading] = useState(false)

  const addResult = (message: string, success: boolean = true) => {
    const status = success ? '✅' : '❌'
    setTestResults(prev => [...prev, `${status} ${message}`])
  }

  const clearResults = () => {
    setTestResults([])
  }

  // اختبار API الأقسام
  const testDepartmentsAPI = async () => {
    try {
      addResult('بدء اختبار API الأقسام...')

      // اختبار GET - استرجاع الأقسام
      const getResponse = await fetch('/api/departments')
      if (getResponse.ok) {
        const data = await getResponse.json()
        addResult(`GET /api/departments: نجح - تم العثور على ${data.data?.length || 0} قسم`)
      } else {
        addResult(`GET /api/departments: فشل - ${getResponse.status}`, false)
      }

      // اختبار POST - إنشاء قسم جديد
      const testDepartment = {
        name: `قسم الاختبار ${Date.now()}`,
        description: 'قسم للاختبار'
      }

      const postResponse = await fetch('/api/departments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testDepartment)
      })

      if (postResponse.ok) {
        const data = await postResponse.json()
        addResult(`POST /api/departments: نجح - تم إنشاء قسم بمعرف ${data.data?.id}`)
      } else {
        const error = await postResponse.json()
        addResult(`POST /api/departments: فشل - ${error.error}`, false)
      }

    } catch (error) {
      addResult(`خطأ في اختبار API الأقسام: ${error}`, false)
    }
  }

  // اختبار API المستخدمين
  const testUsersAPI = async () => {
    try {
      addResult('بدء اختبار API المستخدمين...')

      // اختبار GET - استرجاع المستخدمين
      const getResponse = await fetch('/api/users')
      if (getResponse.ok) {
        const data = await getResponse.json()
        addResult(`GET /api/users: نجح - تم العثور على ${data.data?.length || 0} مستخدم`)
      } else {
        addResult(`GET /api/users: فشل - ${getResponse.status}`, false)
      }

      // اختبار POST - إنشاء مستخدم جديد
      const testUser = {
        name: `مستخدم الاختبار ${Date.now()}`,
        email: `test${Date.now()}@example.com`,
        department_id: '68976ccf-9866-4335-a650-8a4556a4ca7f', // تقنية المعلومات
        role_id: 'f4301721-8793-4a9a-be19-51dc3b78eb4b' // موظف
      }

      const postResponse = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testUser)
      })

      if (postResponse.ok) {
        const data = await postResponse.json()
        addResult(`POST /api/users: نجح - تم إنشاء مستخدم بمعرف ${data.data?.id}`)
      } else {
        const error = await postResponse.json()
        addResult(`POST /api/users: فشل - ${error.error}`, false)
      }

    } catch (error) {
      addResult(`خطأ في اختبار API المستخدمين: ${error}`, false)
    }
  }

  // اختبار قاعدة البيانات
  const testDatabase = async () => {
    try {
      addResult('بدء اختبار قاعدة البيانات...')

      // اختبار الجداول
      const tablesResponse = await fetch('/api/database/tables')
      if (tablesResponse.ok) {
        addResult('اتصال قاعدة البيانات: نجح')
      } else {
        addResult('اتصال قاعدة البيانات: فشل', false)
      }

    } catch (error) {
      addResult(`خطأ في اختبار قاعدة البيانات: ${error}`, false)
    }
  }

  // تشغيل جميع الاختبارات
  const runAllTests = async () => {
    setLoading(true)
    clearResults()

    await testDatabase()
    await testDepartmentsAPI()
    await testUsersAPI()

    setLoading(false)
    addResult('انتهت جميع الاختبارات!')
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          اختبار نظام إدارة الأقسام والمستخدمين
        </h1>
        <p className="text-gray-600">
          هذه الصفحة تختبر جميع مكونات النظام للتأكد من عملها بشكل صحيح
        </p>
      </div>

      {/* أزرار الاختبار */}
      <div className="flex gap-2 mb-6">
        <Button
          onClick={runAllTests}
          disabled={loading}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {loading ? '🔄 جاري الاختبار...' : '🚀 تشغيل جميع الاختبارات'}
        </Button>
        <Button
          onClick={testDatabase}
          disabled={loading}
          variant="secondary"
        >
          🗄️ اختبار قاعدة البيانات
        </Button>
        <Button
          onClick={testDepartmentsAPI}
          disabled={loading}
          variant="secondary"
        >
          🏢 اختبار API الأقسام
        </Button>
        <Button
          onClick={testUsersAPI}
          disabled={loading}
          variant="secondary"
        >
          👥 اختبار API المستخدمين
        </Button>
        <Button
          onClick={clearResults}
          variant="secondary"
        >
          🧹 مسح النتائج
        </Button>
      </div>

      {/* نتائج الاختبار */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold mb-4">نتائج الاختبار</h2>
        
        {testResults.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>لم يتم تشغيل أي اختبارات بعد</p>
            <p className="text-sm">انقر على أحد الأزرار أعلاه لبدء الاختبار</p>
          </div>
        ) : (
          <div className="space-y-2">
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg font-mono text-sm ${
                  result.includes('✅') 
                    ? 'bg-green-50 text-green-800' 
                    : result.includes('❌')
                    ? 'bg-red-50 text-red-800'
                    : 'bg-gray-50 text-gray-800'
                }`}
              >
                {result}
              </div>
            ))}
          </div>
        )}
      </Card>

      {/* معلومات إضافية */}
      <Card className="p-6 mt-6">
        <h3 className="text-lg font-semibold mb-3">معلومات النظام</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium text-gray-700">API Endpoints:</h4>
            <ul className="mt-1 space-y-1 text-gray-600">
              <li>• GET/POST/PUT/DELETE /api/departments</li>
              <li>• GET/POST/PUT/DELETE /api/users</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-700">الميزات المختبرة:</h4>
            <ul className="mt-1 space-y-1 text-gray-600">
              <li>• إنشاء وإدارة الأقسام</li>
              <li>• إنشاء وإدارة المستخدمين</li>
              <li>• الصلاحيات العمودية</li>
              <li>• الهيكل الهرمي للأقسام</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* روابط سريعة */}
      <Card className="p-6 mt-6">
        <h3 className="text-lg font-semibold mb-3">روابط سريعة</h3>
        <div className="flex flex-wrap gap-2">
          <a
            href="/departments"
            className="px-3 py-2 bg-blue-100 text-blue-800 rounded-lg hover:bg-blue-200 transition-colors"
          >
            🏢 صفحة إدارة الأقسام
          </a>
          <a
            href="/users"
            className="px-3 py-2 bg-green-100 text-green-800 rounded-lg hover:bg-green-200 transition-colors"
          >
            👥 صفحة إدارة المستخدمين
          </a>
          <a
            href="/dashboard"
            className="px-3 py-2 bg-purple-100 text-purple-800 rounded-lg hover:bg-purple-200 transition-colors"
          >
            📊 لوحة التحكم
          </a>
        </div>
      </Card>
    </div>
  )
} 