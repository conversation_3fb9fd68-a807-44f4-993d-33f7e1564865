-- Migration: Suggestion Feedback System
-- Description: إضافة نظام الملاحظات التفاعلية للمقترحات

-- إضافة حالات جديدة لطلبات المشاريع
ALTER TYPE project_status ADD VALUE IF NOT EXISTS 'under_feedback';
ALTER TYPE project_status ADD VALUE IF NOT EXISTS 'feedback_completed';
ALTER TYPE project_status ADD VALUE IF NOT EXISTS 'converting_to_project';

-- إنشاء جدول الملاحظات والمراجعة التفاعلية
CREATE TABLE IF NOT EXISTS suggestion_feedback (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  request_id UUID REFERENCES project_requests(id) ON DELETE CASCADE NOT NULL,
  reviewer_id TEXT NOT NULL, -- يمكن أن يكون من form_data أو user_id
  reviewer_name TEXT NOT NULL,
  reviewer_type VARCHAR(30) CHECK (reviewer_type IN ('team_leader', 'department_manager', 'team_member', 'pmo_stakeholder')) NOT NULL,
  solution_id TEXT, -- معرف الحل المقترح المراجع
  comment TEXT NOT NULL,
  feedback_type VARCHAR(20) CHECK (feedback_type IN ('suggestion', 'concern', 'improvement', 'approval', 'rejection', 'question')) NOT NULL,
  priority VARCHAR(10) CHECK (priority IN ('low', 'medium', 'high', 'critical')) DEFAULT 'medium',
  status VARCHAR(20) CHECK (status IN ('pending', 'addressed', 'resolved', 'dismissed')) DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES users(id),
  
  -- فهارس للأداء
  INDEX idx_suggestion_feedback_request_id (request_id),
  INDEX idx_suggestion_feedback_reviewer_type (reviewer_type),
  INDEX idx_suggestion_feedback_status (status),
  INDEX idx_suggestion_feedback_created_at (created_at)
);

-- إنشاء جدول تتبع اختيار الحل الأمثل
CREATE TABLE IF NOT EXISTS solution_selection (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  request_id UUID REFERENCES project_requests(id) ON DELETE CASCADE NOT NULL UNIQUE,
  selected_solution_id TEXT NOT NULL,
  selection_rationale TEXT,
  stakeholder_consensus JSONB, -- تسجيل موافقة أصحاب المصلحة
  selected_by UUID REFERENCES users(id),
  selected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  INDEX idx_solution_selection_request_id (request_id)
);

-- إنشاء جدول تتبع عملية التحويل إلى مشروع
CREATE TABLE IF NOT EXISTS suggestion_conversion (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  original_request_id UUID REFERENCES project_requests(id) NOT NULL,
  new_request_id UUID REFERENCES project_requests(id) NOT NULL,
  conversion_data JSONB NOT NULL, -- البيانات المضافة للتحويل
  converted_by UUID REFERENCES users(id),
  converted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  INDEX idx_suggestion_conversion_original (original_request_id),
  INDEX idx_suggestion_conversion_new (new_request_id)
);

-- تحديث جدول project_requests لإضافة حقول المراجعة
ALTER TABLE project_requests 
ADD COLUMN IF NOT EXISTS feedback_summary JSONB,
ADD COLUMN IF NOT EXISTS stakeholder_review_status JSONB,
ADD COLUMN IF NOT EXISTS selected_solution_id TEXT;

-- RLS Policies للأمان
ALTER TABLE suggestion_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE solution_selection ENABLE ROW LEVEL SECURITY;
ALTER TABLE suggestion_conversion ENABLE ROW LEVEL SECURITY;

-- سياسة الوصول للملاحظات
CREATE POLICY suggestion_feedback_access ON suggestion_feedback
  FOR ALL USING (
    -- المراجعون يمكنهم رؤية وإضافة ملاحظاتهم
    reviewer_id = auth.uid()::text OR 
    -- مكتب المشاريع يمكنه رؤية جميع الملاحظات
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'pmo_manager')
    ) OR
    -- صاحب الطلب يمكنه رؤية الملاحظات
    EXISTS (
      SELECT 1 FROM project_requests pr 
      WHERE pr.id = request_id 
      AND pr.created_by = auth.uid()
    )
  );

-- سياسة الوصول لاختيار الحلول
CREATE POLICY solution_selection_access ON solution_selection
  FOR ALL USING (
    -- مكتب المشاريع وقائد الفريق فقط
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND (role = 'admin' OR role = 'pmo_manager' OR role = 'team_leader')
    )
  );

-- سياسة الوصول لتتبع التحويل
CREATE POLICY suggestion_conversion_access ON suggestion_conversion
  FOR SELECT USING (
    -- جميع المستخدمين المخولين يمكنهم رؤية تتبع التحويل
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'pmo_manager', 'team_leader', 'department_manager')
    )
  );

-- إنشاء trigger لتحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_suggestion_feedback_updated_at 
  BEFORE UPDATE ON suggestion_feedback 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments للتوثيق
COMMENT ON TABLE suggestion_feedback IS 'جدول الملاحظات والمراجعة التفاعلية للمقترحات';
COMMENT ON TABLE solution_selection IS 'جدول تتبع اختيار الحل الأمثل من الحلول المقترحة';
COMMENT ON TABLE suggestion_conversion IS 'جدول تتبع عملية تحويل المقترحات إلى طلبات مشاريع'; 