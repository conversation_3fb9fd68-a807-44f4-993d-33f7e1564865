'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/auth'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/Button'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { CheckCircle, XCircle, AlertCircle, Play } from 'lucide-react'
import ProjectRequestTester from '@/components/testing/ProjectRequestTester'
import ApprovalWorkflowTester from '@/components/testing/ApprovalWorkflowTester'
import KanbanTester from '@/components/testing/KanbanTester'

interface TestResult {
  name: string
  status: 'pending' | 'running' | 'passed' | 'failed'
  message: string
  details?: any
}

export default function SystemTestPage() {
  const { user, userRole, userDepartment } = useAuth()
  const [tests, setTests] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const updateTest = (name: string, status: TestResult['status'], message: string, details?: any) => {
    setTests(prev => prev.map(test => 
      test.name === name ? { ...test, status, message, details } : test
    ))
  }

  const runTest = async (testName: string, testFn: () => Promise<void>) => {
    updateTest(testName, 'running', 'جاري التنفيذ...')
    try {
      await testFn()
      updateTest(testName, 'passed', 'نجح الاختبار')
    } catch (error) {
      updateTest(testName, 'failed', `فشل الاختبار: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
    }
  }

  const initializeTests = () => {
    const testList: TestResult[] = [
      { name: 'اختبار المصادقة', status: 'pending', message: 'في انتظار التنفيذ' },
      { name: 'اختبار الصلاحيات', status: 'pending', message: 'في انتظار التنفيذ' },
      { name: 'اختبار قاعدة البيانات', status: 'pending', message: 'في انتظار التنفيذ' },
      { name: 'اختبار الأدوار', status: 'pending', message: 'في انتظار التنفيذ' },
      { name: 'اختبار الأقسام', status: 'pending', message: 'في انتظار التنفيذ' },
      { name: 'اختبار إنشاء طلب', status: 'pending', message: 'في انتظار التنفيذ' },
      { name: 'اختبار RLS', status: 'pending', message: 'في انتظار التنفيذ' }
    ]
    setTests(testList)
  }

  const runAllTests = async () => {
    setIsRunning(true)
    
    // اختبار المصادقة
    await runTest('اختبار المصادقة', async () => {
      if (!user) throw new Error('المستخدم غير مسجل دخول')
      if (!user.email) throw new Error('بريد المستخدم غير موجود')
      updateTest('اختبار المصادقة', 'passed', `مسجل دخول: ${user.email}`, { userId: user.id })
    })

    // اختبار الصلاحيات
    await runTest('اختبار الصلاحيات', async () => {
      if (!userRole) throw new Error('دور المستخدم غير محدد')
      updateTest('اختبار الصلاحيات', 'passed', `الدور: ${userRole.display_name}`, { role: userRole })
    })

    // اختبار قاعدة البيانات
    await runTest('اختبار قاعدة البيانات', async () => {
      const { data, error } = await supabase.from('users').select('id').limit(1)
      if (error) throw new Error(`خطأ في قاعدة البيانات: ${error.message}`)
      updateTest('اختبار قاعدة البيانات', 'passed', 'الاتصال بقاعدة البيانات نجح', { recordCount: data?.length })
    })

    // اختبار الأدوار
    await runTest('اختبار الأدوار', async () => {
      const { data, error } = await supabase.from('roles').select('*')
      if (error) throw new Error(`خطأ في جلب الأدوار: ${error.message}`)
      if (!data || data.length === 0) throw new Error('لا توجد أدوار في النظام')
      updateTest('اختبار الأدوار', 'passed', `تم جلب ${data.length} دور`, { roles: data })
    })

    // اختبار الأقسام
    await runTest('اختبار الأقسام', async () => {
      const { data, error } = await supabase.from('departments').select('*')
      if (error) throw new Error(`خطأ في جلب الأقسام: ${error.message}`)
      if (!data || data.length === 0) throw new Error('لا توجد أقسام في النظام')
      updateTest('اختبار الأقسام', 'passed', `تم جلب ${data.length} قسم`, { departments: data })
    })

    // اختبار إنشاء طلب (محاكاة)
    await runTest('اختبار إنشاء طلب', async () => {
      const testRequest = {
        title: 'طلب تجريبي للاختبار',
        description: 'هذا طلب تجريبي لاختبار النظام',
        main_type: 'improvement_project',
        sub_type: 'quick_win',
        requester_id: user?.id,
        department_id: userDepartment?.id,
        form_data: { test: true }
      }
      
      // محاكاة إنشاء الطلب بدون حفظ فعلي
      if (!testRequest.requester_id) throw new Error('معرف المستخدم مطلوب')
      if (!testRequest.department_id) throw new Error('معرف القسم مطلوب')
      
      updateTest('اختبار إنشاء طلب', 'passed', 'تم التحقق من صحة بيانات الطلب', testRequest)
    })

    // اختبار RLS
    await runTest('اختبار RLS', async () => {
      const { data, error } = await supabase.from('project_requests').select('*').limit(1)
      if (error && error.message.includes('RLS')) {
        updateTest('اختبار RLS', 'passed', 'RLS مفعل ويعمل بشكل صحيح')
      } else {
        updateTest('اختبار RLS', 'passed', 'تم الوصول للبيانات بنجاح', { dataAccess: true })
      }
    })

    setIsRunning(false)
  }

  useEffect(() => {
    initializeTests()
  }, [])

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'passed': return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'failed': return <XCircle className="w-5 h-5 text-red-500" />
      case 'running': return <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
      default: return <AlertCircle className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'passed': return 'bg-green-50 border-green-200'
      case 'failed': return 'bg-red-50 border-red-200'
      case 'running': return 'bg-blue-50 border-blue-200'
      default: return 'bg-gray-50 border-gray-200'
    }
  }

  const passedTests = tests.filter(t => t.status === 'passed').length
  const failedTests = tests.filter(t => t.status === 'failed').length
  const totalTests = tests.length

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">اختبار النظام الشامل</h1>
        <p className="text-gray-600">
          هذه الصفحة تتيح لك اختبار جميع مكونات النظام للتأكد من عملها بشكل صحيح
        </p>
      </div>

      {/* معلومات المستخدم الحالي */}
      <Card className="mb-6">
        <CardHeader>
          <h2 className="text-xl font-semibold">معلومات المستخدم الحالي</h2>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm text-gray-600">البريد الإلكتروني</p>
              <p className="font-medium">{user?.email || 'غير محدد'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">الدور</p>
              <p className="font-medium">{userRole?.display_name || 'غير محدد'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">القسم</p>
              <p className="font-medium">{userDepartment?.name || 'غير محدد'}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* إحصائيات الاختبار */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{totalTests}</div>
            <div className="text-sm text-gray-600">إجمالي الاختبارات</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{passedTests}</div>
            <div className="text-sm text-gray-600">اختبارات ناجحة</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">{failedTests}</div>
            <div className="text-sm text-gray-600">اختبارات فاشلة</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-gray-600">
              {totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0}%
            </div>
            <div className="text-sm text-gray-600">معدل النجاح</div>
          </CardContent>
        </Card>
      </div>

      {/* زر تشغيل الاختبارات */}
      <div className="mb-6">
        <Button 
          onClick={runAllTests} 
          disabled={isRunning}
          className="w-full md:w-auto"
        >
          <Play className="w-4 h-4 mr-2" />
          {isRunning ? 'جاري تشغيل الاختبارات...' : 'تشغيل جميع الاختبارات'}
        </Button>
      </div>

      {/* نتائج الاختبارات */}
      <div className="space-y-4">
        {tests.map((test) => (
          <Card key={test.name} className={`${getStatusColor(test.status)} border-2`}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getStatusIcon(test.status)}
                  <div>
                    <h3 className="font-semibold">{test.name}</h3>
                    <p className="text-sm text-gray-600">{test.message}</p>
                  </div>
                </div>
                <div className="text-sm text-gray-500">
                  {test.status === 'passed' && '✓'}
                  {test.status === 'failed' && '✗'}
                  {test.status === 'running' && '⏳'}
                  {test.status === 'pending' && '⏸️'}
                </div>
              </div>
              
              {test.details && (
                <div className="mt-3 p-3 bg-gray-100 rounded-md">
                  <details>
                    <summary className="cursor-pointer text-sm font-medium">عرض التفاصيل</summary>
                    <pre className="mt-2 text-xs overflow-x-auto">
                      {JSON.stringify(test.details, null, 2)}
                    </pre>
                  </details>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* اختبار طلبات المشاريع */}
      <div className="mt-8">
        <ProjectRequestTester />
      </div>

      {/* اختبار نظام الموافقات */}
      <div className="mt-8">
        <ApprovalWorkflowTester />
      </div>

      {/* اختبار لوحة Kanban */}
      <div className="mt-8">
        <KanbanTester />
      </div>

      {/* تعليمات الاختبار */}
      <Card className="mt-8">
        <CardHeader>
          <h2 className="text-xl font-semibold">تعليمات الاختبار</h2>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• تأكد من تسجيل الدخول قبل تشغيل الاختبارات</p>
            <p>• اختبار المصادقة يتحقق من صحة بيانات المستخدم الحالي</p>
            <p>• اختبار قاعدة البيانات يتحقق من الاتصال بـ Supabase</p>
            <p>• اختبار RLS يتحقق من تفعيل سياسات الأمان</p>
            <p>• اختبار إنشاء الطلب يحاكي عملية إنشاء طلب جديد</p>
            <p>• في حالة فشل أي اختبار، راجع التفاصيل لمعرفة السبب</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 