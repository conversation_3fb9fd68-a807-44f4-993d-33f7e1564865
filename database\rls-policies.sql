-- تفعيل Row Level Security على جميع الجداول
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE approvals ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- سيا<PERSON>ات جدول المستخدمين
-- المستخدمون يمكنهم قراءة بياناتهم الخاصة
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid()::text = id::text);

-- المدراء يمكنهم قراءة بيانات موظفيهم
CREATE POLICY "Managers can view their department users" ON users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM departments d 
      WHERE d.id = users.department_id 
      AND d.manager_id::text = auth.uid()::text
    )
  );

-- المدراء العامون يمكنهم قراءة جميع المستخدمين
CREATE POLICY "Admins can view all users" ON users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN roles r ON u.role_id = r.id 
      WHERE u.id::text = auth.uid()::text 
      AND r.name IN ('admin', 'pmo_manager', 'planning_manager', 'executive_manager')
    )
  );

-- المستخدمون يمكنهم تحديث بياناتهم الشخصية
CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid()::text = id::text)
  WITH CHECK (auth.uid()::text = id::text);

-- سياسات جدول الأدوار
-- جميع المستخدمين يمكنهم قراءة الأدوار
CREATE POLICY "All users can view roles" ON roles
  FOR SELECT USING (true);

-- المدراء فقط يمكنهم إدارة الأدوار
CREATE POLICY "Admins can manage roles" ON roles
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN roles r ON u.role_id = r.id 
      WHERE u.id::text = auth.uid()::text 
      AND r.name = 'admin'
    )
  );

-- سياسات جدول الأقسام
-- جميع المستخدمين يمكنهم قراءة الأقسام
CREATE POLICY "All users can view departments" ON departments
  FOR SELECT USING (true);

-- المدراء يمكنهم إدارة الأقسام
CREATE POLICY "Admins can manage departments" ON departments
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN roles r ON u.role_id = r.id 
      WHERE u.id::text = auth.uid()::text 
      AND r.name IN ('admin', 'pmo_manager')
    )
  );

-- سياسات جدول طلبات المشاريع
-- المستخدمون يمكنهم قراءة طلباتهم
CREATE POLICY "Users can view own requests" ON project_requests
  FOR SELECT USING (requester_id::text = auth.uid()::text);

-- المستخدمون يمكنهم قراءة طلبات قسمهم
CREATE POLICY "Users can view department requests" ON project_requests
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users u 
      WHERE u.id::text = auth.uid()::text 
      AND u.department_id = project_requests.department_id
    )
  );

-- المدراء يمكنهم قراءة جميع الطلبات
CREATE POLICY "Managers can view all requests" ON project_requests
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN roles r ON u.role_id = r.id 
      WHERE u.id::text = auth.uid()::text 
      AND r.name IN ('admin', 'pmo_manager', 'planning_manager', 'executive_manager')
    )
  );

-- المستخدمون يمكنهم إنشاء طلبات جديدة
CREATE POLICY "Users can create requests" ON project_requests
  FOR INSERT WITH CHECK (requester_id::text = auth.uid()::text);

-- المستخدمون يمكنهم تحديث طلباتهم في حالة المسودة
CREATE POLICY "Users can update own draft requests" ON project_requests
  FOR UPDATE USING (
    requester_id::text = auth.uid()::text 
    AND status = 'draft'
  )
  WITH CHECK (
    requester_id::text = auth.uid()::text 
    AND status = 'draft'
  );

-- المدراء يمكنهم تحديث حالة الطلبات
CREATE POLICY "Managers can update request status" ON project_requests
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN roles r ON u.role_id = r.id 
      WHERE u.id::text = auth.uid()::text 
      AND r.name IN ('admin', 'pmo_manager', 'planning_manager', 'executive_manager')
    )
  );

-- سياسات جدول الموافقات
-- المعتمدون يمكنهم قراءة طلبات الموافقة المخصصة لهم
CREATE POLICY "Approvers can view their approvals" ON approvals
  FOR SELECT USING (approver_id::text = auth.uid()::text);

-- المدراء يمكنهم قراءة جميع الموافقات
CREATE POLICY "Managers can view all approvals" ON approvals
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN roles r ON u.role_id = r.id 
      WHERE u.id::text = auth.uid()::text 
      AND r.name IN ('admin', 'pmo_manager')
    )
  );

-- المعتمدون يمكنهم تحديث موافقاتهم
CREATE POLICY "Approvers can update their approvals" ON approvals
  FOR UPDATE USING (approver_id::text = auth.uid()::text)
  WITH CHECK (approver_id::text = auth.uid()::text);

-- النظام يمكنه إنشاء موافقات جديدة
CREATE POLICY "System can create approvals" ON approvals
  FOR INSERT WITH CHECK (true);

-- سياسات جدول المشاريع
-- مديرو المشاريع يمكنهم قراءة مشاريعهم
CREATE POLICY "Project managers can view their projects" ON projects
  FOR SELECT USING (project_manager_id::text = auth.uid()::text);

-- أعضاء الفريق يمكنهم قراءة المشاريع المخصصة لهم
CREATE POLICY "Team members can view assigned projects" ON projects
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM tasks t 
      WHERE t.project_id = projects.id 
      AND t.assignee_id::text = auth.uid()::text
    )
  );

-- المدراء يمكنهم قراءة جميع المشاريع
CREATE POLICY "Managers can view all projects" ON projects
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN roles r ON u.role_id = r.id 
      WHERE u.id::text = auth.uid()::text 
      AND r.name IN ('admin', 'pmo_manager', 'planning_manager', 'executive_manager')
    )
  );

-- مديرو المشاريع يمكنهم تحديث مشاريعهم
CREATE POLICY "Project managers can update their projects" ON projects
  FOR UPDATE USING (project_manager_id::text = auth.uid()::text)
  WITH CHECK (project_manager_id::text = auth.uid()::text);

-- المدراء يمكنهم إنشاء مشاريع جديدة
CREATE POLICY "Managers can create projects" ON projects
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN roles r ON u.role_id = r.id 
      WHERE u.id::text = auth.uid()::text 
      AND r.name IN ('admin', 'pmo_manager')
    )
  );

-- سياسات جدول المهام
-- المكلفون يمكنهم قراءة مهامهم
CREATE POLICY "Assignees can view their tasks" ON tasks
  FOR SELECT USING (assignee_id::text = auth.uid()::text);

-- مديرو المشاريع يمكنهم قراءة جميع مهام مشاريعهم
CREATE POLICY "Project managers can view project tasks" ON tasks
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM projects p 
      WHERE p.id = tasks.project_id 
      AND p.project_manager_id::text = auth.uid()::text
    )
  );

-- المدراء يمكنهم قراءة جميع المهام
CREATE POLICY "Managers can view all tasks" ON tasks
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users u 
      JOIN roles r ON u.role_id = r.id 
      WHERE u.id::text = auth.uid()::text 
      AND r.name IN ('admin', 'pmo_manager')
    )
  );

-- مديرو المشاريع يمكنهم إدارة مهام مشاريعهم
CREATE POLICY "Project managers can manage project tasks" ON tasks
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM projects p 
      WHERE p.id = tasks.project_id 
      AND p.project_manager_id::text = auth.uid()::text
    )
  );

-- المكلفون يمكنهم تحديث حالة مهامهم
CREATE POLICY "Assignees can update task status" ON tasks
  FOR UPDATE USING (assignee_id::text = auth.uid()::text)
  WITH CHECK (assignee_id::text = auth.uid()::text);

-- سياسات جدول الإشعارات
-- المستخدمون يمكنهم قراءة إشعاراتهم فقط
CREATE POLICY "Users can view own notifications" ON notifications
  FOR SELECT USING (user_id::text = auth.uid()::text);

-- المستخدمون يمكنهم تحديث حالة قراءة إشعاراتهم
CREATE POLICY "Users can update own notifications" ON notifications
  FOR UPDATE USING (user_id::text = auth.uid()::text)
  WITH CHECK (user_id::text = auth.uid()::text);

-- النظام يمكنه إنشاء إشعارات جديدة
CREATE POLICY "System can create notifications" ON notifications
  FOR INSERT WITH CHECK (true); 