'use client'

import React, { useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  ChevronRight, 
  ChevronLeft, 
  CheckCircle, 
  Circle,
  PlayCircle,
  BookOpen,
  Clock,
  Users,
  Target,
  AlertCircle
} from 'lucide-react'

interface StepByStepGuideProps {
  currentStep: number
  onClose: () => void
}

interface GuideStep {
  id: number
  title: string
  description: string
  estimatedTime: string
  prerequisites: string[]
  tasks: string[]
  tips: string[]
  commonMistakes: string[]
  successCriteria: string[]
}

const guideSteps: GuideStep[] = [
  {
    id: 1,
    title: 'تحديد معلومات المشروع الأساسية',
    description: 'في هذه الخطوة ستقوم بتحديد الهوية الأساسية للمشروع والإطار الزمني للتنفيذ',
    estimatedTime: '10-15 دقيقة',
    prerequisites: [
      'فهم واضح لهدف المشروع',
      'معرفة الموارد المتاحة',
      'تحديد الإطار الزمني المناسب'
    ],
    tasks: [
      'اختر اسماً واضحاً ومحدداً للمشروع (أقل من 60 حرف)',
      'اكتب وصفاً شاملاً يوضح الهدف والفائدة المتوقعة',
      'حدد تاريخ بداية المشروع (يفضل خلال أسبوعين)',
      'حدد تاريخ نهاية المشروع (واقعي وقابل للتحقيق)',
      'اختر مستوى الأولوية بناءً على تأثير المشكلة'
    ],
    tips: [
      'استخدم أفعال إيجابية في اسم المشروع مثل "تحسين" و"تطوير"',
      'اربط الوصف بأهداف المؤسسة الاستراتيجية',
      'راجع المشاريع المشابهة السابقة لتقدير المدة المناسبة',
      'تأكد من توفر الموارد اللازمة خلال فترة المشروع'
    ],
    commonMistakes: [
      'اختيار أسماء عامة مثل "مشروع التحسين"',
      'وضع تواريخ غير واقعية أو ضيقة جداً',
      'كتابة وصف مبسط لا يوضح الفائدة الحقيقية',
      'عدم مراعاة الإجازات والمناسبات في التخطيط'
    ],
    successCriteria: [
      'اسم المشروع واضح ومفهوم لجميع المعنيين',
      'الوصف يجيب على أسئلة: ماذا؟ لماذا؟ ما الفائدة؟',
      'التواريخ واقعية ومتفق عليها مع الفريق',
      'الأولوية تعكس الأهمية الحقيقية للمشروع'
    ]
  },
  {
    id: 2,
    title: 'تحديد وقياس المشكلة',
    description: 'الخطوة الأهم - تحديد المشكلة بدقة وقياسها كمياً لضمان إمكانية حلها',
    estimatedTime: '20-30 دقيقة',
    prerequisites: [
      'بيانات دقيقة عن المشكلة الحالية',
      'فهم واضح لتأثير المشكلة',
      'تحديد مصادر البيانات الموثوقة'
    ],
    tasks: [
      'اكتب وصفاً دقيقاً للمشكلة مع ذكر الأرقام والإحصائيات',
      'حدد اسم المؤشر الذي ستستخدمه لقياس التحسن',
      'أدخل القيمة الحالية للمؤشر مع مصدر البيانات',
      'حدد القيمة المستهدفة (واقعية وقابلة للتحقيق)',
      'اختر اتجاه التحسن (زيادة أم تقليل الرقم)',
      'حدد وحدة القياس ومصدر البيانات وطريقة القياس'
    ],
    tips: [
      'استخدم نموذج "ماذا، متى، أين، كيف، لماذا" لوصف المشكلة',
      'اجمع البيانات من مصادر متعددة للتأكد من الدقة',
      'اختر مؤشراً واحداً واضحاً بدلاً من مؤشرات متعددة',
      'تأكد من أن المؤشر يعكس تجربة المستفيد النهائي'
    ],
    commonMistakes: [
      'الخلط بين المشكلة والحل أو الأعراض',
      'استخدام وصف عام مثل "الخدمة بطيئة"',
      'اختيار مؤشرات غير قابلة للقياس أو الحصول على بياناتها',
      'وضع أهداف غير واقعية أو مبالغ فيها'
    ],
    successCriteria: [
      'المشكلة محددة بدقة مع أرقام وإحصائيات',
      'المؤشر قابل للقياس ومرتبط مباشرة بالمشكلة',
      'القيم الحالية والمستهدفة واقعية ومدعومة بالبيانات',
      'اتجاه التحسن صحيح ومنطقي للمؤشر المختار'
    ]
  },
  {
    id: 3,
    title: 'تنظيم الفريق والموارد',
    description: 'تشكيل فريق متوازن وتحديد الموارد والمهام اللازمة لنجاح المشروع',
    estimatedTime: '15-25 دقيقة',
    prerequisites: [
      'تحديد المهارات المطلوبة للمشروع',
      'معرفة الأشخاص المتاحين والمؤهلين',
      'تقدير الموارد والميزانية المطلوبة'
    ],
    tasks: [
      'حدد القسم المسؤول عن المشروع',
      'اختر قائد الفريق المناسب (الاسم، الجوال، الإيميل)',
      'أضف أعضاء الفريق (3-7 أعضاء كحد أقصى)',
      'حدد الأقسام المشاركة في المشروع',
      'أضف المهام الرئيسية مع المسؤوليات والمواعيد',
      'حدد الموارد المطلوبة (مالية، تقنية، بشرية)'
    ],
    tips: [
      'اختر فريقاً متنوعاً من التخصصات المختلفة',
      'تأكد من توفر الوقت الكافي لدى أعضاء الفريق',
      'حدد الأدوار والمسؤوليات بوضوح منذ البداية',
      'اشرك ممثلين عن المستخدمين النهائيين'
    ],
    commonMistakes: [
      'تشكيل فريق كبير جداً أو صغير جداً',
      'اختيار أعضاء غير متفرغين أو غير مهتمين',
      'عدم وضوح الأدوار والمسؤوليات',
      'تجاهل أهمية تمثيل جميع الأطراف المعنية'
    ],
    successCriteria: [
      'قائد الفريق لديه الخبرة والصلاحيات المناسبة',
      'الفريق متوازن ويمثل جميع التخصصات المطلوبة',
      'المهام محددة بوضوح مع مواعيد نهائية واقعية',
      'الموارد المطلوبة محددة ومتاحة'
    ]
  }
]

export function StepByStepGuide({ currentStep, onClose }: StepByStepGuideProps) {
  const [activeGuideStep, setActiveGuideStep] = useState(currentStep)
  const currentGuide = guideSteps.find(step => step.id === activeGuideStep)

  if (!currentGuide) return null

  const progress = (activeGuideStep / guideSteps.length) * 100

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* رأس النافذة */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200 p-6">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-xl font-bold text-blue-900 mb-2">
                دليل إكمال المشروع خطوة بخطوة
              </h2>
              <div className="flex items-center gap-4 text-sm text-blue-700">
                <div className="flex items-center gap-1">
                  <BookOpen className="w-4 h-4" />
                  الخطوة {activeGuideStep} من {guideSteps.length}
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  {currentGuide.estimatedTime}
                </div>
              </div>
            </div>
            <Button onClick={onClose} variant="outline" size="sm">
              إغلاق
            </Button>
          </div>
          
          {/* شريط التقدم */}
          <div className="mt-4">
            <div className="bg-blue-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        </div>

        {/* محتوى الدليل */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          <div className="space-y-6">
            {/* عنوان الخطوة */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {currentGuide.title}
              </h3>
              <p className="text-gray-700">
                {currentGuide.description}
              </p>
            </div>

            {/* المتطلبات المسبقة */}
            {currentGuide.prerequisites.length > 0 && (
              <Card className="p-4">
                <h4 className="font-medium text-amber-800 mb-3 flex items-center gap-2">
                  <AlertCircle className="w-5 h-5" />
                  تأكد من توفر هذه المتطلبات قبل البدء
                </h4>
                <ul className="space-y-2">
                  {currentGuide.prerequisites.map((req, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm text-amber-700">
                      <Circle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      {req}
                    </li>
                  ))}
                </ul>
              </Card>
            )}

            {/* المهام المطلوبة */}
            <Card className="p-4">
              <h4 className="font-medium text-blue-800 mb-3 flex items-center gap-2">
                <Target className="w-5 h-5" />
                المهام المطلوبة في هذه الخطوة
              </h4>
              <ul className="space-y-3">
                {currentGuide.tasks.map((task, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5">
                      {index + 1}
                    </div>
                    <span className="text-sm text-gray-700">{task}</span>
                  </li>
                ))}
              </ul>
            </Card>

            {/* النصائح */}
            {currentGuide.tips.length > 0 && (
              <Card className="p-4">
                <h4 className="font-medium text-green-800 mb-3 flex items-center gap-2">
                  <PlayCircle className="w-5 h-5" />
                  نصائح للنجاح
                </h4>
                <ul className="space-y-2">
                  {currentGuide.tips.map((tip, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm text-green-700">
                      <CheckCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      {tip}
                    </li>
                  ))}
                </ul>
              </Card>
            )}

            {/* الأخطاء الشائعة */}
            {currentGuide.commonMistakes.length > 0 && (
              <Card className="p-4">
                <h4 className="font-medium text-red-800 mb-3 flex items-center gap-2">
                  <AlertCircle className="w-5 h-5" />
                  تجنب هذه الأخطاء الشائعة
                </h4>
                <ul className="space-y-2">
                  {currentGuide.commonMistakes.map((mistake, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm text-red-700">
                      <div className="w-4 h-4 bg-red-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0">
                        <span className="text-red-600 text-xs">✕</span>
                      </div>
                      {mistake}
                    </li>
                  ))}
                </ul>
              </Card>
            )}

            {/* معايير النجاح */}
            {currentGuide.successCriteria.length > 0 && (
              <Card className="p-4">
                <h4 className="font-medium text-purple-800 mb-3 flex items-center gap-2">
                  <CheckCircle className="w-5 h-5" />
                  معايير إتمام الخطوة بنجاح
                </h4>
                <ul className="space-y-2">
                  {currentGuide.successCriteria.map((criteria, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm text-purple-700">
                      <CheckCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      {criteria}
                    </li>
                  ))}
                </ul>
              </Card>
            )}
          </div>
        </div>

        {/* تذييل النافذة */}
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          <div className="flex justify-between items-center">
            <Button
              variant="outline"
              onClick={() => setActiveGuideStep(Math.max(1, activeGuideStep - 1))}
              disabled={activeGuideStep === 1}
            >
              <ChevronLeft className="w-4 h-4 mr-2" />
              الخطوة السابقة
            </Button>
            
            <div className="text-sm text-gray-600">
              خطوة {activeGuideStep} من {guideSteps.length}
            </div>
            
            <Button
              onClick={() => setActiveGuideStep(Math.min(guideSteps.length, activeGuideStep + 1))}
              disabled={activeGuideStep === guideSteps.length}
            >
              الخطوة التالية
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
} 