'use client'

import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Save, X, Users, Shield } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'

interface Role {
  id: string
  name: string
  display_name: string
  description: string | null
  permissions: Record<string, any>
  created_at: string
  users_count?: number
}

interface RoleFormData {
  name: string
  display_name: string
  description: string
}

export default function RolesManagement() {
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingRole, setEditingRole] = useState<Role | null>(null)
  const [formData, setFormData] = useState<RoleFormData>({
    name: '',
    display_name: '',
    description: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  // بيانات تجريبية للأدوار
  const mockRoles: Role[] = [
    {
      id: '1',
      name: 'admin',
      display_name: 'مدير النظام',
      description: 'صلاحيات كاملة لإدارة النظام',
      permissions: { all: true },
      created_at: '2024-01-01',
      users_count: 2
    },
    {
      id: '2',
      name: 'pmo_manager',
      display_name: 'مدير مكتب المشاريع',
      description: 'إدارة المشاريع والطلبات',
      permissions: { 
        projects: ['read', 'write', 'approve'], 
        requests: ['read', 'write', 'approve'] 
      },
      created_at: '2024-01-01',
      users_count: 3
    },
    {
      id: '3',
      name: 'planning_manager',
      display_name: 'مدير إدارة التخطيط',
      description: 'اعتماد المشاريع الكبيرة',
      permissions: { 
        projects: ['read', 'approve'], 
        requests: ['read', 'approve'] 
      },
      created_at: '2024-01-01',
      users_count: 1
    },
    {
      id: '4',
      name: 'project_manager',
      display_name: 'مدير مشروع',
      description: 'إدارة المشاريع المعينة',
      permissions: { 
        projects: ['read', 'write'], 
        tasks: ['read', 'write'] 
      },
      created_at: '2024-01-01',
      users_count: 5
    },
    {
      id: '5',
      name: 'employee',
      display_name: 'موظف',
      description: 'تقديم طلبات المشاريع',
      permissions: { 
        requests: ['read', 'write'], 
        projects: ['read'] 
      },
      created_at: '2024-01-01',
      users_count: 25
    }
  ]

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setRoles(mockRoles)
      setLoading(false)
    }, 1000)
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setErrors({})

    // التحقق من البيانات
    const newErrors: Record<string, string> = {}
    
    if (!formData.name.trim()) {
      newErrors.name = 'اسم الدور مطلوب'
    } else if (!/^[a-z_]+$/.test(formData.name)) {
      newErrors.name = 'اسم الدور يجب أن يحتوي على أحرف إنجليزية صغيرة وشرطة سفلية فقط'
    }
    
    if (!formData.display_name.trim()) {
      newErrors.display_name = 'الاسم المعروض مطلوب'
    }

    // التحقق من عدم تكرار اسم الدور
    const existingRole = roles.find(r => 
      r.name === formData.name && r.id !== editingRole?.id
    )
    if (existingRole) {
      newErrors.name = 'اسم الدور موجود بالفعل'
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }

    try {
      if (editingRole) {
        // تحديث دور موجود
        const updatedRoles = roles.map(role =>
          role.id === editingRole.id
            ? { ...role, ...formData }
            : role
        )
        setRoles(updatedRoles)
      } else {
        // إضافة دور جديد
        const newRole: Role = {
          id: Date.now().toString(),
          ...formData,
          permissions: {},
          created_at: new Date().toISOString(),
          users_count: 0
        }
        setRoles([...roles, newRole])
      }

      // إعادة تعيين النموذج
      setFormData({ name: '', display_name: '', description: '' })
      setShowForm(false)
      setEditingRole(null)
    } catch (error) {
      console.error('Error saving role:', error)
    }
  }

  const handleEdit = (role: Role) => {
    setEditingRole(role)
    setFormData({
      name: role.name,
      display_name: role.display_name,
      description: role.description || ''
    })
    setShowForm(true)
  }

  const handleDelete = async (roleId: string) => {
    const role = roles.find(r => r.id === roleId)
    if (role && role.users_count && role.users_count > 0) {
      alert('لا يمكن حذف دور مرتبط بمستخدمين')
      return
    }

    if (confirm('هل أنت متأكد من حذف هذا الدور؟')) {
      setRoles(roles.filter(r => r.id !== roleId))
    }
  }

  const handleCancel = () => {
    setFormData({ name: '', display_name: '', description: '' })
    setShowForm(false)
    setEditingRole(null)
    setErrors({})
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">إدارة الأدوار</h2>
          <p className="text-gray-600 mt-1">
            إضافة وتعديل أدوار المستخدمين في النظام
          </p>
        </div>
        <Button
          onClick={() => setShowForm(true)}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          إضافة دور جديد
        </Button>
      </div>

      {/* Form */}
      {showForm && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">
            {editingRole ? 'تعديل الدور' : 'إضافة دور جديد'}
          </h3>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اسم الدور (بالإنجليزية) *
                </label>
                <Input
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="مثال: project_manager"
                  error={errors.name}
                  disabled={!!editingRole} // منع تعديل اسم الدور
                />
                <p className="text-xs text-gray-500 mt-1">
                  أحرف إنجليزية صغيرة وشرطة سفلية فقط
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الاسم المعروض *
                </label>
                <Input
                  value={formData.display_name}
                  onChange={(e) => setFormData({ ...formData, display_name: e.target.value })}
                  placeholder="مثال: مدير المشروع"
                  error={errors.display_name}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الوصف
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="وصف مختصر لمسؤوليات هذا الدور..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="flex gap-3">
              <Button type="submit" className="flex items-center gap-2">
                <Save className="w-4 h-4" />
                {editingRole ? 'تحديث' : 'إضافة'}
              </Button>
              <Button 
                type="button" 
                variant="secondary" 
                onClick={handleCancel}
                className="flex items-center gap-2"
              >
                <X className="w-4 h-4" />
                إلغاء
              </Button>
            </div>
          </form>
        </Card>
      )}

      {/* Roles List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {roles.map((role) => (
          <Card key={role.id} className="p-6 hover:shadow-lg transition-shadow">
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Shield className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">
                    {role.display_name}
                  </h3>
                  <p className="text-sm text-gray-500">
                    {role.name}
                  </p>
                </div>
              </div>
              
              <div className="flex gap-2">
                <button
                  onClick={() => handleEdit(role)}
                  className="p-1 text-gray-500 hover:text-blue-600 transition-colors"
                >
                  <Edit className="w-4 h-4" />
                </button>
                {role.name !== 'admin' && (
                  <button
                    onClick={() => handleDelete(role.id)}
                    className="p-1 text-gray-500 hover:text-red-600 transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>

            <p className="text-gray-600 text-sm mb-4">
              {role.description || 'لا يوجد وصف'}
            </p>

            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2 text-gray-500">
                <Users className="w-4 h-4" />
                <span>{role.users_count || 0} مستخدم</span>
              </div>
              
              <div className="text-xs text-gray-400">
                {new Date(role.created_at).toLocaleDateString('ar-SA')}
              </div>
            </div>
          </Card>
        ))}
      </div>

      {roles.length === 0 && (
        <div className="text-center py-12">
          <Shield className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            لا توجد أدوار
          </h3>
          <p className="text-gray-600 mb-4">
            ابدأ بإضافة أول دور في النظام
          </p>
          <Button onClick={() => setShowForm(true)}>
            إضافة دور جديد
          </Button>
        </div>
      )}
    </div>
  )
} 