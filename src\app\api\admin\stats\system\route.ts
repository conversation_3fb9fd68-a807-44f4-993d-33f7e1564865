import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // إحصائيات النظام (بيانات تجريبية)
    // في التطبيق الحقيقي، يمكن جلب هذه البيانات من جداول الجلسات وسجلات الدخول
    const stats = {
      total_sessions: 156,
      active_sessions: 23,
      login_attempts: 89,
      failed_logins: 4,
      success_rate: 95.5
    }

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('Error in system stats API:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في الخادم' },
      { status: 500 }
    )
  }
} 