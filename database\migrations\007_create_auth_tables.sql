-- إضافة عمود password_hash لجدول المستخدمين
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255);

-- إن<PERSON>اء جدول الجلسات
CREATE TABLE IF NOT EXISTS user_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  session_token VARCHAR(255) UNIQUE NOT NULL,
  expires_at TIMESTAMPTZ NOT NULL,
  user_agent TEXT,
  ip_address INET,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- إنشاء جدول سجلات المصادقة
CREATE TABLE IF NOT EXISTS auth_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  action VARCHAR(50) NOT NULL,
  status VARCHAR(20) CHECK (status IN ('success', 'failed')) NOT NULL,
  ip_address INET,
  user_agent TEXT,
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  details JSONB DEFAULT '{}'
);

-- إنشاء فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_auth_logs_user_id ON auth_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_logs_timestamp ON auth_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_auth_logs_action ON auth_logs(action);

-- إنشاء trigger للتحديث التلقائي
CREATE TRIGGER update_user_sessions_updated_at 
  BEFORE UPDATE ON user_sessions
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- إنشاء دالة لتنظيف الجلسات المنتهية الصلاحية
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM user_sessions 
  WHERE expires_at < NOW();
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- إنشاء مستخدم admin افتراضي (كلمة المرور: admin123)
INSERT INTO users (
  email, 
  name, 
  role_id, 
  department_id, 
  password_hash,
  is_active
) 
SELECT 
  '<EMAIL>',
  'مدير النظام',
  r.id,
  d.id,
  'ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f', -- SHA256 hash for 'admin123'
  true
FROM roles r, departments d
WHERE r.name = 'admin' 
  AND d.name = 'مكتب إدارة المشاريع'
  AND NOT EXISTS (
    SELECT 1 FROM users WHERE email = '<EMAIL>'
  );

-- إنشاء مستخدمين تجريبيين
INSERT INTO users (
  email, 
  name, 
  role_id, 
  department_id, 
  password_hash,
  is_active
) 
SELECT 
  '<EMAIL>',
  'مدير مكتب المشاريع',
  r.id,
  d.id,
  'ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f', -- SHA256 hash for 'admin123'
  true
FROM roles r, departments d
WHERE r.name = 'pmo_manager' 
  AND d.name = 'مكتب إدارة المشاريع'
  AND NOT EXISTS (
    SELECT 1 FROM users WHERE email = '<EMAIL>'
  );

INSERT INTO users (
  email, 
  name, 
  role_id, 
  department_id, 
  password_hash,
  is_active
) 
SELECT 
  '<EMAIL>',
  'مدير إدارة التخطيط',
  r.id,
  d.id,
  'ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f', -- SHA256 hash for 'admin123'
  true
FROM roles r, departments d
WHERE r.name = 'planning_manager' 
  AND d.name = 'إدارة التخطيط'
  AND NOT EXISTS (
    SELECT 1 FROM users WHERE email = '<EMAIL>'
  );

INSERT INTO users (
  email, 
  name, 
  role_id, 
  department_id, 
  password_hash,
  is_active
) 
SELECT 
  '<EMAIL>',
  'المدير التنفيذي',
  r.id,
  d.id,
  'ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f', -- SHA256 hash for 'admin123'
  true
FROM roles r, departments d
WHERE r.name = 'executive_manager' 
  AND d.name = 'الإدارة التنفيذية'
  AND NOT EXISTS (
    SELECT 1 FROM users WHERE email = '<EMAIL>'
  );

INSERT INTO users (
  email, 
  name, 
  role_id, 
  department_id, 
  password_hash,
  is_active
) 
SELECT 
  '<EMAIL>',
  'موظف تجريبي',
  r.id,
  d.id,
  'ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f', -- SHA256 hash for 'admin123'
  true
FROM roles r, departments d
WHERE r.name = 'employee' 
  AND d.name = 'تقنية المعلومات'
  AND NOT EXISTS (
    SELECT 1 FROM users WHERE email = '<EMAIL>'
  ); 