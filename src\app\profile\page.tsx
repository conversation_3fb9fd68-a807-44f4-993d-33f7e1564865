'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/core/auth'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { 
  User, 
  Mail, 
  Phone, 
  Building, 
  Calendar, 
  Edit, 
  Save, 
  X,
  Camera,
  Shield,
  Key,
  Bell,
  Settings,
  Activity,
  FileText,
  BarChart3
} from 'lucide-react'

export default function ProfilePage() {
  const { user, userRole } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [profileData, setProfileData] = useState({
    name: '',
    email: '',
    phone: '',
    department: '',
    position: '',
    bio: ''
  })

  useEffect(() => {
    if (user) {
      setProfileData({
        name: user.user_metadata?.name || '',
        email: user.email || '',
        phone: user.user_metadata?.phone || '',
        department: user.user_metadata?.department || '',
        position: '',
        bio: ''
      })
    }
  }, [user])

  const handleSave = async () => {
    setIsLoading(true)
    try {
      // هنا يمكن إضافة API لحفظ البيانات
      await new Promise(resolve => setTimeout(resolve, 1000)) // محاكاة API
      setIsEditing(false)
      alert('تم حفظ البيانات بنجاح!')
    } catch (error) {
      console.error('Error saving profile:', error)
      alert('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    if (user) {
      setProfileData({
        name: user.user_metadata?.name || '',
        email: user.email || '',
        phone: user.user_metadata?.phone || '',
        department: user.user_metadata?.department || '',
        position: '',
        bio: ''
      })
    }
    setIsEditing(false)
  }

  const stats = [
    { label: 'الطلبات المقدمة', value: '12', icon: FileText, color: 'blue' },
    { label: 'المشاريع النشطة', value: '3', icon: Activity, color: 'green' },
    { label: 'الطلبات المعتمدة', value: '8', icon: BarChart3, color: 'purple' },
    { label: 'معدل النجاح', value: '85%', icon: Shield, color: 'orange' }
  ]

  if (!user) {
    return (
      <ProtectedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل الملف الشخصي...</p>
          </div>
        </div>
      </ProtectedLayout>
    )
  }

  return (
    <ProtectedLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">الملف الشخصي</h1>
            <p className="text-gray-600 mt-1">إدارة معلوماتك الشخصية وإعداداتك</p>
          </div>
          
          {!isEditing ? (
            <Button
              variant="primary"
              icon={<Edit className="w-4 h-4" />}
              onClick={() => setIsEditing(true)}
            >
              تحرير الملف
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button
                variant="primary"
                icon={<Save className="w-4 h-4" />}
                onClick={handleSave}
                disabled={isLoading}
              >
                {isLoading ? 'جاري الحفظ...' : 'حفظ'}
              </Button>
              <Button
                variant="ghost"
                icon={<X className="w-4 h-4" />}
                onClick={handleCancel}
                disabled={isLoading}
              >
                إلغاء
              </Button>
            </div>
          )}
        </div>

        {/* Profile Card */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-start gap-6">
              {/* Profile Picture */}
              <div className="relative">
                <div className="w-24 h-24 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center">
                  <User className="w-12 h-12 text-blue-600" />
                </div>
                {isEditing && (
                  <button className="absolute -bottom-2 -right-2 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white hover:bg-blue-700 transition-colors">
                    <Camera className="w-4 h-4" />
                  </button>
                )}
              </div>

              {/* Profile Info */}
              <div className="flex-1 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">الاسم الكامل</label>
                    {isEditing ? (
                      <Input
                        value={profileData.name}
                        onChange={(e) => setProfileData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="أدخل اسمك الكامل"
                      />
                    ) : (
                      <p className="text-gray-900 font-medium">{profileData.name || 'غير محدد'}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4 text-gray-400" />
                      <p className="text-gray-900">{profileData.email}</p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف</label>
                    {isEditing ? (
                      <Input
                        value={profileData.phone}
                        onChange={(e) => setProfileData(prev => ({ ...prev, phone: e.target.value }))}
                        placeholder="أدخل رقم الهاتف"
                      />
                    ) : (
                      <div className="flex items-center gap-2">
                        <Phone className="w-4 h-4 text-gray-400" />
                        <p className="text-gray-900">{profileData.phone || 'غير محدد'}</p>
                      </div>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">القسم</label>
                    {isEditing ? (
                      <Input
                        value={profileData.department}
                        onChange={(e) => setProfileData(prev => ({ ...prev, department: e.target.value }))}
                        placeholder="أدخل القسم"
                      />
                    ) : (
                      <div className="flex items-center gap-2">
                        <Building className="w-4 h-4 text-gray-400" />
                        <p className="text-gray-900">{profileData.department || 'غير محدد'}</p>
                      </div>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">المنصب</label>
                    {isEditing ? (
                      <Input
                        value={profileData.position}
                        onChange={(e) => setProfileData(prev => ({ ...prev, position: e.target.value }))}
                        placeholder="أدخل المنصب"
                      />
                    ) : (
                      <p className="text-gray-900">{profileData.position || 'غير محدد'}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">الدور</label>
                    <div className="flex items-center gap-2">
                      <Shield className="w-4 h-4 text-gray-400" />
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm">
                        {userRole?.display_name || 'مستخدم'}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">نبذة شخصية</label>
                  {isEditing ? (
                    <textarea
                      value={profileData.bio}
                      onChange={(e) => setProfileData(prev => ({ ...prev, bio: e.target.value }))}
                      placeholder="أدخل نبذة عن نفسك..."
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  ) : (
                    <p className="text-gray-900">{profileData.bio || 'لا توجد نبذة شخصية'}</p>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Statistics */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">إحصائياتي</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {stats.map((stat) => {
              const Icon = stat.icon
              return (
                <Card key={stat.label}>
                  <CardContent className="p-4">
                    <div className="flex items-center">
                      <div className={`p-2 bg-${stat.color}-100 rounded-lg`}>
                        <Icon className={`w-5 h-5 text-${stat.color}-600`} />
                      </div>
                      <div className="mr-3">
                        <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                        <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* Account Settings */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">إعدادات الحساب</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <Key className="w-6 h-6 text-blue-600" />
                  <h3 className="font-semibold text-gray-900">كلمة المرور</h3>
                </div>
                <p className="text-gray-600 mb-4">تغيير كلمة المرور الخاصة بك</p>
                <Button variant="ghost" size="sm">
                  تغيير كلمة المرور
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <Bell className="w-6 h-6 text-green-600" />
                  <h3 className="font-semibold text-gray-900">الإشعارات</h3>
                </div>
                <p className="text-gray-600 mb-4">إدارة تفضيلات الإشعارات</p>
                <Button variant="ghost" size="sm">
                  إعدادات الإشعارات
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Account Info */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">معلومات الحساب</h3>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">تاريخ الانضمام:</span>
                <div className="flex items-center gap-2 mt-1">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-900">
                    غير محدد
                  </span>
                </div>
              </div>
              <div>
                <span className="text-gray-600">آخر تسجيل دخول:</span>
                <div className="flex items-center gap-2 mt-1">
                  <Activity className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-900">
                    غير محدد
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ProtectedLayout>
  )
} 