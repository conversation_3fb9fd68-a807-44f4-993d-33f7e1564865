'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import SolutionReviewCard from './SolutionReviewCard';
import FeedbackForm from './FeedbackForm';
import { 
  InteractiveReviewState,
  SolutionEvaluation,
  NewFeedbackForm,
  ParticipantInfo,
  ParticipatingDepartment,
  FeedbackStatistics,
  SuggestionFeedback
} from '@/types/feedback.types';

interface InteractiveSolutionReviewProps {
  suggestionId: string;
  suggestedSolutions: Array<{
    id: string;
    title: string;
    description: string;
    feasibilityScore: number;
    expectedBenefits: string[];
    implementationSteps: string[];
  }>;
  participants: ParticipantInfo[]; // المشاركون في المراجعة (بدون أعضاء الفريق)
  participatingDepartments: ParticipatingDepartment[]; // الأقسام المشاركة
  currentUser: {
    id: string;
    name: string;
    role: string;
  };
  onFeedbackSubmit: (feedback: NewFeedbackForm) => Promise<void>;
  onSolutionSelect: (solutionId: string, rationale: string) => Promise<void>;
  onConversionProceed: () => void;
  initialFeedbacks?: SuggestionFeedback[];
}

export default function InteractiveSolutionReview({
  suggestionId,
  suggestedSolutions,
  participants,
  participatingDepartments,
  currentUser,
  onFeedbackSubmit,
  onSolutionSelect,
  onConversionProceed,
  initialFeedbacks = []
}: InteractiveSolutionReviewProps) {
  const [reviewState, setReviewState] = useState<InteractiveReviewState>({
    requestId: suggestionId,
    participants,
    participatingDepartments,
    feedbacks: initialFeedbacks,
    solutionEvaluations: [],
    statistics: {
      totalFeedbacks: 0,
      byType: {
        suggestion: 0,
        concern: 0,
        improvement: 0,
        approval: 0,
        rejection: 0,
        question: 0
      },
      byPriority: {
        low: 0,
        medium: 0,
        high: 0,
        critical: 0
      },
      byStatus: {
        pending: 0,
        addressed: 0,
        resolved: 0,
        dismissed: 0
      },
      byReviewerType: {
        team_leader: 0,
        department_manager: 0,
        participating_department_manager: 0,
        pmo_stakeholder: 0
      }
    },
    currentPhase: 'feedback_collection',
    // للتوافق مع النظام القديم
    stakeholders: participants
  });

  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [selectedSolutionForFeedback, setSelectedSolutionForFeedback] = useState<string>('');
  const [isSubmittingFeedback, setIsSubmittingFeedback] = useState(false);

  // حساب تقييمات الحلول
  useEffect(() => {
    const evaluations: SolutionEvaluation[] = suggestedSolutions.map(solution => {
      const solutionFeedbacks = reviewState.feedbacks.filter(f => f.solutionId === solution.id);
      
      // حساب مستوى التوصية
      const approvals = solutionFeedbacks.filter(f => f.feedbackType === 'approval').length;
      const rejections = solutionFeedbacks.filter(f => f.feedbackType === 'rejection').length;
      const concerns = solutionFeedbacks.filter(f => f.feedbackType === 'concern').length;
      
      let recommendationLevel: SolutionEvaluation['recommendationLevel'] = 'neutral';
      if (approvals > rejections + concerns) {
        recommendationLevel = approvals >= solutionFeedbacks.length * 0.7 ? 'highly_recommended' : 'recommended';
      } else if (rejections > approvals || concerns > approvals) {
        recommendationLevel = 'not_recommended';
      }

      // حساب مستوى الإجماع بناءً على المشاركين
      const totalParticipants = participants.length;
      const participantsWhoCommented = new Set(solutionFeedbacks.map(f => f.reviewerId)).size;
      let consensusLevel: SolutionEvaluation['consensusLevel'] = 'low';
      if (participantsWhoCommented >= totalParticipants * 0.8) {
        consensusLevel = 'high';
      } else if (participantsWhoCommented >= totalParticipants * 0.5) {
        consensusLevel = 'medium';
      }

      return {
        solutionId: solution.id,
        feedbacks: solutionFeedbacks,
        consensusLevel,
        recommendationLevel
      };
    });

    // حساب الإحصائيات العامة
    const statistics: FeedbackStatistics = {
      totalFeedbacks: reviewState.feedbacks.length,
      byType: {
        suggestion: reviewState.feedbacks.filter(f => f.feedbackType === 'suggestion').length,
        concern: reviewState.feedbacks.filter(f => f.feedbackType === 'concern').length,
        improvement: reviewState.feedbacks.filter(f => f.feedbackType === 'improvement').length,
        approval: reviewState.feedbacks.filter(f => f.feedbackType === 'approval').length,
        rejection: reviewState.feedbacks.filter(f => f.feedbackType === 'rejection').length,
        question: reviewState.feedbacks.filter(f => f.feedbackType === 'question').length
      },
      byPriority: {
        low: reviewState.feedbacks.filter(f => f.priority === 'low').length,
        medium: reviewState.feedbacks.filter(f => f.priority === 'medium').length,
        high: reviewState.feedbacks.filter(f => f.priority === 'high').length,
        critical: reviewState.feedbacks.filter(f => f.priority === 'critical').length
      },
      byStatus: {
        pending: reviewState.feedbacks.filter(f => f.status === 'pending').length,
        addressed: reviewState.feedbacks.filter(f => f.status === 'addressed').length,
        resolved: reviewState.feedbacks.filter(f => f.status === 'resolved').length,
        dismissed: reviewState.feedbacks.filter(f => f.status === 'dismissed').length
      },
      byReviewerType: {
        team_leader: reviewState.feedbacks.filter(f => f.reviewerType === 'team_leader').length,
        department_manager: reviewState.feedbacks.filter(f => f.reviewerType === 'department_manager').length,
        participating_department_manager: reviewState.feedbacks.filter(f => f.reviewerType === 'participating_department_manager').length,
        pmo_stakeholder: reviewState.feedbacks.filter(f => f.reviewerType === 'pmo_stakeholder').length
      }
    };

    setReviewState(prev => ({
      ...prev,
      solutionEvaluations: evaluations,
      statistics
    }));
  }, [reviewState.feedbacks, participants, suggestedSolutions]);

  // التحقق من إمكانية إضافة ملاحظات (المشاركون فقط وليس مكتب المشاريع)
  const canAddFeedback = () => {
    // التحقق من أن المستخدم من المشاركين في المراجعة
    const isParticipant = participants.some(p => p.name === currentUser.name);
    return isParticipant;
  };

  // إضافة ملاحظة جديدة
  const handleAddFeedback = (solutionId: string) => {
    setSelectedSolutionForFeedback(solutionId);
    setShowFeedbackForm(true);
  };

  // إرسال الملاحظة
  const handleFeedbackSubmit = async (feedback: NewFeedbackForm) => {
    setIsSubmittingFeedback(true);
    try {
      await onFeedbackSubmit(feedback);
      
      // العثور على نوع المراجع من المشاركين
      const participant = participants.find(p => p.name === currentUser.name);
      const reviewerType = participant?.role || 'pmo_stakeholder';
      
      // إضافة الملاحظة محلياً للعرض الفوري
      const newFeedback: SuggestionFeedback = {
        id: `temp_${Date.now()}`,
        requestId: suggestionId,
        reviewerId: currentUser.id,
        reviewerName: currentUser.name,
        reviewerType,
        solutionId: feedback.solutionId,
        comment: feedback.comment,
        feedbackType: feedback.feedbackType,
        priority: feedback.priority,
        status: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: currentUser.id
      };

      setReviewState(prev => ({
        ...prev,
        feedbacks: [...prev.feedbacks, newFeedback]
      }));

      setShowFeedbackForm(false);
      setSelectedSolutionForFeedback('');
    } catch (error) {
      console.error('خطأ في إرسال الملاحظة:', error);
    } finally {
      setIsSubmittingFeedback(false);
    }
  };

  // عرض تفاصيل الحل
  const handleViewSolutionDetails = (solutionId: string) => {
    // يمكن إضافة modal أو صفحة منفصلة لعرض التفاصيل
    console.log('عرض تفاصيل الحل:', solutionId);
  };

  // الحصول على الحل المحدد للملاحظة
  const selectedSolution = suggestedSolutions.find(s => s.id === selectedSolutionForFeedback);

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* رأس الصفحة */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          مراجعة تفاعلية للحلول المقترحة
        </h1>
        
        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {suggestedSolutions.length}
              </div>
              <div className="text-sm text-gray-600">حلول مقترحة</div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {reviewState.statistics.totalFeedbacks}
              </div>
              <div className="text-sm text-gray-600">إجمالي الملاحظات</div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {participants.length}
              </div>
              <div className="text-sm text-gray-600">مشاركون</div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {new Set(reviewState.feedbacks.map(f => f.reviewerId)).size}
              </div>
              <div className="text-sm text-gray-600">مراجعين نشطين</div>
            </div>
          </Card>
        </div>

        {/* معلومات أصحاب المصلحة */}
        <Card className="p-4 mb-6">
          <h3 className="font-semibold text-gray-800 mb-3">المشاركون:</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {participants.map((participant) => (
              <div key={participant.id} className="flex items-center space-x-3 space-x-reverse">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 text-sm font-medium">
                    {participant.name.charAt(0)}
                  </span>
                </div>
                <div>
                  <div className="font-medium text-sm">{participant.name}</div>
                  <div className="text-xs text-gray-500">{participant.role}</div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* عرض الحلول المقترحة */}
      <div className="space-y-6">
        <h2 className="text-xl font-semibold text-gray-900">الحلول المقترحة</h2>
        
        {suggestedSolutions.map((solution) => {
          const evaluation = reviewState.solutionEvaluations.find(e => e.solutionId === solution.id);
          
          return (
            <SolutionReviewCard
              key={solution.id}
              solution={solution}
              evaluation={evaluation || {
                solutionId: solution.id,
                feedbacks: [],
                consensusLevel: 'low',
                recommendationLevel: 'neutral'
              }}
              canAddFeedback={canAddFeedback()}
              onAddFeedback={handleAddFeedback}
              onViewDetails={handleViewSolutionDetails}
            />
          );
        })}
      </div>

      {/* نموذج إضافة الملاحظة */}
      {showFeedbackForm && selectedSolution && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <FeedbackForm
              solutionId={selectedSolutionForFeedback}
              solutionTitle={selectedSolution.title}
              reviewerName={currentUser.name}
              onSubmit={handleFeedbackSubmit}
              onCancel={() => {
                setShowFeedbackForm(false);
                setSelectedSolutionForFeedback('');
              }}
              isSubmitting={isSubmittingFeedback}
            />
          </div>
        </div>
      )}

      {/* إجراءات المتابعة */}
      {canAddFeedback() && reviewState.statistics.totalFeedbacks > 0 && (
        <Card className="mt-8 p-6">
          <h3 className="font-semibold text-gray-800 mb-4">الخطوات التالية</h3>
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm text-gray-600 mb-2">
                تم جمع {reviewState.statistics.totalFeedbacks} ملاحظة من المشاركين
              </p>
              <p className="text-sm text-gray-600">
                يمكن الآن اختيار الحل الأمثل والمتابعة لتحويل المقترح إلى مشروع
              </p>
            </div>
            <Button
              variant="primary"
              onClick={onConversionProceed}
            >
              اختيار الحل والمتابعة
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
} 