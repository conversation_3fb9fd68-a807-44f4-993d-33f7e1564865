// الأنواع المشتركة عبر النظام
export interface BaseEntity {
  id: string
  createdAt: string
  updatedAt: string
}

export interface User extends BaseEntity {
  email: string
  name: string
  departmentId: string
  roleId: string
  phone?: string
  avatarUrl?: string
  isActive: boolean
}

export interface Department extends BaseEntity {
  name: string
  description?: string
  parentId?: string
  managerId?: string
}

export interface Role extends BaseEntity {
  name: string
  displayName: string
  description?: string
  permissions: Record<string, boolean>
}

// أنواع النماذج
export type FormStatus = 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected'
export type Priority = 'low' | 'medium' | 'high' | 'urgent'
export type ProjectStatus = 'planning' | 'in_progress' | 'on_hold' | 'completed' | 'cancelled'
export type TaskStatus = 'todo' | 'in_progress' | 'review' | 'done'

// أنواع الاستجابة
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T = any> {
  data: T[]
  total: number
  page: number
  limit: number
  hasMore: boolean
}

// أنواع المكونات
export interface ComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface LoadingState {
  isLoading: boolean
  error?: string
  data?: any
}

// أنواع الأحداث
export interface AuditLog extends BaseEntity {
  userId: string
  action: string
  entityType: string
  entityId: string
  oldValues?: Record<string, any>
  newValues?: Record<string, any>
  ipAddress?: string
  userAgent?: string
}

// أنواع الملفات
export interface FileInfo {
  id: string
  name: string
  size: number
  type: string
  url: string
  uploadedBy: string
  uploadedAt: string
}

// أنواع الإحصائيات
export interface StatCard {
  title: string
  value: number | string
  change?: number
  changeType?: 'increase' | 'decrease' | 'neutral'
  icon?: string
  color?: string
} 