import { supabase } from './supabase'

// واجهات البيانات
export interface ApprovalData {
  request_id: string
  approver_id: string
  decision: 'approved' | 'rejected'
  notes?: string
}

export interface ApprovalFilters {
  approver_id?: string
  status?: 'pending' | 'approved' | 'rejected'
  request_id?: string
}

export interface WorkflowStep {
  level: number
  role: string
  roleName: string
  status: 'pending' | 'approved' | 'rejected'
  approvedAt?: string
  notes?: string
  approver?: {
    name: string
    email: string
  }
}

// دوال API للموافقات
export class ApprovalsAPI {
  
  // معالجة موافقة
  static async processApproval(data: ApprovalData) {
    try {
      const response = await fetch('/api/approvals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في معالجة الموافقة')
      }

      return await response.json()
    } catch (error) {
      console.error('Error processing approval:', error)
      throw error
    }
  }

  // استرجاع الموافقات
  static async getApprovals(filters: ApprovalFilters = {}) {
    try {
      const params = new URLSearchParams()
      
      if (filters.approver_id) params.append('approver_id', filters.approver_id)
      if (filters.status) params.append('status', filters.status)
      if (filters.request_id) params.append('request_id', filters.request_id)

      const response = await fetch(`/api/approvals?${params.toString()}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في استرجاع الموافقات')
      }

      return await response.json()
    } catch (error) {
      console.error('Error fetching approvals:', error)
      throw error
    }
  }

  // الحصول على خطوات سير العمل للطلب
  static async getWorkflowSteps(requestId: string): Promise<WorkflowStep[]> {
    try {
      const { data: request, error: requestError } = await supabase
        .from('project_requests')
        .select('main_type, sub_type, approval_level, status')
        .eq('id', requestId)
        .single()

      if (requestError || !request) {
        throw new Error('الطلب غير موجود')
      }

      const { data: approvals, error: approvalsError } = await supabase
        .from('approvals')
        .select(`
          *,
          approver:users!approver_id(name, email, role:roles!role_id(name, display_name))
        `)
        .eq('request_id', requestId)
        .order('created_at')

      if (approvalsError) {
        throw new Error('حدث خطأ في استرجاع الموافقات')
      }

      const requiredLevels = this.getRequiredApprovalLevels(request.main_type, request.sub_type)
      const steps: WorkflowStep[] = []

      for (let level = 1; level <= requiredLevels; level++) {
        const approval = approvals?.find(a => 
          this.getApproverLevelByRole(a.approver?.role?.name) === level
        )

        steps.push({
          level,
          role: this.getRoleByLevel(level),
          roleName: this.getRoleNameByLevel(level),
          status: approval ? approval.status as 'pending' | 'approved' | 'rejected' : 'pending',
          approvedAt: approval?.approved_at || undefined,
          notes: approval?.notes || undefined,
          approver: approval?.approver ? {
            name: approval.approver.name,
            email: approval.approver.email
          } : undefined
        })
      }

      return steps
    } catch (error) {
      console.error('Error getting workflow steps:', error)
      throw error
    }
  }

  // التحقق من إمكانية الموافقة
  static async canApprove(requestId: string, userId: string): Promise<boolean> {
    try {
      const { data: approval, error } = await supabase
        .from('approvals')
        .select('status')
        .eq('request_id', requestId)
        .eq('approver_id', userId)
        .eq('status', 'pending')
        .single()

      return !error && !!approval
    } catch (error) {
      console.error('Error checking approval permission:', error)
      return false
    }
  }

  // الحصول على الموافقات المعلقة للمستخدم
  static async getPendingApprovals(userId: string) {
    try {
      const { data, error } = await supabase
        .from('approvals')
        .select(`
          *,
          request:project_requests(
            id,
            title,
            description,
            main_type,
            sub_type,
            status,
            priority,
            estimated_budget,
            created_at,
            requester:users!requester_id(name, email),
            department:departments!department_id(name)
          )
        `)
        .eq('approver_id', userId)
        .eq('status', 'pending')
        .order('created_at', { ascending: false })

      if (error) {
        throw new Error('حدث خطأ في استرجاع الموافقات المعلقة')
      }

      return { success: true, data: data || [] }
    } catch (error) {
      console.error('Error fetching pending approvals:', error)
      throw error
    }
  }

  // إحصائيات الموافقات
  static async getApprovalStats(userId?: string) {
    try {
      let query = supabase
        .from('approvals')
        .select('status, created_at')

      if (userId) {
        query = query.eq('approver_id', userId)
      }

      const { data, error } = await query

      if (error) {
        throw new Error('حدث خطأ في استرجاع إحصائيات الموافقات')
      }

      const stats = {
        total: data?.length || 0,
        pending: data?.filter(a => a.status === 'pending').length || 0,
        approved: data?.filter(a => a.status === 'approved').length || 0,
        rejected: data?.filter(a => a.status === 'rejected').length || 0,
                 thisMonth: data?.filter(a => 
           a.created_at && new Date(a.created_at).getMonth() === new Date().getMonth()
         ).length || 0
      }

      return { success: true, data: stats }
    } catch (error) {
      console.error('Error fetching approval stats:', error)
      throw error
    }
  }

  // دوال مساعدة
  private static getRequiredApprovalLevels(mainType: string, subType: string | null): number {
    if (mainType === 'general_project') return 3
    
    if (mainType === 'improvement_project') {
      switch (subType) {
        case 'quick_win': return 1
        case 'suggestion': return 2
        case 'improvement_full': return 3
        default: return 1
      }
    }
    
    return 1
  }

  private static getApproverLevelByRole(roleName: string): number {
    switch (roleName) {
      case 'pmo_manager': return 1
      case 'planning_manager': return 2
      case 'executive_manager': return 3
      default: return 0
    }
  }

  private static getRoleByLevel(level: number): string {
    switch (level) {
      case 1: return 'pmo_manager'
      case 2: return 'planning_manager'
      case 3: return 'executive_manager'
      default: return 'unknown'
    }
  }

  private static getRoleNameByLevel(level: number): string {
    switch (level) {
      case 1: return 'مدير مكتب المشاريع'
      case 2: return 'مدير إدارة التخطيط'
      case 3: return 'المدير التنفيذي'
      default: return 'غير معروف'
    }
  }
}

// دوال مساعدة للموافقات
export class ApprovalHelpers {
  
  // تحديد لون الحالة
  static getStatusColor(status: string): string {
    switch (status) {
      case 'pending': return 'text-yellow-600 bg-yellow-100'
      case 'approved': return 'text-green-600 bg-green-100'
      case 'rejected': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  // تحديد نص الحالة
  static getStatusText(status: string): string {
    switch (status) {
      case 'pending': return 'في الانتظار'
      case 'approved': return 'معتمد'
      case 'rejected': return 'مرفوض'
      default: return 'غير معروف'
    }
  }

  // تحديد أولوية الطلب
  static getPriorityColor(priority: string): string {
    switch (priority) {
      case 'low': return 'text-gray-600 bg-gray-100'
      case 'medium': return 'text-blue-600 bg-blue-100'
      case 'high': return 'text-orange-600 bg-orange-100'
      case 'urgent': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  // تحديد نص الأولوية
  static getPriorityText(priority: string): string {
    switch (priority) {
      case 'low': return 'منخفض'
      case 'medium': return 'متوسط'
      case 'high': return 'عالي'
      case 'urgent': return 'عاجل'
      default: return 'غير محدد'
    }
  }

  // تحديد نوع المشروع
  static getProjectTypeText(mainType: string, subType: string | null): string {
    if (mainType === 'general_project') {
      return 'مشروع عام'
    }
    
    if (mainType === 'improvement_project') {
      switch (subType) {
        case 'quick_win': return 'كويك وين'
        case 'suggestion': return 'مقترح تحسين'
        case 'improvement_full': return 'تحسين شامل'
        default: return 'مشروع تحسين'
      }
    }
    
    return 'غير محدد'
  }

  // حساب الوقت المنقضي
  static getTimeElapsed(createdAt: string): string {
    const now = new Date()
    const created = new Date(createdAt)
    const diffInHours = Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      return 'أقل من ساعة'
    } else if (diffInHours < 24) {
      return `${diffInHours} ساعة`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays} يوم`
    }
  }

  // التحقق من انتهاء المهلة
  static isOverdue(createdAt: string, maxDays: number = 3): boolean {
    const now = new Date()
    const created = new Date(createdAt)
    const diffInDays = Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24))
    
    return diffInDays > maxDays
  }
} 