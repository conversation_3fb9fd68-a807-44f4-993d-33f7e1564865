'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth, usePermissions } from '@/lib/auth'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { KanbanBoard } from '@/components/kanban/KanbanBoard'
import { ProgressTracker } from '@/components/kanban/ProgressTracker'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { 
  Plus, 
  BarChart3,
  Download,
  Settings,
  TrendingUp,
  Clock,
  CheckCircle,
  Target,
  Eye,
  EyeOff
} from 'lucide-react'

export default function ProjectsPage() {
  const { } = useAuth()
  const permissions = usePermissions()
  const router = useRouter()
  const [showProgressTracker, setShowProgressTracker] = useState(true)

  const [projects, setProjects] = useState([])
  const [loading, setLoading] = useState(true)

  // جلب المشاريع من API
  useEffect(() => {
    fetchProjects()
  }, [])

  const fetchProjects = async () => {
    try {
      const response = await fetch('/api/projects')
      const data = await response.json()
      if (data.success) {
        setProjects(data.data)
      }
    } catch (error) {
      console.error('Error fetching projects:', error)
    } finally {
      setLoading(false)
    }
  }

  // بيانات وهمية للمشاريع (احتياطية)
  const mockTasks = projects.length > 0 ? projects : [
    {
      id: '1',
      title: 'تطوير نظام إدارة المستندات',
      description: 'تطوير نظام شامل لإدارة المستندات الإلكترونية مع إمكانيات البحث والأرشفة',
      type: 'project' as const,
      priority: 'high' as const,
      assignee: 'أحمد محمد',
      dueDate: '2024-06-15',
      createdDate: '2024-02-01',
      tags: ['تقنية', 'مستندات', 'أرشفة'],
      progress: 75,
      pdcaPhase: 'check' as const,
      estimatedHours: 320,
      actualHours: 280,
      attachments: 5,
      comments: 12
    },
    {
      id: '2',
      title: 'تحسين عملية إدارة المخزون',
      description: 'مراجعة وتحسين العمليات المتعلقة بإدارة المخزون وتقليل الهدر',
      type: 'suggestion' as const,
      priority: 'medium' as const,
      assignee: 'سارة أحمد',
      dueDate: '2024-05-20',
      createdDate: '2024-03-10',
      tags: ['مخزون', 'تحسين', 'تكلفة'],
      progress: 45,
      pdcaPhase: 'do' as const,
      estimatedHours: 160,
      actualHours: 95,
      attachments: 3,
      comments: 8
    },
    {
      id: '3',
      title: 'تطبيق نظام جديد للحضور والانصراف',
      description: 'تطبيق نظام إلكتروني متقدم لتسجيل الحضور والانصراف',
      type: 'project' as const,
      priority: 'urgent' as const,
      assignee: 'محمد علي',
      dueDate: '2024-04-30',
      createdDate: '2024-01-15',
      tags: ['موارد بشرية', 'حضور', 'تقنية'],
      progress: 90,
      pdcaPhase: 'act' as const,
      estimatedHours: 240,
      actualHours: 220,
      attachments: 7,
      comments: 15
    },
    {
      id: '4',
      title: 'تحديث إجراءات الأمان السيبراني',
      description: 'مراجعة وتحديث جميع إجراءات الأمان السيبراني وتدريب الموظفين',
      type: 'project' as const,
      priority: 'high' as const,
      assignee: 'ليلى حسن',
      dueDate: '2024-07-01',
      createdDate: '2024-02-20',
      tags: ['أمان', 'تدريب', 'سيبراني'],
      progress: 25,
      pdcaPhase: 'plan' as const,
      estimatedHours: 400,
      actualHours: 120,
      attachments: 4,
      comments: 6
    },
    {
      id: '5',
      title: 'تحسين عملية طلب الإجازات',
      description: 'تبسيط وتحسين عملية طلب الإجازات عبر النظام الإلكتروني',
      type: 'suggestion' as const,
      priority: 'medium' as const,
      assignee: 'فاطمة أحمد',
      dueDate: '2024-05-15',
      createdDate: '2024-03-01',
      tags: ['موارد بشرية', 'إجازات', 'تحسين'],
      progress: 60,
      pdcaPhase: 'do' as const,
      estimatedHours: 120,
      actualHours: 85,
      attachments: 1,
      comments: 4
    },
    {
      id: '6',
      title: 'تنظيم مكتب الاستقبال',
      description: 'إعادة تنظيم مكتب الاستقبال لتحسين تجربة الزوار وتسهيل العمل',
      type: 'quickwin' as const,
      priority: 'low' as const,
      assignee: 'نورا أحمد',
      dueDate: '2024-04-01',
      createdDate: '2024-03-15',
      tags: ['مكتب الاستقبال', 'تنظيم', 'خدمة العملاء'],
      progress: 15,
      pdcaPhase: 'plan' as const,
      estimatedHours: 20,
      actualHours: 8,
      attachments: 0,
      comments: 2
    }
  ]

  // حساب الإحصائيات
  const calculateMetrics = () => {
    const totalTasks = mockTasks.length
    const completedTasks = mockTasks.filter(task => task.progress >= 100).length
    const overdueTasks = mockTasks.filter(task => 
      new Date(task.dueDate) < new Date() && task.progress < 100
    ).length
    const averageProgress = mockTasks.reduce((sum, task) => sum + task.progress, 0) / totalTasks
    const onTrackTasks = mockTasks.filter(task => {
      const today = new Date()
      const dueDate = new Date(task.dueDate)
      const daysDiff = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
      return daysDiff > 0 && task.progress >= 50
    }).length
    const atRiskTasks = mockTasks.filter(task => {
      const today = new Date()
      const dueDate = new Date(task.dueDate)
      const daysDiff = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
      return daysDiff <= 7 && task.progress < 70
    }).length
    const estimatedHours = mockTasks.reduce((sum, task) => sum + (task.estimatedHours || 0), 0)
    const actualHours = mockTasks.reduce((sum, task) => sum + (task.actualHours || 0), 0)
    const efficiency = estimatedHours > 0 ? (estimatedHours / actualHours) * 100 : 100

    return {
      totalTasks,
      completedTasks,
      overdueTasks,
      averageProgress,
      onTrackTasks,
      atRiskTasks,
      estimatedHours,
      actualHours,
      efficiency
    }
  }

  // حساب تقدم مراحل PDCA
  const calculatePhaseProgress = () => {
    const phases = [
      { id: 'plan', title: 'التخطيط', color: 'blue', icon: <Target className="w-5 h-5" /> },
      { id: 'do', title: 'التنفيذ', color: 'orange', icon: <Clock className="w-5 h-5" /> },
      { id: 'check', title: 'المراجعة', color: 'purple', icon: <CheckCircle className="w-5 h-5" /> },
      { id: 'act', title: 'العمل', color: 'green', icon: <TrendingUp className="w-5 h-5" /> }
    ]

    return phases.map(phase => {
      const phaseTasks = mockTasks.filter(task => task.pdcaPhase === phase.id)
      const averageProgress = phaseTasks.length > 0 
        ? phaseTasks.reduce((sum, task) => sum + task.progress, 0) / phaseTasks.length 
        : 0

      return {
        phase: phase.id,
        title: phase.title,
        color: phase.color,
        tasks: phaseTasks.length,
        progress: averageProgress,
        icon: phase.icon
      }
    })
  }

  // إحصائيات المشاريع
  const projectStats = {
    total: mockTasks.length,
    planning: mockTasks.filter(task => task.pdcaPhase === 'plan').length,
    inProgress: mockTasks.filter(task => task.pdcaPhase === 'do').length,
    reviewing: mockTasks.filter(task => task.pdcaPhase === 'check').length,
    completed: mockTasks.filter(task => task.pdcaPhase === 'act').length,
    overdue: mockTasks.filter(task => 
      new Date(task.dueDate) < new Date() && task.progress < 100
    ).length
  }

  const metrics = calculateMetrics()
  const phaseProgress = calculatePhaseProgress()

  const handleTaskMove = (taskId: string, fromColumn: string, toColumn: string) => {
    console.log(`Moving task ${taskId} from ${fromColumn} to ${toColumn}`)
    // هنا يمكن إضافة منطق حفظ التغيير في قاعدة البيانات
  }

  const handleTaskUpdate = (task: any) => {
    console.log('Updating task:', task)
    // هنا يمكن إضافة منطق تحديث المهمة
  }

  const handleTaskCreate = (columnId: string) => {
    console.log(`Creating new task in column: ${columnId}`)
    // هنا يمكن إضافة منطق إنشاء مهمة جديدة
    router.push('/requests/new')
  }

  return (
    <ProtectedLayout>
      <div className="container mx-auto">
          {/* Page Header */}
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">إدارة المشاريع</h1>
              <p className="text-gray-600 mt-1">
                متابعة وإدارة مشاريع التحسين المستمر باستخدام منهجية PDCA
              </p>
            </div>
            
            <div className="flex flex-wrap gap-3">
              <Button
                variant="ghost"
                icon={showProgressTracker ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                onClick={() => setShowProgressTracker(!showProgressTracker)}
              >
                {showProgressTracker ? 'إخفاء التقارير' : 'عرض التقارير'}
              </Button>
              
              <Button
                variant="ghost"
                icon={<BarChart3 className="w-4 h-4" />}
              >
                التقارير
              </Button>
              
              <Button
                variant="ghost"
                icon={<Download className="w-4 h-4" />}
              >
                تصدير
              </Button>
              
              <Button
                variant="ghost"
                icon={<Settings className="w-4 h-4" />}
              >
                الإعدادات
              </Button>
              
              {permissions.canCreateRequests() && (
                <Button
                  icon={<Plus className="w-4 h-4" />}
                  onClick={() => router.push('/requests/new')}
                >
                  طلب جديد
                </Button>
              )}
            </div>
          </div>

          {/* Progress Tracker */}
          {showProgressTracker && (
            <div className="mb-6">
              <ProgressTracker 
                metrics={metrics} 
                phaseProgress={phaseProgress}
                showDetailed={true}
              />
            </div>
          )}

          {/* Quick Stats Cards */}
          <div className="grid grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <Card className="p-4 bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                  <Target className="w-5 h-5 text-white" />
                </div>
                <div>
                  <p className="text-sm text-blue-600 font-medium">التخطيط</p>
                  <p className="text-2xl font-bold text-blue-900">{projectStats.planning}</p>
                </div>
              </div>
          </Card>

            <Card className="p-4 bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-orange-600 rounded-full flex items-center justify-center">
                  <Clock className="w-5 h-5 text-white" />
                </div>
                <div>
                  <p className="text-sm text-orange-600 font-medium">التنفيذ</p>
                  <p className="text-2xl font-bold text-orange-900">{projectStats.inProgress}</p>
                    </div>
                    </div>
                </Card>

            <Card className="p-4 bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-white" />
                      </div>
                <div>
                  <p className="text-sm text-purple-600 font-medium">المراجعة</p>
                  <p className="text-2xl font-bold text-purple-900">{projectStats.reviewing}</p>
                    </div>
                  </div>
            </Card>

            <Card className="p-4 bg-gradient-to-r from-green-50 to-green-100 border-green-200">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-white" />
                          </div>
                            <div>
                  <p className="text-sm text-green-600 font-medium">مكتمل</p>
                  <p className="text-2xl font-bold text-green-900">{projectStats.completed}</p>
                            </div>
                            </div>
            </Card>

            <Card className="p-4 bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">الكل</span>
                            </div>
                            <div>
                  <p className="text-sm text-gray-600 font-medium">المجموع</p>
                  <p className="text-2xl font-bold text-gray-900">{projectStats.total}</p>
                            </div>
                          </div>
            </Card>
                        </div>
                        
          {/* Kanban Board */}
          <KanbanBoard
            initialTasks={mockTasks}
            onTaskMove={handleTaskMove}
            onTaskUpdate={handleTaskUpdate}
            onTaskCreate={handleTaskCreate}
          />
        </div>
    </ProtectedLayout>
  )
} 