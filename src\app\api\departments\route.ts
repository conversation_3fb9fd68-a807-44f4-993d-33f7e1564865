import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'

// استخدام admin client للتجاوز RLS
const supabase = supabaseAdmin

// واجهة بيانات القسم
interface DepartmentData {
  name: string
  description?: string
  parent_id?: string
  manager_id?: string
}

// POST - إنشاء قسم جديد
export async function POST(request: NextRequest) {
  try {
    const body: DepartmentData = await request.json()
    
    // التحقق من صحة البيانات
    if (!body.name) {
      return NextResponse.json(
        { error: 'اسم القسم مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من عدم وجود قسم بنفس الاسم في نفس المستوى
    let duplicateQuery = supabase
      .from('departments')
      .select('id')
      .eq('name', body.name)

    if (body.parent_id) {
      duplicateQuery = duplicateQuery.eq('parent_id', body.parent_id)
    } else {
      duplicateQuery = duplicateQuery.is('parent_id', null)
    }

    const { data: existingDept } = await duplicateQuery.single()

    if (existingDept) {
      return NextResponse.json(
        { error: 'يوجد قسم بنفس الاسم في هذا المستوى' },
        { status: 409 }
      )
    }

    // التحقق من وجود القسم الأب إذا تم تحديده
    if (body.parent_id) {
      const { data: parentDept, error: parentError } = await supabase
        .from('departments')
        .select('id, name')
        .eq('id', body.parent_id)
        .single()

      if (parentError || !parentDept) {
        return NextResponse.json(
          { error: 'القسم الأب غير موجود' },
          { status: 404 }
        )
      }
    }

    // التحقق من وجود المدير إذا تم تحديده
    if (body.manager_id) {
      const { data: manager, error: managerError } = await supabase
        .from('users')
        .select('id, name')
        .eq('id', body.manager_id)
        .eq('is_active', true)
        .single()

      if (managerError || !manager) {
        return NextResponse.json(
          { error: 'المدير المحدد غير موجود أو غير نشط' },
          { status: 404 }
        )
      }
    }

    // إنشاء القسم
    const { data, error } = await supabase
      .from('departments')
      .insert({
        name: body.name,
        description: body.description,
        parent_id: body.parent_id,
        manager_id: body.manager_id
      })
      .select(`
        *,
        parent:departments!parent_id(id, name),
        manager:users!manager_id(id, name, email)
      `)
      .single()

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في إنشاء القسم' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء القسم بنجاح',
      data
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// GET - استرجاع الأقسام
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const parentId = searchParams.get('parent_id')
    const includeChildren = searchParams.get('include_children') === 'true'
    const includeUsers = searchParams.get('include_users') === 'true'
    const hierarchy = searchParams.get('hierarchy') === 'true'

    let selectFields = `
      *,
      parent:departments!parent_id(id, name),
      manager:users!manager_id(id, name, email)
    `

    // نتجنب إضافة المستخدمين في الاستعلام الأساسي لتجنب التعقيد
    // سنحمل المستخدمين بشكل منفصل إذا لزم الأمر

    let query = supabase
      .from('departments')
      .select(selectFields.trim())

    // تطبيق الفلاتر
    if (parentId === 'null') {
      query = query.is('parent_id', null)
    } else if (parentId) {
      query = query.eq('parent_id', parentId)
    }

    // ترتيب النتائج
    query = query.order('name')

    const { data, error } = await query

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في استرجاع الأقسام' },
        { status: 500 }
      )
    }

    // إنشاء الهيكل الهرمي إذا طُلب
    let result = data || []
    if (hierarchy) {
      result = buildDepartmentHierarchy(data || [])
    }

    return NextResponse.json({
      success: true,
      data: result
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// PUT - تحديث قسم
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        { error: 'معرف القسم مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود القسم
    const { data: existingDept, error: existingError } = await supabase
      .from('departments')
      .select('id, name, parent_id')
      .eq('id', id)
      .single()

    if (existingError || !existingDept) {
      return NextResponse.json(
        { error: 'القسم غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من عدم جعل القسم أب لنفسه
    if (updateData.parent_id === id) {
      return NextResponse.json(
        { error: 'لا يمكن جعل القسم أب لنفسه' },
        { status: 400 }
      )
    }

    // التحقق من عدم إنشاء حلقة في الهيكل الهرمي
    if (updateData.parent_id) {
      const isCircular = await checkCircularReference(id, updateData.parent_id)
      if (isCircular) {
        return NextResponse.json(
          { error: 'لا يمكن إنشاء حلقة في الهيكل الهرمي' },
          { status: 400 }
        )
      }
    }

    // تحديث القسم
    const { data, error } = await supabase
      .from('departments')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        parent:departments!parent_id(id, name),
        manager:users!manager_id(id, name, email),
        children:departments!parent_id(id, name),
        users(id, name, email)
      `)
      .single()

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في تحديث القسم' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث القسم بنجاح',
      data
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// DELETE - حذف قسم
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'معرف القسم مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود القسم
    const { data: dept, error: deptError } = await supabase
      .from('departments')
      .select('id, name')
      .eq('id', id)
      .single()

    if (deptError || !dept) {
      return NextResponse.json(
        { error: 'القسم غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من عدم وجود أقسام فرعية
    const { data: children } = await supabase
      .from('departments')
      .select('id')
      .eq('parent_id', id)

    if (children && children.length > 0) {
      return NextResponse.json(
        { error: 'لا يمكن حذف قسم يحتوي على أقسام فرعية' },
        { status: 400 }
      )
    }

    // التحقق من عدم وجود مستخدمين
    const { data: users } = await supabase
      .from('users')
      .select('id')
      .eq('department_id', id)

    if (users && users.length > 0) {
      return NextResponse.json(
        { error: 'لا يمكن حذف قسم يحتوي على مستخدمين' },
        { status: 400 }
      )
    }

    // حذف القسم
    const { error } = await supabase
      .from('departments')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في حذف القسم' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف القسم بنجاح'
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// دالة بناء الهيكل الهرمي
function buildDepartmentHierarchy(departments: any[]): any[] {
  const departmentMap = new Map()
  const rootDepartments: any[] = []

  // إنشاء خريطة للأقسام
  departments.forEach(dept => {
    departmentMap.set(dept.id, { ...dept, children: [] })
  })

  // بناء الهيكل الهرمي
  departments.forEach(dept => {
    if (dept.parent_id) {
      const parent = departmentMap.get(dept.parent_id)
      if (parent) {
        parent.children.push(departmentMap.get(dept.id))
      }
    } else {
      rootDepartments.push(departmentMap.get(dept.id))
    }
  })

  return rootDepartments
}

// دالة التحقق من المراجع الدائرية
async function checkCircularReference(deptId: string, parentId: string): Promise<boolean> {
  let currentParentId = parentId
  const visited = new Set<string>()

  while (currentParentId && !visited.has(currentParentId)) {
    if (currentParentId === deptId) {
      return true // وجدنا حلقة
    }

    visited.add(currentParentId)

    const { data: parent } = await supabase
      .from('departments')
      .select('parent_id')
      .eq('id', currentParentId)
      .single()

    currentParentId = parent?.parent_id || ''
  }

  return false
} 