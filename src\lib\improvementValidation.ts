// نظام التحقق المتقدم لنموذج التحسين
interface ValidationRule {
  field: string
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  customValidator?: (value: any, formData: any) => string | null
  dependencies?: string[]
}

interface ValidationResult {
  isValid: boolean
  errors: Record<string, string>
  warnings: Record<string, string>
  suggestions: Record<string, string>
}

export class ImprovementValidation {
  private static rules: Record<number, ValidationRule[]> = {
    1: [ // Find
      {
        field: 'problemDescription',
        required: true,
        minLength: 50,
        maxLength: 500,
        customValidator: (value: string) => {
          if (!value.includes('تأخر') && !value.includes('زيادة') && !value.includes('نقص') && !value.includes('انخفاض')) {
            return 'يُفضل أن يحتوي وصف المشكلة على كلمات تدل على المشكلة مثل: تأخر، زيادة، نقص، انخفاض'
          }
          return null
        }
      },
      {
        field: 'indicatorName',
        required: true,
        minLength: 3,
        maxLength: 100,
        customValidator: (value: string) => {
          const commonIndicators = ['زمن', 'معدل', 'نسبة', 'عدد', 'مؤشر', 'مستوى']
          if (!commonIndicators.some(indicator => value.includes(indicator))) {
            return 'يُفضل أن يحتوي اسم المؤشر على كلمات مثل: زمن، معدل، نسبة، عدد'
          }
          return null
        }
      },
      {
        field: 'currentValue',
        required: true,
        customValidator: (value: number, formData: any) => {
          if (value <= 0) {
            return 'القيمة الحالية يجب أن تكون أكبر من صفر'
          }
          if (formData.targetValue && value === formData.targetValue) {
            return 'القيمة الحالية يجب أن تختلف عن القيمة المستهدفة'
          }
          return null
        }
      },
      {
        field: 'targetValue',
        required: true,
        customValidator: (value: number, formData: any) => {
          if (value <= 0) {
            return 'القيمة المستهدفة يجب أن تكون أكبر من صفر'
          }
          if (formData.currentValue && Math.abs(value - formData.currentValue) < 0.1) {
            return 'الفجوة بين القيمة الحالية والمستهدفة صغيرة جداً - قد لا تستحق مشروع تحسين'
          }
          return null
        }
      },
      {
        field: 'unit',
        required: true
      },
      {
        field: 'dataSource',
        required: true,
        minLength: 5,
        customValidator: (value: string) => {
          if (!value.includes('نظام') && !value.includes('تقرير') && !value.includes('قاعدة بيانات')) {
            return 'يُفضل ذكر مصدر البيانات بوضوح (نظام، تقرير، قاعدة بيانات)'
          }
          return null
        }
      },
      {
        field: 'measurementMethod',
        required: true,
        minLength: 10,
        customValidator: (value: string) => {
          if (!value.includes('متوسط') && !value.includes('إجمالي') && !value.includes('نسبة')) {
            return 'يُفضل توضيح طريقة الحساب (متوسط، إجمالي، نسبة)'
          }
          return null
        }
      },
      {
        field: 'improvementDirection',
        required: true,
        customValidator: (value: string, formData: any) => {
          if (!formData.currentValue || !formData.targetValue) {
            return null
          }
          
          const isIncrease = formData.targetValue > formData.currentValue
          const shouldIncrease = value === 'increase'
          
          if (isIncrease !== shouldIncrease) {
            return `اتجاه التحسن لا يتطابق مع القيم: ${
              isIncrease 
                ? 'القيمة المستهدفة أكبر من الحالية - يجب اختيار "زيادة الرقم = تحسن"'
                : 'القيمة المستهدفة أقل من الحالية - يجب اختيار "تقليل الرقم = تحسن"'
            }`
          }
          return null
        }
      }
    ],
    2: [ // Organize
      {
        field: 'responsibleDepartment',
        required: true
      },
      {
        field: 'teamLeader.name',
        required: true,
        minLength: 3,
        pattern: /^[\u0600-\u06FF\s]+$/,
        customValidator: (value: string) => {
          if (value.length < 3) {
            return 'اسم قائد الفريق يجب أن يكون 3 أحرف على الأقل'
          }
          return null
        }
      },
      {
        field: 'teamLeader.phone',
        pattern: /^(05|5)[0-9]{8}$/,
        customValidator: (value: string) => {
          if (value && !value.match(/^(05|5)[0-9]{8}$/)) {
            return 'رقم الجوال يجب أن يبدأ بـ 05 ويتكون من 10 أرقام'
          }
          return null
        }
      },
      {
        field: 'teamLeader.email',
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        customValidator: (value: string) => {
          if (value && !value.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
            return 'البريد الإلكتروني غير صحيح'
          }
          return null
        }
      },
      {
        field: 'teamMembers',
        customValidator: (value: any[]) => {
          if (value.length > 7) {
            return 'يُفضل أن يكون عدد أعضاء الفريق 7 أو أقل للفعالية'
          }
          if (value.length > 0) {
            const incompleteMembers = value.filter(member => !member.name || !member.role)
            if (incompleteMembers.length > 0) {
              return 'يجب ملء اسم ودور كل عضو في الفريق'
            }
          }
          return null
        }
      }
    ],
    3: [ // Clarify
      {
        field: 'processDescription',
        required: true,
        minLength: 100,
        maxLength: 1000,
        customValidator: (value: string) => {
          const steps = value.split(/[.،؛\n]/).filter(step => step.trim().length > 0)
          if (steps.length < 3) {
            return 'يجب أن يحتوي وصف العملية على 3 خطوات على الأقل'
          }
          return null
        }
      },
      {
        field: 'problemScope',
        required: true,
        minLength: 20,
        customValidator: (value: string) => {
          if (!value.includes('فقط') && !value.includes('يشمل') && !value.includes('لا يشمل')) {
            return 'يُفضل توضيح ما يشمله النطاق وما لا يشمله'
          }
          return null
        }
      },
      {
        field: 'affectedOutputs',
        customValidator: (value: string[]) => {
          if (value.length === 0) {
            return 'يُفضل ذكر المخرجات المتأثرة بالمشكلة'
          }
          return null
        }
      }
    ],
    4: [ // Understand
      {
        field: 'analysisMethod',
        required: true
      },
      {
        field: 'rootCause',
        required: true,
        minLength: 20,
        customValidator: (value: string) => {
          if (value.includes('لا أعرف') || value.includes('غير واضح')) {
            return 'يجب تحديد السبب الجذري بوضوح'
          }
          if (!value.includes('عدم') && !value.includes('نقص') && !value.includes('غياب')) {
            return 'السبب الجذري عادة ما يكون عدم وجود شيء أو نقص فيه'
          }
          return null
        }
      },
      {
        field: 'fiveWhysSteps',
        dependencies: ['analysisMethod'],
        customValidator: (value: string[], formData: any) => {
          if (formData.analysisMethod === 'five_whys') {
            const filledSteps = value.filter(step => step.trim().length > 0)
            if (filledSteps.length < 3) {
              return 'يجب ملء 3 خطوات على الأقل في تحليل خمسة لماذا'
            }
            // التحقق من التسلسل المنطقي
            for (let i = 1; i < filledSteps.length; i++) {
              if (filledSteps[i].includes(filledSteps[i-1])) {
                return `الخطوة ${i+1} تكرر نفس محتوى الخطوة ${i}`
              }
            }
          }
          return null
        }
      }
    ],
    5: [ // Select
      {
        field: 'proposedSolutions',
        required: true,
        customValidator: (value: any[]) => {
          if (value.length === 0) {
            return 'يجب إضافة حل واحد على الأقل'
          }
          if (value.length > 5) {
            return 'يُفضل التركيز على 5 حلول كحد أقصى'
          }
          
          const incompleteSolutions = value.filter(solution => 
            !solution.title || !solution.description || !solution.expectedBenefits
          )
          if (incompleteSolutions.length > 0) {
            return 'يجب ملء العنوان والوصف والفوائد المتوقعة لكل حل'
          }
          
          // التحقق من عدم تكرار الحلول
          const titles = value.map(solution => solution.title.toLowerCase())
          const uniqueTitles = [...new Set(titles)]
          if (titles.length !== uniqueTitles.length) {
            return 'يوجد حلول مكررة - يجب أن يكون كل حل مختلف'
          }
          
          return null
        }
      }
    ]
  }

  static validateStep(step: number, formData: any): ValidationResult {
    const stepRules = this.rules[step] || []
    const errors: Record<string, string> = {}
    const warnings: Record<string, string> = {}
    const suggestions: Record<string, string> = {}

    stepRules.forEach(rule => {
      const value = this.getFieldValue(formData, rule.field)
      
      // التحقق من الحقول المطلوبة
      if (rule.required && this.isEmpty(value)) {
        errors[rule.field] = `${this.getFieldLabel(rule.field)} مطلوب`
        return
      }

      // التحقق من الحد الأدنى للطول
      if (rule.minLength && typeof value === 'string' && value.length < rule.minLength) {
        errors[rule.field] = `${this.getFieldLabel(rule.field)} يجب أن يكون ${rule.minLength} أحرف على الأقل`
        return
      }

      // التحقق من الحد الأقصى للطول
      if (rule.maxLength && typeof value === 'string' && value.length > rule.maxLength) {
        errors[rule.field] = `${this.getFieldLabel(rule.field)} يجب أن يكون ${rule.maxLength} حرف كحد أقصى`
        return
      }

      // التحقق من النمط
      if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
        errors[rule.field] = `${this.getFieldLabel(rule.field)} غير صحيح`
        return
      }

      // التحقق المخصص
      if (rule.customValidator) {
        const customError = rule.customValidator(value, formData)
        if (customError) {
          if (customError.includes('يُفضل')) {
            suggestions[rule.field] = customError
          } else {
            errors[rule.field] = customError
          }
        }
      }
    })

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      warnings,
      suggestions
    }
  }

  static validateComplete(formData: any): ValidationResult {
    let allErrors: Record<string, string> = {}
    let allWarnings: Record<string, string> = {}
    let allSuggestions: Record<string, string> = {}

    // التحقق من جميع المراحل
    for (let step = 1; step <= 5; step++) {
      const result = this.validateStep(step, formData)
      allErrors = { ...allErrors, ...result.errors }
      allWarnings = { ...allWarnings, ...result.warnings }
      allSuggestions = { ...allSuggestions, ...result.suggestions }
    }

    // تحققات شاملة إضافية
    const comprehensiveChecks = this.performComprehensiveChecks(formData)
    allErrors = { ...allErrors, ...comprehensiveChecks.errors }
    allWarnings = { ...allWarnings, ...comprehensiveChecks.warnings }
    allSuggestions = { ...allSuggestions, ...comprehensiveChecks.suggestions }

    return {
      isValid: Object.keys(allErrors).length === 0,
      errors: allErrors,
      warnings: allWarnings,
      suggestions: allSuggestions
    }
  }

  private static performComprehensiveChecks(formData: any): ValidationResult {
    const errors: Record<string, string> = {}
    const warnings: Record<string, string> = {}
    const suggestions: Record<string, string> = {}

    // التحقق من التناسق بين المراحل
    if (formData.problemDescription && formData.rootCause) {
      if (!this.isRootCauseRelatedToProblem(formData.problemDescription, formData.rootCause)) {
        warnings.consistency = 'السبب الجذري قد لا يكون مرتبطاً بوضوح بالمشكلة المذكورة'
      }
    }

    // التحقق من جودة الحلول
    if (formData.proposedSolutions && formData.proposedSolutions.length > 0) {
      const lowCostSolutions = formData.proposedSolutions.filter((sol: any) => sol.estimatedCost < 1000)
      if (lowCostSolutions.length === 0) {
        suggestions.solutions = 'يُفضل إضافة حلول منخفضة التكلفة للبدء بها'
      }

      const quickSolutions = formData.proposedSolutions.filter((sol: any) => 
        sol.implementationTime && (sol.implementationTime.includes('أسبوع') || sol.implementationTime.includes('شهر'))
      )
      if (quickSolutions.length === 0) {
        suggestions.implementation = 'يُفضل إضافة حلول سريعة التنفيذ لتحقيق نتائج مبكرة'
      }
    }

    // التحقق من اكتمال الفريق
    if (formData.teamMembers && formData.teamMembers.length === 0) {
      warnings.team = 'يُفضل إضافة أعضاء فريق لضمان تنفيذ المشروع بفعالية'
    }

    return { isValid: true, errors, warnings, suggestions }
  }

  private static isRootCauseRelatedToProblem(problem: string, rootCause: string): boolean {
    // خوارزمية بسيطة للتحقق من الترابط
    const problemWords = problem.toLowerCase().split(' ')
    const rootCauseWords = rootCause.toLowerCase().split(' ')
    
    const commonWords = problemWords.filter(word => 
      rootCauseWords.includes(word) && word.length > 3
    )
    
    return commonWords.length > 0
  }

  private static getFieldValue(formData: any, fieldPath: string): any {
    const keys = fieldPath.split('.')
    let value = formData
    
    for (const key of keys) {
      value = value?.[key]
    }
    
    return value
  }

  private static isEmpty(value: any): boolean {
    if (value === null || value === undefined) return true
    if (typeof value === 'string') return value.trim().length === 0
    if (Array.isArray(value)) return value.length === 0
    if (typeof value === 'number') return value === 0
    return false
  }

  private static getFieldLabel(field: string): string {
    const labels: Record<string, string> = {
      'problemDescription': 'وصف المشكلة',
      'indicatorName': 'اسم المؤشر',
      'currentValue': 'القيمة الحالية',
      'targetValue': 'القيمة المستهدفة',
      'unit': 'وحدة القياس',
      'dataSource': 'مصدر البيانات',
      'measurementMethod': 'طريقة القياس',
      'responsibleDepartment': 'القسم المسؤول',
      'teamLeader.name': 'اسم قائد الفريق',
      'teamLeader.phone': 'رقم جوال قائد الفريق',
      'teamLeader.email': 'بريد قائد الفريق',
      'processDescription': 'وصف العملية',
      'problemScope': 'نطاق المشكلة',
      'analysisMethod': 'طريقة التحليل',
      'rootCause': 'السبب الجذري',
      'fiveWhysSteps': 'خطوات خمسة لماذا',
      'proposedSolutions': 'الحلول المقترحة'
    }
    
    return labels[field] || field
  }

  // دالة لتقييم جودة المشروع
  static assessProjectQuality(formData: any): {
    score: number
    level: 'ممتاز' | 'جيد' | 'متوسط' | 'ضعيف'
    recommendations: string[]
  } {
    let score = 0
    const recommendations: string[] = []

    // تقييم وضوح المشكلة (25 نقطة)
    if (formData.problemDescription && formData.problemDescription.length > 100) {
      score += 15
    } else {
      recommendations.push('قم بتوضيح المشكلة بشكل أكثر تفصيلاً')
    }

    if (formData.calculatedGap > 0) {
      score += 10
    }

    // تقييم تنظيم الفريق (20 نقطة)
    if (formData.teamLeader.name && formData.teamLeader.phone) {
      score += 10
    }
    if (formData.teamMembers && formData.teamMembers.length > 0) {
      score += 10
    } else {
      recommendations.push('أضف أعضاء فريق للمساعدة في التنفيذ')
    }

    // تقييم وضوح العملية (20 نقطة)
    if (formData.processDescription && formData.processDescription.length > 200) {
      score += 15
    } else {
      recommendations.push('قم بوصف العملية بشكل أكثر تفصيلاً')
    }
    if (formData.problemScope) {
      score += 5
    }

    // تقييم تحليل الأسباب (20 نقطة)
    if (formData.rootCause && formData.rootCause.length > 30) {
      score += 15
    } else {
      recommendations.push('قم بتحليل السبب الجذري بشكل أعمق')
    }
    if (formData.analysisMethod === 'five_whys' && formData.fiveWhysSteps.filter((s: string) => s.trim()).length >= 3) {
      score += 5
    }

    // تقييم الحلول (15 نقطة)
    if (formData.proposedSolutions && formData.proposedSolutions.length > 0) {
      score += 10
      if (formData.proposedSolutions.length >= 2) {
        score += 5
      }
    } else {
      recommendations.push('أضف حلول مقترحة للمشكلة')
    }

    // تحديد المستوى
    let level: 'ممتاز' | 'جيد' | 'متوسط' | 'ضعيف'
    if (score >= 80) level = 'ممتاز'
    else if (score >= 65) level = 'جيد'
    else if (score >= 50) level = 'متوسط'
    else level = 'ضعيف'

    return { score, level, recommendations }
  }
} 