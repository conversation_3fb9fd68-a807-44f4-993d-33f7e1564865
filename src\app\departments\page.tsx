'use client'

import { useState, useEffect } from 'react'
import { DepartmentsAPI, UsersAPI, Department, User } from '@/lib/departmentsUsersApi'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { HelpSystem } from '@/components/ui/HelpSystem'

interface DepartmentWithUsers extends Department {
  users: User[]
  children: DepartmentWithUsers[]
}

export default function DepartmentsPage() {
  const [departments, setDepartments] = useState<DepartmentWithUsers[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'departments' | 'users'>('departments')
  const [selectedDepartment, setSelectedDepartment] = useState<string | null>(null)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [showUserForm, setShowUserForm] = useState(false)

  // بيانات النماذج
  const [departmentForm, setDepartmentForm] = useState({
    name: '',
    description: '',
    parent_id: '',
    manager_id: ''
  })

  const [userForm, setUserForm] = useState({
    name: '',
    email: '',
    department_id: '',
    role_id: '',
    phone: '',
    password: ''
  })

  // تحميل البيانات
  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      setError(null)

      // تحميل الأقسام مع الهيكل الهرمي
      const departmentsResponse = await fetch('/api/departments?hierarchy=true&include_users=true&include_children=true')
      const departmentsData = await departmentsResponse.json()

      // تحميل المستخدمين
      const usersResponse = await fetch('/api/users')
      const usersData = await usersResponse.json()

      if (departmentsData.success) {
        setDepartments(departmentsData.data || [])
      } else {
        throw new Error(departmentsData.error || 'حدث خطأ في تحميل الأقسام')
      }

      if (usersData.success) {
        setUsers(usersData.data || [])
      } else {
        throw new Error(usersData.error || 'حدث خطأ في تحميل المستخدمين')
      }

    } catch (err) {
      console.error('Error loading data:', err)
      setError(err instanceof Error ? err.message : 'حدث خطأ في تحميل البيانات')
    } finally {
      setLoading(false)
    }
  }

  // إنشاء قسم جديد
  const handleCreateDepartment = async () => {
    try {
      if (!departmentForm.name.trim()) {
        setError('اسم القسم مطلوب')
        return
      }

      const response = await fetch('/api/departments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: departmentForm.name,
          description: departmentForm.description,
          parent_id: departmentForm.parent_id || undefined,
          manager_id: departmentForm.manager_id || undefined
        })
      })

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'حدث خطأ في إنشاء القسم')
      }

      // إعادة تحميل البيانات
      await loadData()

      // إعادة تعيين النموذج
      setDepartmentForm({
        name: '',
        description: '',
        parent_id: '',
        manager_id: ''
      })
      setShowCreateForm(false)
      setError(null)

    } catch (err) {
      console.error('Error creating department:', err)
      setError(err instanceof Error ? err.message : 'حدث خطأ في إنشاء القسم')
    }
  }

  // إنشاء مستخدم جديد
  const handleCreateUser = async () => {
    try {
      if (!userForm.name.trim() || !userForm.email.trim() || !userForm.department_id || !userForm.role_id) {
        setError('جميع الحقول المطلوبة يجب ملؤها')
        return
      }

      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: userForm.name,
          email: userForm.email,
          department_id: userForm.department_id,
          role_id: userForm.role_id,
          phone: userForm.phone,
          password: userForm.password || 'password123'
        })
      })

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'حدث خطأ في إنشاء المستخدم')
      }

      // إعادة تحميل البيانات
      await loadData()

      // إعادة تعيين النموذج
      setUserForm({
        name: '',
        email: '',
        department_id: '',
        role_id: '',
        phone: '',
        password: ''
      })
      setShowUserForm(false)
      setError(null)

    } catch (err) {
      console.error('Error creating user:', err)
      setError(err instanceof Error ? err.message : 'حدث خطأ في إنشاء المستخدم')
    }
  }

  // عرض قسم واحد مع الأقسام الفرعية
  const renderDepartment = (dept: DepartmentWithUsers, level: number = 0) => {
    const indent = '  '.repeat(level)
    const hasChildren = dept.children && dept.children.length > 0
    const userCount = dept.users?.length || 0

    return (
      <div key={dept.id} className="mb-2">
        <div 
          className={`p-3 rounded-lg border cursor-pointer transition-colors ${
            selectedDepartment === dept.id 
              ? 'border-blue-500 bg-blue-50' 
              : 'border-gray-200 hover:bg-gray-50'
          }`}
          onClick={() => setSelectedDepartment(selectedDepartment === dept.id ? null : dept.id)}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-gray-400">{indent}</span>
              {hasChildren && (
                <span className="text-gray-500">
                  {selectedDepartment === dept.id ? '📂' : '📁'}
                </span>
              )}
              <span className="font-medium">{dept.name}</span>
              {dept.parent && (
                <span className="text-xs text-gray-500">
                  ← {dept.parent.name}
                </span>
              )}
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">
                {userCount} مستخدم
              </span>
              {dept.manager && (
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                  مدير: {dept.manager.name}
                </span>
              )}
            </div>
          </div>
          
          {dept.description && (
            <p className="text-sm text-gray-600 mt-1">{dept.description}</p>
          )}
        </div>

        {/* عرض المستخدمين إذا كان القسم محدد */}
        {selectedDepartment === dept.id && dept.users && dept.users.length > 0 && (
          <div className="mt-2 ml-4 p-3 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-700 mb-2">المستخدمين:</h4>
            <div className="space-y-1">
              {dept.users.map(user => (
                <div key={user.id} className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    <span className={`w-2 h-2 rounded-full ${
                      user.is_active ? 'bg-green-500' : 'bg-red-500'
                    }`}></span>
                    <span>{user.name}</span>
                    <span className="text-gray-500">({user.email})</span>
                  </div>
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    {user.role?.display_name || user.role?.name}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* عرض الأقسام الفرعية */}
        {hasChildren && selectedDepartment === dept.id && (
          <div className="mt-2 ml-4">
            {dept.children.map(child => renderDepartment(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* العنوان والتنقل */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الأقسام والمستخدمين</h1>
          <p className="text-gray-600">إدارة الهيكل التنظيمي والمستخدمين مع الصلاحيات العمودية</p>
        </div>
        <HelpSystem 
          title="دليل إدارة الأقسام والمستخدمين"
          content="هذا النظام يتيح لك إدارة الهيكل التنظيمي للمؤسسة مع الصلاحيات العمودية"
        />
      </div>

      {/* رسالة الخطأ */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2">
            <span className="text-red-600">⚠️</span>
            <span className="text-red-800">{error}</span>
          </div>
        </div>
      )}

      {/* تبويبات */}
      <div className="flex gap-1 mb-6">
        <button
          onClick={() => setActiveTab('departments')}
          className={`px-4 py-2 rounded-lg font-medium transition-colors ${
            activeTab === 'departments'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          الأقسام ({departments.length})
        </button>
        <button
          onClick={() => setActiveTab('users')}
          className={`px-4 py-2 rounded-lg font-medium transition-colors ${
            activeTab === 'users'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          المستخدمين ({users.length})
        </button>
      </div>

      {/* محتوى تبويب الأقسام */}
      {activeTab === 'departments' && (
        <div className="space-y-6">
          {/* أزرار الإجراءات */}
          <div className="flex gap-2">
            <Button
              onClick={() => setShowCreateForm(!showCreateForm)}
              className="bg-green-600 hover:bg-green-700"
            >
              ➕ إنشاء قسم جديد
            </Button>
            <Button
              onClick={loadData}
              variant="outline"
            >
              🔄 تحديث البيانات
            </Button>
          </div>

          {/* نموذج إنشاء قسم */}
          {showCreateForm && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">إنشاء قسم جديد</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    اسم القسم *
                  </label>
                  <Input
                    type="text"
                    value={departmentForm.name}
                    onChange={(e) => setDepartmentForm({...departmentForm, name: e.target.value})}
                    placeholder="أدخل اسم القسم"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    القسم الأب
                  </label>
                  <select
                    value={departmentForm.parent_id}
                    onChange={(e) => setDepartmentForm({...departmentForm, parent_id: e.target.value})}
                    className="w-full p-2 border border-gray-300 rounded-lg"
                  >
                    <option value="">-- اختر القسم الأب --</option>
                    {departments.map(dept => (
                      <option key={dept.id} value={dept.id}>{dept.name}</option>
                    ))}
                  </select>
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    وصف القسم
                  </label>
                  <textarea
                    value={departmentForm.description}
                    onChange={(e) => setDepartmentForm({...departmentForm, description: e.target.value})}
                    placeholder="أدخل وصف القسم"
                    className="w-full p-2 border border-gray-300 rounded-lg"
                    rows={3}
                  />
                </div>
              </div>
              <div className="flex gap-2 mt-4">
                <Button onClick={handleCreateDepartment}>
                  إنشاء القسم
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowCreateForm(false)}
                >
                  إلغاء
                </Button>
              </div>
            </Card>
          )}

          {/* قائمة الأقسام */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">الهيكل التنظيمي</h3>
            {departments.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p>لا توجد أقسام حتى الآن</p>
                <p className="text-sm">انقر على "إنشاء قسم جديد" لإضافة أول قسم</p>
              </div>
            ) : (
              <div className="space-y-2">
                {departments.map(dept => renderDepartment(dept))}
              </div>
            )}
          </Card>
        </div>
      )}

      {/* محتوى تبويب المستخدمين */}
      {activeTab === 'users' && (
        <div className="space-y-6">
          {/* أزرار الإجراءات */}
          <div className="flex gap-2">
            <Button
              onClick={() => setShowUserForm(!showUserForm)}
              className="bg-green-600 hover:bg-green-700"
            >
              ➕ إنشاء مستخدم جديد
            </Button>
            <Button
              onClick={loadData}
              variant="outline"
            >
              🔄 تحديث البيانات
            </Button>
          </div>

          {/* نموذج إنشاء مستخدم */}
          {showUserForm && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">إنشاء مستخدم جديد</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الاسم *
                  </label>
                  <Input
                    type="text"
                    value={userForm.name}
                    onChange={(e) => setUserForm({...userForm, name: e.target.value})}
                    placeholder="أدخل اسم المستخدم"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    البريد الإلكتروني *
                  </label>
                  <Input
                    type="email"
                    value={userForm.email}
                    onChange={(e) => setUserForm({...userForm, email: e.target.value})}
                    placeholder="أدخل البريد الإلكتروني"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    القسم *
                  </label>
                  <select
                    value={userForm.department_id}
                    onChange={(e) => setUserForm({...userForm, department_id: e.target.value})}
                    className="w-full p-2 border border-gray-300 rounded-lg"
                    required
                  >
                    <option value="">-- اختر القسم --</option>
                    {departments.map(dept => (
                      <option key={dept.id} value={dept.id}>{dept.name}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الدور *
                  </label>
                  <select
                    value={userForm.role_id}
                    onChange={(e) => setUserForm({...userForm, role_id: e.target.value})}
                    className="w-full p-2 border border-gray-300 rounded-lg"
                    required
                  >
                    <option value="">-- اختر الدور --</option>
                    <option value="e43a1b08-7212-4bd7-afdd-7788e57cf3ad">مدير النظام</option>
                    <option value="f4301721-8793-4a9a-be19-51dc3b78eb4b">موظف</option>
                    <option value="ef718f6f-fd95-4c52-96fd-ff636289868d">مدير مشروع</option>
                    <option value="00b19a1d-ca86-4e3d-b0e2-346e7be070f0">مدير مكتب المشاريع</option>
                    <option value="33e14066-f262-4e45-8e2e-c45bc8d575eb">المدير التنفيذي</option>
                    <option value="1a6f5096-88b3-41ef-8350-6e5fb189bc85">مدير إدارة التخطيط</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    رقم الهاتف
                  </label>
                  <Input
                    type="tel"
                    value={userForm.phone}
                    onChange={(e) => setUserForm({...userForm, phone: e.target.value})}
                    placeholder="أدخل رقم الهاتف"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    كلمة المرور
                  </label>
                  <Input
                    type="password"
                    value={userForm.password}
                    onChange={(e) => setUserForm({...userForm, password: e.target.value})}
                    placeholder="اتركه فارغاً للكلمة الافتراضية"
                  />
                </div>
              </div>
              <div className="flex gap-2 mt-4">
                <Button onClick={handleCreateUser}>
                  إنشاء المستخدم
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowUserForm(false)}
                >
                  إلغاء
                </Button>
              </div>
            </Card>
          )}

          {/* قائمة المستخدمين */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">قائمة المستخدمين</h3>
            {users.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p>لا يوجد مستخدمين حتى الآن</p>
                <p className="text-sm">انقر على "إنشاء مستخدم جديد" لإضافة أول مستخدم</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right py-2">الاسم</th>
                      <th className="text-right py-2">البريد الإلكتروني</th>
                      <th className="text-right py-2">القسم</th>
                      <th className="text-right py-2">الدور</th>
                      <th className="text-right py-2">الحالة</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.map(user => (
                      <tr key={user.id} className="border-b">
                        <td className="py-2">{user.name}</td>
                        <td className="py-2">{user.email}</td>
                        <td className="py-2">{user.department?.name || 'غير محدد'}</td>
                        <td className="py-2">
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            {user.role?.display_name || user.role?.name}
                          </span>
                        </td>
                        <td className="py-2">
                          <span className={`text-xs px-2 py-1 rounded ${
                            user.is_active 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {user.is_active ? 'نشط' : 'معطل'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </Card>
        </div>
      )}
    </div>
  )
} 