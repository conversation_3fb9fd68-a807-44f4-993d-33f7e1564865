'use client'

import React, { useState, useEffect } from 'react'
import { Shield, <PERSON>, Setting<PERSON>, Eye, Edit, Check, X, TrendingUp, AlertCircle } from 'lucide-react'
import { Card } from '@/components/ui/Card'

interface PermissionSummary {
  totalRoles: number
  totalUsers: number
  activeUsers: number
  inactiveUsers: number
  rolesDistribution: Array<{
    role: string
    display_name: string
    count: number
    percentage: number
    color: string
  }>
  permissionsMatrix: Array<{
    permission: string
    display_name: string
    roles: Array<{
      role: string
      display_name: string
      actions: string[]
    }>
  }>
  securityAlerts: Array<{
    type: 'warning' | 'info' | 'success'
    message: string
  }>
}

export default function PermissionsOverview() {
  const [summary, setSummary] = useState<PermissionSummary | null>(null)
  const [loading, setLoading] = useState(true)

  // بيانات تجريبية
  const mockSummary: PermissionSummary = {
    totalRoles: 5,
    totalUsers: 36,
    activeUsers: 34,
    inactiveUsers: 2,
    rolesDistribution: [
      { role: 'employee', display_name: 'موظف', count: 25, percentage: 69, color: 'bg-gray-500' },
      { role: 'project_manager', display_name: 'مدير مشروع', count: 5, percentage: 14, color: 'bg-green-500' },
      { role: 'pmo_manager', display_name: 'مدير مكتب المشاريع', count: 3, percentage: 8, color: 'bg-blue-500' },
      { role: 'admin', display_name: 'مدير النظام', count: 2, percentage: 6, color: 'bg-red-500' },
      { role: 'planning_manager', display_name: 'مدير إدارة التخطيط', count: 1, percentage: 3, color: 'bg-purple-500' }
    ],
    permissionsMatrix: [
      {
        permission: 'projects',
        display_name: 'إدارة المشاريع',
        roles: [
          { role: 'admin', display_name: 'مدير النظام', actions: ['read', 'write', 'approve', 'delete'] },
          { role: 'pmo_manager', display_name: 'مدير مكتب المشاريع', actions: ['read', 'write', 'approve'] },
          { role: 'planning_manager', display_name: 'مدير إدارة التخطيط', actions: ['read', 'approve'] },
          { role: 'project_manager', display_name: 'مدير مشروع', actions: ['read', 'write'] },
          { role: 'employee', display_name: 'موظف', actions: ['read'] }
        ]
      },
      {
        permission: 'requests',
        display_name: 'طلبات المشاريع',
        roles: [
          { role: 'admin', display_name: 'مدير النظام', actions: ['read', 'write', 'approve', 'delete'] },
          { role: 'pmo_manager', display_name: 'مدير مكتب المشاريع', actions: ['read', 'write', 'approve'] },
          { role: 'planning_manager', display_name: 'مدير إدارة التخطيط', actions: ['read', 'approve'] },
          { role: 'project_manager', display_name: 'مدير مشروع', actions: ['read'] },
          { role: 'employee', display_name: 'موظف', actions: ['read', 'write'] }
        ]
      },
      {
        permission: 'users',
        display_name: 'إدارة المستخدمين',
        roles: [
          { role: 'admin', display_name: 'مدير النظام', actions: ['read', 'write', 'delete'] },
          { role: 'pmo_manager', display_name: 'مدير مكتب المشاريع', actions: ['read'] }
        ]
      },
      {
        permission: 'reports',
        display_name: 'التقارير',
        roles: [
          { role: 'admin', display_name: 'مدير النظام', actions: ['read', 'write', 'export'] },
          { role: 'pmo_manager', display_name: 'مدير مكتب المشاريع', actions: ['read', 'write'] },
          { role: 'planning_manager', display_name: 'مدير إدارة التخطيط', actions: ['read'] },
          { role: 'project_manager', display_name: 'مدير مشروع', actions: ['read'] }
        ]
      }
    ],
    securityAlerts: [
      { type: 'success', message: 'جميع المستخدمين لديهم أدوار محددة' },
      { type: 'info', message: 'آخر تحديث للصلاحيات: منذ 3 أيام' },
      { type: 'warning', message: 'يوجد 2 مستخدم غير نشط منذ أكثر من 30 يوم' }
    ]
  }

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setSummary(mockSummary)
      setLoading(false)
    }, 1000)
  }, [])

  const actionLabels: Record<string, string> = {
    read: 'عرض',
    write: 'تعديل',
    approve: 'اعتماد',
    delete: 'حذف',
    assign: 'تعيين',
    export: 'تصدير'
  }

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'read': return <Eye className="w-3 h-3" />
      case 'write': return <Edit className="w-3 h-3" />
      case 'approve': return <Check className="w-3 h-3" />
      case 'delete': return <X className="w-3 h-3" />
      default: return <Settings className="w-3 h-3" />
    }
  }

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'warning': return <AlertCircle className="w-5 h-5 text-yellow-500" />
      case 'success': return <Check className="w-5 h-5 text-green-500" />
      case 'info': return <Shield className="w-5 h-5 text-blue-500" />
      default: return <Shield className="w-5 h-5 text-gray-500" />
    }
  }

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'warning': return 'bg-yellow-50 border-yellow-200 text-yellow-800'
      case 'success': return 'bg-green-50 border-green-200 text-green-800'
      case 'info': return 'bg-blue-50 border-blue-200 text-blue-800'
      default: return 'bg-gray-50 border-gray-200 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!summary) return null

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">نظرة عامة على الصلاحيات</h2>
        <p className="text-gray-600 mt-1">
          ملخص شامل لحالة الأدوار والصلاحيات في النظام
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي الأدوار</p>
              <p className="text-3xl font-bold text-gray-900">{summary.totalRoles}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Shield className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي المستخدمين</p>
              <p className="text-3xl font-bold text-gray-900">{summary.totalUsers}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <Users className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">المستخدمون النشطون</p>
              <p className="text-3xl font-bold text-green-600">{summary.activeUsers}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">المستخدمون غير النشطون</p>
              <p className="text-3xl font-bold text-red-600">{summary.inactiveUsers}</p>
            </div>
            <div className="p-3 bg-red-100 rounded-full">
              <AlertCircle className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Security Alerts */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">تنبيهات الأمان</h3>
        <div className="space-y-3">
          {summary.securityAlerts.map((alert, index) => (
            <div
              key={index}
              className={`flex items-center gap-3 p-3 border rounded-lg ${getAlertColor(alert.type)}`}
            >
              {getAlertIcon(alert.type)}
              <span className="text-sm">{alert.message}</span>
            </div>
          ))}
        </div>
      </Card>

      {/* Roles Distribution */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">توزيع الأدوار</h3>
        <div className="space-y-4">
          {summary.rolesDistribution.map((role) => (
            <div key={role.role} className="space-y-2">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${role.color}`}></div>
                  <span className="font-medium text-gray-900">{role.display_name}</span>
                </div>
                <div className="text-sm text-gray-600">
                  {role.count} مستخدم ({role.percentage}%)
                </div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${role.color}`}
                  style={{ width: `${role.percentage}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Permissions Matrix */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">مصفوفة الصلاحيات</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-right py-3 px-4 font-medium text-gray-900">الصلاحية</th>
                {summary.rolesDistribution.map((role) => (
                  <th key={role.role} className="text-center py-3 px-2 font-medium text-gray-900">
                    <div className="text-xs">{role.display_name}</div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {summary.permissionsMatrix.map((permission) => (
                <tr key={permission.permission} className="border-b border-gray-100">
                  <td className="py-4 px-4 font-medium text-gray-900">
                    {permission.display_name}
                  </td>
                  {summary.rolesDistribution.map((roleInfo) => {
                    const rolePermission = permission.roles.find(r => r.role === roleInfo.role)
                    return (
                      <td key={roleInfo.role} className="py-4 px-2 text-center">
                        {rolePermission ? (
                          <div className="flex flex-wrap justify-center gap-1">
                            {rolePermission.actions.map((action) => (
                              <div
                                key={action}
                                className="flex items-center gap-1 bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs"
                                title={actionLabels[action]}
                              >
                                {getActionIcon(action)}
                                <span className="hidden sm:inline">{actionLabels[action]}</span>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <span className="text-gray-400 text-xs">لا يوجد</span>
                        )}
                      </td>
                    )
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Quick Actions */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">الإجراءات السريعة</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Shield className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">إضافة دور جديد</h4>
                <p className="text-sm text-gray-600">إنشاء دور جديد مع صلاحيات مخصصة</p>
              </div>
            </div>
          </div>

          <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Users className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">تعيين أدوار المستخدمين</h4>
                <p className="text-sm text-gray-600">ربط المستخدمين بالأدوار المناسبة</p>
              </div>
            </div>
          </div>

          <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Settings className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">تحديث الصلاحيات</h4>
                <p className="text-sm text-gray-600">تعديل صلاحيات الأدوار الموجودة</p>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
} 