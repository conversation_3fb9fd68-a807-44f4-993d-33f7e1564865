'use client'

import React, { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input, Select } from '@/components/ui/Input'
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Download, 
  Filter,
  Calendar,
  Target,
  Activity,
  FileText,
  Users,
  Clock,
  Award
} from 'lucide-react'

interface KpiReportData {
  kpi_id: string
  kpi_name: string
  project_name: string
  project_type: string
  current_value: number
  target_value: number
  baseline_value: number
  unit: string
  improvement_percentage: number
  status: 'on_track' | 'at_risk' | 'behind'
  last_measurement: string
  measurements_count: number
}

interface ReportFilters {
  dateRange: string
  projectType: string
  kpiType: string
  status: string
}

export default function KpiReportsPage() {
  const [reportData, setReportData] = useState<KpiReportData[]>([])
  const [filters, setFilters] = useState<ReportFilters>({
    dateRange: '30',
    projectType: 'all',
    kpiType: 'all',
    status: 'all'
  })
  const [isLoading, setIsLoading] = useState(true)
  const [selectedView, setSelectedView] = useState<'summary' | 'detailed'>('summary')

  useEffect(() => {
    loadReportData()
  }, [filters])

  const loadReportData = async () => {
    try {
      setIsLoading(true)
      
      // TODO: استدعاء API لجلب بيانات التقارير
      const mockData: KpiReportData[] = [
        {
          kpi_id: '1',
          kpi_name: 'وقت انتظار المريض في الطوارئ',
          project_name: 'تحسين نظام الطوارئ',
          project_type: 'تحسين العمليات',
          current_value: 32,
          target_value: 20,
          baseline_value: 45,
          unit: 'دقيقة',
          improvement_percentage: 28.9,
          status: 'on_track',
          last_measurement: '2024-01-15T10:30:00Z',
          measurements_count: 15
        },
        {
          kpi_id: '2',
          kpi_name: 'عدد المرضى المعالجين يومياً',
          project_name: 'تطوير نظام إدارة المرضى',
          project_type: 'تحسين العمليات',
          current_value: 135,
          target_value: 150,
          baseline_value: 120,
          unit: 'مريض',
          improvement_percentage: 12.5,
          status: 'at_risk',
          last_measurement: '2024-01-14T16:45:00Z',
          measurements_count: 12
        },
        {
          kpi_id: '3',
          kpi_name: 'نسبة رضا المرضى',
          project_name: 'اقتراح تحسين خدمة العملاء',
          project_type: 'مقترح',
          current_value: 85,
          target_value: 90,
          baseline_value: 70,
          unit: '%',
          improvement_percentage: 21.4,
          status: 'on_track',
          last_measurement: '2024-01-13T14:20:00Z',
          measurements_count: 8
        }
      ]
      
      setReportData(mockData)
    } catch (error) {
      console.error('Error loading report data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const updateFilter = (key: keyof ReportFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'on_track': return 'text-green-600'
      case 'at_risk': return 'text-yellow-600'
      case 'behind': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'on_track': return 'على المسار'
      case 'at_risk': return 'في خطر'
      case 'behind': return 'متأخر'
      default: return 'غير محدد'
    }
  }

  const getStatusBg = (status: string) => {
    switch (status) {
      case 'on_track': return 'bg-green-100 text-green-800'
      case 'at_risk': return 'bg-yellow-100 text-yellow-800'
      case 'behind': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const calculateSummaryStats = () => {
    const total = reportData.length
    const onTrack = reportData.filter(item => item.status === 'on_track').length
    const atRisk = reportData.filter(item => item.status === 'at_risk').length
    const behind = reportData.filter(item => item.status === 'behind').length
    const avgImprovement = reportData.reduce((sum, item) => sum + item.improvement_percentage, 0) / total

    return { total, onTrack, atRisk, behind, avgImprovement }
  }

  const exportReport = () => {
    // TODO: تنفيذ تصدير التقرير
    console.log('Exporting report...')
  }

  const stats = calculateSummaryStats()

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* العنوان */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <FileText className="w-8 h-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              تقارير المؤشرات
            </h1>
            <p className="text-gray-600">
              تحليل شامل لأداء المؤشرات عبر جميع المشاريع
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="flex bg-gray-100 rounded-lg p-1">
            <Button
              variant={selectedView === 'summary' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setSelectedView('summary')}
            >
              ملخص
            </Button>
            <Button
              variant={selectedView === 'detailed' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setSelectedView('detailed')}
            >
              تفصيلي
            </Button>
          </div>
          
          <Button
            variant="primary"
            onClick={exportReport}
            className="flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            تصدير التقرير
          </Button>
        </div>
      </div>

      {/* الفلاتر */}
      <Card className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <Filter className="w-5 h-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">فلاتر التقرير</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Select
            label="الفترة الزمنية"
            value={filters.dateRange}
            onChange={(e) => updateFilter('dateRange', e.target.value)}
          >
            <option value="7">آخر 7 أيام</option>
            <option value="30">آخر 30 يوم</option>
            <option value="90">آخر 3 أشهر</option>
            <option value="365">آخر سنة</option>
          </Select>
          
          <Select
            label="نوع المشروع"
            value={filters.projectType}
            onChange={(e) => updateFilter('projectType', e.target.value)}
          >
            <option value="all">جميع الأنواع</option>
            <option value="تحسين العمليات">تحسين العمليات</option>
            <option value="مقترح">مقترح</option>
            <option value="كويك وين">كويك وين</option>
          </Select>
          
          <Select
            label="نوع المؤشر"
            value={filters.kpiType}
            onChange={(e) => updateFilter('kpiType', e.target.value)}
          >
            <option value="all">جميع المؤشرات</option>
            <option value="time">مؤشرات الوقت</option>
            <option value="count">مؤشرات العدد</option>
            <option value="percentage">مؤشرات النسبة</option>
            <option value="cost">مؤشرات التكلفة</option>
          </Select>
          
          <Select
            label="الحالة"
            value={filters.status}
            onChange={(e) => updateFilter('status', e.target.value)}
          >
            <option value="all">جميع الحالات</option>
            <option value="on_track">على المسار</option>
            <option value="at_risk">في خطر</option>
            <option value="behind">متأخر</option>
          </Select>
        </div>
      </Card>

      {/* الإحصائيات الموجزة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Target className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {stats.total}
              </div>
              <div className="text-sm text-gray-600">إجمالي المؤشرات</div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">
                +{stats.avgImprovement.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">متوسط التحسن</div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Award className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">
                {stats.onTrack}
              </div>
              <div className="text-sm text-gray-600">على المسار</div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <Activity className="w-6 h-6 text-red-600" />
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">
                {stats.atRisk + stats.behind}
              </div>
              <div className="text-sm text-gray-600">تحتاج متابعة</div>
            </div>
          </div>
        </Card>
      </div>

      {/* محتوى التقرير */}
      {selectedView === 'summary' ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* أفضل المؤشرات أداءً */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-green-600" />
              أفضل المؤشرات أداءً
            </h3>
            <div className="space-y-3">
              {reportData
                .sort((a, b) => b.improvement_percentage - a.improvement_percentage)
                .slice(0, 3)
                .map(item => (
                  <div key={item.kpi_id} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{item.kpi_name}</div>
                      <div className="text-sm text-gray-600">{item.project_name}</div>
                    </div>
                    <div className="text-lg font-bold text-green-600">
                      +{item.improvement_percentage.toFixed(1)}%
                    </div>
                  </div>
                ))}
            </div>
          </Card>

          {/* المؤشرات التي تحتاج متابعة */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <TrendingDown className="w-5 h-5 text-red-600" />
              تحتاج متابعة
            </h3>
            <div className="space-y-3">
              {reportData
                .filter(item => item.status === 'at_risk' || item.status === 'behind')
                .slice(0, 3)
                .map(item => (
                  <div key={item.kpi_id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{item.kpi_name}</div>
                      <div className="text-sm text-gray-600">{item.project_name}</div>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded-full ${getStatusBg(item.status)}`}>
                      {getStatusText(item.status)}
                    </span>
                  </div>
                ))}
            </div>
          </Card>
        </div>
      ) : (
        /* العرض التفصيلي */
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            تفاصيل جميع المؤشرات
          </h3>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-right py-3 px-4 font-medium text-gray-900">المؤشر</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">المشروع</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">القيمة الحالية</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">الهدف</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">التحسن</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">الحالة</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">آخر قياس</th>
                </tr>
              </thead>
              <tbody>
                {reportData.map(item => (
                  <tr key={item.kpi_id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div className="font-medium text-gray-900">{item.kpi_name}</div>
                      <div className="text-sm text-gray-600">{item.measurements_count} قياس</div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="font-medium text-gray-900">{item.project_name}</div>
                      <div className="text-sm text-gray-600">{item.project_type}</div>
                    </td>
                    <td className="py-3 px-4">
                      <span className="font-medium">{item.current_value} {item.unit}</span>
                    </td>
                    <td className="py-3 px-4">
                      <span className="font-medium">{item.target_value} {item.unit}</span>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`font-medium ${
                        item.improvement_percentage > 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {item.improvement_percentage > 0 ? '+' : ''}{item.improvement_percentage.toFixed(1)}%
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusBg(item.status)}`}>
                        {getStatusText(item.status)}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-1 text-sm text-gray-600">
                        <Clock className="w-4 h-4" />
                        {new Date(item.last_measurement).toLocaleDateString('ar-SA')}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      )}
    </div>
  )
} 