'use client'

import React from 'react'
import { Loader2, AlertCircle, CheckCircle, Info, AlertTriangle } from 'lucide-react'
import { ComponentProps } from './SharedTypes'
import { cn } from './SharedUtils'

// مكون التحميل
export function LoadingSpinner({ 
  className = '', 
  size = 'md',
  text = 'جاري التحميل...' 
}: ComponentProps & { 
  size?: 'sm' | 'md' | 'lg'
  text?: string 
}) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6', 
    lg: 'w-8 h-8'
  }

  return (
    <div className={cn('flex items-center justify-center gap-2', className)}>
      <Loader2 className={cn('animate-spin', sizeClasses[size])} />
      {text && <span className="text-sm text-gray-600">{text}</span>}
    </div>
  )
}

// مكون الحالة الفارغة
export function EmptyState({
  title,
  description,
  icon: Icon = Info,
  action,
  className = ''
}: ComponentProps & {
  title: string
  description?: string
  icon?: React.ComponentType<any>
  action?: React.ReactNode
}) {
  return (
    <div className={cn('flex flex-col items-center justify-center p-8 text-center', className)}>
      <Icon className="w-12 h-12 text-gray-400 mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      {description && (
        <p className="text-gray-600 mb-4 max-w-md">{description}</p>
      )}
      {action}
    </div>
  )
}

// مكون الخطأ
export function ErrorState({
  title = 'حدث خطأ',
  description = 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
  onRetry,
  className = ''
}: ComponentProps & {
  title?: string
  description?: string
  onRetry?: () => void
}) {
  return (
    <div className={cn('flex flex-col items-center justify-center p-8 text-center', className)}>
      <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600 mb-4 max-w-md">{description}</p>
      {onRetry && (
        <button
          onClick={onRetry}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          إعادة المحاولة
        </button>
      )}
    </div>
  )
}

// مكون الشارة
export function Badge({
  children,
  variant = 'default',
  size = 'md',
  className = ''
}: ComponentProps & {
  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info'
  size?: 'sm' | 'md' | 'lg'
}) {
  const variantClasses = {
    default: 'bg-gray-100 text-gray-800',
    success: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    danger: 'bg-red-100 text-red-800',
    info: 'bg-blue-100 text-blue-800'
  }

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1 text-sm',
    lg: 'px-4 py-2 text-base'
  }

  return (
    <span className={cn(
      'inline-flex items-center rounded-full font-medium',
      variantClasses[variant],
      sizeClasses[size],
      className
    )}>
      {children}
    </span>
  )
}

// مكون الإحصائية
export function StatCard({
  title,
  value,
  change,
  changeType = 'neutral',
  icon: Icon,
  color = 'blue',
  className = ''
}: ComponentProps & {
  title: string
  value: string | number
  change?: number
  changeType?: 'increase' | 'decrease' | 'neutral'
  icon?: React.ComponentType<any>
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple'
}) {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600',
    green: 'bg-green-50 text-green-600',
    red: 'bg-red-50 text-red-600',
    yellow: 'bg-yellow-50 text-yellow-600',
    purple: 'bg-purple-50 text-purple-600'
  }

  const changeIcon = changeType === 'increase' ? '↗' : changeType === 'decrease' ? '↘' : ''
  const changeColor = changeType === 'increase' ? 'text-green-600' : changeType === 'decrease' ? 'text-red-600' : 'text-gray-600'

  return (
    <div className={cn('bg-white rounded-lg shadow p-6', className)}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {change !== undefined && (
            <p className={cn('text-sm font-medium', changeColor)}>
              {changeIcon} {Math.abs(change)}%
            </p>
          )}
        </div>
        {Icon && (
          <div className={cn('p-3 rounded-full', colorClasses[color])}>
            <Icon className="w-6 h-6" />
          </div>
        )}
      </div>
    </div>
  )
}

// مكون الفاصل
export function Divider({ 
  orientation = 'horizontal',
  className = '' 
}: ComponentProps & {
  orientation?: 'horizontal' | 'vertical'
}) {
  return (
    <div className={cn(
      'bg-gray-200',
      orientation === 'horizontal' ? 'h-px w-full' : 'w-px h-full',
      className
    )} />
  )
}

// مكون الحاوية
export function Container({
  children,
  size = 'default',
  className = ''
}: ComponentProps & {
  size?: 'sm' | 'default' | 'lg' | 'xl' | 'full'
}) {
  const sizeClasses = {
    sm: 'max-w-2xl',
    default: 'max-w-4xl',
    lg: 'max-w-6xl',
    xl: 'max-w-7xl',
    full: 'max-w-full'
  }

  return (
    <div className={cn('mx-auto px-4 sm:px-6 lg:px-8', sizeClasses[size], className)}>
      {children}
    </div>
  )
}

// مكون الشبكة
export function Grid({
  children,
  cols = 1,
  gap = 4,
  className = ''
}: ComponentProps & {
  cols?: 1 | 2 | 3 | 4 | 5 | 6
  gap?: 1 | 2 | 3 | 4 | 5 | 6 | 8
}) {
  const colsClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
    6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
  }

  const gapClasses = {
    1: 'gap-1',
    2: 'gap-2',
    3: 'gap-3',
    4: 'gap-4',
    5: 'gap-5',
    6: 'gap-6',
    8: 'gap-8'
  }

  return (
    <div className={cn('grid', colsClasses[cols], gapClasses[gap], className)}>
      {children}
    </div>
  )
}

// مكون الكشف
export function Accordion({
  items,
  defaultOpen = [],
  className = ''
}: ComponentProps & {
  items: Array<{
    id: string
    title: string
    content: React.ReactNode
  }>
  defaultOpen?: string[]
}) {
  const [openItems, setOpenItems] = React.useState<string[]>(defaultOpen)

  const toggleItem = (id: string) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  return (
    <div className={cn('space-y-2', className)}>
      {items.map(item => (
        <div key={item.id} className="border border-gray-200 rounded-lg">
          <button
            onClick={() => toggleItem(item.id)}
            className="w-full px-4 py-3 text-right font-medium text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
          >
            <div className="flex items-center justify-between">
              <span>{item.title}</span>
              <span className={cn(
                'transform transition-transform duration-200',
                openItems.includes(item.id) ? 'rotate-180' : ''
              )}>
                ▼
              </span>
            </div>
          </button>
          {openItems.includes(item.id) && (
            <div className="px-4 pb-3 text-gray-700">
              {item.content}
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

// مكون التنبيه
export function Alert({
  children,
  variant = 'info',
  title,
  onClose,
  className = ''
}: ComponentProps & {
  variant?: 'info' | 'success' | 'warning' | 'danger'
  title?: string
  onClose?: () => void
}) {
  const variantClasses = {
    info: 'bg-blue-50 border-blue-200 text-blue-800',
    success: 'bg-green-50 border-green-200 text-green-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    danger: 'bg-red-50 border-red-200 text-red-800'
  }

  const icons = {
    info: Info,
    success: CheckCircle,
    warning: AlertTriangle,
    danger: AlertCircle
  }

  const Icon = icons[variant]

  return (
    <div className={cn(
      'flex items-start gap-3 p-4 rounded-lg border',
      variantClasses[variant],
      className
    )}>
      <Icon className="w-5 h-5 flex-shrink-0 mt-0.5" />
      <div className="flex-1">
        {title && (
          <h4 className="font-medium mb-1">{title}</h4>
        )}
        <div className="text-sm">{children}</div>
      </div>
      {onClose && (
        <button
          onClick={onClose}
          className="flex-shrink-0 p-1 rounded-full hover:bg-black hover:bg-opacity-10 transition-colors"
        >
          ×
        </button>
      )}
    </div>
  )
} 