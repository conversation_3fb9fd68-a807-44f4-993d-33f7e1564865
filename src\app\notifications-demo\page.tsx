'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { Form<PERSON>lert, Success<PERSON>lert, <PERSON>rror<PERSON>lert, WarningAlert, InfoAlert, ValidationAlert } from '@/components/ui/FormAlert'

export default function NotificationsDemoPage() {
  const [showAlert, setShowAlert] = useState<string | null>(null)
  const [validationErrors, setValidationErrors] = useState<string[]>([])

  const handleValidationDemo = () => {
    const errors = [
      'البريد الإلكتروني مطلوب',
      'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
      'رقم الهاتف غير صحيح'
    ]
    setValidationErrors(errors)
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            عرض توضيحي لنظام الإخطارات
          </h1>
          <p className="text-gray-600">
            أنواع التنبيهات والإشعارات المتاحة في النظام
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Form Alerts */}
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold">تنبيهات النماذج</h2>
              <p className="text-sm text-gray-600">تنبيهات ثابتة داخل النماذج</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Button onClick={() => setShowAlert('success')} variant="success" size="sm">
                  تنبيه نجاح
                </Button>
                <Button onClick={() => setShowAlert('error')} variant="danger" size="sm">
                  تنبيه خطأ
                </Button>
                <Button onClick={() => setShowAlert('warning')} variant="warning" size="sm">
                  تنبيه تحذير
                </Button>
                <Button onClick={() => setShowAlert('info')} variant="secondary" size="sm">
                  تنبيه معلومات
                </Button>
                <Button onClick={handleValidationDemo} variant="secondary" size="sm">
                  أخطاء التحقق
                </Button>
              </div>

              {/* عرض التنبيهات */}
              <div className="mt-6 space-y-4">
                {showAlert === 'success' && (
                  <SuccessAlert
                    title="تم بنجاح!"
                    message="تم حفظ البيانات بنجاح"
                    onClose={() => setShowAlert(null)}
                  />
                )}
                {showAlert === 'error' && (
                  <ErrorAlert
                    title="حدث خطأ!"
                    message="فشل في حفظ البيانات، يرجى المحاولة مرة أخرى"
                    onClose={() => setShowAlert(null)}
                  />
                )}
                {showAlert === 'warning' && (
                  <WarningAlert
                    title="تحذير!"
                    message="يرجى مراجعة البيانات المدخلة قبل الحفظ"
                    onClose={() => setShowAlert(null)}
                  />
                )}
                {showAlert === 'info' && (
                  <InfoAlert
                    title="معلومة"
                    message="تم تحديث النظام إلى الإصدار الجديد"
                    onClose={() => setShowAlert(null)}
                  />
                )}
                {validationErrors.length > 0 && (
                  <ValidationAlert
                    errors={validationErrors}
                    onClose={() => setValidationErrors([])}
                  />
                )}
              </div>
            </CardContent>
          </Card>

          {/* معلومات إضافية */}
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold">معلومات النظام</h2>
              <p className="text-sm text-gray-600">تفاصيل حول نظام الإشعارات</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h3 className="font-semibold text-blue-900">أنواع الإشعارات</h3>
                  <ul className="mt-2 text-sm text-blue-800 space-y-1">
                    <li>• إشعارات النجاح (خضراء)</li>
                    <li>• إشعارات الخطأ (حمراء)</li>
                    <li>• إشعارات التحذير (صفراء)</li>
                    <li>• إشعارات المعلومات (زرقاء)</li>
                  </ul>
                </div>

                <div className="p-4 bg-green-50 rounded-lg">
                  <h3 className="font-semibold text-green-900">الميزات</h3>
                  <ul className="mt-2 text-sm text-green-800 space-y-1">
                    <li>• تصميم متجاوب</li>
                    <li>• إغلاق تلقائي</li>
                    <li>• أيقونات واضحة</li>
                    <li>• ألوان مميزة</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 