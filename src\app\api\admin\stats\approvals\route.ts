import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'

export async function GET(request: NextRequest) {
  try {
    const { data: approvals, error: approvalsError } = await supabaseAdmin
      .from('approvals')
      .select('id, status, created_at, approved_at')

    if (approvalsError) {
      console.error('Error fetching approvals:', approvalsError)
      return NextResponse.json(
        { success: false, error: 'فشل في جلب إحصائيات الموافقات' },
        { status: 500 }
      )
    }

    const pending = approvals?.filter(a => a.status === 'pending').length || 0
    const approved = approvals?.filter(a => a.status === 'approved').length || 0
    const rejected = approvals?.filter(a => a.status === 'rejected').length || 0
    const total = approvals?.length || 0

    // حساب الموافقات المتأخرة (أكثر من 3 أيام)
    const threeDaysAgo = new Date()
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3)
    
    const overdue = approvals?.filter(a => {
      if (a.status !== 'pending' || !a.created_at) return false
      const createdAt = new Date(a.created_at)
      return createdAt < threeDaysAgo
    }).length || 0

    // حساب متوسط وقت الموافقة
    const approvedWithTimes = approvals?.filter(a => 
      a.status === 'approved' && a.created_at && a.approved_at
    ) || []
    
    let avgApprovalTime = 0
    if (approvedWithTimes.length > 0) {
      const totalTime = approvedWithTimes.reduce((sum, a) => {
        const created = new Date(a.created_at!)
        const approved = new Date(a.approved_at!)
        const diffDays = Math.ceil((approved.getTime() - created.getTime()) / (1000 * 60 * 60 * 24))
        return sum + diffDays
      }, 0)
      avgApprovalTime = Math.round((totalTime / approvedWithTimes.length) * 10) / 10
    }

    const approvalRate = total > 0 ? Math.round((approved / total) * 100 * 10) / 10 : 0

    const stats = {
      pending,
      overdue,
      avg_approval_time: avgApprovalTime,
      approval_rate: approvalRate
    }

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('Error in approvals stats API:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في الخادم' },
      { status: 500 }
    )
  }
} 