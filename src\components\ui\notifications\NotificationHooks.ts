import { useNotifications } from './NotificationSystem'
import { NotificationType } from './NotificationTypes'

// هوك مساعد للإشعارات السريعة
export function useNotificationHelpers() {
  const { addToast, addSystemNotification, addPopupNotification } = useNotifications()

  // إشعارات Toast سريعة
  const showSuccess = (title: string, message?: string, duration?: number) => {
    return addToast({
      type: 'success',
      title,
      message,
      duration: duration ?? 3000
    })
  }

  const showError = (title: string, message?: string, duration?: number) => {
    return addToast({
      type: 'error',
      title,
      message,
      duration: duration ?? 5000
    })
  }

  const showWarning = (title: string, message?: string, duration?: number) => {
    return addToast({
      type: 'warning',
      title,
      message,
      duration: duration ?? 4000
    })
  }

  const showInfo = (title: string, message?: string, duration?: number) => {
    return addToast({
      type: 'info',
      title,
      message,
      duration: duration ?? 3000
    })
  }

  const showLoading = (title: string, message?: string) => {
    return addToast({
      type: 'loading',
      title,
      message,
      duration: 0,
      persistent: true
    })
  }

  // إشعارات النظام
  const notifySystem = (
    type: NotificationType,
    title: string,
    message: string,
    priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium'
  ) => {
    return addSystemNotification({
      type,
      title,
      message,
      priority
    })
  }

  // إشعارات منبثقة
  const showPopup = (
    type: NotificationType,
    title: string,
    message: string,
    duration?: number
  ) => {
    return addPopupNotification({
      type,
      title,
      message,
      duration: duration ?? 5000
    })
  }

  // إشعارات خاصة بالمشاريع
  const notifyProjectUpdate = (projectName: string, status: string) => {
    return showSuccess(
      'تحديث المشروع',
      `تم تحديث حالة مشروع "${projectName}" إلى ${status}`
    )
  }

  const notifyApprovalRequired = (requestTitle: string, approverName: string) => {
    return notifySystem(
      'warning',
      'طلب موافقة',
      `طلب "${requestTitle}" يحتاج موافقة من ${approverName}`,
      'high'
    )
  }

  const notifyTaskAssigned = (taskTitle: string, assigneeName: string) => {
    return showInfo(
      'مهمة جديدة',
      `تم تعيين مهمة "${taskTitle}" إلى ${assigneeName}`
    )
  }

  return {
    // Toast helpers
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showLoading,
    
    // System notifications
    notifySystem,
    
    // Popup notifications
    showPopup,
    
    // Project-specific notifications
    notifyProjectUpdate,
    notifyApprovalRequired,
    notifyTaskAssigned
  }
}

// هوك لإدارة الإشعارات المتقدمة
export function useAdvancedNotifications() {
  const { 
    toasts, 
    systemNotifications, 
    popupNotifications,
    updateToast,
    markAsRead,
    markAllAsRead,
    clearAll,
    settings,
    updateSettings
  } = useNotifications()

  // إحصائيات الإشعارات
  const getStats = () => {
    const unreadSystem = systemNotifications.filter(n => !n.isRead).length
    const totalToasts = toasts.length
    const totalPopups = popupNotifications.length
    const totalUnread = unreadSystem

    return {
      unreadSystem,
      totalToasts,
      totalPopups,
      totalUnread,
      total: totalToasts + systemNotifications.length + totalPopups
    }
  }

  // تحديث Toast إلى نجاح
  const updateToastToSuccess = (id: string, title: string, message?: string) => {
    updateToast(id, {
      type: 'success',
      title,
      message,
      duration: 3000
    })
  }

  // تحديث Toast إلى خطأ
  const updateToastToError = (id: string, title: string, message?: string) => {
    updateToast(id, {
      type: 'error',
      title,
      message,
      duration: 5000
    })
  }

  // تصفية الإشعارات
  const getFilteredNotifications = (filter: 'all' | 'unread' | NotificationType) => {
    if (filter === 'all') return systemNotifications
    if (filter === 'unread') return systemNotifications.filter(n => !n.isRead)
    return systemNotifications.filter(n => n.type === filter)
  }

  // تنظيف الإشعارات القديمة
  const cleanupOldNotifications = (daysOld: number = 7) => {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysOld)
    
    // هذا يتطلب تحديث الـ store لإزالة الإشعارات القديمة
    // يمكن تنفيذه عبر API أو localStorage
  }

  return {
    getStats,
    updateToastToSuccess,
    updateToastToError,
    getFilteredNotifications,
    cleanupOldNotifications,
    markAsRead,
    markAllAsRead,
    clearAll,
    settings,
    updateSettings
  }
} 