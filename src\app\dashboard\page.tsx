'use client'

import { useAuth, usePermissions } from '@/lib/core/auth'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  FolderPlus, 
  CheckSquare, 
  BarChart3, 
  Users, 
  TrendingUp,
  Clock,
  AlertTriangle,
  ArrowLeft,
  Shield,
  Database,
  FileText,
  Settings,
  Activity,
  Bell,
  UserCheck,
  Target,
  Zap,
  PieChart,
  Lock,
  BookOpen,
  HelpCircle
} from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function DashboardPage() {
  const { user, userRole } = useAuth()
  const permissions = usePermissions()
  const router = useRouter()

  // إحصائيات وهمية للعرض
  const stats = {
    totalRequests: 24,
    pendingApprovals: 8,
    activeProjects: 12,
    completedProjects: 45
  }

  const quickActions = [
    {
      title: 'طلب مشروع جديد',
      description: 'تقديم طلب مشروع تحسين جديد',
      icon: FolderPlus,
      color: 'bg-blue-600',
      href: '/requests/new',
      show: permissions.canCreateRequests()
    },
    {
      title: 'مراجعة الموافقات',
      description: 'مراجعة الطلبات المعلقة للموافقة',
      icon: CheckSquare,
      color: 'bg-green-600',
      href: '/approvals',
      show: permissions.canApproveRequests()
    },
    {
      title: 'إدارة المشاريع',
      description: 'متابعة وإدارة المشاريع الجارية',
      icon: BarChart3,
      color: 'bg-purple-600',
      href: '/projects',
      show: permissions.canViewProjects()
    },
    {
      title: 'إدارة المستخدمين',
      description: 'إضافة وإدارة المستخدمين',
      icon: Users,
      color: 'bg-orange-600',
      href: '/users',
      show: permissions.isAdmin() || permissions.isPMOManager()
    }
  ]

  // جميع الأنظمة المتاحة لمدير النظام
  const allSystems = [
    {
      title: 'طلبات المشاريع',
      description: 'إدارة وتتبع جميع طلبات المشاريع',
      icon: FolderPlus,
      color: 'bg-blue-600',
      href: '/requests',
      show: true
    },
    {
      title: 'الموافقات',
      description: 'مراجعة واعتماد الطلبات',
      icon: CheckSquare,
      color: 'bg-green-600',
      href: '/approvals',
      show: permissions.canApproveRequests()
    },
    {
      title: 'إدارة المشاريع',
      description: 'متابعة المشاريع الجارية والمكتملة',
      icon: BarChart3,
      color: 'bg-purple-600',
      href: '/projects',
      show: true
    },
    {
      title: 'إدارة المستخدمين',
      description: 'إضافة وإدارة حسابات المستخدمين',
      icon: Users,
      color: 'bg-orange-600',
      href: '/users',
      show: permissions.isAdmin()
    },
    {
      title: 'إدارة الصلاحيات',
      description: 'تحديد الأدوار والصلاحيات',
      icon: Shield,
      color: 'bg-red-600',
      href: '/permissions',
      show: permissions.isAdmin()
    },
    {
      title: 'قاعدة البيانات',
      description: 'إدارة ومراقبة قاعدة البيانات',
      icon: Database,
      color: 'bg-gray-600',
      href: '/database',
      show: permissions.isAdmin()
    },
    {
      title: 'مؤشرات الأداء',
      description: 'إدارة وتتبع مؤشرات الأداء الرئيسية',
      icon: Target,
      color: 'bg-indigo-600',
      href: '/kpis',
      show: true
    },
    {
      title: 'التقارير',
      description: 'إنشاء وعرض التقارير التفصيلية',
      icon: FileText,
      color: 'bg-teal-600',
      href: '/reports',
      show: true
    },

    {
      title: 'إدارة الملفات',
      description: 'تنظيم وإدارة ملفات المشاريع',
      icon: FolderPlus,
      color: 'bg-cyan-600',
      href: '/files',
      show: true
    },
    {
      title: 'الإشعارات',
      description: 'إدارة ومراقبة إشعارات النظام',
      icon: Bell,
      color: 'bg-blue-500',
      href: '/notifications-demo',
      show: permissions.isAdmin()
    },
    {
      title: 'اختبار النظام',
      description: 'اختبار وظائف النظام المختلفة',
      icon: Activity,
      color: 'bg-green-500',
      href: '/system-test',
      show: permissions.isAdmin()
    },
    {
      title: 'الأمان والحماية',
      description: 'مراقبة أمان النظام والبيانات',
      icon: Lock,
      color: 'bg-red-700',
      href: '/security',
      show: permissions.isAdmin()
    },
    {
      title: 'إعدادات النظام',
      description: 'تكوين وضبط إعدادات النظام',
      icon: Settings,
      color: 'bg-gray-700',
      href: '/settings',
      show: permissions.isAdmin()
    },
    {
      title: 'اختبار موحد',
      description: 'اختبار النماذج الموحدة للمشاريع',
      icon: Zap,
      color: 'bg-purple-500',
      href: '/unified-test',
      show: permissions.isAdmin()
    },
    {
      title: 'دليل المستخدم',
      description: 'دليل استخدام النظام والمساعدة',
      icon: BookOpen,
      color: 'bg-amber-600',
      href: '/help',
      show: true
    }
  ]

  const recentActivities = [
    {
      id: 1,
      title: 'تم اعتماد مشروع تحسين نظام الأرشيف',
      time: 'منذ ساعتين',
      type: 'approval',
      icon: CheckSquare,
      color: 'text-green-600'
    },
    {
      id: 2,
      title: 'طلب جديد: تطوير تطبيق الهاتف المحمول',
      time: 'منذ 4 ساعات',
      type: 'request',
      icon: FolderPlus,
      color: 'text-blue-600'
    },
    {
      id: 3,
      title: 'اكتمال مشروع تحديث قاعدة البيانات',
      time: 'أمس',
      type: 'completion',
      icon: BarChart3,
      color: 'text-purple-600'
    }
  ]

  return (
    <ProtectedLayout>
      <div className="max-w-7xl mx-auto">
          {/* Welcome Section */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              مرحباً، {user?.user_metadata?.name || 'المستخدم'}
            </h1>
            <p className="text-gray-600">
              {userRole?.display_name} - إليك نظرة عامة على نشاطك اليوم
            </p>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                    <p className="text-3xl font-bold text-gray-900">{stats.totalRequests}</p>
                  </div>
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <FolderPlus className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <TrendingUp className="w-4 h-4 text-green-500 ml-1" />
                  <span className="text-green-600">+12%</span>
                  <span className="text-gray-500 mr-2">من الشهر الماضي</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">الموافقات المعلقة</p>
                    <p className="text-3xl font-bold text-gray-900">{stats.pendingApprovals}</p>
                  </div>
                  <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <Clock className="w-6 h-6 text-yellow-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <AlertTriangle className="w-4 h-4 text-yellow-500 ml-1" />
                  <span className="text-yellow-600">تحتاج مراجعة</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">المشاريع النشطة</p>
                    <p className="text-3xl font-bold text-gray-900">{stats.activeProjects}</p>
                  </div>
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <BarChart3 className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <TrendingUp className="w-4 h-4 text-green-500 ml-1" />
                  <span className="text-green-600">+8%</span>
                  <span className="text-gray-500 mr-2">من الشهر الماضي</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">المشاريع المكتملة</p>
                    <p className="text-3xl font-bold text-gray-900">{stats.completedProjects}</p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <CheckSquare className="w-6 h-6 text-green-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm">
                  <TrendingUp className="w-4 h-4 text-green-500 ml-1" />
                  <span className="text-green-600">+15%</span>
                  <span className="text-gray-500 mr-2">من الشهر الماضي</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <h2 className="text-xl font-semibold text-gray-900">الإجراءات السريعة</h2>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {quickActions
                    .filter(action => action.show)
                    .map((action, index) => {
                      const Icon = action.icon
                      return (
                        <button
                          key={index}
                          onClick={() => router.push(action.href)}
                          className="w-full flex items-center gap-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-right"
                        >
                          <div className={`w-12 h-12 ${action.color} rounded-lg flex items-center justify-center`}>
                            <Icon className="w-6 h-6 text-white" />
                          </div>
                          <div className="flex-1">
                            <h3 className="font-medium text-gray-900">{action.title}</h3>
                            <p className="text-sm text-gray-500">{action.description}</p>
                          </div>
                          <ArrowLeft className="w-5 h-5 text-gray-400" />
                        </button>
                      )
                    })}
                </div>
              </CardContent>
            </Card>

            {/* Recent Activities */}
            <Card>
              <CardHeader>
                <h2 className="text-xl font-semibold text-gray-900">النشاطات الأخيرة</h2>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivities.map((activity) => {
                    const Icon = activity.icon
                    return (
                      <div key={activity.id} className="flex items-start gap-3">
                        <div className={`w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center`}>
                          <Icon className={`w-4 h-4 ${activity.color}`} />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">
                            {activity.title}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            {activity.time}
                          </p>
                        </div>
                      </div>
                    )
                  })}
                </div>
                
                <div className="mt-6 pt-4 border-t border-gray-200">
                  <Button 
                    variant="ghost" 
                    className="w-full"
                    onClick={() => router.push('/activities')}
                  >
                    عرض جميع النشاطات
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* All Systems Section */}
          <div className="mt-8">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">جميع الأنظمة المتاحة</h2>
                    <p className="text-sm text-gray-600">الوصول إلى جميع أنظمة إدارة المشاريع</p>
                  </div>
                  <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                    {allSystems.filter(system => system.show).length} نظام
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {allSystems
                    .filter(system => system.show)
                    .map((system, index) => {
                      const Icon = system.icon
                      return (
                        <button
                          key={index}
                          onClick={() => router.push(system.href)}
                          className="group p-4 border border-gray-200 rounded-lg hover:shadow-md hover:border-gray-300 transition-all duration-200 text-center"
                        >
                          <div className={`w-12 h-12 ${system.color} rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform`}>
                            <Icon className="w-6 h-6 text-white" />
                          </div>
                          <h3 className="font-medium text-gray-900 mb-1 text-sm">{system.title}</h3>
                          <p className="text-xs text-gray-500 line-clamp-2">{system.description}</p>
                        </button>
                      )
                    })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Project Types Section */}
          <div className="mt-8">
            <Card>
              <CardHeader>
                <h2 className="text-xl font-semibold text-gray-900">أنواع مشاريع التحسين</h2>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center p-6 border-2 border-dashed border-blue-200 rounded-lg hover:border-blue-400 transition-colors">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <FolderPlus className="w-8 h-8 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">مقترح مشروع تحسين</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      للمقترحات التي تحتاج دراسة وتقييم من فريق التخطيط
                    </p>
                    <p className="text-xs text-blue-600 font-medium">
                      يحتاج موافقة: مدير المكتب + مدير التخطيط
                    </p>
                  </div>

                  <div className="text-center p-6 border-2 border-dashed border-green-200 rounded-lg hover:border-green-400 transition-colors">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <BarChart3 className="w-8 h-8 text-green-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">طلب مشروع تحسين</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      للمشاريع الكبيرة التي تحتاج موافقة الإدارة العليا
                    </p>
                    <p className="text-xs text-green-600 font-medium">
                      يحتاج موافقة: المكتب + التخطيط + الإدارة التنفيذية
                    </p>
                  </div>

                  <div className="text-center p-6 border-2 border-dashed border-purple-200 rounded-lg hover:border-purple-400 transition-colors">
                    <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <TrendingUp className="w-8 h-8 text-purple-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">مشروع كويك وين</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      للتحسينات السريعة التي يمكن تنفيذها خلال فترة قصيرة
                    </p>
                    <p className="text-xs text-purple-600 font-medium">
                      يحتاج موافقة: مدير مكتب المشاريع فقط
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
    </ProtectedLayout>
  )
} 