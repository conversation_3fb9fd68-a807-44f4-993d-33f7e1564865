import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * دمج فئات CSS مع Tailwind
 * @param inputs - فئات CSS المراد دمجها
 * @returns فئات CSS مدموجة
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * تنسيق التاريخ للعرض بالعربية
 * @param date - التاريخ المراد تنسيقه
 * @returns التاريخ منسق بالعربية
 */
export function formatDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  return new Intl.DateTimeFormat('ar-SA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    timeZone: 'Asia/Riyadh'
  }).format(dateObj)
}

/**
 * تنسيق التاريخ والوقت للعرض بالعربية
 * @param date - التاريخ والوقت المراد تنسيقهما
 * @returns التاريخ والوقت منسقان بالعربية
 */
export function formatDateTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  return new Intl.DateTimeFormat('ar-SA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'Asia/Riyadh'
  }).format(dateObj)
}

/**
 * تحويل النص إلى slug مناسب للـ URL
 * @param text - النص المراد تحويله
 * @returns النص محول إلى slug
 */
export function slugify(text: string): string {
  return text
    .toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w\-]+/g, '')
    .replace(/\-\-+/g, '-')
    .replace(/^-+/, '')
    .replace(/-+$/, '')
}

/**
 * اختصار النص مع إضافة نقاط
 * @param text - النص المراد اختصاره
 * @param length - الطول المطلوب
 * @returns النص مختصر
 */
export function truncateText(text: string, length: number): string {
  if (text.length <= length) return text
  return text.substring(0, length).trim() + '...'
}

/**
 * التحقق من صحة البريد الإلكتروني
 * @param email - البريد الإلكتروني
 * @returns true إذا كان صحيحاً
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * توليد معرف فريد
 * @returns معرف فريد
 */
export function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
} 