'use client'

import React, { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { 
  HelpCircle, 
  X, 
  ChevronRight, 
  ChevronLeft,
  Lightbulb,
  CheckCircle,
  AlertCircle,
  Info,
  FileText
} from 'lucide-react'

interface HelpContent {
  title: string
  description: string
  tips: string[]
  examples: string[]
  commonMistakes: string[]
}

interface HelpSystemProps {
  isOpen: boolean
  onClose: () => void
  currentStep: number
  field?: string
}

const helpContent: Record<number, Record<string, HelpContent>> = {
  1: { // Find
    problemDescription: {
      title: 'وصف المشكلة بدقة',
      description: 'اكتب وصفاً واضحاً ومحدداً للمشكلة التي تواجهها',
      tips: [
        'استخدم أرقام وحقائق محددة',
        'اذكر متى وأين تحدث المشكلة',
        'حدد من يتأثر بهذه المشكلة',
        'اشرح تأثير المشكلة على العمل'
      ],
      examples: [
        'تأخر وصول نتائج المختبر لأكثر من 48 ساعة في 70% من الحالات',
        'زيادة شكاوى العملاء بنسبة 25% خلال الربع الأخير',
        'تكرار الأخطاء في إدخال البيانات بمعدل 15 خطأ يومياً'
      ],
      commonMistakes: [
        'وصف عام وغير محدد',
        'عدم ذكر الأرقام والإحصائيات',
        'الخلط بين المشكلة والحل'
      ]
    },
    indicatorName: {
      title: 'اسم المؤشر',
      description: 'المؤشر الذي ستستخدمه لقياس المشكلة وتتبع التحسن',
      tips: [
        'اختر مؤشراً قابلاً للقياس',
        'يجب أن يكون مرتبطاً مباشرة بالمشكلة',
        'استخدم مؤشرات معروفة في مجال عملك',
        'تأكد من إمكانية الحصول على البيانات'
      ],
      examples: [
        'زمن انتظار النتيجة',
        'معدل رضا العملاء',
        'عدد الأخطاء اليومية',
        'وقت الاستجابة',
        'معدل الإنجاز'
      ],
      commonMistakes: [
        'اختيار مؤشر غير قابل للقياس',
        'استخدام مؤشرات متعددة في نفس الوقت',
        'عدم ربط المؤشر بالمشكلة الأساسية'
      ]
    },
    currentValue: {
      title: 'القيمة الحالية',
      description: 'القيمة الحالية للمؤشر المختار',
      tips: [
        'استخدم بيانات حقيقية وموثقة',
        'احسب المتوسط لفترة زمنية مناسبة',
        'تأكد من دقة مصدر البيانات',
        'وثق طريقة الحساب'
      ],
      examples: [
        '72 ساعة (متوسط آخر 3 أشهر)',
        '3.2 نقطة (من 5 نقاط)',
        '15 خطأ يومياً',
        '45 دقيقة وقت استجابة'
      ],
      commonMistakes: [
        'استخدام تقديرات غير دقيقة',
        'عدم تحديد فترة القياس',
        'الاعتماد على بيانات قديمة'
      ]
    },
    improvementDirection: {
      title: 'اتجاه التحسن',
      description: 'تحديد ما إذا كان التحسن يحدث بزيادة الرقم أم تقليله',
      tips: [
        'فكر في طبيعة المؤشر المختار',
        'المؤشرات الإيجابية: كلما زادت كان أفضل',
        'المؤشرات السلبية: كلما قلت كان أفضل',
        'تأكد من منطقية الاختيار'
      ],
      examples: [
        'زيادة = تحسن: رضا العملاء، الإنتاجية، الجودة، المبيعات',
        'تقليل = تحسن: وقت الانتظار، عدد الأخطاء، التكلفة، الشكاوى'
      ],
      commonMistakes: [
        'اختيار اتجاه خاطئ للمؤشر',
        'عدم التفكير في منطق المؤشر',
        'الخلط بين المؤشرات الإيجابية والسلبية'
      ]
    }
  },
  2: { // Organize
    teamLeader: {
      title: 'قائد الفريق',
      description: 'الشخص المسؤول عن قيادة مشروع التحسين',
      tips: [
        'اختر شخصاً لديه خبرة في المجال',
        'يجب أن يكون متفرغاً للمشروع',
        'يفضل أن يكون من نفس القسم',
        'لديه مهارات قيادة وتواصل'
      ],
      examples: [
        'مدير القسم أو نائبه',
        'مشرف العمليات',
        'خبير في المجال المحدد',
        'شخص لديه تجربة سابقة في التحسين'
      ],
      commonMistakes: [
        'اختيار شخص غير متفرغ',
        'عدم وضوح الصلاحيات',
        'اختيار شخص بعيد عن المشكلة'
      ]
    },
    teamMembers: {
      title: 'أعضاء الفريق',
      description: 'الأشخاص الذين سيشاركون في تنفيذ التحسين',
      tips: [
        'اختر 3-7 أعضاء كحد أقصى',
        'تنوع في التخصصات والخبرات',
        'تأكد من توفر الوقت للمشاركة',
        'حدد دور كل عضو بوضوح'
      ],
      examples: [
        'خبير تقني - تحليل النظام',
        'مختص جودة - مراجعة العمليات',
        'ممثل المستخدمين - تقييم الحلول',
        'محلل بيانات - قياس النتائج'
      ],
      commonMistakes: [
        'فريق كبير جداً أو صغير جداً',
        'عدم وضوح الأدوار',
        'اختيار أعضاء غير مهتمين'
      ]
    }
  },
  3: { // Clarify
    processDescription: {
      title: 'وصف العملية',
      description: 'شرح مفصل للعملية الحالية التي تحتوي على المشكلة',
      tips: [
        'اكتب الخطوات بالترتيب الزمني',
        'حدد المسؤوليات في كل خطوة',
        'اذكر المدخلات والمخرجات',
        'حدد نقاط القرار المهمة'
      ],
      examples: [
        '1. استلام طلب التحليل من الطبيب',
        '2. تسجيل الطلب في النظام',
        '3. أخذ العينة من المريض',
        '4. نقل العينة للمختبر',
        '5. تحليل العينة',
        '6. إدخال النتائج في النظام'
      ],
      commonMistakes: [
        'وصف مبسط جداً',
        'عدم ذكر المسؤوليات',
        'تجاهل الخطوات الفرعية المهمة'
      ]
    },
    problemScope: {
      title: 'نطاق المشكلة',
      description: 'تحديد حدود المشكلة بدقة',
      tips: [
        'حدد ما يدخل في النطاق وما لا يدخل',
        'اذكر الأقسام المتأثرة',
        'حدد الفترة الزمنية',
        'اذكر أنواع الحالات المشمولة'
      ],
      examples: [
        'مرضى الطوارئ فقط (لا يشمل العيادات)',
        'تحاليل الدم الروتينية (لا يشمل التحاليل الخاصة)',
        'ساعات العمل الرسمية (8 صباحاً - 5 مساءً)',
        'المرضى الداخليين في الجناح الطبي'
      ],
      commonMistakes: [
        'نطاق واسع جداً',
        'عدم وضوح الحدود',
        'تجاهل العوامل الزمنية'
      ]
    }
  },
  4: { // Understand
    analysisMethod: {
      title: 'طريقة التحليل',
      description: 'الأداة المناسبة لتحليل الأسباب الجذرية',
      tips: [
        'خمسة لماذا: للمشاكل البسيطة',
        'عظمة السمكة: للمشاكل المعقدة',
        'تحليل السبب الجذري: للمشاكل الحرجة',
        'يمكن دمج أكثر من طريقة'
      ],
      examples: [
        'خمسة لماذا: تأخر التقارير',
        'عظمة السمكة: جودة الخدمة',
        'تحليل السبب الجذري: مشاكل الجودة'
      ],
      commonMistakes: [
        'اختيار طريقة معقدة لمشكلة بسيطة',
        'التوقف عند الأسباب السطحية',
        'عدم إشراك الفريق في التحليل'
      ]
    },
    rootCause: {
      title: 'السبب الجذري',
      description: 'السبب الأساسي الذي يؤدي إلى المشكلة',
      tips: [
        'يجب أن يكون قابلاً للتحكم',
        'إذا حُل هذا السبب، ستختفي المشكلة',
        'مدعوم بالأدلة والتحليل',
        'محدد وقابل للقياس'
      ],
      examples: [
        'عدم وجود إجراءات واضحة لتسليم العينات',
        'نقص في التدريب على النظام الجديد',
        'عدم كفاية الموارد البشرية في الفترة المسائية'
      ],
      commonMistakes: [
        'الخلط بين السبب والعرض',
        'سبب عام وغير محدد',
        'سبب خارج نطاق التحكم'
      ]
    }
  },
  5: { // Select
    proposedSolutions: {
      title: 'الحلول المقترحة',
      description: 'الحلول العملية للتعامل مع السبب الجذري',
      tips: [
        'اقترح عدة حلول مختلفة',
        'رتب الحلول حسب الأولوية',
        'حدد التكلفة والوقت المطلوب',
        'تأكد من إمكانية التنفيذ'
      ],
      examples: [
        'تطوير دليل إجراءات موحد',
        'تنفيذ برنامج تدريبي شامل',
        'تعيين موظفين إضافيين',
        'تحسين نظام تتبع العينات'
      ],
      commonMistakes: [
        'حلول مكلفة جداً',
        'حلول غير قابلة للتنفيذ',
        'عدم ربط الحل بالسبب الجذري'
      ]
    },
    failedSolutions: {
      title: 'الحلول السابقة الفاشلة',
      description: 'الحلول التي جُربت من قبل ولم تنجح',
      tips: [
        'اذكر الحلول التي جُربت فعلاً',
        'وضح سبب فشل كل حل',
        'تعلم من الأخطاء السابقة',
        'تجنب تكرار نفس الأخطاء'
      ],
      examples: [
        'تذكير الموظفين بالإيميل - فشل بسبب عدم المتابعة',
        'تركيب لوحات إرشادية - فشل بسبب عدم الوضوح',
        'اجتماعات دورية - فشل بسبب عدم الالتزام'
      ],
      commonMistakes: [
        'عدم ذكر الحلول السابقة',
        'عدم تحليل أسباب الفشل',
        'تكرار نفس الأخطاء'
      ]
    }
  }
}

export function HelpSystem({ isOpen, onClose, currentStep, field }: HelpSystemProps) {
  const [selectedTopic, setSelectedTopic] = useState<string | null>(null)
  
  if (!isOpen) return null

  const stepContent = helpContent[currentStep] || {}
  const topics = Object.keys(stepContent)
  
  const currentContent = selectedTopic ? stepContent[selectedTopic] : null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-blue-600 text-white p-4 flex items-center justify-between">
          <div className="flex items-center">
            <HelpCircle className="w-6 h-6 mr-3" />
            <h2 className="text-xl font-bold">
              دليل المساعدة - المرحلة {currentStep}
            </h2>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-white hover:bg-blue-700"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        <div className="flex h-[calc(90vh-80px)]">
          {/* Sidebar */}
          <div className="w-1/3 bg-gray-50 border-r overflow-y-auto">
            <div className="p-4">
              <h3 className="font-semibold text-gray-900 mb-3">المواضيع المتاحة</h3>
              <div className="space-y-2">
                {topics.map(topic => (
                  <button
                    key={topic}
                    onClick={() => setSelectedTopic(topic)}
                    className={`w-full text-right p-3 rounded-lg transition-colors ${
                      selectedTopic === topic
                        ? 'bg-blue-100 text-blue-800 border-blue-300'
                        : 'bg-white hover:bg-gray-100 text-gray-700'
                    } border`}
                  >
                    {stepContent[topic]?.title}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {currentContent ? (
              <div className="p-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {currentContent.title}
                </h3>
                
                <div className="space-y-6">
                  {/* Description */}
                  <div>
                    <div className="flex items-center mb-2">
                      <Info className="w-5 h-5 text-blue-600 mr-2" />
                      <h4 className="font-semibold text-gray-900">الوصف</h4>
                    </div>
                    <p className="text-gray-700 bg-blue-50 p-3 rounded-lg">
                      {currentContent.description}
                    </p>
                  </div>

                  {/* Tips */}
                  <div>
                    <div className="flex items-center mb-2">
                      <Lightbulb className="w-5 h-5 text-yellow-600 mr-2" />
                      <h4 className="font-semibold text-gray-900">نصائح مهمة</h4>
                    </div>
                    <ul className="space-y-2">
                      {currentContent.tips.map((tip, index) => (
                        <li key={index} className="flex items-start">
                          <CheckCircle className="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{tip}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Examples */}
                  <div>
                    <div className="flex items-center mb-2">
                      <FileText className="w-5 h-5 text-green-600 mr-2" />
                      <h4 className="font-semibold text-gray-900">أمثلة</h4>
                    </div>
                    <div className="bg-green-50 p-3 rounded-lg">
                      <ul className="space-y-1">
                        {currentContent.examples.map((example, index) => (
                          <li key={index} className="text-gray-700">
                            • {example}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {/* Common Mistakes */}
                  <div>
                    <div className="flex items-center mb-2">
                      <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
                      <h4 className="font-semibold text-gray-900">أخطاء شائعة</h4>
                    </div>
                    <div className="bg-red-50 p-3 rounded-lg">
                      <ul className="space-y-1">
                        {currentContent.commonMistakes.map((mistake, index) => (
                          <li key={index} className="text-red-700">
                            ✗ {mistake}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-6 text-center">
                <HelpCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  اختر موضوعاً للمساعدة
                </h3>
                <p className="text-gray-600">
                  اختر أحد المواضيع من القائمة الجانبية للحصول على المساعدة التفصيلية
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 p-4 border-t flex justify-end">
          <Button onClick={onClose}>
            إغلاق
          </Button>
        </div>
      </div>
    </div>
  )
}

// مكون مساعدة مبسط للحقول
interface FieldHelpProps {
  content: string
  field: string
  step: number
}

export function FieldHelp({ content, field, step }: FieldHelpProps) {
  const [showHelp, setShowHelp] = useState(false)
  
  return (
    <div className="relative inline-block">
      <button
        type="button"
        onClick={() => setShowHelp(!showHelp)}
        className="ml-2 text-blue-500 hover:text-blue-700 transition-colors"
      >
        <HelpCircle className="w-4 h-4" />
      </button>
      
      {showHelp && (
        <div className="absolute z-20 w-80 p-4 bg-white border border-gray-200 rounded-lg shadow-lg top-6 right-0">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-semibold text-gray-900">مساعدة</h4>
            <button
              onClick={() => setShowHelp(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
          <p className="text-sm text-gray-700 mb-3">{content}</p>
          
          {helpContent[step]?.[field] && (
            <div className="space-y-2">
              <div>
                <h5 className="text-xs font-medium text-gray-600 mb-1">مثال:</h5>
                <p className="text-xs text-green-700 bg-green-50 p-2 rounded">
                  {helpContent[step][field].examples[0]}
                </p>
              </div>
              
              <div>
                <h5 className="text-xs font-medium text-gray-600 mb-1">تجنب:</h5>
                <p className="text-xs text-red-700 bg-red-50 p-2 rounded">
                  {helpContent[step][field].commonMistakes[0]}
                </p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
} 