// أنواع نظام الملاحظات والمراجعة التفاعلية للمقترحات

export type ReviewerType = 
  | 'team_leader' 
  | 'department_manager' 
  | 'participating_department_manager'
  | 'pmo_stakeholder';

export type FeedbackType = 
  | 'suggestion' 
  | 'concern' 
  | 'improvement' 
  | 'approval' 
  | 'rejection' 
  | 'question';

export type FeedbackPriority = 'low' | 'medium' | 'high' | 'critical';

export type FeedbackStatus = 'pending' | 'addressed' | 'resolved' | 'dismissed';

// معلومات المشاركين في المراجعة (بدون أعضاء الفريق)
export interface ParticipantInfo {
  id: string;
  name: string;
  phone: string;
  email?: string;
  role: ReviewerType;
  department: string;
  position?: string;
}

// معلومات أصحاب المصلحة المستخرجة من form_data (للتوافق مع النظام القديم)
export interface StakeholderInfo extends ParticipantInfo {}

// معلومات القسم المشارك
export interface ParticipatingDepartment {
  id: string;
  name: string;
  manager: {
    name: string;
    phone: string;
    email?: string;
    position?: string;
  };
  involvement: 'primary' | 'secondary' | 'supporting';
  responsibilities: string[];
}

// ملاحظة واحدة على المقترح
export interface SuggestionFeedback {
  id: string;
  requestId: string;
  reviewerId: string;
  reviewerName: string;
  reviewerType: ReviewerType;
  solutionId?: string; // معرف الحل المراجع
  comment: string;
  feedbackType: FeedbackType;
  priority: FeedbackPriority;
  status: FeedbackStatus;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
}

// إحصائيات الملاحظات
export interface FeedbackStatistics {
  totalFeedbacks: number;
  byType: Record<FeedbackType, number>;
  byPriority: Record<FeedbackPriority, number>;
  byStatus: Record<FeedbackStatus, number>;
  byReviewerType: Record<ReviewerType, number>;
}

// تقييم حل مقترح
export interface SolutionEvaluation {
  solutionId: string;
  feedbacks: SuggestionFeedback[];
  averageRating?: number;
  consensusLevel: 'high' | 'medium' | 'low';
  recommendationLevel: 'highly_recommended' | 'recommended' | 'neutral' | 'not_recommended';
}

// موافقة المشاركين على الحل المختار
export interface ParticipantConsensus {
  participantId: string;
  participantName: string;
  role: ReviewerType;
  approval: 'approved' | 'approved_with_conditions' | 'neutral' | 'rejected';
  conditions?: string;
  timestamp: string;
}

// موافقة أصحاب المصلحة على الحل المختار (للتوافق مع النظام القديم)
export interface StakeholderConsensus extends ParticipantConsensus {}

// اختيار الحل الأمثل (يتم بواسطة المشاركين وليس PMO)
export interface SolutionSelection {
  id: string;
  requestId: string;
  selectedSolutionId: string;
  selectionRationale: string;
  participantConsensus: ParticipantConsensus[];
  selectedBy: string; // معرف المشارك الذي قام بالاختيار النهائي
  selectedAt: string;
}

// بيانات التحويل إلى مشروع
export interface SuggestionConversionData {
  // البيانات الأساسية المضافة
  projectName: string;
  projectDescription: string;
  startDate: string;
  endDate: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  
  // تفاصيل الحل المختار
  selectedSolution: {
    id: string;
    title: string;
    description: string;
    expectedBenefits: string[];
    implementationSteps: string[];
  };
  
  // التخطيط المضاف
  projectTasks: Array<{
    id: string;
    title: string;
    description: string;
    assignedTo: string;
    dueDate: string;
    priority: string;
  }>;
  
  requiredResources: Array<{
    id: string;
    type: string;
    description: string;
    quantity: number;
    cost: number;
  }>;
  
  // إدارة المخاطر المضافة
  risks: Array<{
    id: string;
    description: string;
    probability: 'low' | 'medium' | 'high';
    impact: 'low' | 'medium' | 'high';
    mitigation: string;
  }>;
}

// تتبع عملية التحويل
export interface SuggestionConversion {
  id: string;
  originalRequestId: string;
  newRequestId: string;
  conversionData: SuggestionConversionData;
  convertedBy: string; // PMO staff member
  convertedAt: string;
}

// حالة المراجعة التفاعلية
export interface InteractiveReviewState {
  requestId: string;
  participants: ParticipantInfo[]; // المشاركون في المراجعة فقط
  participatingDepartments: ParticipatingDepartment[]; // الأقسام المشاركة
  feedbacks: SuggestionFeedback[];
  solutionEvaluations: SolutionEvaluation[];
  statistics: FeedbackStatistics;
  selectedSolution?: SolutionSelection;
  conversionData?: SuggestionConversionData;
  currentPhase: 'feedback_collection' | 'solution_selection' | 'conversion_preparation' | 'completed';
  
  // للتوافق مع النظام القديم
  stakeholders: StakeholderInfo[];
}

// نموذج إضافة ملاحظة جديدة
export interface NewFeedbackForm {
  solutionId?: string;
  comment: string;
  feedbackType: FeedbackType;
  priority: FeedbackPriority;
}

// نموذج اختيار الحل
export interface SolutionSelectionForm {
  selectedSolutionId: string;
  selectionRationale: string;
  participantApprovals: Array<{
    participantId: string;
    approval: ParticipantConsensus['approval'];
    conditions?: string;
  }>;
  
  // للتوافق مع النظام القديم
  stakeholderApprovals: Array<{
    stakeholderId: string;
    approval: StakeholderConsensus['approval'];
    conditions?: string;
  }>;
}

// خصائص مكون المراجعة التفاعلية
// بيانات المراجعة التفاعلية للمقترح مع النموذج الكامل
export interface InteractiveReviewData {
  suggestion: {
    id: string;
    title: string;
    description: string;
    submittedAt: string;
    submittedBy: {
      id: string;
      name: string;
      department: string;
      role: string;
      email?: string;
      phone?: string;
    };
    status: string;
    currentPhase: string;
    // البيانات الكاملة للنموذج (7 مراحل)
    fullFormData?: {
      find: {
        problemDescription: string;
        currentSituation: string;
        targetAudience: string;
      };
      organize: {
        responsibleDepartment: {
          name: string;
          manager: string;
          managerPhone: string;
        };
        teamLeader: {
          name: string;
          phone: string;
          email?: string;
        };
        participatingDepartments: Array<{
          name: string;
          manager: {
            name: string;
            phone: string;
            email?: string;
          };
          involvement: string;
        }>;
      };
      clarify: {
        processDescription: string;
        processMap?: string;
      };
      understand: {
        analysisMethod: 'whys_5' | 'fishbone' | 'root_cause_analysis';
        rootCause: string;
        analysisDetails?: string;
      };
      select: {
        proposedSolutions: Array<{
          id: string;
          title: string;
          description: string;
          expectedBenefits: string[];
          implementationSteps: string[];
          feasibilityScore?: number;
        }>;
      };
      recommendations: {
        finalRecommendations: string;
        implementationTips: string[];
      };
      kpi: {
        name: string;
        currentValue: number;
        targetValue: number;
        unit: string;
        improvementDirection: 'increase' | 'decrease';
        improvementPercentage: number;
      };
    };
  };
  participants: ParticipantInfo[];
  participatingDepartments: ParticipatingDepartment[];
  feedbacks: SuggestionFeedback[];
  solutionEvaluations: SolutionEvaluation[];
  statistics: FeedbackStatistics;
  currentPhase: 'feedback_collection' | 'solution_selection' | 'conversion_preparation' | 'completed';
}

export interface InteractiveSolutionReviewProps {
  suggestionId: string;
  suggestedSolutions: any[]; // من نوع SuggestedSolution
  participants: ParticipantInfo[]; // المشاركون في المراجعة
  participatingDepartments: ParticipatingDepartment[]; // الأقسام المشاركة
  currentUser: {
    id: string;
    name: string;
    role: string;
  };
  onFeedbackSubmit: (feedback: NewFeedbackForm) => Promise<void>;
  onSolutionSelect: (selection: SolutionSelectionForm) => Promise<void>;
  onConversionProceed: () => void;
  
  // للتوافق مع النظام القديم
  stakeholders: StakeholderInfo[];
} 