/**
 * ثوابت واجهة المستخدم الموحدة
 * يحتوي على أحجام الخطوط، الألوان، والثوابت الأخرى للتصميم
 */

// ==================== FONT SIZES ====================

/**
 * نظام أحجام الخطوط الموحد - محسن للداشبورد - مصغر بنسبة 10%
 * التوزيع: 9px - 11px - 13px - 14px - 16px
 */
export const FontSizes = {
  // الأحجام الأساسية - مصغرة بنسبة 10%
  micro: '9px',    // text-xs - للتفاصيل الدقيقة والملاحظات الصغيرة
  small: '11px',   // text-sm - للنصوص الثانوية والتسميات
  normal: '13px',  // text-base - للنصوص الأساسية والمحتوى
  medium: '14px',  // text-lg - للعناوين الفرعية والمهمة
  large: '16px',   // text-xl - للعناوين الرئيسية والمهمة جداً
} as const;

export const FontSizeClasses = {
  // فئات Tailwind المحدثة - مصغرة بنسبة 10%
  micro: 'text-xs',    // 9px
  small: 'text-sm',    // 11px
  normal: 'text-base', // 13px
  medium: 'text-lg',   // 14px
  large: 'text-xl',    // 16px
} as const;

export const FontSizeUsage = {
  // استخدامات الأحجام
  micro: [
    'أيقونات صغيرة',
    'تسميات الحقول الصغيرة',
    'رقم الصفحة',
    'تواريخ صغيرة',
    'badges صغيرة'
  ],
  small: [
    'تسميات الحقول',
    'نصوص مساعدة',
    'رسائل الأخطاء',
    'تواريخ وأوقات',
    'نصوص ثانوية'
  ],
  normal: [
    'النصوص الأساسية',
    'محتوى الصفحات',
    'النماذج',
    'الأزرار',
    'القوائم'
  ],
  medium: [
    'العناوين الفرعية',
    'أسماء الأقسام',
    'عناوين الكروت',
    'النصوص المهمة',
    'عناوين الجداول'
  ],
  large: [
    'العناوين الرئيسية',
    'عناوين الصفحات',
    'رسائل الحالة المهمة',
    'العناوين الكبيرة',
    'نصوص الترحيب'
  ]
} as const;

export const CustomFontClasses = {
  // فئات CSS مخصصة
  micro: 'text-micro',
  small: 'text-small', 
  normal: 'text-normal',
  medium: 'text-medium',
  large: 'text-large',
  
  // فئات العناوين
  headingMain: 'heading-main',     // 18px
  headingSub: 'heading-sub',       // 16px
  headingSection: 'heading-section', // 14px
  
  // فئات النصوص
  body: 'text-body',       // 14px
  caption: 'text-caption', // 12px
  label: 'text-label',     // 10px
} as const;

// ==================== COLOR CONSTANTS ====================

export const Colors = {
  // الألوان الأساسية
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },
  
  // ألوان الحالة
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },
  
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },
  
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },
  
  // الألوان الرمادية
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  }
} as const;

// ==================== SPACING CONSTANTS ====================

export const Spacing = {
  // المسافات الأساسية
  xs: '0.25rem',   // 4px
  sm: '0.5rem',    // 8px
  md: '1rem',      // 16px
  lg: '1.5rem',    // 24px
  xl: '2rem',      // 32px
  '2xl': '3rem',   // 48px
  '3xl': '4rem',   // 64px
  
  // مسافات مخصصة للمشروع
  cardPadding: '1.5rem',
  sectionGap: '2rem',
  componentGap: '1rem',
} as const;

// ==================== BORDER RADIUS ====================

export const BorderRadius = {
  none: '0',
  sm: '0.125rem',   // 2px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  full: '9999px',
  
  // أنصاف أقطار مخصصة
  card: '0.5rem',
  button: '0.375rem',
  input: '0.375rem',
} as const;

// ==================== SHADOWS ====================

export const Shadows = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  
  // ظلال مخصصة
  card: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
  button: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  dropdown: '0 10px 15px -3px rgb(0 0 0 / 0.1)',
} as const;

// ==================== BREAKPOINTS ====================

export const Breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

// ==================== ANIMATION DURATIONS ====================

export const AnimationDurations = {
  fast: '150ms',
  normal: '300ms',
  slow: '500ms',
  
  // مدد مخصصة
  hover: '150ms',
  modal: '300ms',
  page: '500ms',
} as const;

// ==================== Z-INDEX ====================

export const ZIndex = {
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modal: 1040,
  popover: 1050,
  tooltip: 1060,
  toast: 1070,
} as const;

// ==================== TYPES ====================

export type FontSizeType = keyof typeof FontSizes;
export type FontSizeClass = keyof typeof FontSizeClasses;
export type CustomFontClass = keyof typeof CustomFontClasses;
export type ColorType = keyof typeof Colors;
export type SpacingType = keyof typeof Spacing;
export type BorderRadiusType = keyof typeof BorderRadius;
export type ShadowType = keyof typeof Shadows;
export type BreakpointType = keyof typeof Breakpoints;
export type AnimationDurationType = keyof typeof AnimationDurations;
export type ZIndexType = keyof typeof ZIndex;
