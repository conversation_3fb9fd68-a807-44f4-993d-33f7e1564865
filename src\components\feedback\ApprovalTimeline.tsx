'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface TimelineEntry {
  id: string;
  participantId: string;
  participantName: string;
  participantRole: string;
  department: string;
  action: 'review' | 'approve' | 'reject' | 'comment' | 'solution_selection';
  selectedSolutionId?: string;
  selectedSolutionTitle?: string;
  comment: string;
  timestamp: string;
  status: 'pending' | 'completed';
}

interface Solution {
  id: string;
  title: string;
  description: string;
}

interface ParticipantApproval {
  participantId: string;
  participantName: string;
  participantRole: string;
  department: string;
  status: 'pending' | 'approved' | 'rejected';
  selectedSolutionId?: string;
  comment?: string;
  timestamp?: string;
}

interface ApprovalTimelineProps {
  solutions: Solution[];
  participants: Array<{
    id: string;
    name: string;
    role: string;
    department: string;
  }>;
  currentUserId: string;
  currentUserRole: string;
  onApprovalSubmit: (approval: {
    selectedSolutionId: string;
    comment: string;
    action: 'approve' | 'reject';
  }) => void;
  timeline: TimelineEntry[];
  participantApprovals: ParticipantApproval[];
}

export default function ApprovalTimeline({
  solutions,
  participants,
  currentUserId,
  currentUserRole,
  onApprovalSubmit,
  timeline,
  participantApprovals
}: ApprovalTimelineProps) {
  const [selectedSolutionId, setSelectedSolutionId] = useState<string>('');
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const currentParticipant = participants.find(p => p.id === currentUserId);
  const currentApproval = participantApprovals.find(p => p.participantId === currentUserId);
  const hasSubmitted = currentApproval?.status !== 'pending';

  const handleSubmitApproval = async (action: 'approve' | 'reject') => {
    if (!selectedSolutionId || !comment.trim()) {
      alert('يرجى اختيار حل وإضافة تعليق');
      return;
    }

    setIsSubmitting(true);
    try {
      await onApprovalSubmit({
        selectedSolutionId,
        comment: comment.trim(),
        action
      });
      
      // إعادة تعيين النموذج
      setSelectedSolutionId('');
      setComment('');
    } catch (error) {
      console.error('Error submitting approval:', error);
      alert('حدث خطأ أثناء إرسال الموافقة');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusDisplay = (status: 'pending' | 'approved' | 'rejected') => {
    switch (status) {
      case 'approved':
        return {
          icon: '✅',
          color: 'text-green-600 bg-green-50 border-green-200',
          textColor: 'text-green-800',
          statusText: 'تمت الموافقة',
          bgColor: 'bg-green-100'
        };
      case 'rejected':
        return {
          icon: '❌',
          color: 'text-red-600 bg-red-50 border-red-200',
          textColor: 'text-red-800',
          statusText: 'تم الرفض',
          bgColor: 'bg-red-100'
        };
      case 'pending':
      default:
        return {
          icon: '⏳',
          color: 'text-orange-600 bg-orange-50 border-orange-200',
          textColor: 'text-orange-800',
          statusText: 'بانتظار الموافقة',
          bgColor: 'bg-orange-100'
        };
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'approve':
        return (
          <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'reject':
        return (
          <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      case 'comment':
        return (
          <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        );
      case 'solution_selection':
        return (
          <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  const allApproved = participantApprovals.every(p => p.status === 'approved');
  const hasRejections = participantApprovals.some(p => p.status === 'rejected');

  return (
    <div className="space-y-6">
      {/* ملخص حالة الموافقات */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">حالة الموافقات</h3>
            <p className="text-sm text-gray-600 mt-1">
              بانتظار الموافقة من: القسم المسؤول، قائد الفريق، والأقسام المشاركة
            </p>
          </div>
          <div className="flex items-center space-x-2 space-x-reverse">
            {allApproved && (
              <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                ✅ تمت الموافقة من جميع الأقسام
              </span>
            )}
            {hasRejections && (
              <span className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
                ❌ يوجد رفض من بعض الأقسام
              </span>
            )}
            {!allApproved && !hasRejections && (
              <span className="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm font-medium">
                ⏳ في انتظار الموافقات
              </span>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {participantApprovals.map((approval) => {
            const statusDisplay = getStatusDisplay(approval.status);
            return (
              <div key={approval.participantId} className={`border-2 rounded-lg p-4 ${statusDisplay.color}`}>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <span className="text-2xl">{statusDisplay.icon}</span>
                    <h4 className={`font-semibold ${statusDisplay.textColor}`}>{approval.participantName}</h4>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${statusDisplay.bgColor} ${statusDisplay.textColor}`}>
                    {statusDisplay.statusText}
                  </span>
                </div>
              
              <div className={`text-sm mb-2 font-medium ${statusDisplay.textColor}`}>
                {approval.participantRole === 'team_leader' ? '👨‍💼 قائد الفريق' :
                 approval.participantRole === 'department_manager' ? '🏢 مدير القسم المسؤول' :
                 approval.participantRole === 'participating_department_manager' ? '🤝 مدير قسم مشارك' :
                 approval.participantRole} - {approval.department}
              </div>

              {approval.selectedSolutionId && (
                <div className="text-sm text-gray-600 mb-2">
                  <span className="font-medium">الحل المختار:</span> {
                    solutions.find(s => s.id === approval.selectedSolutionId)?.title || 'غير محدد'
                  }
                </div>
              )}

              {approval.comment && (
                <div className="text-sm text-gray-600 mb-2">
                  <span className="font-medium">التعليق:</span> {approval.comment}
                </div>
              )}

              {approval.timestamp && (
                <div className="text-xs text-gray-500">
                  {new Date(approval.timestamp).toLocaleString('ar-SA')}
                </div>
              )}
            </div>
            );
          })}
        </div>
      </Card>

      {/* نموذج إضافة موافقة (للمشاركين فقط) */}
      {currentParticipant && !hasSubmitted && (
        <Card className="p-6 border-blue-200 bg-blue-50">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            إضافة موافقتك على المقترح
          </h3>

          <div className="space-y-4">
            {/* اختيار الحل */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اختر الحل الأمثل من وجهة نظرك *
              </label>
              <div className="space-y-3">
                {solutions.map((solution) => (
                  <div key={solution.id} className="flex items-start space-x-3 space-x-reverse">
                    <input
                      type="radio"
                      id={`solution-${solution.id}`}
                      name="selectedSolution"
                      value={solution.id}
                      checked={selectedSolutionId === solution.id}
                      onChange={(e) => setSelectedSolutionId(e.target.value)}
                      className="mt-1"
                    />
                    <label htmlFor={`solution-${solution.id}`} className="flex-1 cursor-pointer">
                      <div className="font-medium text-gray-900">{solution.title}</div>
                      <div className="text-sm text-gray-600 mt-1">{solution.description}</div>
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* التعليق */}
            <div>
              <label htmlFor="comment" className="block text-sm font-medium text-gray-700 mb-2">
                تعليقك ومبرر الاختيار *
              </label>
              <textarea
                id="comment"
                rows={4}
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                placeholder="اكتب تعليقك ومبرر اختيار هذا الحل..."
              />
            </div>

            {/* أزرار الإجراء */}
            <div className="flex space-x-3 space-x-reverse">
              <Button
                onClick={() => handleSubmitApproval('approve')}
                disabled={!selectedSolutionId || !comment.trim() || isSubmitting}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                {isSubmitting ? 'جاري الإرسال...' : 'موافق على المقترح'}
              </Button>
              
              <Button
                onClick={() => handleSubmitApproval('reject')}
                disabled={!selectedSolutionId || !comment.trim() || isSubmitting}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isSubmitting ? 'جاري الإرسال...' : 'رفض المقترح'}
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* التايم لاين */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">تايم لاين موافقة المشاركين على المقترح</h3>
        
        <div className="flow-root">
          <ul className="-mb-8">
            {timeline.map((entry, index) => (
              <li key={entry.id}>
                <div className="relative pb-8">
                  {index !== timeline.length - 1 && (
                    <span className="absolute top-4 right-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true" />
                  )}
                  
                  <div className="relative flex space-x-3 space-x-reverse">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full border-2 border-white bg-gray-100 shadow">
                      {getActionIcon(entry.action)}
                    </div>
                    
                    <div className="flex min-w-0 flex-1 justify-between space-x-4 space-x-reverse pt-1.5">
                      <div className="flex-1">
                        <p className="text-sm text-gray-900">
                          <span className="font-medium">{entry.participantName}</span>
                          {' '}
                          <span className="text-gray-600">({entry.participantRole} - {entry.department})</span>
                        </p>
                        
                        {entry.action === 'solution_selection' && entry.selectedSolutionTitle && (
                          <p className="mt-1 text-sm text-gray-600">
                            <span className="font-medium">اختار الحل:</span> {entry.selectedSolutionTitle}
                          </p>
                        )}
                        
                        {entry.comment && (
                          <p className="mt-1 text-sm text-gray-600">
                            <span className="font-medium">التعليق:</span> {entry.comment}
                          </p>
                        )}
                      </div>
                      
                      <div className="whitespace-nowrap text-right text-sm text-gray-500">
                        <time dateTime={entry.timestamp}>
                          {new Date(entry.timestamp).toLocaleString('ar-SA')}
                        </time>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>

        {timeline.length === 0 && (
          <div className="text-center py-8">
            <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-gray-500">لا توجد أنشطة بعد</p>
            <p className="text-sm text-gray-400 mt-1">ستظهر هنا جميع الموافقات والتعليقات</p>
          </div>
        )}
      </Card>
    </div>
  );
} 