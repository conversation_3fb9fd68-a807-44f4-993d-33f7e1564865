'use client'

import React from 'react'
import { Input, Textarea, Select } from '@/components/ui/Input'
import { FieldHelp } from '@/components/ui/HelpSystem'
import { Brain, HelpCircle, Plus, Trash2 } from 'lucide-react'
import { FormType, UnifiedFormData } from '../UnifiedProjectForm'

interface AdaptiveUnderstandStepProps {
  formType: FormType
  data: UnifiedFormData
  updateData: (field: string, value: any) => void
  errors: Record<string, string>
}

export function AdaptiveUnderstandStep({ formType, data, updateData, errors }: AdaptiveUnderstandStepProps) {
  const analysisMethodOptions = [
    { value: 'five_whys', label: '5 Whys - خمسة أسئلة لماذا', description: 'تحليل بسيط ومباشر للوصول للسبب الجذري' },
    { value: 'fishbone', label: 'Fishbone - مخطط عظمة السمك', description: 'تحليل شامل للأسباب المحتملة في 6 فئات' },
    { value: 'root_cause_analysis', label: 'Root Cause Analysis - تحليل السبب الجذري', description: 'تحليل متقدم ومفصل للأسباب' }
  ]

  const fishboneCategories = [
    { key: 'people', label: 'الأشخاص (People)', icon: '👥', placeholder: 'مثال: نقص التدريب، قلة الخبرة' },
    { key: 'process', label: 'العمليات (Process)', icon: '⚙️', placeholder: 'مثال: عدم وضوح الإجراءات، تعقيد العملية' },
    { key: 'equipment', label: 'المعدات (Equipment)', icon: '🔧', placeholder: 'مثال: أعطال متكررة، قدم المعدات' },
    { key: 'environment', label: 'البيئة (Environment)', icon: '🌍', placeholder: 'مثال: ضوضاء، مساحة ضيقة' },
    { key: 'materials', label: 'المواد (Materials)', icon: '📦', placeholder: 'مثال: جودة منخفضة، نقص في المواد' },
    { key: 'measurement', label: 'القياس (Measurement)', icon: '📊', placeholder: 'مثال: عدم دقة القياسات، نقص البيانات' }
  ]

  const getStepTitle = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'Understand - فهم الأسباب الجذرية'
      case 'suggestion':
        return 'Understand - فهم أسباب المشكلة'
      case 'quick_win':
        return 'Understand - فهم السبب الرئيسي'
      default:
        return 'Understand - فهم الأسباب'
    }
  }

  const getStepDescription = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'حلل المشكلة بعمق لفهم الأسباب الجذرية باستخدام أدوات التحليل المناسبة'
      case 'suggestion':
        return 'حلل أسباب المشكلة أو الفرصة التحسينية لفهم جذور المشكلة'
      case 'quick_win':
        return 'حدد السبب الرئيسي للمشكلة بطريقة سريعة ومباشرة'
      default:
        return 'حلل أسباب المشكلة'
    }
  }

  const getStepColor = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'purple'
      case 'suggestion':
        return 'indigo'
      case 'quick_win':
        return 'yellow'
      default:
        return 'purple'
    }
  }

  const colorClasses = {
    purple: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      text: 'text-purple-900',
      icon: 'text-purple-600'
    },
    indigo: {
      bg: 'bg-indigo-50',
      border: 'border-indigo-200',
      text: 'text-indigo-900',
      icon: 'text-indigo-600'
    },
    yellow: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      text: 'text-yellow-900',
      icon: 'text-yellow-600'
    }
  }

  const stepColor = getStepColor()
  const colors = colorClasses[stepColor]

  const updateFiveWhysStep = (index: number, value: string) => {
    const updated = [...data.fiveWhysSteps]
    updated[index] = value
    updateData('fiveWhysSteps', updated)
  }

  const addFishboneFactor = (category: string) => {
    const updated = { ...data.fishboneFactors }
    updated[category as keyof typeof data.fishboneFactors].push('')
    updateData('fishboneFactors', updated)
  }

  const removeFishboneFactor = (category: string, index: number) => {
    const updated = { ...data.fishboneFactors }
    updated[category as keyof typeof data.fishboneFactors] = 
      updated[category as keyof typeof data.fishboneFactors].filter((_, i) => i !== index)
    updateData('fishboneFactors', updated)
  }

  const updateFishboneFactor = (category: string, index: number, value: string) => {
    const updated = { ...data.fishboneFactors }
    updated[category as keyof typeof data.fishboneFactors][index] = value
    updateData('fishboneFactors', updated)
  }

  return (
    <div className="space-y-6">
      <div className={`${colors.bg} border ${colors.border} rounded-lg p-4 mb-6`}>
        <div className="flex items-center gap-2 mb-2">
          <Brain className={`w-5 h-5 ${colors.icon}`} />
          <h3 className={`font-semibold ${colors.text}`}>{getStepTitle()}</h3>
        </div>
        <p className={`${colors.text} text-sm`}>
          {getStepDescription()}
        </p>
      </div>

      <div className="space-y-6">
        {/* طريقة التحليل */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <label className="block text-sm font-medium text-gray-700">
              طريقة التحليل *
            </label>
            <FieldHelp 
              content="اختر الطريقة المناسبة لتحليل أسباب المشكلة"
              field="analysisMethod"
              step={5}
            />
          </div>

          <div className="grid grid-cols-1 gap-3">
            {analysisMethodOptions.map((method) => (
              <label
                key={method.value}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  data.analysisMethod === method.value
                    ? 'border-blue-500 bg-blue-50 text-blue-900'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <div className="flex items-center">
                  <input
                    type="radio"
                    value={method.value}
                    checked={data.analysisMethod === method.value}
                    onChange={(e) => updateData('analysisMethod', e.target.value)}
                    className="mr-3"
                  />
                  <div>
                    <div className="font-medium">{method.label}</div>
                    <div className="text-sm text-gray-600 mt-1">{method.description}</div>
                  </div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* السبب الجذري */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <label className="block text-sm font-medium text-gray-700">
              السبب الجذري المحدد *
            </label>
            <FieldHelp 
              content="بناءً على التحليل، ما هو السبب الجذري الرئيسي للمشكلة؟"
              field="rootCause"
              step={5}
            />
          </div>
          <Textarea
            value={data.rootCause}
            onChange={(e) => updateData('rootCause', e.target.value)}
            placeholder="اكتب السبب الجذري الذي توصلت إليه من خلال التحليل..."
            rows={3}
            error={errors.rootCause}
            maxLength={500}
          />
          <div className="text-xs text-gray-500 mt-1">
            {data.rootCause.length}/500 حرف
          </div>
        </div>

        {/* تفاصيل التحليل */}
        {data.analysisMethod === 'five_whys' && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-semibold text-blue-900 mb-4 flex items-center gap-2">
              <HelpCircle className="w-5 h-5" />
              تحليل الخمسة أسئلة (5 Whys)
            </h4>
            <div className="space-y-4">
              {data.fiveWhysSteps.map((step, index) => (
                <div key={index}>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    لماذا {index + 1}؟
                  </label>
                  <Input
                    value={step}
                    onChange={(e) => updateFiveWhysStep(index, e.target.value)}
                    placeholder={`${index === 0 ? 'لماذا حدثت المشكلة؟' : 'لماذا حدث السبب السابق؟'}`}
                  />
                </div>
              ))}
            </div>
            <div className="mt-4 p-3 bg-blue-100 rounded-lg">
              <p className="text-blue-800 text-sm">
                <strong>نصيحة:</strong> استمر في السؤال &quot;لماذا؟&quot; حتى تصل للسبب الجذري الحقيقي
              </p>
            </div>
          </div>
        )}

        {data.analysisMethod === 'fishbone' && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-semibold text-green-900 mb-4 flex items-center gap-2">
              <Brain className="w-5 h-5" />
              مخطط عظمة السمك (Fishbone Diagram)
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {fishboneCategories.map((category) => (
                <div key={category.key} className="bg-white border rounded-lg p-4">
                  <div className="flex justify-between items-center mb-3">
                    <h5 className="font-medium text-gray-900 flex items-center gap-2">
                      <span>{category.icon}</span>
                      {category.label}
                    </h5>
                    <button
                      type="button"
                      onClick={() => addFishboneFactor(category.key)}
                      className="text-green-600 hover:text-green-700"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>

                  <div className="space-y-2">
                    {data.fishboneFactors[category.key as keyof typeof data.fishboneFactors].map((factor, index) => (
                      <div key={index} className="flex gap-2">
                        <Input
                          value={factor}
                          onChange={(e) => updateFishboneFactor(category.key, index, e.target.value)}
                          placeholder={category.placeholder}
                          className="flex-1 text-sm"
                        />
                        <button
                          type="button"
                          onClick={() => removeFishboneFactor(category.key, index)}
                          className="text-red-600 hover:text-red-700 p-1"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    ))}

                    {data.fishboneFactors[category.key as keyof typeof data.fishboneFactors].length === 0 && (
                      <div className="text-center py-3 text-gray-500 text-sm">
                        لا توجد عوامل مضافة
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {data.analysisMethod === 'root_cause_analysis' && (
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <h4 className="font-semibold text-purple-900 mb-4">
              تحليل السبب الجذري التفصيلي
            </h4>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وصف التحليل المفصل
                </label>
                <Textarea
                  value={data.rootCause}
                  onChange={(e) => updateData('rootCause', e.target.value)}
                  placeholder="اكتب تحليلاً مفصلاً للأسباب المحتملة والعوامل المؤثرة..."
                  rows={6}
                  maxLength={1000}
                />
                <div className="text-xs text-gray-500 mt-1">
                  {data.rootCause.length}/1000 حرف
                </div>
              </div>
            </div>
          </div>
        )}

        {/* ملخص التحليل */}
        {data.rootCause && (
          <div className="bg-gray-50 border rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 mb-2">ملخص التحليل</h4>
            <div className="text-sm text-gray-700">
              <p><strong>الطريقة المستخدمة:</strong> {analysisMethodOptions.find(m => m.value === data.analysisMethod)?.label}</p>
              <p className="mt-2"><strong>السبب الجذري:</strong> {data.rootCause}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
} 