-- Migration: تحديث جدول project_requests لدعم النظام الهرمي الجديد
-- التاريخ: 2024-01-25
-- الوصف: إضافة حقول main_type, sub_type, form_data, approval_level وإزالة حقل type القديم

-- إضافة الحقول الجديدة
ALTER TABLE project_requests 
ADD COLUMN IF NOT EXISTS main_type VARCHAR(30),
ADD COLUMN IF NOT EXISTS sub_type VARCHAR(30),
ADD COLUMN IF NOT EXISTS form_data JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS approval_level INTEGER DEFAULT 0;

-- تحديث البيانات الموجودة (إذا وجدت)
UPDATE project_requests 
SET 
  main_type = CASE 
    WHEN type = 'request' THEN 'general_project'
    WHEN type IN ('suggestion', 'quick_win') THEN 'improvement_project'
    ELSE 'general_project'
  END,
  sub_type = CASE 
    WHEN type = 'suggestion' THEN 'suggestion'
    WHEN type = 'quick_win' THEN 'quick_win'
    ELSE NULL
  END
WHERE main_type IS NULL;

-- إضافة القيود الجديدة
ALTER TABLE project_requests 
ADD CONSTRAINT check_main_type 
CHECK (main_type IN ('general_project', 'improvement_project'));

ALTER TABLE project_requests 
ADD CONSTRAINT check_sub_type 
CHECK (sub_type IN ('quick_win', 'improvement_full', 'suggestion') OR sub_type IS NULL);

-- جعل main_type مطلوب
ALTER TABLE project_requests 
ALTER COLUMN main_type SET NOT NULL;

-- إزالة القيد القديم وحذف العمود
ALTER TABLE project_requests 
DROP CONSTRAINT IF EXISTS project_requests_type_check;

-- حذف العمود القديم (بعد التأكد من نقل البيانات)
-- ALTER TABLE project_requests DROP COLUMN IF EXISTS type;

-- إضافة فهارس جديدة للأداء
CREATE INDEX IF NOT EXISTS idx_project_requests_main_type ON project_requests(main_type);
CREATE INDEX IF NOT EXISTS idx_project_requests_sub_type ON project_requests(sub_type);
CREATE INDEX IF NOT EXISTS idx_project_requests_approval_level ON project_requests(approval_level);

-- إضافة تعليق للجدول
COMMENT ON TABLE project_requests IS 'جدول طلبات المشاريع مع دعم النظام الهرمي الجديد';
COMMENT ON COLUMN project_requests.main_type IS 'النوع الرئيسي للطلب (مشروع عام أو تحسين)';
COMMENT ON COLUMN project_requests.sub_type IS 'النوع الفرعي لمشاريع التحسين';
COMMENT ON COLUMN project_requests.form_data IS 'بيانات النموذج المحفوظة كـ JSON';
COMMENT ON COLUMN project_requests.approval_level IS 'المستوى الحالي للاعتماد'; 
-- التاريخ: 2024-01-25
-- الوصف: إضافة حقول main_type, sub_type, form_data, approval_level وإزالة حقل type القديم

-- إضافة الحقول الجديدة
ALTER TABLE project_requests 
ADD COLUMN IF NOT EXISTS main_type VARCHAR(30),
ADD COLUMN IF NOT EXISTS sub_type VARCHAR(30),
ADD COLUMN IF NOT EXISTS form_data JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS approval_level INTEGER DEFAULT 0;

-- تحديث البيانات الموجودة (إذا وجدت)
UPDATE project_requests 
SET 
  main_type = CASE 
    WHEN type = 'request' THEN 'general_project'
    WHEN type IN ('suggestion', 'quick_win') THEN 'improvement_project'
    ELSE 'general_project'
  END,
  sub_type = CASE 
    WHEN type = 'suggestion' THEN 'suggestion'
    WHEN type = 'quick_win' THEN 'quick_win'
    ELSE NULL
  END
WHERE main_type IS NULL;

-- إضافة القيود الجديدة
ALTER TABLE project_requests 
ADD CONSTRAINT check_main_type 
CHECK (main_type IN ('general_project', 'improvement_project'));

ALTER TABLE project_requests 
ADD CONSTRAINT check_sub_type 
CHECK (sub_type IN ('quick_win', 'improvement_full', 'suggestion') OR sub_type IS NULL);

-- جعل main_type مطلوب
ALTER TABLE project_requests 
ALTER COLUMN main_type SET NOT NULL;

-- إزالة القيد القديم وحذف العمود
ALTER TABLE project_requests 
DROP CONSTRAINT IF EXISTS project_requests_type_check;

-- حذف العمود القديم (بعد التأكد من نقل البيانات)
-- ALTER TABLE project_requests DROP COLUMN IF EXISTS type;

-- إضافة فهارس جديدة للأداء
CREATE INDEX IF NOT EXISTS idx_project_requests_main_type ON project_requests(main_type);
CREATE INDEX IF NOT EXISTS idx_project_requests_sub_type ON project_requests(sub_type);
CREATE INDEX IF NOT EXISTS idx_project_requests_approval_level ON project_requests(approval_level);

-- إضافة تعليق للجدول
COMMENT ON TABLE project_requests IS 'جدول طلبات المشاريع مع دعم النظام الهرمي الجديد';
COMMENT ON COLUMN project_requests.main_type IS 'النوع الرئيسي للطلب (مشروع عام أو تحسين)';
COMMENT ON COLUMN project_requests.sub_type IS 'النوع الفرعي لمشاريع التحسين';
COMMENT ON COLUMN project_requests.form_data IS 'بيانات النموذج المحفوظة كـ JSON';
COMMENT ON COLUMN project_requests.approval_level IS 'المستوى الحالي للاعتماد'; 