'use client'

import React from 'react'
import { Card } from '@/components/ui/Card'
import { 
  Briefcase, 
  Target, 
  Zap, 
  Lightbulb, 
  Settings,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  TrendingUp,
  Users,
  Calendar
} from 'lucide-react'

interface RequestStats {
  total: number
  byMainType: {
    general_project: number
    improvement_project: number
  }
  bySubType: {
    quick_win: number
    improvement_full: number
    suggestion: number
  }
  byStatus: {
    draft: number
    submitted: number
    under_review: number
    approved: number
    rejected: number
    completed: number
  }
  byPriority: {
    urgent: number
    high: number
    medium: number
    low: number
  }
}

interface RequestsOverviewProps {
  stats?: RequestStats
}

export function RequestsOverview({ stats }: RequestsOverviewProps) {
  // بيانات وهمية للتطوير
  const defaultStats: RequestStats = {
    total: 25,
    byMainType: {
      general_project: 12,
      improvement_project: 13
    },
    bySubType: {
      quick_win: 6,
      improvement_full: 4,
      suggestion: 3
    },
    byStatus: {
      draft: 3,
      submitted: 5,
      under_review: 8,
      approved: 6,
      rejected: 1,
      completed: 2
    },
    byPriority: {
      urgent: 2,
      high: 8,
      medium: 12,
      low: 3
    }
  }

  const currentStats = stats || defaultStats

  const mainTypeCards = [
    {
      id: 'general_project',
      title: 'المشاريع العامة',
      count: currentStats.byMainType.general_project,
      icon: <Briefcase className="w-8 h-8" />,
      color: 'purple',
      description: 'مشاريع البنية التحتية والتطوير العام'
    },
    {
      id: 'improvement_project',
      title: 'مشاريع التحسين',
      count: currentStats.byMainType.improvement_project,
      icon: <Target className="w-8 h-8" />,
      color: 'blue',
      description: 'مشاريع التحسين المستمر والجودة'
    }
  ]

  const subTypeCards = [
    {
      id: 'quick_win',
      title: 'كويك وين',
      count: currentStats.bySubType.quick_win,
      icon: <Zap className="w-6 h-6" />,
      color: 'orange',
      description: 'تحسين سريع، نتائج خلال أسابيع'
    },
    {
      id: 'improvement_full',
      title: 'تحسين شامل',
      count: currentStats.bySubType.improvement_full,
      icon: <Settings className="w-6 h-6" />,
      color: 'green',
      description: 'مشاريع تحسين كاملة'
    },
    {
      id: 'suggestion',
      title: 'مقترحات',
      count: currentStats.bySubType.suggestion,
      icon: <Lightbulb className="w-6 h-6" />,
      color: 'yellow',
      description: 'مقترحات التحسين'
    }
  ]

  const statusCards = [
    {
      id: 'under_review',
      title: 'قيد المراجعة',
      count: currentStats.byStatus.under_review,
      icon: <Clock className="w-6 h-6" />,
      color: 'yellow'
    },
    {
      id: 'approved',
      title: 'معتمد',
      count: currentStats.byStatus.approved,
      icon: <CheckCircle className="w-6 h-6" />,
      color: 'green'
    },
    {
      id: 'submitted',
      title: 'مرسل',
      count: currentStats.byStatus.submitted,
      icon: <Users className="w-6 h-6" />,
      color: 'blue'
    },
    {
      id: 'draft',
      title: 'مسودة',
      count: currentStats.byStatus.draft,
      icon: <AlertTriangle className="w-6 h-6" />,
      color: 'gray'
    }
  ]

  const getColorClasses = (color: string) => {
    const colorMap: Record<string, string> = {
      purple: 'bg-purple-100 text-purple-600 border-purple-200',
      blue: 'bg-blue-100 text-blue-600 border-blue-200',
      orange: 'bg-orange-100 text-orange-600 border-orange-200',
      green: 'bg-green-100 text-green-600 border-green-200',
      yellow: 'bg-yellow-100 text-yellow-600 border-yellow-200',
      gray: 'bg-gray-100 text-gray-600 border-gray-200'
    }
    return colorMap[color] || colorMap.gray
  }

  return (
    <div className="space-y-6">
      {/* إجمالي الطلبات */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">
              إجمالي طلبات المشاريع
            </h3>
            <p className="text-3xl font-bold text-blue-600">{currentStats.total}</p>
          </div>
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
            <TrendingUp className="w-8 h-8 text-blue-600" />
          </div>
        </div>
      </Card>

      {/* الأنواع الرئيسية */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">الأنواع الرئيسية</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {mainTypeCards.map(card => (
            <Card key={card.id} className={`p-6 border-2 ${getColorClasses(card.color)}`}>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h4 className="font-semibold text-lg mb-1">{card.title}</h4>
                  <p className="text-2xl font-bold mb-2">{card.count}</p>
                  <p className="text-sm opacity-80">{card.description}</p>
                </div>
                <div className="opacity-60">
                  {card.icon}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* أنواع التحسين الفرعية */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">أنواع مشاريع التحسين</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {subTypeCards.map(card => (
            <Card key={card.id} className={`p-4 border-2 ${getColorClasses(card.color)}`}>
              <div className="flex items-center gap-3">
                <div className="opacity-60">
                  {card.icon}
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold">{card.title}</h4>
                  <p className="text-xl font-bold">{card.count}</p>
                  <p className="text-xs opacity-80">{card.description}</p>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* حالات الطلبات */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">حالات الطلبات</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {statusCards.map(card => (
            <Card key={card.id} className={`p-4 border-2 ${getColorClasses(card.color)}`}>
              <div className="text-center">
                <div className="flex justify-center mb-2 opacity-60">
                  {card.icon}
                </div>
                <h4 className="font-semibold text-sm mb-1">{card.title}</h4>
                <p className="text-xl font-bold">{card.count}</p>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* الأولويات */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">توزيع الأولويات</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
            <p className="text-sm text-red-600 font-medium">عاجل</p>
            <p className="text-2xl font-bold text-red-700">{currentStats.byPriority.urgent}</p>
          </div>
          <div className="text-center p-3 bg-orange-50 rounded-lg border border-orange-200">
            <p className="text-sm text-orange-600 font-medium">عالي</p>
            <p className="text-2xl font-bold text-orange-700">{currentStats.byPriority.high}</p>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-sm text-blue-600 font-medium">متوسط</p>
            <p className="text-2xl font-bold text-blue-700">{currentStats.byPriority.medium}</p>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg border border-gray-200">
            <p className="text-sm text-gray-600 font-medium">منخفض</p>
            <p className="text-2xl font-bold text-gray-700">{currentStats.byPriority.low}</p>
          </div>
        </div>
      </Card>
    </div>
  )
} 