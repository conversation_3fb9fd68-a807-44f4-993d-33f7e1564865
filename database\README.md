# 🗄️ قاعدة البيانات - مشروع إرادة PMO 2025

## 📋 **نظرة عامة**

هذا المجلد يحتوي على جميع ملفات قاعدة البيانات وإعداداتها منظمة لسهولة الإدارة والصيانة.

---

## 📁 **هيكل المجلد**

### **📜 ملفات SQL (sql/)**
ملفات قاعدة البيانات والاستعلامات:

- **[CREATE_ADMIN_USER.sql](./sql/CREATE_ADMIN_USER.sql)**
  - إنشاء مستخدم المدير الرئيسي
  - إعداد الصلاحيات الأساسية

### **⚙️ ملفات الإعداد (config/)**
إعدادات الاتصال والتكوين:

- **[supabase-config.env](./config/supabase-config.env)**
  - متغيرات البيئة لـ Supabase
  - إعدادات الاتصال

- **[supabase-config.txt](./config/supabase-config.txt)**
  - ملاحظات وتوثيق الإعدادات
  - معلومات إضافية للتكوين

---

## 🔧 **إعداد قاعدة البيانات**

### **الخطوة 1: إعداد Supabase**
```bash
# نسخ ملف الإعدادات
cp database/config/supabase-config.env .env.local

# تحديث المتغيرات حسب مشروعك
# NEXT_PUBLIC_SUPABASE_URL=your_project_url
# NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
# SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### **الخطوة 2: إنشاء المستخدم الإداري**
```sql
-- تشغيل ملف SQL في Supabase Dashboard
\i database/sql/CREATE_ADMIN_USER.sql
```

### **الخطوة 3: التحقق من الاتصال**
```bash
# تشغيل اختبار الاتصال
npm run dev
# زيارة /testing/database للتحقق
```

---

## 📊 **هيكل قاعدة البيانات**

### **الجداول الرئيسية**
- `users` - بيانات المستخدمين
- `user_roles` - أدوار المستخدمين
- `departments` - الأقسام
- `projects` - المشاريع
- `requests` - الطلبات
- `approvals` - الموافقات

### **جداول المراجعة**
- `audit_logs` - سجل العمليات
- `login_attempts` - محاولات تسجيل الدخول
- `user_sessions` - جلسات المستخدمين

---

## 🔐 **الأمان والصلاحيات**

### **Row Level Security (RLS)**
جميع الجداول محمية بـ RLS لضمان:
- المستخدمون يرون بياناتهم فقط
- الأدوار تحدد مستوى الوصول
- العمليات مسجلة في Audit Log

### **أدوار المستخدمين**
- `super_admin` - مدير النظام الرئيسي
- `admin` - مدير النظام
- `pmo_manager` - مدير مكتب المشاريع
- `department_manager` - مدير القسم
- `team_leader` - قائد الفريق
- `employee` - موظف

---

## 🛠️ **الصيانة والنسخ الاحتياطي**

### **النسخ الاحتياطي**
```bash
# نسخ احتياطي يومي (يتم تلقائياً في Supabase)
# للنسخ اليدوي:
supabase db dump > backup_$(date +%Y%m%d).sql
```

### **التحديثات**
```bash
# تطبيق تحديثات قاعدة البيانات
supabase db push

# مراجعة التغييرات
supabase db diff
```

---

## 📈 **المراقبة والأداء**

### **مؤشرات الأداء**
- عدد الاستعلامات في الثانية
- وقت الاستجابة المتوسط
- استخدام الذاكرة
- حجم قاعدة البيانات

### **التحسين**
- فهرسة الجداول المهمة
- تحسين الاستعلامات البطيئة
- تنظيف البيانات القديمة
- ضغط الجداول الكبيرة

---

## 🔍 **استكشاف الأخطاء**

### **مشاكل شائعة**
1. **خطأ في الاتصال**
   - تحقق من متغيرات البيئة
   - تأكد من صحة URL و Keys

2. **مشاكل الصلاحيات**
   - راجع إعدادات RLS
   - تحقق من أدوار المستخدمين

3. **بطء في الأداء**
   - راجع الاستعلامات المعقدة
   - تحقق من الفهارس

### **أدوات التشخيص**
- Supabase Dashboard
- صفحة `/testing/database`
- سجلات النظام

---

## 📞 **للمساعدة**

- **الوثائق الرسمية**: [Supabase Docs](https://supabase.com/docs)
- **دليل التكامل**: [../docs/guides/SUPABASE_INTEGRATION_GUIDE.md](../docs/guides/SUPABASE_INTEGRATION_GUIDE.md)
- **حل المشاكل**: [../docs/guides/TROUBLESHOOTING_GUIDE.md](../docs/guides/TROUBLESHOOTING_GUIDE.md)

---

**آخر تحديث**: 2025-07-11  
**المسؤول**: Augment Agent  
**حالة قاعدة البيانات**: مُعدة ومحسنة ✅
