'use client'

import React from 'react'
import { Input, Textarea, Select } from '@/components/ui/Input'
import { Button } from '@/components/ui/Button'
import { FieldHelp } from '@/components/ui/HelpSystem'
import { Calendar, Plus, Trash2, DollarSign, Users, Settings } from 'lucide-react'
import { FormType, UnifiedFormData, EnhancedImprovementData } from '../UnifiedProjectForm'

interface AdaptiveProjectPlanningStepProps {
  formType: FormType
  data: UnifiedFormData
  updateData: (field: string, value: any) => void
  errors: Record<string, string>
}

export function AdaptiveProjectPlanningStep({ formType, data, updateData, errors }: AdaptiveProjectPlanningStepProps) {
  // هذا المكون خاص بالتحسين الشامل فقط
  if (formType !== 'enhanced_improvement') {
    return null
  }

  const enhancedData = data as EnhancedImprovementData

  // دوال إدارة المهام
  const addTask = () => {
    const newTask = {
      title: '',
      description: '',
      assignee: enhancedData.teamLeader.name || 'قائد المشروع',
      startDate: enhancedData.startDate || '',
      endDate: enhancedData.endDate || '',
      status: 'pending' as const
    }
    updateData('projectTasks', [...enhancedData.projectTasks, newTask])
  }

  const removeTask = (index: number) => {
    const updated = enhancedData.projectTasks.filter((_, i) => i !== index)
    updateData('projectTasks', updated)
  }

  const updateTask = (index: number, field: string, value: any) => {
    const updated = enhancedData.projectTasks.map((task, i) => 
      i === index ? { ...task, [field]: value } : task
    )
    updateData('projectTasks', updated)
  }

  // دوال إدارة الموارد
  const addResource = () => {
    const newResource = {
      type: 'human' as const,
      description: '',
      quantity: 1,
      cost: 0,
      unit: 'شخص'
    }
    updateData('requiredResources', [...enhancedData.requiredResources, newResource])
  }

  const removeResource = (index: number) => {
    const updated = enhancedData.requiredResources.filter((_, i) => i !== index)
    updateData('requiredResources', updated)
  }

  const updateResource = (index: number, field: string, value: any) => {
    const updated = enhancedData.requiredResources.map((resource, i) => 
      i === index ? { ...resource, [field]: value } : resource
    )
    updateData('requiredResources', updated)
  }

  // حساب التكلفة الإجمالية
  const totalCost = enhancedData.requiredResources.reduce((sum, resource) => 
    sum + (resource.quantity * resource.cost), 0
  )

  const getResourceTypeIcon = (type: string) => {
    switch (type) {
      case 'human': return <Users className="w-4 h-4" />
      case 'financial': return <DollarSign className="w-4 h-4" />
      case 'equipment': return <Settings className="w-4 h-4" />
      default: return <Settings className="w-4 h-4" />
    }
  }

  const getResourceTypeOptions = () => [
    { value: 'human', label: 'موارد بشرية' },
    { value: 'financial', label: 'موارد مالية' },
    { value: 'equipment', label: 'معدات وأجهزة' },
    { value: 'software', label: 'برمجيات وأنظمة' },
    { value: 'other', label: 'أخرى' }
  ]

  const getDefaultUnit = (type: string) => {
    switch (type) {
      case 'human': return 'شخص'
      case 'financial': return 'ريال'
      case 'equipment': return 'قطعة'
      case 'software': return 'ترخيص'
      default: return 'وحدة'
    }
  }

  return (
    <div className="space-y-8">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex items-center gap-2 mb-2">
          <Calendar className="w-5 h-5 text-blue-600" />
          <h3 className="font-semibold text-blue-900">تخطيط المشروع</h3>
        </div>
        <p className="text-blue-800 text-sm">
          حدد مهام المشروع والموارد المطلوبة بناءً على الحل المختار. هذه المعلومات ستكون أساس إدارة المشروع لاحقاً.
        </p>
      </div>

      {/* مهام المشروع */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <h4 className="font-semibold text-gray-900">مهام المشروع *</h4>
            <FieldHelp 
              content="حدد المهام الرئيسية اللازمة لتنفيذ الحل المختار مع توزيع المسؤوليات والمواعيد"
              field="projectTasks"
              step={7}
            />
          </div>
          <Button onClick={addTask} variant="secondary" size="sm">
            <Plus className="w-4 h-4 mr-2" />
            إضافة مهمة
          </Button>
        </div>

        {enhancedData.projectTasks.length === 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <p className="text-yellow-800 text-sm">
              أضف على الأقل مهمة واحدة لتخطيط المشروع
            </p>
          </div>
        )}

        {enhancedData.projectTasks.map((task, index) => (
          <div key={index} className="bg-white border rounded-lg p-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    عنوان المهمة *
                  </label>
                  <Input
                    value={task.title}
                    onChange={(e) => updateTask(index, 'title', e.target.value)}
                    placeholder="اكتب عنوان المهمة..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    المسؤول *
                  </label>
                  <Input
                    value={task.assignee}
                    onChange={(e) => updateTask(index, 'assignee', e.target.value)}
                    placeholder="اسم المسؤول..."
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وصف المهمة
                </label>
                <Textarea
                  value={task.description}
                  onChange={(e) => updateTask(index, 'description', e.target.value)}
                  placeholder="اكتب وصف تفصيلي للمهمة..."
                  rows={2}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    تاريخ البداية
                  </label>
                  <Input
                    type="date"
                    value={task.startDate}
                    onChange={(e) => updateTask(index, 'startDate', e.target.value)}
                    min={enhancedData.startDate}
                    max={enhancedData.endDate}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    تاريخ النهاية
                  </label>
                  <Input
                    type="date"
                    value={task.endDate}
                    onChange={(e) => updateTask(index, 'endDate', e.target.value)}
                    min={task.startDate || enhancedData.startDate}
                    max={enhancedData.endDate}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الحالة
                  </label>
                  <Select
                    value={task.status}
                    onChange={(e) => updateTask(index, 'status', e.target.value)}
                  >
                    <option value="pending">في الانتظار</option>
                    <option value="in_progress">قيد التنفيذ</option>
                    <option value="completed">مكتملة</option>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end pt-2">
                <Button 
                  onClick={() => removeTask(index)}
                  variant="outline" 
                  size="sm"
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  حذف المهمة
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* الموارد المطلوبة */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <h4 className="font-semibold text-gray-900">الموارد المطلوبة *</h4>
            <FieldHelp 
              content="حدد جميع الموارد اللازمة لتنفيذ المشروع مع تقدير التكاليف"
              field="requiredResources"
              step={7}
            />
          </div>
          <Button onClick={addResource} variant="outline" size="sm">
            <Plus className="w-4 h-4 mr-2" />
            إضافة مورد
          </Button>
        </div>

        {enhancedData.requiredResources.length === 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <p className="text-yellow-800 text-sm">
              أضف على الأقل مورد واحد مطلوب للمشروع
            </p>
          </div>
        )}

        {enhancedData.requiredResources.map((resource, index) => (
          <div key={index} className="bg-white border rounded-lg p-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نوع المورد *
                  </label>
                  <Select
                    value={resource.type}
                    onChange={(e) => {
                      updateResource(index, 'type', e.target.value)
                      updateResource(index, 'unit', getDefaultUnit(e.target.value))
                    }}
                  >
                    {getResourceTypeOptions().map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    وصف المورد *
                  </label>
                  <Input
                    value={resource.description}
                    onChange={(e) => updateResource(index, 'description', e.target.value)}
                    placeholder="اكتب وصف المورد..."
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الكمية *
                  </label>
                  <Input
                    type="number"
                    value={resource.quantity}
                    onChange={(e) => updateResource(index, 'quantity', parseInt(e.target.value) || 0)}
                    min="1"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الوحدة
                  </label>
                  <Input
                    value={resource.unit}
                    onChange={(e) => updateResource(index, 'unit', e.target.value)}
                    placeholder="وحدة القياس..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    التكلفة لكل وحدة (ريال)
                  </label>
                  <Input
                    type="number"
                    value={resource.cost}
                    onChange={(e) => updateResource(index, 'cost', parseFloat(e.target.value) || 0)}
                    min="0"
                    step="0.01"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    التكلفة الإجمالية
                  </label>
                  <div className="flex items-center gap-2 p-2 bg-gray-50 rounded border">
                    {getResourceTypeIcon(resource.type)}
                    <span className="font-medium">
                      {(resource.quantity * resource.cost).toLocaleString()} ريال
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex justify-end pt-2">
                <Button 
                  onClick={() => removeResource(index)}
                  variant="outline" 
                  size="sm"
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  حذف المورد
                </Button>
              </div>
            </div>
          </div>
        ))}

        {/* ملخص التكلفة */}
        {enhancedData.requiredResources.length > 0 && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <DollarSign className="w-5 h-5 text-green-600" />
                <span className="font-semibold text-green-900">إجمالي تكلفة الموارد:</span>
              </div>
              <span className="text-xl font-bold text-green-900">
                {totalCost.toLocaleString()} ريال
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
} 