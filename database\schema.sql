-- إنشاء جدول الأدوار
CREATE TABLE IF NOT EXISTS roles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(50) UNIQUE NOT NULL,
  display_name VARCHAR(100) NOT NULL,
  description TEXT,
  permissions JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- إنشاء جدول الأقسام
CREATE TABLE IF NOT EXISTS departments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  parent_id UUID REFERENCES departments(id),
  manager_id UUID,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- إن<PERSON><PERSON><PERSON> جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  department_id UUID REFERENCES departments(id),
  role_id UUID REFERENCES roles(id) NOT NULL,
  phone VARCHAR(20),
  avatar_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- إضافة foreign key للمدير في جدول الأقسام
ALTER TABLE departments 
ADD CONSTRAINT fk_departments_manager 
FOREIGN KEY (manager_id) REFERENCES users(id);

-- إنشاء جدول طلبات المشاريع
CREATE TABLE IF NOT EXISTS project_requests (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title VARCHAR(200) NOT NULL,
  description TEXT NOT NULL,
  main_type VARCHAR(30) CHECK (main_type IN ('general_project', 'improvement_project')) NOT NULL,
  sub_type VARCHAR(30) CHECK (sub_type IN ('quick_win', 'improvement_full', 'suggestion')) NULL,
  status VARCHAR(20) CHECK (status IN ('draft', 'submitted', 'under_review', 'approved', 'rejected', 'in_progress', 'completed')) DEFAULT 'draft',
  priority VARCHAR(10) CHECK (priority IN ('low', 'medium', 'high', 'urgent')) DEFAULT 'medium',
  requester_id UUID REFERENCES users(id) NOT NULL,
  department_id UUID REFERENCES departments(id) NOT NULL,
  expected_start_date DATE,
  expected_end_date DATE,
  estimated_budget DECIMAL(15,2),
  business_case TEXT,
  expected_benefits TEXT,
  risks_and_challenges TEXT,
  form_data JSONB DEFAULT '{}',
  attachments JSONB DEFAULT '[]',
  approval_level INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- إنشاء جدول الموافقات
CREATE TABLE IF NOT EXISTS approvals (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  request_id UUID REFERENCES project_requests(id) NOT NULL,
  approver_id UUID REFERENCES users(id) NOT NULL,
  status VARCHAR(10) CHECK (status IN ('pending', 'approved', 'rejected')) DEFAULT 'pending',
  notes TEXT,
  approved_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(request_id, approver_id)
);

-- إنشاء جدول المشاريع
CREATE TABLE IF NOT EXISTS projects (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  request_id UUID REFERENCES project_requests(id) NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT NOT NULL,
  project_manager_id UUID REFERENCES users(id) NOT NULL,
  status VARCHAR(20) CHECK (status IN ('planning', 'in_progress', 'on_hold', 'completed', 'cancelled')) DEFAULT 'planning',
  methodology VARCHAR(20) CHECK (methodology IN ('pdca', 'agile', 'waterfall')) DEFAULT 'pdca',
  start_date DATE,
  end_date DATE,
  actual_start_date DATE,
  actual_end_date DATE,
  budget DECIMAL(15,2),
  progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- إنشاء جدول المهام
CREATE TABLE IF NOT EXISTS tasks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID REFERENCES projects(id) NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  status VARCHAR(20) CHECK (status IN ('todo', 'in_progress', 'review', 'done')) DEFAULT 'todo',
  priority VARCHAR(10) CHECK (priority IN ('low', 'medium', 'high')) DEFAULT 'medium',
  assignee_id UUID REFERENCES users(id),
  phase VARCHAR(10) CHECK (phase IN ('plan', 'do', 'check', 'act')) NOT NULL,
  due_date DATE,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- إنشاء جدول الإشعارات
CREATE TABLE IF NOT EXISTS notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) NOT NULL,
  title VARCHAR(200) NOT NULL,
  message TEXT NOT NULL,
  type VARCHAR(10) CHECK (type IN ('info', 'success', 'warning', 'error')) DEFAULT 'info',
  read_at TIMESTAMPTZ,
  action_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- إنشاء فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_department ON users(department_id);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role_id);
CREATE INDEX IF NOT EXISTS idx_project_requests_requester ON project_requests(requester_id);
CREATE INDEX IF NOT EXISTS idx_project_requests_department ON project_requests(department_id);
CREATE INDEX IF NOT EXISTS idx_project_requests_status ON project_requests(status);
CREATE INDEX IF NOT EXISTS idx_project_requests_type ON project_requests(type);
CREATE INDEX IF NOT EXISTS idx_approvals_request ON approvals(request_id);
CREATE INDEX IF NOT EXISTS idx_approvals_approver ON approvals(approver_id);
CREATE INDEX IF NOT EXISTS idx_projects_manager ON projects(project_manager_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_tasks_project ON tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_tasks_assignee ON tasks(assignee_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX IF NOT EXISTS idx_notifications_user ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read_at);

-- إنشاء دوال التحديث التلقائي للتاريخ
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء triggers للتحديث التلقائي
CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_departments_updated_at BEFORE UPDATE ON departments
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_project_requests_updated_at BEFORE UPDATE ON project_requests
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_approvals_updated_at BEFORE UPDATE ON approvals
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إدراج البيانات الأولية
INSERT INTO roles (name, display_name, description, permissions) VALUES
('admin', 'مدير النظام', 'صلاحيات كاملة لإدارة النظام', '{"all": true}'),
('pmo_manager', 'مدير مكتب المشاريع', 'إدارة المشاريع والطلبات', '{"projects": ["read", "write", "approve"], "requests": ["read", "write", "approve"]}'),
('planning_manager', 'مدير إدارة التخطيط', 'اعتماد المشاريع الكبيرة', '{"projects": ["read", "approve"], "requests": ["read", "approve"]}'),
('executive_manager', 'المدير التنفيذي', 'الاعتماد النهائي للمشاريع', '{"projects": ["read", "final_approve"], "requests": ["read", "final_approve"]}'),
('project_manager', 'مدير مشروع', 'إدارة المشاريع المعينة', '{"projects": ["read", "write"], "tasks": ["read", "write"]}'),
('employee', 'موظف', 'تقديم طلبات المشاريع', '{"requests": ["read", "write"], "projects": ["read"]}')
ON CONFLICT (name) DO NOTHING;

INSERT INTO departments (name, description) VALUES
('مكتب إدارة المشاريع', 'مكتب إدارة المشاريع والتحسين المؤسسي'),
('إدارة التخطيط', 'إدارة التخطيط الاستراتيجي والتطوير'),
('الإدارة التنفيذية', 'الإدارة العليا للمؤسسة'),
('تقنية المعلومات', 'قسم تقنية المعلومات والتحول الرقمي'),
('الموارد البشرية', 'قسم الموارد البشرية والتطوير'),
('المالية', 'القسم المالي والمحاسبة'),
('العمليات', 'قسم العمليات والإنتاج')
ON CONFLICT DO NOTHING; 
('المالية', 'القسم المالي والمحاسبة'),
('العمليات', 'قسم العمليات والإنتاج')
ON CONFLICT DO NOTHING; 