-- إضافة الحقول المطلوبة للوحة Kanban إلى جدول المشاريع
ALTER TABLE projects 
ADD COLUMN IF NOT EXISTS type VARCHAR(50) DEFAULT 'project',
ADD COLUMN IF NOT EXISTS priority VARCHAR(20) DEFAULT 'medium',
ADD COLUMN IF NOT EXISTS assignee_id UUID REFERENCES users(id),
ADD COLUMN IF NOT EXISTS due_date DATE,
ADD COLUMN IF NOT EXISTS estimated_hours INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS actual_hours INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS pdca_phase VARCHAR(20) DEFAULT 'plan',
ADD COLUMN IF NOT EXISTS progress INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS tags JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS attachments JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS comments INTEGER DEFAULT 0;

-- إ<PERSON><PERSON><PERSON>ة فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_projects_type ON projects(type);
CREATE INDEX IF NOT EXISTS idx_projects_priority ON projects(priority);
CREATE INDEX IF NOT EXISTS idx_projects_assignee_id ON projects(assignee_id);
CREATE INDEX IF NOT EXISTS idx_projects_pdca_phase ON projects(pdca_phase);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_request_id ON projects(request_id);

-- إضافة قيود للتحقق من صحة البيانات
ALTER TABLE projects 
ADD CONSTRAINT chk_projects_type CHECK (type IN ('project', 'suggestion', 'quickwin')),
ADD CONSTRAINT chk_projects_priority CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
ADD CONSTRAINT chk_projects_pdca_phase CHECK (pdca_phase IN ('plan', 'do', 'check', 'act')),
ADD CONSTRAINT chk_projects_progress CHECK (progress >= 0 AND progress <= 100),
ADD CONSTRAINT chk_projects_status CHECK (status IN ('active', 'completed', 'on_hold', 'cancelled'));

-- تحديث البيانات الموجودة
UPDATE projects 
SET assignee_id = project_manager_id 
WHERE assignee_id IS NULL;

UPDATE projects 
SET due_date = end_date 
WHERE due_date IS NULL;

UPDATE projects 
SET progress = COALESCE(progress_percentage, 0) 
WHERE progress = 0;

-- إضافة تعليقات للتوضيح
COMMENT ON COLUMN projects.type IS 'نوع المشروع: project, suggestion, quickwin';
COMMENT ON COLUMN projects.priority IS 'أولوية المشروع: low, medium, high, urgent';
COMMENT ON COLUMN projects.assignee_id IS 'معرف المسؤول عن المشروع';
COMMENT ON COLUMN projects.pdca_phase IS 'مرحلة PDCA: plan, do, check, act';
COMMENT ON COLUMN projects.progress IS 'نسبة التقدم من 0 إلى 100';
COMMENT ON COLUMN projects.tags IS 'علامات المشروع (JSON array)';
COMMENT ON COLUMN projects.attachments IS 'مرفقات المشروع (JSON array)';
COMMENT ON COLUMN projects.comments IS 'عدد التعليقات';

-- إنشاء view للمشاريع مع البيانات المطلوبة للوحة Kanban
CREATE OR REPLACE VIEW kanban_projects AS
SELECT 
    p.id,
    p.title,
    p.description,
    p.type,
    p.priority,
    p.assignee_id,
    u.name as assignee_name,
    p.due_date,
    p.created_at,
    p.tags,
    p.progress,
    p.pdca_phase,
    p.estimated_hours,
    p.actual_hours,
    p.attachments,
    p.comments,
    p.request_id,
    p.status,
    pr.title as original_request_title,
    pr.main_type as original_request_type,
    pr.sub_type as original_request_subtype
FROM projects p
LEFT JOIN users u ON p.assignee_id = u.id
LEFT JOIN project_requests pr ON p.request_id = pr.id
WHERE p.status IN ('active', 'on_hold')
ORDER BY p.created_at DESC;

-- إضافة RLS policies للمشاريع
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;

-- سياسة القراءة - يمكن لجميع المستخدمين المصرح لهم قراءة المشاريع
CREATE POLICY "Users can view projects" ON projects
    FOR SELECT USING (auth.uid() IS NOT NULL);

-- سياسة الإنشاء - يمكن لمديري المشاريع إنشاء مشاريع جديدة
CREATE POLICY "Project managers can create projects" ON projects
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role_id IN (
                SELECT id FROM roles 
                WHERE name IN ('pmo_manager', 'project_manager', 'planning_manager')
            )
        )
    );

-- سياسة التحديث - يمكن للمسؤول عن المشروع أو مديري المشاريع تحديثه
CREATE POLICY "Project assignees and managers can update projects" ON projects
    FOR UPDATE USING (
        assignee_id = auth.uid() OR 
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role_id IN (
                SELECT id FROM roles 
                WHERE name IN ('pmo_manager', 'project_manager', 'planning_manager')
            )
        )
    );

-- سياسة الحذف - يمكن لمديري المشاريع فقط حذف المشاريع
CREATE POLICY "Project managers can delete projects" ON projects
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role_id IN (
                SELECT id FROM roles 
                WHERE name IN ('pmo_manager', 'project_manager')
            )
        )
    ); 