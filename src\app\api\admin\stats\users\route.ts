import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'

export async function GET(request: NextRequest) {
  try {
    // جلب إحصائيات المستخدمين
    const { data: users, error: usersError } = await supabaseAdmin
      .from('users')
      .select('id, is_active, created_at')

    if (usersError) {
      console.error('Error fetching users:', usersError)
      return NextResponse.json(
        { success: false, error: 'فشل في جلب إحصائيات المستخدمين' },
        { status: 500 }
      )
    }

    // حساب الإحصائيات
    const total = users?.length || 0
    const active = users?.filter(user => user.is_active !== false).length || 0
    const inactive = total - active

    // حساب المستخدمين الجدد هذا الشهر
    const currentMonth = new Date()
    currentMonth.setDate(1)
    currentMonth.setHours(0, 0, 0, 0)
    
    const newThisMonth = users?.filter(user => {
      if (!user.created_at) return false
      const createdAt = new Date(user.created_at)
      return createdAt >= currentMonth
    }).length || 0

    // حساب معدل النمو (مقارنة بالشهر الماضي)
    const lastMonth = new Date()
    lastMonth.setMonth(lastMonth.getMonth() - 1)
    lastMonth.setDate(1)
    lastMonth.setHours(0, 0, 0, 0)
    
    const lastMonthEnd = new Date(currentMonth)
    lastMonthEnd.setSeconds(-1)
    
    const newLastMonth = users?.filter(user => {
      if (!user.created_at) return false
      const createdAt = new Date(user.created_at)
      return createdAt >= lastMonth && createdAt <= lastMonthEnd
    }).length || 0

    const growthRate = newLastMonth > 0 ? ((newThisMonth - newLastMonth) / newLastMonth) * 100 : 0

    const stats = {
      total,
      active,
      inactive,
      new_this_month: newThisMonth,
      growth_rate: Math.round(growthRate * 10) / 10
    }

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('Error in users stats API:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في الخادم' },
      { status: 500 }
    )
  }
} 