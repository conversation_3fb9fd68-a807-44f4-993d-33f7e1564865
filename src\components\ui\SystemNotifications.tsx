'use client';

import React, { useState, useEffect } from 'react';
import { Bell, X, CheckCircle, AlertCircle, Info, AlertTriangle, Clock } from 'lucide-react';

// أنواع الإشعارات النظامية
export type SystemNotificationType = 'success' | 'error' | 'warning' | 'info' | 'system';

// بيانات الإشعار النظامي
export interface SystemNotificationData {
  id: string;
  type: SystemNotificationType;
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
  action?: {
    label: string;
    onClick: () => void;
  };
}

// مكون الإشعارات النظامية
export const SystemNotifications: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<SystemNotificationData[]>([]);

  // تحميل الإشعارات التجريبية
  useEffect(() => {
    const demoNotifications: SystemNotificationData[] = [
      {
        id: '1',
        type: 'info',
        title: 'تحديث النظام',
        message: 'تم تحديث النظام إلى الإصدار الجديد بنجاح',
        timestamp: new Date(Date.now() - 1000 * 60 * 5), // 5 دقائق مضت
        read: false,
        priority: 'medium'
      },
      {
        id: '2',
        type: 'success',
        title: 'تم الحفظ',
        message: 'تم حفظ طلب المشروع الجديد بنجاح',
        timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 دقيقة مضت
        read: false,
        priority: 'low'
      },
      {
        id: '3',
        type: 'warning',
        title: 'مراجعة مطلوبة',
        message: 'يوجد 3 طلبات مشاريع في انتظار المراجعة',
        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 دقيقة مضت
        read: true,
        priority: 'high',
        action: {
          label: 'مراجعة الطلبات',
          onClick: () => console.log('انتقال إلى صفحة المراجعة')
        }
      },
      {
        id: '4',
        type: 'system',
        title: 'صيانة مجدولة',
        message: 'ستتم صيانة النظام غداً من الساعة 2:00 إلى 4:00 صباحاً',
        timestamp: new Date(Date.now() - 1000 * 60 * 60), // ساعة مضت
        read: true,
        priority: 'medium'
      }
    ];
    
    setNotifications(demoNotifications);
  }, []);

  // عدد الإشعارات غير المقروءة
  const unreadCount = notifications.filter(n => !n.read).length;

  // أيقونات الإشعارات
  const getIcon = (type: SystemNotificationType) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'info':
        return <Info className="w-4 h-4 text-blue-500" />;
      case 'system':
        return <Clock className="w-4 h-4 text-purple-500" />;
      default:
        return <Info className="w-4 h-4 text-blue-500" />;
    }
  };

  // تنسيق الوقت
  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days} يوم`;
    if (hours > 0) return `${hours} ساعة`;
    if (minutes > 0) return `${minutes} دقيقة`;
    return 'الآن';
  };

  // تعيين الإشعار كمقروء
  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  // حذف الإشعار
  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  // تعيين جميع الإشعارات كمقروءة
  const markAllAsRead = () => {
    setNotifications(prev => prev.map(notification => ({ ...notification, read: true })));
  };

  return (
    <div className="relative">
      {/* زر الإشعارات */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
      >
        <Bell className="w-5 h-5 text-gray-600" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      {/* قائمة الإشعارات */}
      {isOpen && (
        <>
          {/* خلفية شفافة للإغلاق */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* قائمة الإشعارات */}
          <div className="absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
            {/* رأس القائمة */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">الإشعارات</h3>
              <div className="flex items-center gap-2">
                {unreadCount > 0 && (
                  <button
                    onClick={markAllAsRead}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    تعيين الكل كمقروء
                  </button>
                )}
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-1 rounded-md hover:bg-gray-100 focus:outline-none"
                >
                  <X className="w-4 h-4 text-gray-500" />
                </button>
              </div>
            </div>

            {/* قائمة الإشعارات */}
            <div className="max-h-96 overflow-y-auto">
              {notifications.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  <Bell className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p>لا توجد إشعارات</p>
                </div>
              ) : (
                notifications.map(notification => (
                  <div
                    key={notification.id}
                    className={`
                      p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors cursor-pointer
                      ${!notification.read ? 'bg-blue-50/50' : ''}
                    `}
                    onClick={() => !notification.read && markAsRead(notification.id)}
                  >
                    <div className="flex items-start gap-3">
                      {/* أيقونة الإشعار */}
                      <div className="flex-shrink-0 mt-1">
                        {getIcon(notification.type)}
                      </div>

                      {/* محتوى الإشعار */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className={`text-sm font-medium ${!notification.read ? 'text-gray-900' : 'text-gray-700'}`}>
                            {notification.title}
                          </h4>
                          <span className="text-xs text-gray-500">
                            {formatTime(notification.timestamp)}
                          </span>
                        </div>
                        
                        <p className="mt-1 text-sm text-gray-600 line-clamp-2">
                          {notification.message}
                        </p>

                        {/* زر الإجراء */}
                        {notification.action && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              notification.action!.onClick();
                            }}
                            className="mt-2 text-sm font-medium text-blue-600 hover:text-blue-800"
                          >
                            {notification.action.label}
                          </button>
                        )}
                      </div>

                      {/* زر الحذف */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          removeNotification(notification.id);
                        }}
                        className="flex-shrink-0 p-1 rounded-md hover:bg-gray-200 focus:outline-none opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="w-3 h-3 text-gray-400" />
                      </button>

                      {/* نقطة الإشعار غير المقروء */}
                      {!notification.read && (
                        <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2" />
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* تذييل القائمة */}
            {notifications.length > 0 && (
              <div className="p-3 border-t border-gray-200">
                <button className="w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium">
                  عرض جميع الإشعارات
                </button>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}; 