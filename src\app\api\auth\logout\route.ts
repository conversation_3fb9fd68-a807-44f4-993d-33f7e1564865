import { NextRequest, NextResponse } from 'next/server'

// POST - تسجيل الخروج
export async function POST(request: NextRequest) {
  try {
    // إنشاء response
    const response = NextResponse.json({
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    })

    // حذف cookie الجلسة
    response.cookies.delete('session_token')

    return response

  } catch (error) {
    console.error('Logout API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
} 