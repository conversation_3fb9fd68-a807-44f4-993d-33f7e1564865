'use client'

import React, { useState, useCallback } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  Plus, 
  MoreVertical, 
  Calendar, 
  User, 
  AlertCircle, 
  CheckCircle,
  Clock,
  Target,
  TrendingUp,
  RotateCcw,
  Filter,
  Search,
  Move
} from 'lucide-react'

interface ProjectTask {
  id: string
  title: string
  description: string
  type: 'suggestion' | 'project' | 'quickwin'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  assignee: string
  dueDate: string
  createdDate: string
  tags: string[]
  progress: number
  pdcaPhase: 'plan' | 'do' | 'check' | 'act'
  estimatedHours?: number
  actualHours?: number
  attachments?: number
  comments?: number
}

interface KanbanColumn {
  id: string
  title: string
  description: string
  color: string
  icon: React.ReactNode
  tasks: ProjectTask[]
  maxTasks?: number
}

interface KanbanBoardProps {
  initialTasks?: ProjectTask[]
  onTaskMove?: (taskId: string, fromColumn: string, toColumn: string) => void
  onTaskUpdate?: (task: ProjectTask) => void
  onTaskCreate?: (columnId: string) => void
}

export function KanbanBoard({ 
  initialTasks = [], 
  onTaskMove, 
  onTaskUpdate, 
  onTaskCreate 
}: KanbanBoardProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'suggestion' | 'project' | 'quickwin'>('all')
  const [filterPriority, setFilterPriority] = useState<'all' | 'low' | 'medium' | 'high' | 'urgent'>('all')
  const [draggedTask, setDraggedTask] = useState<ProjectTask | null>(null)
  const [draggedFromColumn, setDraggedFromColumn] = useState<string | null>(null)
  const [dragOverColumn, setDragOverColumn] = useState<string | null>(null)
  const [isValidDrop, setIsValidDrop] = useState<boolean>(true)

  // تقسيم المهام حسب مرحلة PDCA
  const [columns, setColumns] = useState<KanbanColumn[]>([
    {
      id: 'plan',
      title: 'التخطيط (Plan)',
      description: 'تحديد المشكلة ووضع الخطة',
      color: 'blue',
      icon: <Target className="w-5 h-5" />,
      tasks: initialTasks.filter(task => task.pdcaPhase === 'plan'),
      maxTasks: 10
    },
    {
      id: 'do',
      title: 'التنفيذ (Do)',
      description: 'تطبيق الخطة والحلول',
      color: 'orange',
      icon: <Clock className="w-5 h-5" />,
      tasks: initialTasks.filter(task => task.pdcaPhase === 'do'),
      maxTasks: 8
    },
    {
      id: 'check',
      title: 'المراجعة (Check)',
      description: 'تقييم النتائج والأداء',
      color: 'purple',
      icon: <CheckCircle className="w-5 h-5" />,
      tasks: initialTasks.filter(task => task.pdcaPhase === 'check'),
      maxTasks: 6
    },
    {
      id: 'act',
      title: 'العمل (Act)',
      description: 'تطبيق التحسينات وتوثيق الدروس',
      color: 'green',
      icon: <TrendingUp className="w-5 h-5" />,
      tasks: initialTasks.filter(task => task.pdcaPhase === 'act'),
      maxTasks: 5
    }
  ])

  // فلترة المهام
  const filterTasks = useCallback((tasks: ProjectTask[]) => {
    return tasks.filter(task => {
      const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           task.description.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesType = filterType === 'all' || task.type === filterType
      const matchesPriority = filterPriority === 'all' || task.priority === filterPriority
      
      return matchesSearch && matchesType && matchesPriority
    })
  }, [searchTerm, filterType, filterPriority])

  // التحقق من صحة عملية النقل
  const validateDrop = (targetColumnId: string) => {
    if (!draggedTask || !draggedFromColumn) return false
    
    const targetColumn = columns.find(col => col.id === targetColumnId)
    if (!targetColumn) return false
    
    // التحقق من الحد الأقصى للمهام
    if (targetColumn.maxTasks && targetColumn.tasks.length >= targetColumn.maxTasks && draggedFromColumn !== targetColumnId) {
      return false
    }
    
    // قواعد منطق العمل - يمكن النقل للأمام أو للخلف خطوة واحدة فقط
    const phases = ['plan', 'do', 'check', 'act']
    const fromIndex = phases.indexOf(draggedFromColumn)
    const toIndex = phases.indexOf(targetColumnId)
    
    // يمكن النقل للمرحلة التالية أو السابقة أو نفس المرحلة
    return Math.abs(toIndex - fromIndex) <= 1
  }

  const handleDragStart = (task: ProjectTask, columnId: string) => {
    setDraggedTask(task)
    setDraggedFromColumn(columnId)
    
    // إضافة تأثير بصري للبطاقة المسحوبة
    const element = document.querySelector(`[data-task-id="${task.id}"]`) as HTMLElement
    if (element) {
      element.style.opacity = '0.5'
      element.style.transform = 'rotate(5deg)'
    }
  }

  const handleDragEnd = () => {
    // إزالة التأثيرات البصرية
    if (draggedTask) {
      const element = document.querySelector(`[data-task-id="${draggedTask.id}"]`) as HTMLElement
      if (element) {
        element.style.opacity = '1'
        element.style.transform = 'none'
      }
    }
    
    setDraggedTask(null)
    setDraggedFromColumn(null)
    setDragOverColumn(null)
    setIsValidDrop(true)
  }

  const handleDragOver = (e: React.DragEvent, columnId: string) => {
    e.preventDefault()
    
    if (!draggedTask) return
    
    setDragOverColumn(columnId)
    const valid = validateDrop(columnId)
    setIsValidDrop(valid)
    
    // تغيير شكل المؤشر
    e.dataTransfer.dropEffect = valid ? 'move' : 'none'
  }

  const handleDragLeave = () => {
    setDragOverColumn(null)
    setIsValidDrop(true)
  }

  const handleDrop = (e: React.DragEvent, targetColumnId: string) => {
    e.preventDefault()
    
    if (!draggedTask || !draggedFromColumn) return

    const valid = validateDrop(targetColumnId)
    
    if (!valid) {
      const targetColumn = columns.find(col => col.id === targetColumnId)
      if (targetColumn?.maxTasks && targetColumn.tasks.length >= targetColumn.maxTasks) {
        alert(`❌ لا يمكن إضافة المزيد من المهام. الحد الأقصى: ${targetColumn.maxTasks}`)
      } else {
        alert('❌ لا يمكن نقل المهمة إلى هذه المرحلة. يمكن النقل للمرحلة التالية أو السابقة فقط.')
      }
      handleDragEnd()
      return
    }

    if (draggedFromColumn !== targetColumnId) {
      // نقل المهمة بين الأعمدة
      setColumns(prev => prev.map(column => {
        if (column.id === draggedFromColumn) {
          return {
            ...column,
            tasks: column.tasks.filter(task => task.id !== draggedTask.id)
          }
        }
        if (column.id === targetColumnId) {
          const updatedTask = { ...draggedTask, pdcaPhase: targetColumnId as any }
          return {
            ...column,
            tasks: [...column.tasks, updatedTask]
          }
        }
        return column
      }))

      // استدعاء callback إذا كان متوفراً
      onTaskMove?.(draggedTask.id, draggedFromColumn, targetColumnId)
      
      // رسالة نجاح
      const phaseNames = {
        plan: 'التخطيط',
        do: 'التنفيذ', 
        check: 'المراجعة',
        act: 'العمل'
      }
      console.log(`✅ تم نقل "${draggedTask.title}" إلى مرحلة ${phaseNames[targetColumnId as keyof typeof phaseNames]}`)
    }

    handleDragEnd()
  }

  const getTaskTypeIcon = (type: string) => {
    switch (type) {
      case 'suggestion': return '💡'
      case 'project': return '📋'
      case 'quickwin': return '⚡'
      default: return '📝'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-700 border-red-200'
      case 'high': return 'bg-orange-100 text-orange-700 border-orange-200'
      case 'medium': return 'bg-blue-100 text-blue-700 border-blue-200'
      case 'low': return 'bg-gray-100 text-gray-700 border-gray-200'
      default: return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500'
    if (progress >= 50) return 'bg-blue-500'
    if (progress >= 25) return 'bg-orange-500'
    return 'bg-gray-300'
  }

  return (
    <div className="h-full bg-gray-50">
      {/* Header with Filters */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
              <RotateCcw className="w-6 h-6 text-blue-600" />
              لوحة إدارة المشاريع - PDCA
            </h1>
            <p className="text-gray-600 mt-1">
              إدارة مشاريع التحسين باستخدام منهجية التحسين المستمر
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
            {/* Search */}
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="بحث في المهام..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-3 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-full sm:w-64"
              />
            </div>
            
            {/* Filters */}
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">جميع الأنواع</option>
              <option value="suggestion">مقترحات</option>
              <option value="project">مشاريع</option>
              <option value="quickwin">كويك وين</option>
            </select>
            
            <select
              value={filterPriority}
              onChange={(e) => setFilterPriority(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">جميع الأولويات</option>
              <option value="urgent">عاجل</option>
              <option value="high">عالي</option>
              <option value="medium">متوسط</option>
              <option value="low">منخفض</option>
            </select>
          </div>
        </div>
      </div>

      {/* Drag and Drop Instructions */}
      {draggedTask && (
        <div className="bg-blue-50 border-b border-blue-200 p-3">
          <div className="flex items-center gap-2 text-blue-700 text-sm">
            <Move className="w-4 h-4" />
            <span>
              يتم نقل &quot;{draggedTask.title}&quot; - يمكن النقل للمرحلة التالية أو السابقة فقط
            </span>
          </div>
        </div>
      )}

      {/* Kanban Board */}
      <div className="flex-1 p-4 overflow-x-auto">
        <div className="flex gap-6 min-w-max h-full">
          {columns.map((column) => {
            const filteredTasks = filterTasks(column.tasks)
            const isDragOver = dragOverColumn === column.id
            const isValidDropTarget = isDragOver && isValidDrop
            const isInvalidDropTarget = isDragOver && !isValidDrop
            
            return (
              <div
                key={column.id}
                className={`flex-shrink-0 w-80 transition-all duration-200 ${
                  isValidDropTarget ? 'scale-105' : ''
                } ${isInvalidDropTarget ? 'opacity-50' : ''}`}
                onDragOver={(e) => handleDragOver(e, column.id)}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, column.id)}
              >
                {/* Column Header */}
                <Card className={`mb-4 border-t-4 border-t-${column.color}-500 ${
                  isValidDropTarget ? `bg-${column.color}-50 border-${column.color}-300` : ''
                } ${isInvalidDropTarget ? 'bg-red-50 border-red-300' : ''}`}>
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <div className={`text-${column.color}-600`}>
                          {column.icon}
                        </div>
                        <h3 className="font-semibold text-gray-900">
                          {column.title}
                        </h3>
                        <span className={`px-2 py-1 text-xs rounded-full bg-${column.color}-100 text-${column.color}-700`}>
                          {filteredTasks.length}
                          {column.maxTasks && `/${column.maxTasks}`}
                        </span>
                      </div>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onTaskCreate?.(column.id)}
                        icon={<Plus className="w-4 h-4" />}
                      />
                    </div>
                    
                    <p className="text-sm text-gray-600">{column.description}</p>
                    
                    {/* Drop Zone Indicator */}
                    {isValidDropTarget && (
                      <div className="mt-2 p-2 border-2 border-dashed border-green-300 bg-green-50 rounded-lg text-center">
                        <span className="text-green-700 text-xs">إفلات هنا ✓</span>
                      </div>
                    )}
                    
                    {isInvalidDropTarget && (
                      <div className="mt-2 p-2 border-2 border-dashed border-red-300 bg-red-50 rounded-lg text-center">
                        <span className="text-red-700 text-xs">لا يمكن الإفلات هنا ✗</span>
                      </div>
                    )}
                  </div>
                </Card>

                {/* Tasks */}
                <div className="space-y-3 max-h-[calc(100vh-300px)] overflow-y-auto">
                  {filteredTasks.map((task) => (
                    <Card
                      key={task.id}
                      data-task-id={task.id}
                      className={`cursor-move hover:shadow-lg transition-all duration-200 ${
                        draggedTask?.id === task.id ? 'opacity-50 transform rotate-2' : ''
                      }`}
                      draggable
                      onDragStart={() => handleDragStart(task, column.id)}
                      onDragEnd={handleDragEnd}
                    >
                      <div className="p-4">
                        {/* Task Header */}
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <span className="text-lg">{getTaskTypeIcon(task.type)}</span>
                            <span className={`px-2 py-1 text-xs rounded-full border ${getPriorityColor(task.priority)}`}>
                              {task.priority === 'urgent' && 'عاجل'}
                              {task.priority === 'high' && 'عالي'}
                              {task.priority === 'medium' && 'متوسط'}
                              {task.priority === 'low' && 'منخفض'}
                            </span>
                          </div>
                          
                          <Button variant="ghost" size="sm" icon={<MoreVertical className="w-4 h-4" />} />
                        </div>

                        {/* Task Title */}
                        <h4 className="font-medium text-gray-900 mb-2 line-clamp-2">
                          {task.title}
                        </h4>

                        {/* Task Description */}
                        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                          {task.description}
                        </p>

                        {/* Progress Bar */}
                        <div className="mb-3">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-xs text-gray-500">التقدم</span>
                            <span className="text-xs font-medium text-gray-700">{task.progress}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full transition-all ${getProgressColor(task.progress)}`}
                              style={{ width: `${task.progress}%` }}
                            />
                          </div>
                        </div>

                        {/* Task Tags */}
                        {task.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mb-3">
                            {task.tags.slice(0, 3).map((tag, index) => (
                              <span 
                                key={index}
                                className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full"
                              >
                                {tag}
                              </span>
                            ))}
                            {task.tags.length > 3 && (
                              <span className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full">
                                +{task.tags.length - 3}
                              </span>
                            )}
                          </div>
                        )}

                        {/* Task Footer */}
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <div className="flex items-center gap-3">
                            <div className="flex items-center gap-1">
                              <User className="w-3 h-3" />
                              <span>{task.assignee}</span>
                            </div>
                            
                            {task.dueDate && (
                              <div className="flex items-center gap-1">
                                <Calendar className="w-3 h-3" />
                                <span>{new Date(task.dueDate).toLocaleDateString('ar-SA')}</span>
                              </div>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-2">
                            {task.attachments && task.attachments > 0 && (
                              <span className="flex items-center gap-1">
                                📎 {task.attachments}
                              </span>
                            )}
                            
                            {task.comments && task.comments > 0 && (
                              <span className="flex items-center gap-1">
                                💬 {task.comments}
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Overdue Warning */}
                        {task.dueDate && new Date(task.dueDate) < new Date() && task.progress < 100 && (
                          <div className="mt-2 flex items-center gap-1 text-red-600 text-xs">
                            <AlertCircle className="w-3 h-3" />
                            <span>متأخر عن الموعد</span>
                          </div>
                        )}
                      </div>
                    </Card>
                  ))}
                  
                  {/* Empty State */}
                  {filteredTasks.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <div className={`w-12 h-12 mx-auto mb-3 rounded-full bg-${column.color}-100 flex items-center justify-center`}>
                        {column.icon}
                      </div>
                      <p className="text-sm">لا توجد مهام في هذه المرحلة</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onTaskCreate?.(column.id)}
                        className="mt-2"
                      >
                        إضافة مهمة
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* PDCA Methodology Info */}
      <div className="bg-white border-t border-gray-200 p-4">
        <div className="max-w-6xl mx-auto">
          <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <RotateCcw className="w-5 h-5 text-blue-600" />
            دورة التحسين المستمر (PDCA)
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Target className="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <h4 className="font-medium text-blue-900">Plan - التخطيط</h4>
                <p className="text-gray-600">تحديد المشكلة ووضع خطة للحل</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                <Clock className="w-4 h-4 text-orange-600" />
              </div>
              <div>
                <h4 className="font-medium text-orange-900">Do - التنفيذ</h4>
                <p className="text-gray-600">تطبيق الخطة والحلول المقترحة</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <CheckCircle className="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <h4 className="font-medium text-purple-900">Check - المراجعة</h4>
                <p className="text-gray-600">تقييم النتائج وقياس الأداء</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <TrendingUp className="w-4 h-4 text-green-600" />
              </div>
              <div>
                <h4 className="font-medium text-green-900">Act - العمل</h4>
                <p className="text-gray-600">تطبيق التحسينات وتوثيق التعلم</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 