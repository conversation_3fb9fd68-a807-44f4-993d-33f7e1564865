'use client'

import { useState } from 'react'
import Link from 'next/link'
import { 
  TestTube, 
  Database, 
  Shield, 
  FileText, 
  Building2, 
  Settings, 
  Bug,
  Layers,
  CheckCircle,
  AlertCircle,
  Clock,
  Play
} from 'lucide-react'

interface TestCategory {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  path: string
  status: 'active' | 'warning' | 'success' | 'pending'
  tests: Array<{
    name: string
    path: string
    description: string
  }>
}

export default function TestingDashboard() {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  const testCategories: TestCategory[] = [
    {
      id: 'auth',
      title: 'اختبارات المصادقة',
      description: 'اختبار تسجيل الدخول والخروج والصلاحيات',
      icon: <Shield className="w-6 h-6" />,
      path: '/testing/auth',
      status: 'success',
      tests: [
        {
          name: 'تسجيل الدخول',
          path: '/testing/auth',
          description: 'اختبار تسجيل الدخول بالمستخدمين المختلفين'
        }
      ]
    },
    {
      id: 'database',
      title: 'اختبارات قاعدة البيانات',
      description: 'اختبار الاتصال بـ Supabase والعمليات',
      icon: <Database className="w-6 h-6" />,
      path: '/testing/database',
      status: 'success',
      tests: [
        {
          name: 'اتصال Supabase',
          path: '/testing/database',
          description: 'اختبار الاتصال وقراءة البيانات'
        }
      ]
    },
    {
      id: 'forms',
      title: 'اختبارات النماذج',
      description: 'اختبار النماذج والتحقق من البيانات',
      icon: <FileText className="w-6 h-6" />,
      path: '/testing/forms',
      status: 'active',
      tests: [
        {
          name: 'نماذج الطلبات',
          path: '/testing/forms',
          description: 'اختبار نماذج إنشاء الطلبات'
        },
        {
          name: 'إرسال النماذج',
          path: '/testing/forms/submission',
          description: 'اختبار عملية إرسال البيانات'
        },
        {
          name: 'الاختبار الموحد',
          path: '/testing/forms/unified',
          description: 'اختبار شامل لجميع النماذج'
        }
      ]
    },
    {
      id: 'departments',
      title: 'اختبارات الأقسام',
      description: 'اختبار إدارة الأقسام والمستخدمين',
      icon: <Building2 className="w-6 h-6" />,
      path: '/testing/departments',
      status: 'warning',
      tests: [
        {
          name: 'إدارة الأقسام',
          path: '/testing/departments',
          description: 'اختبار عمليات الأقسام والمستخدمين'
        }
      ]
    },
    {
      id: 'system',
      title: 'اختبارات النظام',
      description: 'اختبارات شاملة للنظام والأداء',
      icon: <Settings className="w-6 h-6" />,
      path: '/testing/system',
      status: 'pending',
      tests: [
        {
          name: 'اختبار النظام',
          path: '/testing/system',
          description: 'اختبار شامل لوظائف النظام'
        },
        {
          name: 'تصحيح الأخطاء',
          path: '/testing/system/debug',
          description: 'أدوات تصحيح الأخطاء والتشخيص'
        }
      ]
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />
      case 'active':
        return <Play className="w-5 h-5 text-blue-500" />
      case 'pending':
        return <Clock className="w-5 h-5 text-gray-500" />
      default:
        return <TestTube className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50'
      case 'warning':
        return 'border-yellow-200 bg-yellow-50'
      case 'active':
        return 'border-blue-200 bg-blue-50'
      case 'pending':
        return 'border-gray-200 bg-gray-50'
      default:
        return 'border-gray-200 bg-white'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <TestTube className="w-8 h-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">
              لوحة تحكم الاختبارات
            </h1>
          </div>
          <p className="text-gray-600 text-lg">
            مركز شامل لجميع اختبارات النظام والمكونات
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الاختبارات</p>
                <p className="text-2xl font-bold text-gray-900">
                  {testCategories.reduce((acc, cat) => acc + cat.tests.length, 0)}
                </p>
              </div>
              <Layers className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">اختبارات ناجحة</p>
                <p className="text-2xl font-bold text-green-600">
                  {testCategories.filter(cat => cat.status === 'success').length}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">تحتاج مراجعة</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {testCategories.filter(cat => cat.status === 'warning').length}
                </p>
              </div>
              <AlertCircle className="w-8 h-8 text-yellow-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">قيد التطوير</p>
                <p className="text-2xl font-bold text-gray-600">
                  {testCategories.filter(cat => cat.status === 'pending').length}
                </p>
              </div>
              <Clock className="w-8 h-8 text-gray-500" />
            </div>
          </div>
        </div>

        {/* Test Categories */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {testCategories.map((category) => (
            <div
              key={category.id}
              className={`bg-white rounded-lg border-2 p-6 hover:shadow-lg transition-all duration-200 cursor-pointer ${getStatusColor(category.status)}`}
              onClick={() => setSelectedCategory(selectedCategory === category.id ? null : category.id)}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  {category.icon}
                  <h3 className="text-lg font-semibold text-gray-900">
                    {category.title}
                  </h3>
                </div>
                {getStatusIcon(category.status)}
              </div>

              <p className="text-gray-600 mb-4">
                {category.description}
              </p>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">
                  {category.tests.length} اختبار
                </span>
                <Link
                  href={category.path}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  onClick={(e) => e.stopPropagation()}
                >
                  <Play className="w-4 h-4" />
                  تشغيل
                </Link>
              </div>

              {/* Expanded Tests */}
              {selectedCategory === category.id && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">
                    الاختبارات المتاحة:
                  </h4>
                  <div className="space-y-2">
                    {category.tests.map((test, index) => (
                      <Link
                        key={index}
                        href={test.path}
                        className="block p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900">{test.name}</p>
                            <p className="text-sm text-gray-600">{test.description}</p>
                          </div>
                          <Play className="w-4 h-4 text-gray-400" />
                        </div>
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="mt-8 bg-white rounded-lg p-6 shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            إجراءات سريعة
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              href="/testing/auth"
              className="flex items-center gap-3 p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
            >
              <Shield className="w-6 h-6 text-blue-600" />
              <div>
                <p className="font-medium text-gray-900">اختبار سريع للمصادقة</p>
                <p className="text-sm text-gray-600">تسجيل دخول تجريبي</p>
              </div>
            </Link>

            <Link
              href="/testing/database"
              className="flex items-center gap-3 p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
            >
              <Database className="w-6 h-6 text-green-600" />
              <div>
                <p className="font-medium text-gray-900">فحص قاعدة البيانات</p>
                <p className="text-sm text-gray-600">اختبار الاتصال</p>
              </div>
            </Link>

            <Link
              href="/testing/forms"
              className="flex items-center gap-3 p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors"
            >
              <FileText className="w-6 h-6 text-purple-600" />
              <div>
                <p className="font-medium text-gray-900">اختبار النماذج</p>
                <p className="text-sm text-gray-600">تجربة إنشاء طلب</p>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
