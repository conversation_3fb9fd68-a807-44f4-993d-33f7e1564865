'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth, usePermissions } from '@/lib/auth'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredPermission?: { resource: string; action: string }
  fallback?: React.ReactNode
}

export function ProtectedRoute({ 
  children, 
  requiredPermission,
  fallback = null 
}: ProtectedRouteProps) {
  const { user, loading } = useAuth()
  const permissions = usePermissions()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  if (requiredPermission && !permissions.hasPermission(requiredPermission.resource, requiredPermission.action)) {
    return fallback || (
      <div className="text-center p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">غير مصرح لك</h2>
        <p className="text-gray-600">ليس لديك صلاحية للوصول إلى هذه الصفحة.</p>
      </div>
    )
  }

  return <>{children}</>
}

export default ProtectedRoute 