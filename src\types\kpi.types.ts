// أنواع البيانات لنظام مؤشرات الأداء (KPIs)

export interface KpiType {
  id: string
  name: string
  display_name: string
  description: string | null
  default_unit: string | null
  icon: string | null
  color: string | null
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface ProjectKpi {
  id: string
  project_request_id: string
  kpi_type_id: string
  name: string
  description: string | null
  measurement_method: string | null
  data_source: string | null
  baseline_value: number | null
  target_value: number
  current_value: number | null
  unit: string | null
  status: 'draft' | 'pending_review' | 'approved' | 'rejected' | 'active' | 'completed'
  priority: 'low' | 'medium' | 'high'
  is_approved: boolean
  approved_by: string | null
  approved_at: string | null
  rejection_reason: string | null
  is_primary: boolean
  calculation_formula: string | null
  target_date: string | null
  created_by: string
  created_at: string
  updated_at: string
  
  // Relations
  kpi_type?: KpiType
  measurements?: KpiMeasurement[]
  analysis?: KpiAnalysis[]
}

export interface KpiMeasurement {
  id: string
  kpi_id: string
  measured_value: number
  measurement_date: string
  measurement_period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | null
  notes: string | null
  data_quality: 'excellent' | 'good' | 'fair' | 'poor'
  confidence_level: 'high' | 'medium' | 'low'
  is_verified: boolean
  verified_by: string | null
  verified_at: string | null
  measured_by: string
  created_at: string
  
  // Relations
  kpi?: ProjectKpi
}

export interface KpiTemplate {
  id: string
  kpi_type_id: string
  name: string
  description: string | null
  category: string | null
  template_data: Record<string, any>
  measurement_method: string | null
  typical_baseline: number | null
  typical_target: number | null
  is_active: boolean
  usage_count: number
  created_by: string
  created_at: string
  updated_at: string
  
  // Relations
  kpi_type?: KpiType
}

export interface KpiAnalysis {
  id: string
  kpi_id: string
  analysis_period_start: string
  analysis_period_end: string
  improvement_percentage: number | null
  trend: 'improving' | 'stable' | 'declining'
  target_achievement_percentage: number | null
  analysis_notes: string | null
  recommendations: string | null
  analyzed_by: string
  created_at: string
  
  // Relations
  kpi?: ProjectKpi
}

// أنواع البيانات للنماذج
export interface KpiFormData {
  kpi_type_id: string
  name: string
  description?: string
  measurement_method?: string
  data_source?: string
  baseline_value?: number
  target_value: number
  unit?: string
  priority: 'low' | 'medium' | 'high'
  is_primary?: boolean
  target_date?: string
}

export interface KpiMeasurementFormData {
  kpi_id: string
  measured_value: number
  measurement_date: string
  measurement_period?: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  notes?: string
  data_quality: 'excellent' | 'good' | 'fair' | 'poor'
  confidence_level: 'high' | 'medium' | 'low'
}

// أنواع البيانات للتحليل
export interface KpiProgress {
  kpi: ProjectKpi
  latest_measurement: KpiMeasurement | null
  improvement_rate: number | null
  target_achievement: number | null
  trend: 'improving' | 'stable' | 'declining'
  days_to_target: number | null
}

export interface KpiSummary {
  total_kpis: number
  approved_kpis: number
  active_kpis: number
  on_track_kpis: number
  behind_schedule_kpis: number
  average_improvement: number | null
}

// أنواع البيانات للتقارير
export interface KpiReport {
  project_request_id: string
  project_title: string
  kpis: KpiProgress[]
  overall_performance: {
    total_improvement: number | null
    target_achievement: number | null
    status: 'excellent' | 'good' | 'fair' | 'poor'
  }
  period: {
    start_date: string
    end_date: string
  }
}

// أنواع البيانات للمقارنة
export interface KpiComparison {
  kpi_name: string
  projects: {
    project_id: string
    project_title: string
    baseline_value: number | null
    current_value: number | null
    target_value: number
    improvement_rate: number | null
  }[]
}

// أنواع البيانات للإحصائيات
export interface KpiStatistics {
  by_type: {
    type_name: string
    count: number
    avg_improvement: number | null
  }[]
  by_department: {
    department_name: string
    count: number
    avg_improvement: number | null
  }[]
  by_status: {
    status: string
    count: number
    percentage: number
  }[]
  trends: {
    period: string
    total_kpis: number
    avg_improvement: number | null
  }[]
}

// أنواع البيانات للتحديثات المباشرة
export interface KpiUpdate {
  kpi_id: string
  update_type: 'measurement' | 'status' | 'approval' | 'target'
  old_value: any
  new_value: any
  updated_by: string
  timestamp: string
  notes?: string
}

// أنواع البيانات للتنبيهات
export interface KpiAlert {
  id: string
  kpi_id: string
  alert_type: 'target_missed' | 'no_measurement' | 'declining_trend' | 'approval_needed'
  message: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  is_read: boolean
  created_at: string
  
  // Relations
  kpi?: ProjectKpi
}

// أنواع البيانات للتصدير
export interface KpiExportData {
  kpi: ProjectKpi
  measurements: KpiMeasurement[]
  analysis: KpiAnalysis[]
  progress: KpiProgress
}

// أنواع البيانات للاستيراد
export interface KpiImportData {
  template_id?: string
  kpi_data: KpiFormData
  measurements?: KpiMeasurementFormData[]
}

// أنواع البيانات للتحقق
export interface KpiValidation {
  is_valid: boolean
  errors: {
    field: string
    message: string
  }[]
  warnings: {
    field: string
    message: string
  }[]
}

// أنواع البيانات للبحث والفلترة
export interface KpiFilters {
  kpi_type_ids?: string[]
  status?: ProjectKpi['status'][]
  priority?: ProjectKpi['priority'][]
  department_ids?: string[]
  date_range?: {
    start: string
    end: string
  }
  search_term?: string
  is_primary?: boolean
  has_measurements?: boolean
}

export interface KpiSearchResult {
  kpis: ProjectKpi[]
  total_count: number
  filters_applied: KpiFilters
  page: number
  per_page: number
}

// أنواع البيانات للإعدادات
export interface KpiSettings {
  auto_calculate_improvement: boolean
  measurement_reminder_days: number
  target_warning_threshold: number
  default_measurement_period: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  require_approval_for_changes: boolean
  enable_notifications: boolean
}

// أنواع البيانات للصلاحيات
export interface KpiPermissions {
  can_create: boolean
  can_edit: boolean
  can_delete: boolean
  can_approve: boolean
  can_measure: boolean
  can_analyze: boolean
  can_export: boolean
  can_import: boolean
} 