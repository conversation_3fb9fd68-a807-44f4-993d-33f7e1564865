'use client'

import React, { useState, useEffect, useRef } from 'react'
import { 
  Bell, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  MessageCircle,
  User,
  FileText,
  Eye,
  X,
  Settings,
  Check
} from 'lucide-react'
import { Button } from './Button'

interface Notification {
  id: string
  type: 'approval' | 'comment' | 'status_change' | 'assignment' | 'system'
  title: string
  message: string
  timestamp: string
  isRead: boolean
  actionUrl?: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  relatedData?: {
    requestId?: string
    userId?: string
    projectId?: string
  }
}

interface NotificationDropdownProps {
  className?: string
}

export function NotificationDropdown({ className = '' }: NotificationDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [filter, setFilter] = useState<'all' | 'unread'>('all')
  const dropdownRef = useRef<HTMLDivElement>(null)

  // بيانات وهمية للإشعارات
  const mockNotifications: Notification[] = [
    {
      id: '1',
      type: 'approval',
      title: 'طلب موافقة جديد',
      message: 'طلب تطوير نظام إدارة المستندات يحتاج موافقتك',
      timestamp: '2024-12-19T10:30:00Z',
      isRead: false,
      actionUrl: '/approvals',
      priority: 'high',
      relatedData: { requestId: 'REQ-001' }
    },
    {
      id: '2',
      type: 'comment',
      title: 'تعليق جديد',
      message: 'أحمد محمد علق على طلب تحسين واجهة التطبيق',
      timestamp: '2024-12-19T09:15:00Z',
      isRead: false,
      actionUrl: '/requests/REQ-002',
      priority: 'medium',
      relatedData: { requestId: 'REQ-002', userId: 'ahmed123' }
    },
    {
      id: '3',
      type: 'status_change',
      title: 'تغيير حالة الطلب',
      message: 'تم اعتماد طلب أتمتة عملية الحضور والانصراف',
      timestamp: '2024-12-19T08:45:00Z',
      isRead: true,
      actionUrl: '/requests/REQ-003',
      priority: 'low',
      relatedData: { requestId: 'REQ-003' }
    },
    {
      id: '4',
      type: 'assignment',
      title: 'مهمة جديدة',
      message: 'تم تكليفك بمراجعة مشروع التدريب الإلكتروني',
      timestamp: '2024-12-18T16:20:00Z',
      isRead: true,
      actionUrl: '/projects/PROJ-001',
      priority: 'medium',
      relatedData: { projectId: 'PROJ-001' }
    },
    {
      id: '5',
      type: 'system',
      title: 'تحديث النظام',
      message: 'تم إضافة ميزات جديدة لنظام إدارة المشاريع',
      timestamp: '2024-12-18T14:00:00Z',
      isRead: true,
      actionUrl: '/changelog',
      priority: 'low'
    }
  ]

  useEffect(() => {
    setNotifications(mockNotifications)
  }, [])

  // إغلاق القائمة عند النقر خارجها
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const getNotificationIcon = (type: string, priority: string) => {
    const iconClass = `w-5 h-5 ${getPriorityColor(priority)}`
    
    switch (type) {
      case 'approval':
        return <CheckCircle className={iconClass} />
      case 'comment':
        return <MessageCircle className={iconClass} />
      case 'status_change':
        return <Clock className={iconClass} />
      case 'assignment':
        return <User className={iconClass} />
      case 'system':
        return <Settings className={iconClass} />
      default:
        return <Bell className={iconClass} />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600'
      case 'high':
        return 'text-orange-600'
      case 'medium':
        return 'text-blue-600'
      case 'low':
        return 'text-gray-600'
      default:
        return 'text-gray-600'
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'الآن'
    if (diffInMinutes < 60) return `${diffInMinutes} دقيقة`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} ساعة`
    return `${Math.floor(diffInMinutes / 1440)} يوم`
  }

  const unreadCount = notifications.filter(n => !n.isRead).length
  const filteredNotifications = filter === 'unread' 
    ? notifications.filter(n => !n.isRead)
    : notifications

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, isRead: true } : n
      )
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(n => ({ ...n, isRead: true }))
    )
  }

  const deleteNotification = (notificationId: string) => {
    setNotifications(prev => 
      prev.filter(n => n.id !== notificationId)
    )
  }

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.isRead) {
      markAsRead(notification.id)
    }
    
    if (notification.actionUrl) {
      window.location.href = notification.actionUrl
    }
    
    setIsOpen(false)
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 rounded-lg hover:bg-gray-100 transition-colors"
      >
        <Bell className="w-5 h-5" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 min-w-[20px] h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center px-1">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden">
          {/* Header */}
          <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-semibold text-gray-900">
                الإشعارات ({unreadCount} غير مقروء)
              </h3>
              
              <div className="flex items-center gap-2">
                {/* Filter Toggle */}
                <button
                  onClick={() => setFilter(filter === 'all' ? 'unread' : 'all')}
                  className={`text-xs px-2 py-1 rounded ${
                    filter === 'unread' 
                      ? 'bg-blue-100 text-blue-800' 
                      : 'bg-gray-100 text-gray-600'
                  }`}
                >
                  {filter === 'unread' ? 'غير مقروء' : 'الكل'}
                </button>

                {/* Mark All as Read */}
                {unreadCount > 0 && (
                  <button
                    onClick={markAllAsRead}
                    className="text-xs text-blue-600 hover:text-blue-800"
                    title="تعليم الكل كمقروء"
                  >
                    <Check className="w-4 h-4" />
                  </button>
                )}

                {/* Close */}
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Notifications List */}
          <div className="max-h-80 overflow-y-auto">
            {filteredNotifications.length === 0 ? (
              <div className="p-8 text-center">
                <Bell className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-500 text-sm">
                  {filter === 'unread' ? 'لا توجد إشعارات غير مقروءة' : 'لا توجد إشعارات'}
                </p>
              </div>
            ) : (
              filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`group relative border-b border-gray-100 hover:bg-gray-50 transition-colors ${
                    !notification.isRead ? 'bg-blue-50' : ''
                  }`}
                >
                  <button
                    onClick={() => handleNotificationClick(notification)}
                    className="w-full p-4 text-right"
                  >
                    <div className="flex items-start gap-3">
                      {/* Icon */}
                      <div className="flex-shrink-0 mt-0.5">
                        {getNotificationIcon(notification.type, notification.priority)}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <p className={`text-sm font-medium ${
                              !notification.isRead ? 'text-gray-900' : 'text-gray-700'
                            }`}>
                              {notification.title}
                            </p>
                            <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                              {notification.message}
                            </p>
                          </div>

                          {/* Timestamp */}
                          <span className="text-xs text-gray-500 flex-shrink-0 mr-2">
                            {formatTimestamp(notification.timestamp)}
                          </span>
                        </div>

                        {/* Read indicator */}
                        {!notification.isRead && (
                          <div className="absolute top-4 right-4">
                            <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                          </div>
                        )}
                      </div>
                    </div>
                  </button>

                  {/* Delete button (visible on hover) */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      deleteNotification(notification.id)
                    }}
                    className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-600 transition-all"
                    title="حذف الإشعار"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))
            )}
          </div>

          {/* Footer */}
          {filteredNotifications.length > 0 && (
            <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  window.location.href = '/notifications'
                  setIsOpen(false)
                }}
                className="w-full text-center text-blue-600 hover:text-blue-800"
              >
                عرض جميع الإشعارات
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

// Hook لإدارة الإشعارات
export function useNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)

  useEffect(() => {
    // حساب عدد الإشعارات غير المقروءة
    const count = notifications.filter(n => !n.isRead).length
    setUnreadCount(count)
  }, [notifications])

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date().toISOString()
    }
    
    setNotifications(prev => [newNotification, ...prev])
  }

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, isRead: true } : n
      )
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(n => ({ ...n, isRead: true }))
    )
  }

  const deleteNotification = (notificationId: string) => {
    setNotifications(prev => 
      prev.filter(n => n.id !== notificationId)
    )
  }

  const clearAll = () => {
    setNotifications([])
  }

  return {
    notifications,
    unreadCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll
  }
} 