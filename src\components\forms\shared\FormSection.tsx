'use client'

import React from 'react'
import { LucideIcon, Target, Users } from 'lucide-react'

interface FormSectionProps {
  icon: LucideIcon
  title: string
  description?: string
  children: React.ReactNode
  className?: string
  variant?: 'default' | 'highlighted' | 'bordered'
}

export function FormSection({ 
  icon: Icon, 
  title, 
  description,
  children, 
  className = '',
  variant = 'default'
}: FormSectionProps) {
  
  const getVariantClasses = () => {
    switch (variant) {
      case 'highlighted':
        return 'bg-gray-50 border border-gray-200 rounded-lg p-4'
      case 'bordered':
        return 'border border-gray-200 rounded-lg p-4'
      default:
        return ''
    }
  }

  return (
    <div className={`${getVariantClasses()} ${className}`}>
      <div className="flex items-center gap-2 mb-4">
        <Icon className="w-5 h-5 text-blue-600" />
        <h4 className="font-semibold text-gray-900">{title}</h4>
      </div>
      
      {description && (
        <p className="text-sm text-gray-600 mb-4 leading-relaxed">
          {description}
        </p>
      )}
      
      {children}
    </div>
  )
}

// مكون مخصص لقسم المؤشرات
interface IndicatorSectionProps {
  children: React.ReactNode
  className?: string
}

export function IndicatorSection({ children, className = '' }: IndicatorSectionProps) {
  return (
    <FormSection
      icon={Target}
      title="بيانات المؤشر"
      variant="highlighted"
      className={className}
    >
      {children}
    </FormSection>
  )
}

// مكون مخصص لقسم الفريق
interface TeamSectionProps {
  children: React.ReactNode
  className?: string
}

export function TeamSection({ children, className = '' }: TeamSectionProps) {
  return (
    <FormSection
      icon={Users}
      title="معلومات الفريق"
      variant="highlighted"
      className={className}
    >
      {children}
    </FormSection>
  )
} 