'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { Input, Textarea } from '@/components/ui/Input'
import { StepHeader } from '../../shared/StepHeader'
import { Plus, Trash2, Target, CheckCircle } from 'lucide-react'
import { FormType, SuggestionData } from '../UnifiedProjectForm'

interface AdaptiveRecommendationsStepProps {
  formType: FormType
  data: SuggestionData
  updateData: (field: string, value: any) => void
  errors: Record<string, string>
}

export function AdaptiveRecommendationsStep({ 
  formType, 
  data, 
  updateData, 
  errors 
}: AdaptiveRecommendationsStepProps) {
  
  const addNextStep = () => {
    const currentSteps = data.nextSteps || []
    updateData('nextSteps', [...currentSteps, ''])
  }

  const updateNextStep = (index: number, value: string) => {
    const currentSteps = [...(data.nextSteps || [])]
    currentSteps[index] = value
    updateData('nextSteps', currentSteps)
  }

  const removeNextStep = (index: number) => {
    const currentSteps = data.nextSteps || []
    const updatedSteps = currentSteps.filter((_, i) => i !== index)
    updateData('nextSteps', updatedSteps)
  }

  // حساب أفضل الحلول بناءً على النتائج
  const getBestSolutions = () => {
    if (!data.suggestedSolutions || data.suggestedSolutions.length === 0) {
      return []
    }

    return data.suggestedSolutions
      .map(solution => ({
        ...solution,
        score: solution.feasibilityScore * solution.impactScore
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 3)
  }

  const bestSolutions = getBestSolutions()

  return (
    <div className="space-y-6">
      {/* رأس الخطوة */}
      <StepHeader
        stepNumber={6}
        title="Recommendations - التوصيات النهائية"
        description="اكتب توصياتك النهائية والخطوات التالية المقترحة"
        icon={Target}
        formType={formType}
      />

      {/* ملخص أفضل الحلول */}
      {bestSolutions.length > 0 && (
        <Card className="p-6 bg-blue-50 border-blue-200">
          <h3 className="text-lg font-semibold text-blue-900 mb-4 flex items-center gap-2">
            <CheckCircle className="w-5 h-5" />
            أفضل الحلول المقترحة (بناءً على التقييم)
          </h3>
          <div className="space-y-3">
            {bestSolutions.map((solution, index) => (
              <div key={solution.id} className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium text-gray-900">
                    {index + 1}. {solution.title}
                  </h4>
                  <div className="text-sm text-blue-600 font-medium">
                    نقاط: {Math.round(solution.score)}
                  </div>
                </div>
                <p className="text-sm text-gray-700 mb-2">{solution.description}</p>
                <div className="flex gap-4 text-xs text-gray-600">
                  <span>جدوى: {solution.feasibilityScore}/10</span>
                  <span>تأثير: {solution.impactScore}/10</span>
                  <span className={`px-2 py-1 rounded ${
                    solution.priority === 'high' ? 'bg-red-100 text-red-800' :
                    solution.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    أولوية: {solution.priority === 'high' ? 'عالية' : 
                             solution.priority === 'medium' ? 'متوسطة' : 'منخفضة'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* التوصيات النهائية */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">التوصيات النهائية</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              اكتب توصياتك النهائية بناءً على التحليل والحلول المقترحة *
            </label>
            <Textarea
              value={data.recommendations || ''}
              onChange={(e) => updateData('recommendations', e.target.value)}
              placeholder="مثال: بناءً على التحليل المقدم، أوصي بتطبيق الحل الأول (تحسين العملية الآلية) كأولوية قصوى لما له من تأثير كبير وجدوى عالية. يمكن تطبيق الحل الثاني كمرحلة ثانية بعد نجاح الحل الأول..."
              rows={6}
              className={`w-full ${errors.recommendations ? 'border-red-500' : ''}`}
            />
            {errors.recommendations && (
              <p className="text-red-500 text-xs mt-1">{errors.recommendations}</p>
            )}
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-800 mb-2">نصائح لكتابة التوصيات:</h4>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• ابدأ بالحل الأكثر جدوى وتأثيراً</li>
              <li>• اذكر الأسباب وراء ترتيب أولوياتك</li>
              <li>• حدد المخاطر المحتملة لكل توصية</li>
              <li>• اقترح خطة تنفيذ مرحلية إذا أمكن</li>
              <li>• اربط التوصيات بالمؤشرات المحددة</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* الخطوات التالية */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900">الخطوات التالية المقترحة</h3>
          <Button
            onClick={addNextStep}
            variant="secondary"
            size="sm"
          >
            <Plus className="w-4 h-4 mr-2" />
            إضافة خطوة
          </Button>
        </div>

        <div className="space-y-3">
          {(data.nextSteps || []).map((step, index) => (
            <div key={index} className="flex gap-3 items-start">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-sm font-medium mt-1">
                {index + 1}
              </div>
              <div className="flex-1">
                <Input
                  value={step}
                  onChange={(e) => updateNextStep(index, e.target.value)}
                  placeholder={`الخطوة ${index + 1}: مثال - تشكيل فريق عمل لتطبيق الحل الأول`}
                  className="w-full"
                />
              </div>
              <Button
                onClick={() => removeNextStep(index)}
                variant="ghost"
                size="sm"
                className="text-red-600 hover:text-red-700 mt-1"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          ))}

          {(!data.nextSteps || data.nextSteps.length === 0) && (
            <div className="text-center py-6 text-gray-500">
              <Target className="w-12 h-12 mx-auto mb-3 text-gray-300" />
              <p>لم يتم إضافة خطوات تالية بعد</p>
              <p className="text-xs mt-1">اضغط "إضافة خطوة" لتحديد الإجراءات المطلوبة</p>
            </div>
          )}
        </div>

        {data.nextSteps && data.nextSteps.length > 0 && (
          <div className="mt-4 bg-green-50 rounded-lg p-4">
            <h4 className="font-medium text-green-800 mb-2">أمثلة على الخطوات التالية:</h4>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• تشكيل فريق عمل متخصص لتنفيذ التوصيات</li>
              <li>• إعداد خطة تنفيذ مفصلة مع جدول زمني</li>
              <li>• الحصول على موافقات الإدارة العليا</li>
              <li>• تخصيص الموارد والميزانية المطلوبة</li>
              <li>• وضع مؤشرات متابعة وقياس النجاح</li>
              <li>• تحديد مواعيد مراجعة دورية للتقدم</li>
            </ul>
          </div>
        )}
      </Card>

      {/* ملاحظات مهمة */}
      <Card className="p-6 bg-yellow-50 border-yellow-200">
        <h4 className="font-semibold text-yellow-900 mb-3">📝 ملاحظات مهمة</h4>
        <ul className="text-sm text-yellow-800 space-y-2">
          <li>• التوصيات يجب أن تكون مبنية على التحليل المقدم في المراحل السابقة</li>
          <li>• حدد أولويات واضحة للتنفيذ بناءً على الجدوى والتأثير</li>
          <li>• اذكر المخاطر المحتملة وكيفية التعامل معها</li>
          <li>• الخطوات التالية يجب أن تكون قابلة للتنفيذ وقابلة للقياس</li>
          <li>• تأكد من ربط التوصيات بالمؤشرات المحددة في بداية المقترح</li>
        </ul>
      </Card>
    </div>
  )
} 