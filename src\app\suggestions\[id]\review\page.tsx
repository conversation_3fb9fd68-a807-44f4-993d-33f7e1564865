'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import FullSuggestionDisplay from '@/components/feedback/FullSuggestionDisplay';
import ApprovalTimeline from '@/components/feedback/ApprovalTimeline';
import { FeedbackManager } from '@/lib/feedbackManager';
import { InteractiveReviewData } from '@/types/feedback.types';
import { Card } from '@/components/ui/Card';
import { AppLayout } from '@/components/layout/AppLayout';

interface TimelineEntry {
  id: string;
  participantId: string;
  participantName: string;
  participantRole: string;
  department: string;
  action: 'review' | 'approve' | 'reject' | 'comment' | 'solution_selection';
  selectedSolutionId?: string;
  selectedSolutionTitle?: string;
  comment: string;
  timestamp: string;
  status: 'pending' | 'completed';
}

interface ParticipantApproval {
  participantId: string;
  participantName: string;
  participantRole: string;
  department: string;
  status: 'pending' | 'approved' | 'rejected';
  selectedSolutionId?: string;
  comment?: string;
  timestamp?: string;
}

export default function SuggestionReviewPage() {
  const params = useParams();
  const suggestionId = params.id as string;
  
  const [reviewData, setReviewData] = useState<InteractiveReviewData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPhase, setCurrentPhase] = useState<'review' | 'approval' | 'conversion'>('review');
  const [timeline, setTimeline] = useState<TimelineEntry[]>([]);
  const [participantApprovals, setParticipantApprovals] = useState<ParticipantApproval[]>([]);

  useEffect(() => {
    const loadReviewData = async () => {
      try {
        setLoading(true);
        const data = await FeedbackManager.getFullInteractiveReviewData(suggestionId);
        
        if (!data) {
          setError('لم يتم العثور على بيانات المراجعة');
          return;
        }
        
        setReviewData(data);
        
        // تهيئة موافقات المشاركين
        const initialApprovals: ParticipantApproval[] = data.participants.map(participant => ({
          participantId: participant.id,
          participantName: participant.name,
          participantRole: participant.role,
          department: participant.department,
          status: 'pending'
        }));
        setParticipantApprovals(initialApprovals);
        
      } catch (err) {
        console.error('خطأ في تحميل بيانات المراجعة:', err);
        setError('حدث خطأ في تحميل البيانات');
      } finally {
        setLoading(false);
      }
    };

    if (suggestionId) {
      loadReviewData();
    }
  }, [suggestionId]);

  const handleApprovalSubmit = async (approval: {
    selectedSolutionId: string;
    comment: string;
    action: 'approve' | 'reject';
  }) => {
    try {
      // المستخدم الحالي (يجب الحصول عليه من نظام المصادقة)
      const currentUserId = 'participant-1'; // مؤقت للاختبار
      const currentParticipant = reviewData?.participants.find(p => p.id === currentUserId);
      
      if (!currentParticipant || !reviewData) return;

      // تحديث موافقة المشارك
      setParticipantApprovals(prev => 
        prev.map(p => 
          p.participantId === currentUserId 
            ? {
                ...p,
                status: approval.action === 'approve' ? 'approved' : 'rejected',
                selectedSolutionId: approval.selectedSolutionId,
                comment: approval.comment,
                timestamp: new Date().toISOString()
              }
            : p
        )
      );

      // إضافة إدخال في التايم لاين
      const selectedSolution = reviewData.suggestion.fullFormData?.select.proposedSolutions
        .find(s => s.id === approval.selectedSolutionId);

      const newTimelineEntry: TimelineEntry = {
        id: `timeline-${Date.now()}`,
        participantId: currentUserId,
        participantName: currentParticipant.name,
        participantRole: currentParticipant.role,
        department: currentParticipant.department,
        action: approval.action,
        selectedSolutionId: approval.selectedSolutionId,
        selectedSolutionTitle: selectedSolution?.title,
        comment: approval.comment,
        timestamp: new Date().toISOString(),
        status: 'completed'
      };

      setTimeline(prev => [newTimelineEntry, ...prev]);

      console.log('تم إرسال الموافقة:', approval);
    } catch (error) {
      console.error('خطأ في إرسال الموافقة:', error);
      throw error;
    }
  };

  const handleRetry = () => {
    setError(null);
    const loadReviewData = async () => {
      try {
        setLoading(true);
        const data = await FeedbackManager.getFullInteractiveReviewData(suggestionId);
        
        if (!data) {
          setError('لم يتم العثور على بيانات المراجعة');
          return;
        }
        
        setReviewData(data);
      } catch (err) {
        console.error('خطأ في تحميل بيانات المراجعة:', err);
        setError('حدث خطأ في تحميل البيانات');
      } finally {
        setLoading(false);
      }
    };

    loadReviewData();
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-body text-gray-600">جاري تحميل بيانات المراجعة...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center max-w-md mx-auto p-6">
            <div className="text-red-500 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h2 className="heading-sub text-gray-900 mb-2">خطأ في التحميل</h2>
            <p className="text-body text-gray-600 mb-4">{error}</p>
            <button
              onClick={handleRetry}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
            >
              إعادة المحاولة
            </button>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (!reviewData || !reviewData.suggestion.fullFormData) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <p className="text-body text-gray-600">لا توجد بيانات متاحة</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  const allApproved = participantApprovals.every(p => p.status === 'approved');
  const hasRejections = participantApprovals.some(p => p.status === 'rejected');

  return (
    <AppLayout>
      {/* شريط التنقل العلوي */}
      <div className="bg-white shadow-sm border-b rounded-lg mb-6">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              <h1 className="heading-sub text-gray-900">
                مراجعة مقترح التحسين
              </h1>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-caption">
                {suggestionId}
              </span>
            </div>
            
            <div className="flex items-center space-x-3 space-x-reverse">
              {allApproved && (
                <button
                  onClick={() => setCurrentPhase('conversion')}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-caption font-medium transition-colors"
                >
                  التحويل لمشروع
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* المحتوى الرئيسي - تخطيط عمودين */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* العمود الأيسر - عرض المقترح (ثلثي العرض) */}
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h2 className="heading-section text-gray-900 mb-4 flex items-center">
              <svg className="w-5 h-5 text-blue-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              عرض المقترح
            </h2>
          </div>
          
          <FullSuggestionDisplay
            suggestionData={reviewData.suggestion.fullFormData}
            suggestionId={suggestionId}
            suggestionTitle={reviewData.suggestion.title}
            suggestionDescription={reviewData.suggestion.description}
            submitterInfo={{
              name: reviewData.suggestion.submittedBy.name,
              department: reviewData.suggestion.submittedBy.department,
              position: reviewData.suggestion.submittedBy.role,
              email: reviewData.suggestion.submittedBy.email,
              phone: reviewData.suggestion.submittedBy.phone,
              submissionDate: new Date(reviewData.suggestion.submittedAt).toLocaleDateString('ar-SA')
            }}
          />
        </div>

        {/* العمود الأيمن - بانتظار الموافقة (ثلث العرض) */}
        <div className="lg:col-span-1 space-y-6">
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h2 className="heading-section text-gray-900 mb-4 flex items-center">
              <svg className="w-5 h-5 text-orange-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              بانتظار الموافقة
            </h2>
          </div>
          
          <ApprovalTimeline
            solutions={reviewData.suggestion.fullFormData.select.proposedSolutions}
            participants={reviewData.participants}
            currentUserId="participant-1" // يجب الحصول عليه من نظام المصادقة
            currentUserRole="team_leader" // يجب الحصول عليه من نظام المصادقة
            onApprovalSubmit={handleApprovalSubmit}
            timeline={timeline}
            participantApprovals={participantApprovals}
          />
        </div>
      </div>

      {/* رسائل الحالة */}
      {currentPhase === 'conversion' && allApproved && (
        <Card className="p-6 mt-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="heading-main text-gray-900 mb-2">
              ✅ تمت الموافقة من جميع الأقسام المشاركة
            </h2>
            <p className="text-body text-gray-600 mb-6">
              يمكن الآن لمكتب المشاريع (PMO) تحويل هذا المقترح إلى مشروع رسمي وإكمال البيانات الناقصة.
              تم اختيار الحل الأمثل بناءً على موافقة جميع الأقسام المعنية.
            </p>
            <button className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-md font-medium transition-colors">
              تحويل إلى مشروع
            </button>
          </div>
        </Card>
      )}

      {hasRejections && (
        <Card className="p-6 mt-6 border-red-200 bg-red-50">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="heading-sub text-red-900 mb-2">
              ❌ تم رفض المقترح من بعض الأقسام
            </h2>
            <p className="text-body text-red-700 mb-4">
              يجب مراجعة التعليقات والملاحظات من الأقسام الرافضة وإجراء التعديلات المطلوبة قبل إعادة تقديم المقترح
            </p>
          </div>
        </Card>
      )}
    </AppLayout>
  );
} 