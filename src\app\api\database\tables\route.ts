import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

// GET - اختبار الاتصال بقاعدة البيانات وعرض الجداول
export async function GET() {
  try {
    // اختبار الاتصال بقاعدة البيانات بطريقة بسيطة
    const { data: testData, error } = await supabase
      .from('departments')
      .select('id')
      .limit(1)

    if (error) {
      console.error('Database connection error:', error)
      return NextResponse.json(
        { 
          success: false, 
          error: 'فشل في الاتصال بقاعدة البيانات',
          details: error.message 
        },
        { status: 500 }
      )
    }

    // إحصائيات الجداول
    const stats = {
      departments: 0,
      users: 0,
      roles: 0,
      requests: 0
    }

    // عد الأقسام
    const { count: departmentsCount } = await supabase
      .from('departments')
      .select('*', { count: 'exact', head: true })
    stats.departments = departmentsCount || 0

    // عد المستخدمين
    const { count: usersCount } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
    stats.users = usersCount || 0

    // عد الأدوار
    const { count: rolesCount } = await supabase
      .from('roles')
      .select('*', { count: 'exact', head: true })
    stats.roles = rolesCount || 0

    // عد الطلبات (إذا كان الجدول موجوداً)
    const { count: requestsCount } = await supabase
      .from('project_requests')
      .select('*', { count: 'exact', head: true })
    stats.requests = requestsCount || 0

    return NextResponse.json({
      success: true,
      message: 'تم الاتصال بقاعدة البيانات بنجاح',
      data: {
        tables: ['departments', 'users', 'roles', 'project_requests', 'approvals', 'notifications', 'projects', 'tasks'],
        stats,
        connection_status: 'connected'
      }
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'حدث خطأ في الخادم',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
} 