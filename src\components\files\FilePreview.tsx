'use client'

import React, { useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  X, 
  Download, 
  ExternalLink, 
  ZoomIn, 
  ZoomOut, 
  RotateCw,
  File,
  Image as ImageIcon,
  FileText,
  AlertCircle,
  Maximize2,
  Minimize2
} from 'lucide-react'

interface FileInfo {
  id: string
  name: string
  type: string
  size: number
  url: string
  uploadDate?: string
  description?: string
}

interface FilePreviewProps {
  file: FileInfo | null
  isOpen: boolean
  onClose: () => void
  onDownload?: (file: FileInfo) => void
  onDelete?: (file: FileInfo) => void
  showActions?: boolean
}

export function FilePreview({ 
  file, 
  isOpen, 
  onClose, 
  onDownload, 
  onDelete,
  showActions = true 
}: FilePreviewProps) {
  const [zoom, setZoom] = useState(100)
  const [rotation, setRotation] = useState(0)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [imageError, setImageError] = useState(false)

  if (!isOpen || !file) return null

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const isImage = file.type.startsWith('image/')
  const isPDF = file.type.includes('pdf')
  const isDocument = file.type.includes('word') || file.type.includes('document') || 
                   file.type.includes('sheet') || file.type.includes('excel') ||
                   file.type.includes('text')

  const handleZoomIn = () => setZoom(prev => Math.min(prev + 25, 200))
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 25, 25))
  const handleRotate = () => setRotation(prev => (prev + 90) % 360)
  // const handleReset = () => {
  //   setZoom(100)
  //   setRotation(0)
  // }

  const renderPreview = () => {
    if (isImage && !imageError) {
      return (
        <div className="flex items-center justify-center h-full bg-gray-100 overflow-hidden">
          <img
            src={file.url}
            alt={file.name}
            className="max-w-full max-h-full object-contain transition-transform duration-200"
            style={{
              transform: `scale(${zoom / 100}) rotate(${rotation}deg)`
            }}
            onError={() => setImageError(true)}
            onLoad={() => setImageError(false)}
          />
        </div>
      )
    }

    if (isPDF) {
      return (
        <div className="h-full">
          <iframe
            src={file.url}
            className="w-full h-full border-0"
            title={file.name}
          />
        </div>
      )
    }

    if (isDocument) {
      return (
        <div className="flex flex-col items-center justify-center h-full bg-gray-50 text-center p-8">
          <FileText className="w-16 h-16 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">{file.name}</h3>
          <p className="text-gray-600 mb-4">
            لا يمكن معاينة هذا النوع من الملفات في المتصفح
          </p>
          <Button
            icon={<Download className="w-4 h-4" />}
            onClick={() => onDownload?.(file)}
          >
            تحميل الملف
          </Button>
        </div>
      )
    }

    return (
      <div className="flex flex-col items-center justify-center h-full bg-gray-50 text-center p-8">
        <AlertCircle className="w-16 h-16 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">غير مدعوم</h3>
        <p className="text-gray-600 mb-4">
          نوع الملف غير مدعوم للمعاينة
        </p>
        <Button
          icon={<Download className="w-4 h-4" />}
          onClick={() => onDownload?.(file)}
        >
          تحميل الملف
        </Button>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center p-4">
      <Card className={`bg-white ${isFullscreen ? 'w-full h-full' : 'w-full max-w-5xl h-[90vh]'} flex flex-col`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            <div className="flex-shrink-0">
              {isImage ? (
                <ImageIcon className="w-5 h-5 text-blue-600" />
              ) : isPDF ? (
                <FileText className="w-5 h-5 text-red-600" />
              ) : (
                <File className="w-5 h-5 text-gray-600" />
              )}
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="font-medium text-gray-900 truncate">{file.name}</h3>
              <p className="text-sm text-gray-500">
                {formatFileSize(file.size)}
                {file.uploadDate && ` • ${formatDate(file.uploadDate)}`}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Image Controls */}
            {isImage && !imageError && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  icon={<ZoomOut className="w-4 h-4" />}
                  onClick={handleZoomOut}
                  disabled={zoom <= 25}
                />
                <span className="text-sm text-gray-600 min-w-[3rem] text-center">
                  {zoom}%
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  icon={<ZoomIn className="w-4 h-4" />}
                  onClick={handleZoomIn}
                  disabled={zoom >= 200}
                />
                <Button
                  variant="ghost"
                  size="sm"
                  icon={<RotateCw className="w-4 h-4" />}
                  onClick={handleRotate}
                />
                <div className="w-px h-6 bg-gray-300 mx-2" />
              </>
            )}

            {/* General Controls */}
            <Button
              variant="ghost"
              size="sm"
              icon={isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
              onClick={() => setIsFullscreen(!isFullscreen)}
            />

            {showActions && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  icon={<Download className="w-4 h-4" />}
                  onClick={() => onDownload?.(file)}
                />
                
                <Button
                  variant="ghost"
                  size="sm"
                  icon={<ExternalLink className="w-4 h-4" />}
                  onClick={() => window.open(file.url, '_blank')}
                />

                {onDelete && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    onClick={() => {
                      if (confirm('هل أنت متأكد من حذف هذا الملف؟')) {
                        onDelete(file)
                        onClose()
                      }
                    }}
                  >
                    حذف
                  </Button>
                )}
              </>
            )}

            <Button
              variant="ghost"
              size="sm"
              icon={<X className="w-4 h-4" />}
              onClick={onClose}
            />
          </div>
        </div>

        {/* Preview Content */}
        <div className="flex-1 overflow-hidden">
          {renderPreview()}
        </div>

        {/* Footer - File Description */}
        {file.description && (
          <div className="p-4 border-t border-gray-200 bg-gray-50">
            <p className="text-sm text-gray-700">{file.description}</p>
          </div>
        )}
      </Card>
    </div>
  )
} 