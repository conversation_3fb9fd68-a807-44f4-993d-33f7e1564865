'use client'

import React from 'react'
import { 
  X, 
  Zap, 
  Clock, 
  DollarSign, 
  TrendingUp, 
  CheckCircle,
  Target,
  Users,
  Lightbulb,
  AlertCircle,
  Star,
  Award
} from 'lucide-react'
import { Button } from './Button'
import { Card } from './Card'

interface QuickWinGuideModalProps {
  isOpen: boolean
  onClose: () => void
}

export function QuickWinGuideModal({ isOpen, onClose }: QuickWinGuideModalProps) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 p-6 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
              <Zap className="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">دليل مشاريع الكويك وين</h2>
              <p className="text-gray-600">كل ما تحتاج معرفته عن مشاريع التحسين السريعة</p>
            </div>
          </div>
          <Button
            variant="ghost"
            onClick={onClose}
            icon={<X className="w-5 h-5" />}
            className="text-gray-500 hover:text-gray-700"
          />
        </div>

        {/* Content */}
        <div className="p-6 space-y-8">
          {/* Definition */}
          <Card className="border-orange-200 bg-orange-50">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <Target className="w-6 h-6 text-orange-600" />
                <h3 className="text-xl font-bold text-orange-900">ما هي مشاريع الكويك وين؟</h3>
              </div>
              <p className="text-orange-800 text-lg leading-relaxed">
                مشاريع تحسين سريعة، منخفضة التكلفة، نتائجها ملموسة خلال أسابيع. هي حلول بسيطة وفعالة 
                لتحسين العمليات والأداء دون الحاجة لموارد كبيرة أو وقت طويل.
              </p>
            </div>
          </Card>

          {/* Key Characteristics */}
          <div>
            <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
              <Star className="w-6 h-6 text-orange-600" />
              الخصائص الرئيسية
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4 border-l-4 border-l-orange-500">
                <div className="flex items-center gap-3 mb-2">
                  <Clock className="w-5 h-5 text-orange-600" />
                  <h4 className="font-semibold text-gray-900">مدة التنفيذ</h4>
                </div>
                <p className="text-gray-700">لا تزيد عن 4 أسابيع كحد أقصى لظهور النتائج</p>
              </Card>

              <Card className="p-4 border-l-4 border-l-green-500">
                <div className="flex items-center gap-3 mb-2">
                  <DollarSign className="w-5 h-5 text-green-600" />
                  <h4 className="font-semibold text-gray-900">التكلفة</h4>
                </div>
                <p className="text-gray-700">تكلفة شبه معدومة أو منخفضة جداً</p>
              </Card>

              <Card className="p-4 border-l-4 border-l-blue-500">
                <div className="flex items-center gap-3 mb-2">
                  <TrendingUp className="w-5 h-5 text-blue-600" />
                  <h4 className="font-semibold text-gray-900">النتائج</h4>
                </div>
                <p className="text-gray-700">نتائج فورية وواضحة يمكن قياسها</p>
              </Card>

              <Card className="p-4 border-l-4 border-l-purple-500">
                <div className="flex items-center gap-3 mb-2">
                  <CheckCircle className="w-5 h-5 text-purple-600" />
                  <h4 className="font-semibold text-gray-900">التنفيذ</h4>
                </div>
                <p className="text-gray-700">تنفيذ سريع وسهل بدون تعقيدات</p>
              </Card>
            </div>
          </div>

          {/* When to Use */}
          <div>
            <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
              <Lightbulb className="w-6 h-6 text-yellow-600" />
              متى نستخدم مشاريع الكويك وين؟
            </h3>
            <div className="space-y-4">
              <Card className="p-4 bg-green-50 border-green-200">
                <h4 className="font-semibold text-green-900 mb-2">الحالات المناسبة:</h4>
                <ul className="text-green-800 space-y-2">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>عندما تكون المشكلة واضحة والحل بسيط</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>عندما لا تحتاج موافقات معقدة أو موارد كبيرة</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>عندما تريد نتائج سريعة لتحفيز الفريق</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>عندما تكون التحسينات في نطاق قسم واحد</span>
                  </li>
                </ul>
              </Card>

              <Card className="p-4 bg-red-50 border-red-200">
                <h4 className="font-semibold text-red-900 mb-2">الحالات غير المناسبة:</h4>
                <ul className="text-red-800 space-y-2">
                  <li className="flex items-start gap-2">
                    <X className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                    <span>المشاريع التي تحتاج تغييرات هيكلية كبيرة</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <X className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                    <span>المشاريع التي تحتاج موازنات ضخمة</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <X className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                    <span>المشاريع التي تؤثر على أقسام متعددة</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <X className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                    <span>المشاريع التي تحتاج دراسات معقدة</span>
                  </li>
                </ul>
              </Card>
            </div>
          </div>

          {/* Examples */}
          <div>
            <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
              <Award className="w-6 h-6 text-purple-600" />
              أمثلة على مشاريع الكويك وين
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4">
                <h4 className="font-semibold text-gray-900 mb-2">تنظيم مكان العمل</h4>
                <p className="text-gray-700 text-sm mb-3">
                  إعادة ترتيب المكتب وتنظيم الأدوات لتحسين الكفاءة
                </p>
                <div className="text-xs text-gray-500 space-y-1">
                  <div>⏱️ المدة: أسبوع واحد</div>
                  <div>💰 التكلفة: 0 ريال</div>
                  <div>📈 النتيجة: زيادة الإنتاجية 15%</div>
                </div>
              </Card>

              <Card className="p-4">
                <h4 className="font-semibold text-gray-900 mb-2">تحسين عملية الأرشفة</h4>
                <p className="text-gray-700 text-sm mb-3">
                  إنشاء نظام أرشفة رقمي بسيط للوثائق المهمة
                </p>
                <div className="text-xs text-gray-500 space-y-1">
                  <div>⏱️ المدة: أسبوعين</div>
                  <div>💰 التكلفة: 100 ريال</div>
                  <div>📈 النتيجة: توفير 30% من وقت البحث</div>
                </div>
              </Card>

              <Card className="p-4">
                <h4 className="font-semibold text-gray-900 mb-2">تحسين التواصل الداخلي</h4>
                <p className="text-gray-700 text-sm mb-3">
                  إنشاء مجموعة واتساب منظمة للفريق
                </p>
                <div className="text-xs text-gray-500 space-y-1">
                  <div>⏱️ المدة: يوم واحد</div>
                  <div>💰 التكلفة: 0 ريال</div>
                  <div>📈 النتيجة: تحسين التواصل 40%</div>
                </div>
              </Card>

              <Card className="p-4">
                <h4 className="font-semibold text-gray-900 mb-2">تحسين عملية الطباعة</h4>
                <p className="text-gray-700 text-sm mb-3">
                  تنظيم منطقة الطباعة وإنشاء نظام طوابير
                </p>
                <div className="text-xs text-gray-500 space-y-1">
                  <div>⏱️ المدة: 3 أيام</div>
                  <div>💰 التكلفة: 50 ريال</div>
                  <div>📈 النتيجة: تقليل الانتظار 60%</div>
                </div>
              </Card>
            </div>
          </div>

          {/* Tips */}
          <div>
            <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
              <Users className="w-6 h-6 text-blue-600" />
              نصائح للنجاح
            </h3>
            <Card className="p-4 bg-blue-50 border-blue-200">
              <ul className="text-blue-800 space-y-3">
                <li className="flex items-start gap-2">
                  <AlertCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span>ابدأ بالمشاكل الصغيرة والواضحة</span>
                </li>
                <li className="flex items-start gap-2">
                  <AlertCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span>اختر حلول بسيطة وعملية</span>
                </li>
                <li className="flex items-start gap-2">
                  <AlertCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span>احرص على قياس النتائج قبل وبعد</span>
                </li>
                <li className="flex items-start gap-2">
                  <AlertCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span>اشرك الفريق في التنفيذ</span>
                </li>
                <li className="flex items-start gap-2">
                  <AlertCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span>وثق النتائج وشاركها مع الآخرين</span>
                </li>
              </ul>
            </Card>
          </div>
        </div>

        {/* Footer */}
        <div className="sticky bottom-0 bg-gray-50 border-t border-gray-200 p-4 flex justify-end">
          <Button
            variant="primary"
            onClick={onClose}
            className="bg-orange-600 hover:bg-orange-700"
          >
            فهمت، أريد إنشاء كويك وين
          </Button>
        </div>
      </div>
    </div>
  )
} 