'use client'

import React, { useState } from 'react'
import { But<PERSON> } from './Button'
import { X, MessageCircle, Send, CheckCircle, XCircle, AlertCircle } from 'lucide-react'

interface CommentModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (comment: string, action: 'approve' | 'reject') => void
  title: string
  requestTitle: string
  requestId: string
  action: 'approve' | 'reject'
  isLoading?: boolean
}

export function CommentModal({
  isOpen,
  onClose,
  onSubmit,
  title,
  requestTitle,
  requestId,
  action,
  isLoading = false
}: CommentModalProps) {
  const [comment, setComment] = useState('')
  const [error, setError] = useState('')

  const handleSubmit = () => {
    if (!comment.trim()) {
      setError('يرجى إدخال تعليق')
      return
    }

    if (comment.trim().length < 10) {
      setError('يجب أن يكون التعليق 10 أحرف على الأقل')
      return
    }

    setError('')
    onSubmit(comment.trim(), action)
  }

  const handleClose = () => {
    if (!isLoading) {
      setComment('')
      setError('')
      onClose()
    }
  }

  const getActionConfig = () => {
    if (action === 'approve') {
      return {
        icon: <CheckCircle className="w-6 h-6 text-green-600" />,
        title: 'موافقة على الطلب',
        buttonText: 'موافقة',
        buttonVariant: 'success' as const,
        placeholder: 'أدخل تعليقك على الموافقة (اختياري)...',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200'
      }
    } else {
      return {
        icon: <XCircle className="w-6 h-6 text-red-600" />,
        title: 'رفض الطلب',
        buttonText: 'رفض',
        buttonVariant: 'danger' as const,
        placeholder: 'أدخل سبب الرفض (مطلوب)...',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200'
      }
    }
  }

  const config = getActionConfig()

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={handleClose}
      />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative w-full max-w-md transform overflow-hidden rounded-lg bg-white shadow-xl transition-all">
          {/* Header */}
          <div className={`px-6 py-4 ${config.bgColor} ${config.borderColor} border-b`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {config.icon}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {config.title}
                  </h3>
                  <p className="text-sm text-gray-600">
                    #{requestId}
                  </p>
                </div>
              </div>
              
              <button
                onClick={handleClose}
                disabled={isLoading}
                className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="px-6 py-4">
            {/* Request Info */}
            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-1">
                الطلب المراد {action === 'approve' ? 'اعتماده' : 'رفضه'}:
              </h4>
              <p className="text-sm text-gray-700">{requestTitle}</p>
            </div>

            {/* Comment Input */}
            <div className="space-y-3">
              <label className="block">
                <span className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <MessageCircle className="w-4 h-4" />
                  {action === 'approve' ? 'تعليق الموافقة' : 'سبب الرفض'}
                  {action === 'reject' && <span className="text-red-500">*</span>}
                </span>
                
                <textarea
                  value={comment}
                  onChange={(e) => {
                    setComment(e.target.value)
                    if (error) setError('')
                  }}
                  placeholder={config.placeholder}
                  rows={4}
                  disabled={isLoading}
                  className={`
                    mt-2 w-full px-3 py-2 border rounded-lg resize-none
                    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                    disabled:bg-gray-100 disabled:cursor-not-allowed
                    ${error ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'}
                  `}
                />
              </label>

              {/* Character Count */}
              <div className="flex justify-between items-center text-xs text-gray-500">
                <span>
                  {action === 'reject' ? 'مطلوب 10 أحرف على الأقل' : 'اختياري'}
                </span>
                <span className={comment.length < 10 && action === 'reject' ? 'text-red-500' : ''}>
                  {comment.length} حرف
                </span>
              </div>

              {/* Error Message */}
              {error && (
                <div className="flex items-center gap-2 text-red-600 text-sm">
                  <AlertCircle className="w-4 h-4" />
                  {error}
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="px-6 py-4 bg-gray-50 border-t flex gap-3 justify-end">
            <Button
              variant="ghost"
              onClick={handleClose}
              disabled={isLoading}
            >
              إلغاء
            </Button>
            
            <Button
              variant={config.buttonVariant}
              onClick={handleSubmit}
              disabled={isLoading}
              icon={isLoading ? undefined : (
                action === 'approve' ? <CheckCircle className="w-4 h-4" /> : <XCircle className="w-4 h-4" />
              )}
            >
              {isLoading ? 'جاري المعالجة...' : config.buttonText}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

// Hook لإدارة حالة modal التعليقات
export function useCommentModal() {
  const [isOpen, setIsOpen] = useState(false)
  const [modalConfig, setModalConfig] = useState<{
    title: string
    requestTitle: string
    requestId: string
    action: 'approve' | 'reject'
    onSubmit: (comment: string, action: 'approve' | 'reject') => void
  } | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  const openModal = (config: {
    title: string
    requestTitle: string
    requestId: string
    action: 'approve' | 'reject'
    onSubmit: (comment: string, action: 'approve' | 'reject') => void
  }) => {
    setModalConfig(config)
    setIsOpen(true)
  }

  const closeModal = () => {
    if (!isLoading) {
      setIsOpen(false)
      setModalConfig(null)
    }
  }

  const handleSubmit = async (comment: string, action: 'approve' | 'reject') => {
    if (!modalConfig) return

    setIsLoading(true)
    try {
      await modalConfig.onSubmit(comment, action)
      closeModal()
    } catch (error) {
      console.error('Error submitting comment:', error)
      // يمكن إضافة معالجة أخطاء هنا
    } finally {
      setIsLoading(false)
    }
  }

  return {
    isOpen,
    modalConfig,
    isLoading,
    openModal,
    closeModal,
    handleSubmit,
    
    // مساعدات سريعة
    openApprovalModal: (requestTitle: string, requestId: string, onSubmit: (comment: string) => void) => {
      openModal({
        title: 'موافقة على الطلب',
        requestTitle,
        requestId,
        action: 'approve',
        onSubmit: (comment) => onSubmit(comment)
      })
    },
    
    openRejectionModal: (requestTitle: string, requestId: string, onSubmit: (comment: string) => void) => {
      openModal({
        title: 'رفض الطلب',
        requestTitle,
        requestId,
        action: 'reject',
        onSubmit: (comment) => onSubmit(comment)
      })
    }
  }
} 