'use client'

import { useState } from 'react'
import { useAuth } from '@/lib/auth'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/Button'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { CheckCircle, XCircle, Play, FileText, Zap, Target } from 'lucide-react'

interface RequestTestData {
  title: string
  description: string
  main_type: 'general_project' | 'improvement_project'
  sub_type: 'quick_win' | 'improvement_full' | 'suggestion'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  estimated_budget: number
  expected_start_date: string
  expected_end_date: string
  form_data: Record<string, any>
}

interface TestResult {
  type: string
  success: boolean
  message: string
  data?: any
  error?: string
}

export default function ProjectRequestTester() {
  const { user, userDepartment } = useAuth()
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [testData, setTestData] = useState<RequestTestData>({
    title: 'طلب اختبار النظام',
    description: 'هذا طلب تجريبي لاختبار عمل النظام',
    main_type: 'improvement_project',
    sub_type: 'quick_win',
    priority: 'medium',
    estimated_budget: 5000,
    expected_start_date: new Date().toISOString().split('T')[0],
    expected_end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    form_data: { test: true, created_by: 'system_test' }
  })

  const addResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result])
  }

  const clearResults = () => {
    setTestResults([])
  }

  const testQuickWin = async () => {
    try {
      if (!user?.id || !userDepartment?.id) {
        throw new Error('معرف المستخدم أو القسم غير متوفر')
      }

      const requestData = {
        ...testData,
        title: 'اختبار كويك وين',
        sub_type: 'quick_win' as const,
        requester_id: user.id,
        department_id: userDepartment.id,
        form_data: {
          ...testData.form_data,
          problem_description: 'مشكلة تحتاج حل سريع',
          proposed_solution: 'حل مقترح للمشكلة',
          expected_benefits: 'فوائد متوقعة من الحل',
          implementation_steps: ['خطوة 1', 'خطوة 2', 'خطوة 3']
        }
      }

      const { data, error } = await supabase
        .from('project_requests')
        .insert(requestData)
        .select()

      if (error) throw error

      addResult({
        type: 'كويك وين',
        success: true,
        message: 'تم إنشاء طلب كويك وين بنجاح',
        data: data[0]
      })
    } catch (error) {
      addResult({
        type: 'كويك وين',
        success: false,
        message: 'فشل في إنشاء طلب كويك وين',
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      })
    }
  }

  const testSuggestion = async () => {
    try {
      if (!user?.id || !userDepartment?.id) {
        throw new Error('معرف المستخدم أو القسم غير متوفر')
      }

      const requestData = {
        ...testData,
        title: 'اختبار مقترح تحسين',
        sub_type: 'suggestion' as const,
        requester_id: user.id,
        department_id: userDepartment.id,
        form_data: {
          ...testData.form_data,
          current_situation: 'الوضع الحالي',
          problem_analysis: 'تحليل المشكلة',
          proposed_improvements: 'التحسينات المقترحة',
          expected_impact: 'التأثير المتوقع',
          implementation_plan: 'خطة التنفيذ'
        }
      }

      const { data, error } = await supabase
        .from('project_requests')
        .insert(requestData)
        .select()

      if (error) throw error

      addResult({
        type: 'مقترح تحسين',
        success: true,
        message: 'تم إنشاء مقترح التحسين بنجاح',
        data: data[0]
      })
    } catch (error) {
      addResult({
        type: 'مقترح تحسين',
        success: false,
        message: 'فشل في إنشاء مقترح التحسين',
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      })
    }
  }

  const testFullProject = async () => {
    try {
      if (!user?.id || !userDepartment?.id) {
        throw new Error('معرف المستخدم أو القسم غير متوفر')
      }

      const requestData = {
        ...testData,
        title: 'اختبار طلب مشروع شامل',
        sub_type: 'improvement_full' as const,
        requester_id: user.id,
        department_id: userDepartment.id,
        form_data: {
          ...testData.form_data,
          project_scope: 'نطاق المشروع',
          objectives: 'أهداف المشروع',
          deliverables: 'مخرجات المشروع',
          timeline: 'الجدول الزمني',
          resources_needed: 'الموارد المطلوبة',
          risk_assessment: 'تقييم المخاطر',
          success_criteria: 'معايير النجاح'
        }
      }

      const { data, error } = await supabase
        .from('project_requests')
        .insert(requestData)
        .select()

      if (error) throw error

      addResult({
        type: 'طلب مشروع شامل',
        success: true,
        message: 'تم إنشاء طلب المشروع الشامل بنجاح',
        data: data[0]
      })
    } catch (error) {
      addResult({
        type: 'طلب مشروع شامل',
        success: false,
        message: 'فشل في إنشاء طلب المشروع الشامل',
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      })
    }
  }

  const runAllTests = async () => {
    setIsRunning(true)
    clearResults()

    await testQuickWin()
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    await testSuggestion()
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    await testFullProject()
    
    setIsRunning(false)
  }

  const deleteTestRequests = async () => {
    try {
      const { error } = await supabase
        .from('project_requests')
        .delete()
        .eq('form_data->test', true)

      if (error) throw error

      addResult({
        type: 'تنظيف',
        success: true,
        message: 'تم حذف جميع الطلبات التجريبية بنجاح',
        data: null
      })
    } catch (error) {
      addResult({
        type: 'تنظيف',
        success: false,
        message: 'فشل في حذف الطلبات التجريبية',
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      })
    }
  }

  const getIcon = (type: string) => {
    switch (type) {
      case 'كويك وين': return <Zap className="w-5 h-5" />
      case 'مقترح تحسين': return <Target className="w-5 h-5" />
      case 'طلب مشروع شامل': return <FileText className="w-5 h-5" />
      default: return <FileText className="w-5 h-5" />
    }
  }

  const successCount = testResults.filter(r => r.success).length
  const totalCount = testResults.length

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">اختبار نماذج طلبات المشاريع</h2>
          <p className="text-gray-600">
            اختبر إنشاء الأنواع المختلفة من طلبات المشاريع
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* بيانات الاختبار */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  عنوان الطلب
                </label>
                <Input
                  value={testData.title}
                  onChange={(e) => setTestData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="عنوان الطلب"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الميزانية المتوقعة
                </label>
                <Input
                  type="number"
                  value={testData.estimated_budget}
                  onChange={(e) => setTestData(prev => ({ ...prev, estimated_budget: Number(e.target.value) }))}
                  placeholder="الميزانية"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                وصف الطلب
              </label>
              <textarea
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows={3}
                value={testData.description}
                onChange={(e) => setTestData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="وصف الطلب"
              />
            </div>

            {/* أزرار الاختبار */}
            <div className="flex flex-wrap gap-3">
              <Button
                onClick={runAllTests}
                disabled={isRunning}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Play className="w-4 h-4 mr-2" />
                {isRunning ? 'جاري الاختبار...' : 'اختبار جميع الأنواع'}
              </Button>
              
              <Button
                onClick={testQuickWin}
                disabled={isRunning}
                variant="secondary"
                className="border-green-300 text-green-700 hover:bg-green-50"
              >
                <Zap className="w-4 h-4 mr-2" />
                كويك وين
              </Button>
              
              <Button
                onClick={testSuggestion}
                disabled={isRunning}
                variant="secondary"
                className="border-blue-300 text-blue-700 hover:bg-blue-50"
              >
                <Target className="w-4 h-4 mr-2" />
                مقترح تحسين
              </Button>
              
              <Button
                onClick={testFullProject}
                disabled={isRunning}
                variant="secondary"
                className="border-purple-300 text-purple-700 hover:bg-purple-50"
              >
                <FileText className="w-4 h-4 mr-2" />
                مشروع شامل
              </Button>
              
              <Button
                onClick={deleteTestRequests}
                disabled={isRunning}
                variant="danger"
                className="border-red-300 text-red-700 hover:bg-red-50"
              >
                تنظيف الطلبات التجريبية
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* النتائج */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">نتائج الاختبار</h3>
              <div className="text-sm text-gray-600">
                {successCount}/{totalCount} نجح
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border-2 ${
                    result.success
                      ? 'bg-green-50 border-green-200'
                      : 'bg-red-50 border-red-200'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getIcon(result.type)}
                      <div>
                        <h4 className="font-medium">{result.type}</h4>
                        <p className="text-sm text-gray-600">{result.message}</p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      {result.success ? (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      ) : (
                        <XCircle className="w-5 h-5 text-red-500" />
                      )}
                    </div>
                  </div>
                  
                  {result.error && (
                    <div className="mt-2 p-2 bg-red-100 rounded text-sm text-red-700">
                      خطأ: {result.error}
                    </div>
                  )}
                  
                  {result.data && (
                    <div className="mt-2">
                      <details className="cursor-pointer">
                        <summary className="text-sm font-medium text-gray-700">
                          عرض البيانات
                        </summary>
                        <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </details>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* معلومات المستخدم */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">معلومات المستخدم الحالي</h3>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">معرف المستخدم:</span>
              <span className="mr-2">{user?.id || 'غير محدد'}</span>
            </div>
            <div>
              <span className="font-medium">البريد الإلكتروني:</span>
              <span className="mr-2">{user?.email || 'غير محدد'}</span>
            </div>
            <div>
              <span className="font-medium">القسم:</span>
              <span className="mr-2">{userDepartment?.name || 'غير محدد'}</span>
            </div>
            <div>
              <span className="font-medium">معرف القسم:</span>
              <span className="mr-2">{userDepartment?.id || 'غير محدد'}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 