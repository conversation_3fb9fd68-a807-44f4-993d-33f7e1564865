'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { 
  SolutionEvaluation,
  StakeholderInfo,
  SolutionSelectionForm,
  StakeholderConsensus
} from '@/types/feedback.types';

interface SolutionSelectionProps {
  suggestedSolutions: Array<{
    id: string;
    title: string;
    description: string;
    feasibilityScore: number;
    expectedBenefits: string[];
    implementationSteps: string[];
  }>;
  solutionEvaluations: SolutionEvaluation[];
  stakeholders: StakeholderInfo[];
  currentUser: {
    id: string;
    name: string;
    role: string;
  };
  onSelectionSubmit: (selection: SolutionSelectionForm) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

export default function SolutionSelection({
  suggestedSolutions,
  solutionEvaluations,
  stakeholders,
  currentUser,
  onSelectionSubmit,
  onCancel,
  isSubmitting = false
}: SolutionSelectionProps) {
  const [selectedSolutionId, setSelectedSolutionId] = useState<string>('');
  const [selectionRationale, setSelectionRationale] = useState<string>('');
  const [stakeholderApprovals, setStakeholderApprovals] = useState<Array<{
    stakeholderId: string;
    approval: StakeholderConsensus['approval'];
    conditions?: string;
  }>>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // تهيئة موافقات أصحاب المصلحة
  React.useEffect(() => {
    if (stakeholderApprovals.length === 0) {
      const initialApprovals = stakeholders.map(stakeholder => ({
        stakeholderId: stakeholder.id,
        approval: 'neutral' as StakeholderConsensus['approval'],
        conditions: ''
      }));
      setStakeholderApprovals(initialApprovals);
    }
  }, [stakeholders, stakeholderApprovals.length]);

  // ترجمة أنواع الموافقة
  const approvalLabels = {
    'approved': 'موافق',
    'approved_with_conditions': 'موافق بشروط',
    'neutral': 'محايد',
    'rejected': 'مرفوض'
  };

  const approvalColors = {
    'approved': 'bg-green-100 text-green-800 border-green-300',
    'approved_with_conditions': 'bg-yellow-100 text-yellow-800 border-yellow-300',
    'neutral': 'bg-gray-100 text-gray-800 border-gray-300',
    'rejected': 'bg-red-100 text-red-800 border-red-300'
  };

  // التحقق من صحة البيانات
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!selectedSolutionId) {
      newErrors.selectedSolution = 'يجب اختيار حل من الحلول المقترحة';
    }

    if (!selectionRationale.trim()) {
      newErrors.rationale = 'يجب تقديم مبرر لاختيار هذا الحل';
    } else if (selectionRationale.trim().length < 50) {
      newErrors.rationale = 'مبرر الاختيار يجب أن يكون 50 حرف على الأقل';
    }

    // التحقق من وجود موافقة من معظم أصحاب المصلحة
    const approvedCount = stakeholderApprovals.filter(a => 
      a.approval === 'approved' || a.approval === 'approved_with_conditions'
    ).length;
    
    if (approvedCount < Math.ceil(stakeholders.length / 2)) {
      newErrors.consensus = 'يجب الحصول على موافقة أغلبية أصحاب المصلحة';
    }

    // التحقق من الشروط للموافقات المشروطة
    stakeholderApprovals.forEach((approval, index) => {
      if (approval.approval === 'approved_with_conditions' && !approval.conditions?.trim()) {
        newErrors[`conditions_${index}`] = 'يجب تحديد الشروط للموافقة المشروطة';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // إرسال النموذج
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      const selectionData: SolutionSelectionForm = {
        selectedSolutionId,
        selectionRationale,
        stakeholderApprovals
      };
      
      await onSelectionSubmit(selectionData);
    } catch (error) {
      console.error('خطأ في إرسال اختيار الحل:', error);
      setErrors({ submit: 'حدث خطأ في إرسال البيانات. يرجى المحاولة مرة أخرى.' });
    }
  };

  // تحديث موافقة صاحب مصلحة
  const updateStakeholderApproval = (
    stakeholderId: string, 
    approval: StakeholderConsensus['approval'],
    conditions?: string
  ) => {
    setStakeholderApprovals(prev => 
      prev.map(item => 
        item.stakeholderId === stakeholderId 
          ? { ...item, approval, conditions }
          : item
      )
    );
  };

  // الحصول على الحل المختار
  const selectedSolution = suggestedSolutions.find(s => s.id === selectedSolutionId);
  const selectedEvaluation = solutionEvaluations.find(e => e.solutionId === selectedSolutionId);

  return (
    <div className="max-w-4xl mx-auto p-6">
      <Card className="p-6">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            اختيار الحل الأمثل
          </h2>
          <p className="text-gray-600">
            بناءً على الملاحظات المجمعة من أصحاب المصلحة، قم باختيار الحل الأمثل للمتابعة
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* اختيار الحل */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-4">
              اختر الحل الأمثل *
            </label>
            <div className="space-y-4">
              {suggestedSolutions.map((solution) => {
                const evaluation = solutionEvaluations.find(e => e.solutionId === solution.id);
                const isSelected = selectedSolutionId === solution.id;
                
                return (
                  <div
                    key={solution.id}
                    className={`relative cursor-pointer rounded-lg border-2 p-6 transition-all ${
                      isSelected
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedSolutionId(solution.id)}
                  >
                    <div className="flex items-start">
                      <input
                        type="radio"
                        name="selectedSolution"
                        value={solution.id}
                        checked={isSelected}
                        onChange={() => setSelectedSolutionId(solution.id)}
                        className="mt-1 h-4 w-4 text-blue-600"
                      />
                      <div className="ml-4 flex-1">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {solution.title}
                          </h3>
                          <div className="flex items-center space-x-4 space-x-reverse">
                            <span className="text-sm font-medium text-blue-600">
                              {solution.feasibilityScore}% جدوى
                            </span>
                            {evaluation && (
                              <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                                evaluation.recommendationLevel === 'highly_recommended' 
                                  ? 'bg-green-100 text-green-800'
                                  : evaluation.recommendationLevel === 'recommended'
                                  ? 'bg-blue-100 text-blue-800'
                                  : evaluation.recommendationLevel === 'not_recommended'
                                  ? 'bg-red-100 text-red-800'
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {evaluation.recommendationLevel === 'highly_recommended' && 'موصى به بشدة'}
                                {evaluation.recommendationLevel === 'recommended' && 'موصى به'}
                                {evaluation.recommendationLevel === 'neutral' && 'محايد'}
                                {evaluation.recommendationLevel === 'not_recommended' && 'غير موصى به'}
                              </span>
                            )}
                          </div>
                        </div>
                        
                        <p className="text-gray-600 text-sm mb-3">
                          {solution.description}
                        </p>
                        
                        {evaluation && (
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="font-medium">عدد الملاحظات: </span>
                              <span className="text-gray-600">{evaluation.feedbacks.length}</span>
                            </div>
                            <div>
                              <span className="font-medium">مستوى الإجماع: </span>
                              <span className={`font-medium ${
                                evaluation.consensusLevel === 'high' ? 'text-green-600' :
                                evaluation.consensusLevel === 'medium' ? 'text-yellow-600' :
                                'text-red-600'
                              }`}>
                                {evaluation.consensusLevel === 'high' && 'عالي'}
                                {evaluation.consensusLevel === 'medium' && 'متوسط'}
                                {evaluation.consensusLevel === 'low' && 'منخفض'}
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            {errors.selectedSolution && (
              <p className="mt-2 text-sm text-red-600">{errors.selectedSolution}</p>
            )}
          </div>

          {/* مبرر الاختيار */}
          <div>
            <label htmlFor="rationale" className="block text-sm font-medium text-gray-700 mb-2">
              مبرر اختيار هذا الحل *
            </label>
            <textarea
              id="rationale"
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              placeholder="اشرح لماذا تم اختيار هذا الحل كأفضل خيار بناءً على الملاحظات والتقييمات..."
              value={selectionRationale}
              onChange={(e) => setSelectionRationale(e.target.value)}
              maxLength={2000}
            />
            <div className="flex justify-between items-center mt-1">
              {errors.rationale ? (
                <p className="text-sm text-red-600">{errors.rationale}</p>
              ) : (
                <p className="text-sm text-gray-500">
                  اشرح الأسباب والمبررات لاختيار هذا الحل (50-2000 حرف)
                </p>
              )}
              <span className="text-sm text-gray-400">
                {selectionRationale.length}/2000
              </span>
            </div>
          </div>

          {/* موافقات أصحاب المصلحة */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              موافقات أصحاب المصلحة
            </h3>
            <div className="space-y-4">
              {stakeholders.map((stakeholder, index) => {
                const approval = stakeholderApprovals.find(a => a.stakeholderId === stakeholder.id);
                
                return (
                  <Card key={stakeholder.id} className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-medium text-gray-900">{stakeholder.name}</h4>
                        <p className="text-sm text-gray-600">{stakeholder.position} - {stakeholder.department}</p>
                      </div>
                    </div>
                    
                    {/* خيارات الموافقة */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-3">
                      {(Object.keys(approvalLabels) as Array<keyof typeof approvalLabels>).map((approvalType) => (
                        <div
                          key={approvalType}
                          className={`cursor-pointer rounded-lg border-2 p-3 text-center transition-all ${
                            approval?.approval === approvalType
                              ? approvalColors[approvalType] + ' border-current'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => updateStakeholderApproval(stakeholder.id, approvalType)}
                        >
                          <input
                            type="radio"
                            name={`approval_${stakeholder.id}`}
                            value={approvalType}
                            checked={approval?.approval === approvalType}
                            onChange={() => updateStakeholderApproval(stakeholder.id, approvalType)}
                            className="sr-only"
                          />
                          <div className="text-sm font-medium">
                            {approvalLabels[approvalType]}
                          </div>
                        </div>
                      ))}
                    </div>
                    
                    {/* شروط الموافقة المشروطة */}
                    {approval?.approval === 'approved_with_conditions' && (
                      <div className="mt-3">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          الشروط المطلوبة *
                        </label>
                        <textarea
                          rows={3}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm"
                          placeholder="حدد الشروط المطلوبة للموافقة..."
                          value={approval.conditions || ''}
                          onChange={(e) => updateStakeholderApproval(
                            stakeholder.id, 
                            'approved_with_conditions', 
                            e.target.value
                          )}
                        />
                        {errors[`conditions_${index}`] && (
                          <p className="mt-1 text-sm text-red-600">
                            {errors[`conditions_${index}`]}
                          </p>
                        )}
                      </div>
                    )}
                  </Card>
                );
              })}
            </div>
            {errors.consensus && (
              <p className="mt-2 text-sm text-red-600">{errors.consensus}</p>
            )}
          </div>

          {/* ملخص الموافقات */}
          <Card className="p-4 bg-gray-50">
            <h4 className="font-semibold text-gray-800 mb-3">ملخص الموافقات</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className="text-lg font-bold text-green-600">
                  {stakeholderApprovals.filter(a => a.approval === 'approved').length}
                </div>
                <div className="text-gray-600">موافق</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-yellow-600">
                  {stakeholderApprovals.filter(a => a.approval === 'approved_with_conditions').length}
                </div>
                <div className="text-gray-600">موافق بشروط</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-gray-600">
                  {stakeholderApprovals.filter(a => a.approval === 'neutral').length}
                </div>
                <div className="text-gray-600">محايد</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-red-600">
                  {stakeholderApprovals.filter(a => a.approval === 'rejected').length}
                </div>
                <div className="text-gray-600">مرفوض</div>
              </div>
            </div>
          </Card>

          {/* رسالة خطأ عامة */}
          {errors.submit && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">{errors.submit}</p>
            </div>
          )}

          {/* أزرار الإجراءات */}
          <div className="flex justify-end space-x-3 space-x-reverse pt-6 border-t border-gray-200">
            <Button
              type="button"
              variant="ghost"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'جاري الحفظ...' : 'تأكيد اختيار الحل'}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
} 