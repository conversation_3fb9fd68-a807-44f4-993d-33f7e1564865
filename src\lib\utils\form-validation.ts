/**
 * نظام إدارة الأخطاء والتحقق من صحة النماذج الموحد
 * يحتوي على فئات وأدوات للتحقق من البيانات وإدارة الأخطاء
 */

// ==================== TYPES & INTERFACES ====================

export interface FormError {
  field: string
  message: string
  type: 'required' | 'validation' | 'format' | 'custom'
}

export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  min?: number
  max?: number
  custom?: (value: any) => string | null
}

// ==================== FORM ERROR HANDLER CLASS ====================

export class FormErrorHandler {
  private errors: Record<string, FormError> = {}

  // إضافة خطأ
  addError(field: string, message: string, type: FormError['type'] = 'validation'): void {
    this.errors[field] = { field, message, type }
  }

  // إزالة خطأ
  removeError(field: string): void {
    delete this.errors[field]
  }

  // الحصول على خطأ محدد
  getError(field: string): FormError | null {
    return this.errors[field] || null
  }

  // الحصول على جميع الأخطاء
  getAllErrors(): Record<string, FormError> {
    return { ...this.errors }
  }

  // الحصول على رسائل الأخطاء فقط
  getErrorMessages(): Record<string, string> {
    const messages: Record<string, string> = {}
    Object.keys(this.errors).forEach(field => {
      messages[field] = this.errors[field].message
    })
    return messages
  }

  // التحقق من وجود أخطاء
  hasErrors(): boolean {
    return Object.keys(this.errors).length > 0
  }

  // مسح جميع الأخطاء
  clearErrors(): void {
    this.errors = {}
  }

  // التحقق من حقل واحد
  validateField(field: string, value: any, rules: ValidationRule): boolean {
    this.removeError(field)

    // التحقق من الحقول المطلوبة
    if (rules.required && (!value || (typeof value === 'string' && !value.trim()))) {
      this.addError(field, 'هذا الحقل مطلوب', 'required')
      return false
    }

    // إذا كان الحقل فارغ وغير مطلوب، تجاهل باقي القواعد
    if (!value && !rules.required) {
      return true
    }

    // التحقق من الطول الأدنى
    if (rules.minLength && typeof value === 'string' && value.length < rules.minLength) {
      this.addError(field, `يجب أن يكون النص ${rules.minLength} أحرف على الأقل`, 'validation')
      return false
    }

    // التحقق من الطول الأقصى
    if (rules.maxLength && typeof value === 'string' && value.length > rules.maxLength) {
      this.addError(field, `يجب أن لا يتجاوز النص ${rules.maxLength} حرف`, 'validation')
      return false
    }

    // التحقق من القيمة الدنيا
    if (rules.min !== undefined && typeof value === 'number' && value < rules.min) {
      this.addError(field, `القيمة يجب أن تكون ${rules.min} أو أكثر`, 'validation')
      return false
    }

    // التحقق من القيمة العليا
    if (rules.max !== undefined && typeof value === 'number' && value > rules.max) {
      this.addError(field, `القيمة يجب أن تكون ${rules.max} أو أقل`, 'validation')
      return false
    }

    // التحقق من النمط
    if (rules.pattern && typeof value === 'string' && !rules.pattern.test(value)) {
      this.addError(field, 'تنسيق البيانات غير صحيح', 'format')
      return false
    }

    // التحقق المخصص
    if (rules.custom) {
      const customError = rules.custom(value)
      if (customError) {
        this.addError(field, customError, 'custom')
        return false
      }
    }

    return true
  }

  // التحقق من عدة حقول
  validateFields(data: Record<string, any>, rules: Record<string, ValidationRule>): boolean {
    let isValid = true
    
    Object.keys(rules).forEach(field => {
      const fieldValid = this.validateField(field, data[field], rules[field])
      if (!fieldValid) {
        isValid = false
      }
    })

    return isValid
  }
}

// ==================== VALIDATION RULES ====================

// قواعد التحقق الشائعة
export const commonValidationRules = {
  // حقول النص
  projectTitle: {
    required: true,
    minLength: 5,
    maxLength: 100
  },
  
  projectDescription: {
    required: true,
    minLength: 10,
    maxLength: 500
  },

  // الأرقام
  currentValue: {
    required: true,
    min: 0
  },

  targetValue: {
    required: true,
    min: 0
  },

  // الهاتف
  phone: {
    pattern: /^05\d{8}$/,
    custom: (value: string) => {
      if (value && !value.startsWith('05')) {
        return 'رقم الجوال يجب أن يبدأ بـ 05'
      }
      if (value && value.length !== 10) {
        return 'رقم الجوال يجب أن يكون 10 أرقام'
      }
      return null
    }
  },

  // البريد الإلكتروني
  email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    custom: (value: string) => {
      if (value && !value.includes('@')) {
        return 'البريد الإلكتروني غير صحيح'
      }
      return null
    }
  },

  // التواريخ
  date: {
    required: true,
    custom: (value: string) => {
      if (value && new Date(value) < new Date()) {
        return 'التاريخ يجب أن يكون في المستقبل'
      }
      return null
    }
  },

  // كلمة المرور
  password: {
    required: true,
    minLength: 8,
    custom: (value: string) => {
      if (value && !/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
        return 'كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم'
      }
      return null
    }
  },

  // تأكيد كلمة المرور
  confirmPassword: {
    required: true,
    custom: (value: string, data?: any) => {
      if (value && data?.password && value !== data.password) {
        return 'كلمة المرور غير متطابقة'
      }
      return null
    }
  }
}

// ==================== VALIDATION UTILITIES ====================

// دوال مساعدة للتحقق
export const ValidationUtils = {
  // التحقق من البريد الإلكتروني
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  // التحقق من رقم الجوال السعودي
  isValidSaudiPhone: (phone: string): boolean => {
    const phoneRegex = /^05\d{8}$/
    return phoneRegex.test(phone)
  },

  // التحقق من قوة كلمة المرور
  getPasswordStrength: (password: string): 'weak' | 'medium' | 'strong' => {
    if (password.length < 6) return 'weak'
    
    let score = 0
    if (password.length >= 8) score++
    if (/[a-z]/.test(password)) score++
    if (/[A-Z]/.test(password)) score++
    if (/\d/.test(password)) score++
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++
    
    if (score < 3) return 'weak'
    if (score < 5) return 'medium'
    return 'strong'
  },

  // تنظيف النص
  sanitizeText: (text: string): string => {
    return text.trim().replace(/\s+/g, ' ')
  },

  // التحقق من التاريخ
  isValidDate: (dateString: string): boolean => {
    const date = new Date(dateString)
    return !isNaN(date.getTime())
  },

  // التحقق من أن التاريخ في المستقبل
  isFutureDate: (dateString: string): boolean => {
    const date = new Date(dateString)
    const now = new Date()
    return date > now
  }
}

// ==================== HOOKS ====================

// Hook للاستخدام في المكونات
export function useFormErrorHandler() {
  const errorHandler = new FormErrorHandler()
  
  return {
    addError: errorHandler.addError.bind(errorHandler),
    removeError: errorHandler.removeError.bind(errorHandler),
    getError: errorHandler.getError.bind(errorHandler),
    getAllErrors: errorHandler.getAllErrors.bind(errorHandler),
    getErrorMessages: errorHandler.getErrorMessages.bind(errorHandler),
    hasErrors: errorHandler.hasErrors.bind(errorHandler),
    clearErrors: errorHandler.clearErrors.bind(errorHandler),
    validateField: errorHandler.validateField.bind(errorHandler),
    validateFields: errorHandler.validateFields.bind(errorHandler)
  }
}

// Hook للتحقق السريع
export function useValidation() {
  return {
    isValidEmail: ValidationUtils.isValidEmail,
    isValidSaudiPhone: ValidationUtils.isValidSaudiPhone,
    getPasswordStrength: ValidationUtils.getPasswordStrength,
    sanitizeText: ValidationUtils.sanitizeText,
    isValidDate: ValidationUtils.isValidDate,
    isFutureDate: ValidationUtils.isFutureDate,
    commonRules: commonValidationRules
  }
}
