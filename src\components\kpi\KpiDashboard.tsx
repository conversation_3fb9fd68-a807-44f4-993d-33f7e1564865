'use client'

import React, { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { KpiProgress } from './KpiProgress'
import { KpiMeasurementForm } from './KpiMeasurementForm'
import { 
  KpiProgress as KpiProgressType,
  KpiMeasurementFormData,
  KpiStatistics
} from '@/types/kpi.types'
import {
  Target,
  TrendingUp,
  TrendingDown,
  Activity,
  Plus,
  Filter,
  Download,
  Calendar,
  BarChart3
} from 'lucide-react'

interface KpiDashboardProps {
  projectId: string
  projectName: string
  canEdit?: boolean
}

export function KpiDashboard({ 
  projectId, 
  projectName, 
  canEdit = false 
}: KpiDashboardProps) {
  const [kpiProgressList, setKpiProgressList] = useState<KpiProgressType[]>([])
  const [statistics, setStatistics] = useState<KpiStatistics | null>(null)
  const [selectedKpi, setSelectedKpi] = useState<string | null>(null)
  const [showMeasurementForm, setShowMeasurementForm] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'on_track' | 'at_risk' | 'behind'>('all')

  useEffect(() => {
    loadKpiData()
  }, [projectId])

  const loadKpiData = async () => {
    try {
      setIsLoading(true)
      
      // TODO: استدعاء API لجلب بيانات المؤشرات
      const mockProgress: KpiProgressType[] = [
        {
          kpi_id: '1',
          kpi_name: 'وقت انتظار المريض في الطوارئ',
          current_value: 32,
          target_value: 20,
          baseline_value: 45,
          unit: 'دقيقة',
          improvement_percentage: 28.9,
          target_percentage: 62.5,
          trend: 'improving',
          status: 'on_track',
          last_updated: '2024-01-15T10:30:00Z'
        },
        {
          kpi_id: '2',
          kpi_name: 'عدد المرضى المعالجين يومياً',
          current_value: 135,
          target_value: 150,
          baseline_value: 120,
          unit: 'مريض',
          improvement_percentage: 12.5,
          target_percentage: 90,
          trend: 'improving',
          status: 'at_risk',
          last_updated: '2024-01-15T09:15:00Z'
        },
        {
          kpi_id: '3',
          kpi_name: 'نسبة رضا المرضى',
          current_value: 78,
          target_value: 85,
          baseline_value: 70,
          unit: '%',
          improvement_percentage: 11.4,
          target_percentage: 91.8,
          trend: 'stable',
          status: 'behind',
          last_updated: '2024-01-14T16:45:00Z'
        }
      ]
      
      const mockStats: KpiStatistics = {
        total_kpis: 3,
        on_track: 1,
        at_risk: 1,
        behind: 1,
        avg_improvement: 17.6,
        avg_target_progress: 81.4
      }
      
      setKpiProgressList(mockProgress)
      setStatistics(mockStats)
    } catch (error) {
      console.error('Error loading KPI data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleMeasurementSubmit = async (data: KpiMeasurementFormData) => {
    try {
      // TODO: استدعاء API لحفظ القياس الجديد
      console.log('Submitting measurement:', data)
      
      // تحديث البيانات
      await loadKpiData()
      setShowMeasurementForm(false)
      setSelectedKpi(null)
    } catch (error) {
      console.error('Error submitting measurement:', error)
      throw error
    }
  }

  const filteredKpis = kpiProgressList.filter(kpi => {
    if (filter === 'all') return true
    return kpi.status === filter
  })

  const getStatusStats = () => {
    if (!statistics) return null
    
    return [
      {
        label: 'على المسار',
        value: statistics.on_track,
        color: 'text-green-600',
        bgColor: 'bg-green-100'
      },
      {
        label: 'في خطر',
        value: statistics.at_risk,
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-100'
      },
      {
        label: 'متأخر',
        value: statistics.behind,
        color: 'text-red-600',
        bgColor: 'bg-red-100'
      }
    ]
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* العنوان والإحصائيات */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Target className="w-6 h-6 text-blue-600" />
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              مؤشرات الأداء
            </h2>
            <p className="text-gray-600">{projectName}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            className="flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            تصدير التقرير
          </Button>
          
          {canEdit && (
            <Button
              variant="primary"
              size="sm"
              onClick={() => setShowMeasurementForm(true)}
              className="flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              تسجيل قياس جديد
            </Button>
          )}
        </div>
      </div>

      {/* الإحصائيات العامة */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {statistics.total_kpis}
                </div>
                <div className="text-sm text-gray-600">إجمالي المؤشرات</div>
              </div>
            </div>
          </Card>

          {getStatusStats()?.map((stat, index) => (
            <Card key={index} className="p-4">
              <div className="flex items-center gap-3">
                <div className={`w-10 h-10 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                  <Activity className={`w-5 h-5 ${stat.color}`} />
                </div>
                <div>
                  <div className={`text-2xl font-bold ${stat.color}`}>
                    {stat.value}
                  </div>
                  <div className="text-sm text-gray-600">{stat.label}</div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* إحصائيات التحسن */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="p-4">
            <div className="flex items-center gap-3">
              <TrendingUp className="w-5 h-5 text-green-600" />
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  متوسط التحسن
                </div>
                <div className="text-2xl font-bold text-green-600">
                  +{statistics.avg_improvement.toFixed(1)}%
                </div>
                <div className="text-sm text-gray-600">
                  من القيمة الأساسية
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center gap-3">
              <Target className="w-5 h-5 text-blue-600" />
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  متوسط تقدم الأهداف
                </div>
                <div className="text-2xl font-bold text-blue-600">
                  {statistics.avg_target_progress.toFixed(1)}%
                </div>
                <div className="text-sm text-gray-600">
                  من الهدف المطلوب
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* فلترة المؤشرات */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-gray-600" />
          <span className="text-sm text-gray-600">فلترة حسب الحالة:</span>
        </div>
        
        <div className="flex gap-2">
          {[
            { key: 'all', label: 'الكل' },
            { key: 'on_track', label: 'على المسار' },
            { key: 'at_risk', label: 'في خطر' },
            { key: 'behind', label: 'متأخر' }
          ].map(option => (
            <Button
              key={option.key}
              variant={filter === option.key ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setFilter(option.key as any)}
            >
              {option.label}
            </Button>
          ))}
        </div>
      </div>

      {/* قائمة المؤشرات */}
      <div className="space-y-4">
        {filteredKpis.length === 0 ? (
          <Card className="p-8 text-center">
            <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              لا توجد مؤشرات
            </h3>
            <p className="text-gray-600">
              {filter === 'all' 
                ? 'لم يتم إضافة أي مؤشرات لهذا المشروع بعد'
                : 'لا توجد مؤشرات تطابق الفلتر المحدد'}
            </p>
          </Card>
        ) : (
          filteredKpis.map(kpi => (
            <div key={kpi.kpi_id}>
              <KpiProgress
                kpiProgress={kpi}
                showDetails={false}
                compact={false}
              />
              
              {canEdit && (
                <div className="mt-3 flex justify-end">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSelectedKpi(kpi.kpi_id)
                      setShowMeasurementForm(true)
                    }}
                    className="flex items-center gap-2"
                  >
                    <Plus className="w-4 h-4" />
                    تسجيل قياس جديد
                  </Button>
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* نموذج تسجيل القياس */}
      {showMeasurementForm && selectedKpi && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {(() => {
                const kpi = kpiProgressList.find(k => k.kpi_id === selectedKpi)
                if (!kpi) return null
                
                return (
                  <KpiMeasurementForm
                    kpiId={selectedKpi}
                    kpiName={kpi.kpi_name}
                    currentValue={kpi.current_value}
                    targetValue={kpi.target_value}
                    unit={kpi.unit}
                    onSubmit={handleMeasurementSubmit}
                    onCancel={() => {
                      setShowMeasurementForm(false)
                      setSelectedKpi(null)
                    }}
                  />
                )
              })()}
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 