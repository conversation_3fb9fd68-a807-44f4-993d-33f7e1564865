'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { 
  FeedbackType, 
  FeedbackPriority, 
  NewFeedbackForm 
} from '@/types/feedback.types';

interface FeedbackFormProps {
  solutionId?: string;
  solutionTitle?: string;
  reviewerName: string;
  onSubmit: (feedback: NewFeedbackForm) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

export default function FeedbackForm({
  solutionId,
  solutionTitle,
  reviewerName,
  onSubmit,
  onCancel,
  isSubmitting = false
}: FeedbackFormProps) {
  const [formData, setFormData] = useState<NewFeedbackForm>({
    solutionId: solutionId,
    comment: '',
    feedbackType: 'suggestion',
    priority: 'medium'
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // أنواع الملاحظات مع الترجمة والوصف
  const feedbackTypes: Array<{
    value: FeedbackType;
    label: string;
    description: string;
    color: string;
  }> = [
    {
      value: 'suggestion',
      label: 'اقتراح تحسين',
      description: 'اقتراح لتحسين أو تطوير الحل',
      color: 'bg-blue-50 border-blue-200 text-blue-800'
    },
    {
      value: 'approval',
      label: 'موافقة',
      description: 'موافقة على الحل كما هو',
      color: 'bg-green-50 border-green-200 text-green-800'
    },
    {
      value: 'concern',
      label: 'قلق أو مخاوف',
      description: 'مخاوف حول تطبيق الحل',
      color: 'bg-yellow-50 border-yellow-200 text-yellow-800'
    },
    {
      value: 'rejection',
      label: 'رفض',
      description: 'رفض الحل مع تبرير',
      color: 'bg-red-50 border-red-200 text-red-800'
    },
    {
      value: 'question',
      label: 'سؤال أو استفسار',
      description: 'سؤال يحتاج توضيح',
      color: 'bg-purple-50 border-purple-200 text-purple-800'
    },
    {
      value: 'improvement',
      label: 'تحسين مطلوب',
      description: 'تحسين ضروري قبل الموافقة',
      color: 'bg-orange-50 border-orange-200 text-orange-800'
    }
  ];

  // مستويات الأولوية
  const priorityLevels: Array<{
    value: FeedbackPriority;
    label: string;
    description: string;
    color: string;
  }> = [
    {
      value: 'low',
      label: 'منخفضة',
      description: 'ملاحظة عامة أو اقتراح اختياري',
      color: 'bg-gray-100 text-gray-700'
    },
    {
      value: 'medium',
      label: 'متوسطة',
      description: 'ملاحظة مهمة تحتاج انتباه',
      color: 'bg-blue-100 text-blue-700'
    },
    {
      value: 'high',
      label: 'عالية',
      description: 'ملاحظة مهمة جداً تحتاج معالجة',
      color: 'bg-orange-100 text-orange-700'
    },
    {
      value: 'critical',
      label: 'حرجة',
      description: 'ملاحظة حرجة تمنع المتابعة',
      color: 'bg-red-100 text-red-700'
    }
  ];

  // التحقق من صحة البيانات
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.comment.trim()) {
      newErrors.comment = 'نص الملاحظة مطلوب';
    } else if (formData.comment.trim().length < 10) {
      newErrors.comment = 'نص الملاحظة يجب أن يكون 10 أحرف على الأقل';
    } else if (formData.comment.trim().length > 1000) {
      newErrors.comment = 'نص الملاحظة يجب ألا يتجاوز 1000 حرف';
    }

    if (!formData.feedbackType) {
      newErrors.feedbackType = 'نوع الملاحظة مطلوب';
    }

    if (!formData.priority) {
      newErrors.priority = 'أولوية الملاحظة مطلوبة';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // إرسال النموذج
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('خطأ في إرسال الملاحظة:', error);
      setErrors({ submit: 'حدث خطأ في إرسال الملاحظة. يرجى المحاولة مرة أخرى.' });
    }
  };

  // تحديث البيانات
  const updateFormData = (field: keyof NewFeedbackForm, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // إزالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <div className="p-6">
        <div className="mb-6">
          <h2 className="text-xl font-bold text-gray-900 mb-2">
            إضافة ملاحظة جديدة
          </h2>
          {solutionTitle && (
            <p className="text-sm text-gray-600">
              على الحل: <span className="font-medium">{solutionTitle}</span>
            </p>
          )}
          <p className="text-sm text-gray-500 mt-1">
            المراجع: {reviewerName}
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* نوع الملاحظة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              نوع الملاحظة *
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {feedbackTypes.map((type) => (
                <div
                  key={type.value}
                  className={`relative cursor-pointer rounded-lg border-2 p-4 transition-all ${
                    formData.feedbackType === type.value
                      ? type.color + ' border-current'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => updateFormData('feedbackType', type.value)}
                >
                  <div className="flex items-center">
                    <input
                      type="radio"
                      name="feedbackType"
                      value={type.value}
                      checked={formData.feedbackType === type.value}
                      onChange={() => updateFormData('feedbackType', type.value)}
                      className="h-4 w-4 text-blue-600"
                    />
                    <div className="ml-3">
                      <div className="text-sm font-medium">{type.label}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {type.description}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            {errors.feedbackType && (
              <p className="mt-1 text-sm text-red-600">{errors.feedbackType}</p>
            )}
          </div>

          {/* أولوية الملاحظة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              أولوية الملاحظة *
            </label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {priorityLevels.map((priority) => (
                <div
                  key={priority.value}
                  className={`relative cursor-pointer rounded-lg border-2 p-3 text-center transition-all ${
                    formData.priority === priority.value
                      ? priority.color + ' border-current'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => updateFormData('priority', priority.value)}
                >
                  <input
                    type="radio"
                    name="priority"
                    value={priority.value}
                    checked={formData.priority === priority.value}
                    onChange={() => updateFormData('priority', priority.value)}
                    className="sr-only"
                  />
                  <div className="text-sm font-medium">{priority.label}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    {priority.description}
                  </div>
                </div>
              ))}
            </div>
            {errors.priority && (
              <p className="mt-1 text-sm text-red-600">{errors.priority}</p>
            )}
          </div>

          {/* نص الملاحظة */}
          <div>
            <label htmlFor="comment" className="block text-sm font-medium text-gray-700 mb-2">
              نص الملاحظة *
            </label>
            <textarea
              id="comment"
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              placeholder="اكتب ملاحظتك هنا... كن محدداً وواضحاً في ملاحظتك"
              value={formData.comment}
              onChange={(e) => updateFormData('comment', e.target.value)}
              maxLength={1000}
            />
            <div className="flex justify-between items-center mt-1">
              {errors.comment ? (
                <p className="text-sm text-red-600">{errors.comment}</p>
              ) : (
                <p className="text-sm text-gray-500">
                  اكتب ملاحظة واضحة ومفيدة (10-1000 حرف)
                </p>
              )}
              <span className="text-sm text-gray-400">
                {formData.comment.length}/1000
              </span>
            </div>
          </div>

          {/* رسالة خطأ عامة */}
          {errors.submit && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">{errors.submit}</p>
            </div>
          )}

          {/* أزرار الإجراءات */}
          <div className="flex justify-end space-x-3 space-x-reverse pt-4 border-t border-gray-200">
            <Button
              type="button"
              variant="ghost"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'جاري الإرسال...' : 'إرسال الملاحظة'}
            </Button>
          </div>
        </form>
      </div>
    </Card>
  );
} 