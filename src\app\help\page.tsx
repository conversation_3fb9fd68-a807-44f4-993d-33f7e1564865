'use client'

import { useState } from 'react'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  HelpCircle, 
  BookOpen, 
  FileText, 
  MessageSquare, 
  Phone, 
  Mail,
  Search,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  Download,
  Video,
  Users,
  Settings,
  Zap,
  Target,
  BarChart3
} from 'lucide-react'

export default function HelpPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [activeCategory, setActiveCategory] = useState('all')
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(null)

  const helpCategories = [
    { id: 'all', name: 'جميع المواضيع', icon: HelpCircle },
    { id: 'getting-started', name: 'البداية', icon: BookOpen },
    { id: 'requests', name: 'الطلبات', icon: FileText },
    { id: 'projects', name: 'المشاريع', icon: Target },
    { id: 'approvals', name: 'الموافقات', icon: Settings },
    { id: 'reports', name: 'التقارير', icon: BarChart3 }
  ]

  const quickGuides = [
    {
      title: 'كيفية تقديم طلب مشروع جديد',
      description: 'دليل مفصل لتقديم طلب مشروع تحسين جديد',
      icon: FileText,
      color: 'blue',
      link: '/help/new-request-guide'
    },
    {
      title: 'فهم منهجية FOCUS-PDCA',
      description: 'شرح مفصل لمنهجية التحسين المستمر',
      icon: Target,
      color: 'green',
      link: '/help/focus-pdca-guide'
    },
    {
      title: 'إدارة المشاريع',
      description: 'كيفية متابعة وإدارة المشاريع الجارية',
      icon: BarChart3,
      color: 'purple',
      link: '/help/project-management'
    },
    {
      title: 'نظام الموافقات',
      description: 'فهم سير العمل والموافقات',
      icon: Settings,
      color: 'orange',
      link: '/help/approval-workflow'
    }
  ]

  const faqData = [
    {
      id: 1,
      category: 'getting-started',
      question: 'كيف أبدأ في استخدام النظام؟',
      answer: 'بعد تسجيل الدخول، يمكنك البدء من لوحة التحكم الرئيسية. انقر على "طلب جديد" لتقديم طلب مشروع، أو تصفح "طلباتي" لمتابعة الطلبات الحالية.'
    },
    {
      id: 2,
      category: 'requests',
      question: 'ما هو الفرق بين أنواع المشاريع المختلفة؟',
      answer: 'هناك ثلاثة أنواع رئيسية: كويك وين (مشاريع سريعة 1-4 أسابيع)، مشاريع التحسين الشاملة (منهجية FOCUS-PDCA)، والمقترحات (أفكار للتحسين).'
    },
    {
      id: 3,
      category: 'projects',
      question: 'كيف أتابع تقدم مشروعي؟',
      answer: 'يمكنك متابعة تقدم مشروعك من خلال صفحة "المشاريع" التي تعرض لوحة Kanban مع جميع مراحل المشروع والمهام المطلوبة.'
    },
    {
      id: 4,
      category: 'approvals',
      question: 'كم من الوقت يستغرق اعتماد الطلب؟',
      answer: 'يعتمد وقت الاعتماد على نوع المشروع وتعقيده. عادة ما يتم الرد خلال 3-5 أيام عمل للمشاريع البسيطة، و7-10 أيام للمشاريع المعقدة.'
    },
    {
      id: 5,
      category: 'requests',
      question: 'هل يمكنني حفظ طلبي كمسودة؟',
      answer: 'نعم، يمكنك حفظ طلبك كمسودة في أي وقت والعودة لاحقاً لإكماله. ستجد المسودات في صفحة "طلباتي".'
    },
    {
      id: 6,
      category: 'reports',
      question: 'كيف أحصل على تقارير المشاريع؟',
      answer: 'يمكنك الوصول للتقارير من خلال صفحة "التقارير" التي تحتوي على إحصائيات مفصلة وتقارير قابلة للتصدير.'
    }
  ]

  const contactOptions = [
    {
      title: 'الدعم الفني',
      description: 'للمساعدة في المشاكل التقنية',
      icon: Settings,
      contact: '<EMAIL>',
      type: 'email'
    },
    {
      title: 'مكتب إدارة المشاريع',
      description: 'للاستفسارات حول المشاريع',
      icon: Users,
      contact: '+966 11 123 4567',
      type: 'phone'
    },
    {
      title: 'التدريب والتطوير',
      description: 'للحصول على تدريب إضافي',
      icon: BookOpen,
      contact: '<EMAIL>',
      type: 'email'
    }
  ]

  const filteredFAQ = faqData.filter(faq => {
    const matchesCategory = activeCategory === 'all' || faq.category === activeCategory
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesCategory && matchesSearch
  })

  const toggleFAQ = (id: number) => {
    setExpandedFAQ(expandedFAQ === id ? null : id)
  }

  return (
    <ProtectedLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Page Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">مركز المساعدة</h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            مرحباً بك في مركز المساعدة. هنا ستجد جميع الإجابات والأدلة التي تحتاجها لاستخدام النظام بكفاءة
          </p>
        </div>

        {/* Search Bar */}
        <Card>
          <CardContent className="p-6">
            <div className="relative max-w-2xl mx-auto">
              <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="ابحث في المساعدة..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-12 pl-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
              />
            </div>
          </CardContent>
        </Card>

        {/* Quick Guides */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">الأدلة السريعة</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickGuides.map((guide) => {
              const Icon = guide.icon
              return (
                <Card key={guide.title} className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardContent className="p-6 text-center">
                    <div className={`w-16 h-16 bg-${guide.color}-100 rounded-full flex items-center justify-center mx-auto mb-4`}>
                      <Icon className={`w-8 h-8 text-${guide.color}-600`} />
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">{guide.title}</h3>
                    <p className="text-sm text-gray-600 mb-4">{guide.description}</p>
                    <Button variant="ghost" size="sm" icon={<ExternalLink className="w-4 h-4" />}>
                      اقرأ المزيد
                    </Button>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* FAQ Section */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">الأسئلة الشائعة</h2>
          
          {/* Category Filter */}
          <div className="flex flex-wrap gap-2 mb-6">
            {helpCategories.map((category) => {
              const Icon = category.icon
              return (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                    activeCategory === category.id
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {category.name}
                </button>
              )
            })}
          </div>

          {/* FAQ List */}
          <div className="space-y-4">
            {filteredFAQ.map((faq) => (
              <Card key={faq.id}>
                <CardContent className="p-0">
                  <button
                    onClick={() => toggleFAQ(faq.id)}
                    className="w-full p-6 text-right flex justify-between items-center hover:bg-gray-50 transition-colors"
                  >
                    <span className="font-medium text-gray-900">{faq.question}</span>
                    {expandedFAQ === faq.id ? (
                      <ChevronUp className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  {expandedFAQ === faq.id && (
                    <div className="px-6 pb-6 text-gray-600">
                      {faq.answer}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredFAQ.length === 0 && (
            <div className="text-center py-12">
              <HelpCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد نتائج</h3>
              <p className="text-gray-600">جرب البحث بكلمات مختلفة أو اختر فئة أخرى</p>
            </div>
          )}
        </div>

        {/* Contact Support */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">تواصل معنا</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {contactOptions.map((option) => {
              const Icon = option.icon
              return (
                <Card key={option.title}>
                  <CardContent className="p-6 text-center">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Icon className="w-8 h-8 text-blue-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">{option.title}</h3>
                    <p className="text-sm text-gray-600 mb-4">{option.description}</p>
                    <div className="flex items-center justify-center gap-2 text-blue-600">
                      {option.type === 'email' ? (
                        <Mail className="w-4 h-4" />
                      ) : (
                        <Phone className="w-4 h-4" />
                      )}
                      <span className="font-medium">{option.contact}</span>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* Additional Resources */}
        <Card>
          <CardHeader>
            <h3 className="text-xl font-semibold text-gray-900">موارد إضافية</h3>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                <Video className="w-6 h-6 text-blue-600" />
                <div>
                  <h4 className="font-medium text-gray-900">فيديوهات تعليمية</h4>
                  <p className="text-sm text-gray-600">شاهد الفيديوهات التعليمية</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                <Download className="w-6 h-6 text-green-600" />
                <div>
                  <h4 className="font-medium text-gray-900">دليل المستخدم</h4>
                  <p className="text-sm text-gray-600">حمل الدليل الكامل PDF</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ProtectedLayout>
  )
} 