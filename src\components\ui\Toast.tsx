'use client';

import React, { createContext, useContext, useCallback, useState, useEffect } from 'react';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';

// أنواع الإشعارات
export type ToastType = 'success' | 'error' | 'warning' | 'info';

// بيانات الإشعار
export interface ToastData {
  id: string;
  type: ToastType;
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// سياق الإشعارات
interface ToastContextType {
  toasts: ToastData[];
  addToast: (toast: Omit<ToastData, 'id'>) => void;
  removeToast: (id: string) => void;
  clearAllToasts: () => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

// خطاف لاستخدام الإشعارات
export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within ToastProvider');
  }
  return context;
};

// مكون الإشعار الواحد
const Toast: React.FC<{ 
  toast: ToastData; 
  onRemove: (id: string) => void; 
}> = ({ toast, onRemove }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    // إظهار الإشعار
    const showTimer = setTimeout(() => setIsVisible(true), 10);
    
    // إخفاء الإشعار تلقائياً
    const hideTimer = setTimeout(() => {
      setIsExiting(true);
      setTimeout(() => onRemove(toast.id), 300);
    }, toast.duration || 5000);

    return () => {
      clearTimeout(showTimer);
      clearTimeout(hideTimer);
    };
  }, [toast.id, toast.duration, onRemove]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => onRemove(toast.id), 300);
  };

  // أيقونات الإشعارات
  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'info':
        return <Info className="w-5 h-5 text-blue-500" />;
      default:
        return <Info className="w-5 h-5 text-blue-500" />;
    }
  };

  // ألوان الإشعارات
  const getToastStyles = () => {
    const baseStyles = "relative flex items-start gap-3 p-4 rounded-lg shadow-lg border transition-all duration-300 ease-in-out";
    
    switch (toast.type) {
      case 'success':
        return `${baseStyles} bg-green-50 border-green-200 text-green-800`;
      case 'error':
        return `${baseStyles} bg-red-50 border-red-200 text-red-800`;
      case 'warning':
        return `${baseStyles} bg-yellow-50 border-yellow-200 text-yellow-800`;
      case 'info':
        return `${baseStyles} bg-blue-50 border-blue-200 text-blue-800`;
      default:
        return `${baseStyles} bg-gray-50 border-gray-200 text-gray-800`;
    }
  };

  return (
    <div
      className={`
        ${getToastStyles()}
        ${isVisible ? 'transform translate-x-0 opacity-100' : 'transform translate-x-full opacity-0'}
        ${isExiting ? 'transform translate-x-full opacity-0' : ''}
        max-w-md w-full
      `}
    >
      {/* أيقونة الإشعار */}
      <div className="flex-shrink-0 mt-0.5">
        {getIcon()}
      </div>

      {/* محتوى الإشعار */}
      <div className="flex-1 min-w-0">
        <h4 className="font-semibold text-sm leading-5">
          {toast.title}
        </h4>
        {toast.message && (
          <p className="mt-1 text-sm opacity-90 leading-5">
            {toast.message}
          </p>
        )}
        
        {/* زر الإجراء */}
        {toast.action && (
          <div className="mt-3">
            <button
              onClick={toast.action.onClick}
              className="text-sm font-medium underline hover:no-underline focus:outline-none"
            >
              {toast.action.label}
            </button>
          </div>
        )}
      </div>

      {/* زر الإغلاق */}
      <button
        onClick={handleClose}
        className="flex-shrink-0 p-1 rounded-md hover:bg-black/10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current transition-colors"
        aria-label="إغلاق الإشعار"
      >
        <X className="w-4 h-4" />
      </button>
    </div>
  );
};

// مكون حاوي الإشعارات
export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastData[]>([]);

  const addToast = useCallback((toast: Omit<ToastData, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    setToasts(prev => [...prev, { ...toast, id }]);
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const clearAllToasts = useCallback(() => {
    setToasts([]);
  }, []);

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast, clearAllToasts }}>
      {children}
      
      {/* حاوي الإشعارات */}
      <div className="fixed top-4 left-4 z-50 space-y-2 pointer-events-none">
        <div className="flex flex-col gap-2 pointer-events-auto">
          {toasts.map(toast => (
            <Toast
              key={toast.id}
              toast={toast}
              onRemove={removeToast}
            />
          ))}
        </div>
      </div>
    </ToastContext.Provider>
  );
};

// خطافات مساعدة للاستخدام السريع
export const useToastHelpers = () => {
  const { addToast, removeToast } = useToast();

  return {
    success: (title: string, message?: string) => 
      addToast({ type: 'success', title, message }),
    
    error: (title: string, message?: string) => 
      addToast({ type: 'error', title, message }),
    
    warning: (title: string, message?: string) => 
      addToast({ type: 'warning', title, message }),
    
    info: (title: string, message?: string) => 
      addToast({ type: 'info', title, message }),

    showSuccess: (title: string, message?: string) => 
      addToast({ type: 'success', title, message }),
    
    showError: (title: string, message?: string) => 
      addToast({ type: 'error', title, message }),
    
    showWarning: (title: string, message?: string) => 
      addToast({ type: 'warning', title, message }),
    
    showInfo: (title: string, message?: string) => 
      addToast({ type: 'info', title, message }),

    showLoading: (title: string, message?: string) => {
      const id = Math.random().toString(36).substr(2, 9);
      addToast({ type: 'info', title, message, duration: 0 });
      return id;
    },

    updateLoadingToSuccess: (id: string, title: string, message?: string) => {
      removeToast(id);
      addToast({ type: 'success', title, message });
    },

    updateLoadingToError: (id: string, title: string, message?: string) => {
      removeToast(id);
      addToast({ type: 'error', title, message });
    },
  };
}; 