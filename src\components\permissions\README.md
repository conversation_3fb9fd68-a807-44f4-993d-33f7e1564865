# نظام إدارة الصلاحيات والأدوار

## نظرة عامة

نظام شامل لإدارة الأدوار والصلاحيات في تطبيق إدارة المشاريع. يوفر واجهة سهلة الاستخدام لتحديد وإدارة صلاحيات المستخدمين بطريقة آمنة ومرنة.

## المكونات الرئيسية

### 1. PermissionsPage (`/permissions`)
الصفحة الرئيسية لإدارة الصلاحيات التي تحتوي على أربعة تبويبات:

- **نظرة عامة**: عرض شامل للنظام
- **إدارة الأدوار**: إضافة وتعديل الأدوار
- **إدارة الصلاحيات**: تحديد صلاحيات كل دور
- **تعيين الأدوار**: ربط المستخدمين بالأدوار

### 2. PermissionsOverview
عرض شامل يتضمن:
- إحصائيات النظام (عدد الأدوار، المستخدمين، النشطين)
- توزيع الأدوار بشكل مرئي
- مصفوفة الصلاحيات
- تنبيهات الأمان
- الإجراءات السريعة

### 3. RolesManagement
إدارة الأدوار مع الميزات التالية:
- إضافة أدوار جديدة
- تعديل الأدوار الموجودة
- حذف الأدوار (مع التحقق من عدم وجود مستخدمين مرتبطين)
- عرض عدد المستخدمين لكل دور
- التحقق من صحة البيانات

### 4. PermissionsManagement
تحديد صلاحيات الأدوار مع:
- واجهة تفاعلية لتعديل الصلاحيات
- تصنيف الصلاحيات حسب الفئات
- حماية خاصة لدور المدير
- إعادة تعيين الصلاحيات
- حفظ التغييرات

### 5. UsersRoleAssignment
تعيين الأدوار للمستخدمين مع:
- البحث والتصفية المتقدمة
- تعديل أدوار المستخدمين
- عرض إحصائيات الأدوار
- حالة المستخدمين (نشط/غير نشط)

## هيكل الصلاحيات

### الأدوار الافتراضية

1. **مدير النظام (admin)**
   - صلاحيات كاملة في جميع أجزاء النظام
   - لا يمكن تعديل صلاحياته

2. **مدير مكتب المشاريع (pmo_manager)**
   - إدارة المشاريع والطلبات
   - اعتماد الطلبات
   - عرض التقارير

3. **مدير إدارة التخطيط (planning_manager)**
   - اعتماد المشاريع الكبيرة
   - عرض المشاريع والطلبات
   - عرض التقارير

4. **مدير المشروع (project_manager)**
   - إدارة المشاريع المعينة
   - إدارة المهام
   - عرض التقارير

5. **الموظف (employee)**
   - تقديم طلبات المشاريع
   - عرض المشاريع

### فئات الصلاحيات

1. **المشاريع (projects)**
   - عرض، تعديل، اعتماد، حذف

2. **طلبات المشاريع (requests)**
   - عرض، تعديل، اعتماد، حذف

3. **إدارة المستخدمين (users)**
   - عرض، تعديل، حذف

4. **إدارة المهام (tasks)**
   - عرض، تعديل، تعيين، حذف

5. **التقارير (reports)**
   - عرض، تعديل، تصدير

6. **إدارة الأقسام (departments)**
   - عرض، تعديل، حذف

## الأمان والحماية

### التحكم في الوصول
- فقط المدير ومدير مكتب المشاريع يمكنهم الوصول لصفحة إدارة الصلاحيات
- التحقق من الصلاحيات على مستوى المكونات
- حماية خاصة لدور المدير من التعديل

### التحقق من البيانات
- التحقق من صحة أسماء الأدوار (أحرف إنجليزية وشرطة سفلية فقط)
- منع تكرار أسماء الأدوار
- التحقق من وجود مستخدمين قبل حذف الأدوار

### تنبيهات الأمان
- عرض تنبيهات حول حالة النظام
- إشعارات حول المستخدمين غير النشطين
- تتبع آخر تحديث للصلاحيات

## الاستخدام

### الوصول للنظام
1. تسجيل الدخول كمدير أو مدير مكتب مشاريع
2. الانتقال إلى "إدارة الصلاحيات" من الشريط الجانبي
3. اختيار التبويب المناسب

### إضافة دور جديد
1. انتقل إلى تبويب "إدارة الأدوار"
2. اضغط "إضافة دور جديد"
3. املأ البيانات المطلوبة
4. احفظ الدور

### تحديد صلاحيات دور
1. انتقل إلى تبويب "إدارة الصلاحيات"
2. اختر الدور المطلوب
3. حدد الصلاحيات المناسبة
4. احفظ التغييرات

### تعيين دور لمستخدم
1. انتقل إلى تبويب "تعيين الأدوار"
2. ابحث عن المستخدم
3. اضغط على أيقونة التعديل
4. اختر الدور الجديد
5. احفظ التغيير

## التطوير والتخصيص

### إضافة صلاحيات جديدة
1. أضف الصلاحية الجديدة في `mockPermissions` في `PermissionsManagement.tsx`
2. حدد الفئة والأعمال المتاحة
3. أضف التسميات العربية في `actionLabels`

### إضافة أدوار جديدة
1. أضف الدور في `mockRoles` في جميع المكونات
2. حدد الصلاحيات الافتراضية
3. أضف اللون المناسب في `getRoleColor`

### تخصيص الواجهة
- جميع المكونات تستخدم Tailwind CSS
- الألوان قابلة للتخصيص في `colorClasses`
- الأيقونات من مكتبة Lucide React

## الملفات المرتبطة

- `/src/app/permissions/page.tsx` - الصفحة الرئيسية
- `/src/components/permissions/` - مجلد المكونات
- `/src/lib/auth.ts` - نظام المصادقة والصلاحيات
- `/database/schema.sql` - هيكل قاعدة البيانات
- `/database/rls-policies.sql` - سياسات الأمان

## المتطلبات التقنية

- React 18+
- Next.js 14+
- TypeScript
- Tailwind CSS
- Lucide React Icons
- Supabase (قاعدة البيانات)

## الملاحظات الهامة

1. **لا تعدل دور المدير**: دور المدير محمي ولا يمكن تعديل صلاحياته
2. **تحقق من المستخدمين**: لا يمكن حذف دور مرتبط بمستخدمين
3. **النسخ الاحتياطي**: احتفظ بنسخة احتياطية قبل إجراء تغييرات كبيرة
4. **الاختبار**: اختبر التغييرات في بيئة التطوير أولاً

## الدعم والصيانة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع هذا الدليل أولاً
2. تحقق من ملفات السجل
3. تواصل مع فريق التطوير

---

تم إنشاء هذا النظام وفقاً لأفضل الممارسات في الأمان وتجربة المستخدم. 