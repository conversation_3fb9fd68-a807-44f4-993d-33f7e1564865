'use client'

import React, { useState, useRef, useCallback } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  Upload, 
  X, 
  File, 
  Image, 
  FileText, 
  Archive,
  AlertCircle,
  CheckCircle,
  Trash2,
  Eye,
  Loader2
} from 'lucide-react'

interface UploadedFile {
  id: string
  file: File
  name: string
  size: number
  type: string
  url?: string
  uploadProgress: number
  status: 'pending' | 'uploading' | 'completed' | 'error'
  error?: string
}

interface FileUploadProps {
  maxFiles?: number
  maxFileSize?: number // في MB
  acceptedTypes?: string[]
  onFilesChange?: (files: UploadedFile[]) => void
  onUpload?: (files: File[]) => Promise<void>
  disabled?: boolean
  showPreview?: boolean
  existingFiles?: UploadedFile[]
}

export function FileUpload({
  maxFiles = 10,
  maxFileSize = 10, // 10MB
  acceptedTypes = ['image/*', 'application/pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt'],
  onFilesChange,
  onUpload,
  disabled = false,
  showPreview = true,
  existingFiles = []
}: FileUploadProps) {
  const [files, setFiles] = useState<UploadedFile[]>(existingFiles)
  const [isDragOver, setIsDragOver] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return <Image className="w-5 h-5 text-blue-600" />
    if (type.includes('pdf')) return <FileText className="w-5 h-5 text-red-600" />
    if (type.includes('word') || type.includes('document')) return <FileText className="w-5 h-5 text-blue-600" />
    if (type.includes('sheet') || type.includes('excel')) return <FileText className="w-5 h-5 text-green-600" />
    if (type.includes('zip') || type.includes('rar')) return <Archive className="w-5 h-5 text-orange-600" />
    return <File className="w-5 h-5 text-gray-600" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const validateFile = (file: File): string | null => {
    // التحقق من حجم الملف
    if (file.size > maxFileSize * 1024 * 1024) {
      return `حجم الملف كبير جداً. الحد الأقصى ${maxFileSize}MB`
    }

    // التحقق من نوع الملف
    const isValidType = acceptedTypes.some(type => {
      if (type.startsWith('.')) {
        return file.name.toLowerCase().endsWith(type.toLowerCase())
      }
      return file.type.match(type)
    })

    if (!isValidType) {
      return 'نوع الملف غير مدعوم'
    }

    return null
  }

  const addFiles = useCallback((newFiles: FileList | File[]) => {
    const fileArray = Array.from(newFiles)
    
    // التحقق من عدد الملفات
    if (files.length + fileArray.length > maxFiles) {
      alert(`لا يمكن إضافة أكثر من ${maxFiles} ملفات`)
      return
    }

    const validFiles: UploadedFile[] = []
    
    fileArray.forEach(file => {
      const error = validateFile(file)
      const uploadedFile: UploadedFile = {
        id: Math.random().toString(36).substr(2, 9),
        file,
        name: file.name,
        size: file.size,
        type: file.type,
        uploadProgress: 0,
        status: error ? 'error' : 'pending',
        error: error || undefined
      }
      validFiles.push(uploadedFile)
    })

    const updatedFiles = [...files, ...validFiles]
    setFiles(updatedFiles)
    onFilesChange?.(updatedFiles)
  }, [files, maxFiles, onFilesChange])

  const removeFile = (fileId: string) => {
    const updatedFiles = files.filter(f => f.id !== fileId)
    setFiles(updatedFiles)
    onFilesChange?.(updatedFiles)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    if (!disabled) {
      setIsDragOver(true)
    }
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    if (disabled) return
    
    const droppedFiles = e.dataTransfer.files
    if (droppedFiles.length > 0) {
      addFiles(droppedFiles)
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files
    if (selectedFiles && selectedFiles.length > 0) {
      addFiles(selectedFiles)
    }
    // إعادة تعيين قيمة input للسماح بتحديد نفس الملف مرة أخرى
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleUpload = async () => {
    if (!onUpload) return
    
    const pendingFiles = files.filter(f => f.status === 'pending')
    if (pendingFiles.length === 0) return

    setIsUploading(true)
    
    try {
      // تحديث حالة الملفات إلى "uploading"
      const updatedFiles = files.map(f => 
        f.status === 'pending' ? { ...f, status: 'uploading' as const, uploadProgress: 0 } : f
      )
      setFiles(updatedFiles)

      // محاكاة تقدم الرفع
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 100))
        setFiles(prev => prev.map(f => 
          f.status === 'uploading' ? { ...f, uploadProgress: i } : f
        ))
      }

      // رفع الملفات
      await onUpload(pendingFiles.map(f => f.file))
      
      // تحديث حالة الملفات إلى "completed"
      setFiles(prev => prev.map(f => 
        f.status === 'uploading' ? { ...f, status: 'completed', uploadProgress: 100 } : f
      ))
      
    } catch {
      // تحديث حالة الملفات إلى "error"
      setFiles(prev => prev.map(f => 
        f.status === 'uploading' ? { 
          ...f, 
          status: 'error', 
          error: 'فشل في رفع الملف' 
        } : f
      ))
    } finally {
      setIsUploading(false)
    }
  }

  const pendingFilesCount = files.filter(f => f.status === 'pending').length
  const completedFilesCount = files.filter(f => f.status === 'completed').length
  const errorFilesCount = files.filter(f => f.status === 'error').length

  return (
    <div className="space-y-4">
      {/* Drop Zone */}
      <Card 
        className={`border-2 border-dashed transition-all duration-200 ${
          isDragOver 
            ? 'border-blue-400 bg-blue-50' 
            : disabled 
              ? 'border-gray-200 bg-gray-50' 
              : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="p-8 text-center">
          <div className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center ${
            disabled ? 'bg-gray-100' : 'bg-blue-100'
          }`}>
            <Upload className={`w-8 h-8 ${disabled ? 'text-gray-400' : 'text-blue-600'}`} />
          </div>
          
          <h3 className={`text-lg font-medium mb-2 ${disabled ? 'text-gray-400' : 'text-gray-900'}`}>
            اسحب الملفات هنا أو اضغط للاختيار
          </h3>
          
          <p className="text-sm text-gray-500 mb-4">
            الحد الأقصى: {maxFiles} ملفات، حجم كل ملف: {maxFileSize}MB
          </p>
          
          <div className="text-xs text-gray-400 mb-4">
            الأنواع المدعومة: {acceptedTypes.join(', ')}
          </div>
          
          <Button
            variant="secondary"
            onClick={() => fileInputRef.current?.click()}
            disabled={disabled || files.length >= maxFiles}
            icon={<Upload className="w-4 h-4" />}
          >
            اختيار الملفات
          </Button>
          
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={acceptedTypes.join(',')}
            onChange={handleFileSelect}
            className="hidden"
            disabled={disabled}
          />
        </div>
      </Card>

      {/* Files List */}
      {files.length > 0 && (
        <Card className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-medium text-gray-900">
              الملفات المختارة ({files.length}/{maxFiles})
            </h4>
            
            <div className="flex items-center gap-2">
              {completedFilesCount > 0 && (
                <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">
                  {completedFilesCount} مكتمل
                </span>
              )}
              {errorFilesCount > 0 && (
                <span className="text-xs text-red-600 bg-red-100 px-2 py-1 rounded-full">
                  {errorFilesCount} خطأ
                </span>
              )}
              {pendingFilesCount > 0 && onUpload && (
                <Button
                  size="sm"
                  onClick={handleUpload}
                  disabled={isUploading}
                  icon={<Upload className="w-4 h-4" />}
                >
                  رفع الملفات ({pendingFilesCount})
                </Button>
              )}
            </div>
          </div>

          <div className="space-y-3">
            {files.map((file) => (
              <div key={file.id} className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg">
                <div className="flex-shrink-0">
                  {getFileIcon(file.type)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {file.name}
                    </p>
                    <div className="flex items-center gap-2">
                      {showPreview && file.type.startsWith('image/') && (
                        <Button
                          variant="ghost"
                          size="sm"
                          icon={<Eye className="w-4 h-4" />}
                          onClick={() => {
                            // يمكن إضافة منطق معاينة الصورة هنا
                            console.log('Preview image:', file.name)
                          }}
                        />
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        icon={<Trash2 className="w-4 h-4" />}
                        onClick={() => removeFile(file.id)}
                        disabled={file.status === 'uploading'}
                      />
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between mt-1">
                    <p className="text-xs text-gray-500">
                      {formatFileSize(file.size)}
                    </p>
                    
                    <div className="flex items-center gap-2">
                      {file.status === 'pending' && (
                        <span className="text-xs text-gray-500">في الانتظار</span>
                      )}
                      
                      {file.status === 'uploading' && (
                        <div className="flex items-center gap-2">
                          <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />
                          <span className="text-xs text-blue-600">{file.uploadProgress}%</span>
                        </div>
                      )}
                      
                      {file.status === 'completed' && (
                        <div className="flex items-center gap-1">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          <span className="text-xs text-green-600">مكتمل</span>
                        </div>
                      )}
                      
                      {file.status === 'error' && (
                        <div className="flex items-center gap-1">
                          <AlertCircle className="w-4 h-4 text-red-600" />
                          <span className="text-xs text-red-600" title={file.error}>
                            خطأ
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* Progress Bar for Uploading */}
                  {file.status === 'uploading' && (
                    <div className="mt-2">
                      <div className="w-full bg-gray-200 rounded-full h-1">
                        <div 
                          className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                          style={{ width: `${file.uploadProgress}%` }}
                        />
                      </div>
                    </div>
                  )}
                  
                  {/* Error Message */}
                  {file.status === 'error' && file.error && (
                    <p className="text-xs text-red-600 mt-1">{file.error}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  )
} 