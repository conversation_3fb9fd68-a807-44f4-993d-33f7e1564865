'use client'

import { useState } from 'react'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  FileText, 
  Download,
  RefreshCw,
  Pie<PERSON>hart,
  Activity,
  Clock,
  CheckCircle,
  AlertTriangle,
  Target,
  Award
} from 'lucide-react'

interface ProjectStats {
  totalProjects: number
  activeProjects: number
  completedProjects: number
  pendingApprovals: number
  averageCompletionTime: number
  successRate: number
}

interface UserStats {
  totalUsers: number
  activeUsers: number
  topPerformers: Array<{
    name: string
    completedTasks: number
    efficiency: number
  }>
}

interface FileStats {
  totalFiles: number
  totalSize: string
  uploadsByType: Record<string, number>
  securityScans: number
  threatsPrevented: number
}

export default function ReportsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState('month')
  const [selectedReport, setSelectedReport] = useState('overview')
  const [isLoading, setIsLoading] = useState(false)

  // بيانات وهمية للتقارير
  const projectStats: ProjectStats = {
    totalProjects: 45,
    activeProjects: 12,
    completedProjects: 28,
    pendingApprovals: 5,
    averageCompletionTime: 14.5,
    successRate: 89.2
  }

  const userStats: UserStats = {
    totalUsers: 23,
    activeUsers: 18,
    topPerformers: [
      { name: 'أحمد محمد', completedTasks: 24, efficiency: 95 },
      { name: 'سارة أحمد', completedTasks: 21, efficiency: 92 },
      { name: 'محمد علي', completedTasks: 19, efficiency: 88 }
    ]
  }

  const fileStats: FileStats = {
    totalFiles: 234,
    totalSize: '4.2 GB',
    uploadsByType: {
      'PDF': 89,
      'صور': 67,
      'Excel': 45,
      'Word': 33
    },
    securityScans: 234,
    threatsPrevented: 3
  }

  const generateReport = async () => {
    setIsLoading(true)
    // محاكاة تحديث البيانات
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsLoading(false)
  }

  const exportReport = (format: 'pdf' | 'excel') => {
    // محاكاة تصدير التقرير
    console.log(`Exporting report as ${format}`)
    alert(`تم بدء تصدير التقرير بصيغة ${format.toUpperCase()}`)
  }

  const getEfficiencyColor = (percentage: number) => {
    if (percentage >= 90) return 'text-green-600'
    if (percentage >= 75) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getEfficiencyBg = (percentage: number) => {
    if (percentage >= 90) return 'bg-green-100'
    if (percentage >= 75) return 'bg-yellow-100'
    return 'bg-red-100'
  }

  return (
    <ProtectedLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Page Header */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">التقارير والإحصائيات</h1>
            <p className="text-gray-600 mt-1">
              تحليل شامل لأداء المشاريع والمستخدمين
            </p>
          </div>
          
          <div className="flex flex-wrap gap-3">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="week">هذا الأسبوع</option>
              <option value="month">هذا الشهر</option>
              <option value="quarter">هذا الربع</option>
              <option value="year">هذا العام</option>
            </select>
            
            <Button
              variant="ghost"
              icon={<RefreshCw className="w-4 h-4" />}
              onClick={generateReport}
              disabled={isLoading}
            >
              {isLoading ? 'جاري التحديث...' : 'تحديث'}
            </Button>
            
            <Button
              variant="ghost"
              icon={<Download className="w-4 h-4" />}
              onClick={() => exportReport('pdf')}
            >
              تصدير PDF
            </Button>
            
            <Button
              variant="ghost"
              icon={<Download className="w-4 h-4" />}
              onClick={() => exportReport('excel')}
            >
              تصدير Excel
            </Button>
          </div>
        </div>

        {/* Report Navigation */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-wrap gap-2">
              {[
                { id: 'overview', label: 'نظرة عامة', icon: BarChart3 },
                { id: 'projects', label: 'المشاريع', icon: Target },
                { id: 'users', label: 'المستخدمين', icon: Users },
                { id: 'files', label: 'الملفات', icon: FileText }
              ].map(tab => (
                <Button
                  key={tab.id}
                  variant={selectedReport === tab.id ? 'primary' : 'ghost'}
                  icon={<tab.icon className="w-4 h-4" />}
                  onClick={() => setSelectedReport(tab.id)}
                  size="sm"
                >
                  {tab.label}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Overview Dashboard */}
        {selectedReport === 'overview' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">إجمالي المشاريع</p>
                    <p className="text-3xl font-bold text-blue-600">{projectStats.totalProjects}</p>
                    <p className="text-sm text-green-600">+12% من الشهر الماضي</p>
                  </div>
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Target className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">معدل النجاح</p>
                    <p className="text-3xl font-bold text-green-600">{projectStats.successRate}%</p>
                    <p className="text-sm text-green-600">+5.2% من الشهر الماضي</p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <Award className="w-6 h-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">المستخدمين النشطين</p>
                    <p className="text-3xl font-bold text-purple-600">{userStats.activeUsers}</p>
                    <p className="text-sm text-green-600">من إجمالي {userStats.totalUsers}</p>
                  </div>
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Users className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">الملفات المحفوظة</p>
                    <p className="text-3xl font-bold text-orange-600">{fileStats.totalFiles}</p>
                    <p className="text-sm text-gray-600">{fileStats.totalSize}</p>
                  </div>
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <FileText className="w-6 h-6 text-orange-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Projects Report */}
        {selectedReport === 'projects' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold text-gray-900">حالة المشاريع</h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <span className="font-medium text-blue-900">نشطة</span>
                    <span className="text-2xl font-bold text-blue-600">{projectStats.activeProjects}</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <span className="font-medium text-green-900">مكتملة</span>
                    <span className="text-2xl font-bold text-green-600">{projectStats.completedProjects}</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                    <span className="font-medium text-yellow-900">في انتظار الاعتماد</span>
                    <span className="text-2xl font-bold text-yellow-600">{projectStats.pendingApprovals}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold text-gray-900">مؤشرات الأداء</h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">معدل النجاح</span>
                      <span className="text-sm font-medium text-green-600">{projectStats.successRate}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${projectStats.successRate}%` }}
                      ></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">متوسط وقت الإنجاز</span>
                      <span className="text-sm font-medium text-blue-600">{projectStats.averageCompletionTime} يوم</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                        style={{ width: '75%' }}
                      ></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Users Report */}
        {selectedReport === 'users' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold text-gray-900">أفضل المؤدين</h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {userStats.topPerformers.map((user, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className={`
                          w-8 h-8 rounded-full flex items-center justify-center text-white font-bold
                          ${index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-gray-400' : 'bg-orange-500'}
                        `}>
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{user.name}</p>
                          <p className="text-sm text-gray-600">{user.completedTasks} مهمة مكتملة</p>
                        </div>
                      </div>
                      <div className={`
                        px-3 py-1 rounded-full text-sm font-medium
                        ${getEfficiencyBg(user.efficiency)} ${getEfficiencyColor(user.efficiency)}
                      `}>
                        {user.efficiency}%
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold text-gray-900">نشاط المستخدمين</h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{userStats.totalUsers}</div>
                      <div className="text-sm text-gray-600">إجمالي المستخدمين</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{userStats.activeUsers}</div>
                      <div className="text-sm text-gray-600">نشط هذا الشهر</div>
                    </div>
                  </div>
                  
                  <div className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">معدل النشاط</span>
                      <span className="text-sm font-medium text-green-600">
                        {Math.round((userStats.activeUsers / userStats.totalUsers) * 100)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${(userStats.activeUsers / userStats.totalUsers) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Files Report */}
        {selectedReport === 'files' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold text-gray-900">إحصائيات الملفات</h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{fileStats.totalFiles}</div>
                      <div className="text-sm text-gray-600">إجمالي الملفات</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{fileStats.totalSize}</div>
                      <div className="text-sm text-gray-600">المساحة المستخدمة</div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">الملفات حسب النوع</h4>
                    <div className="space-y-2">
                      {Object.entries(fileStats.uploadsByType).map(([type, count]) => (
                        <div key={type} className="flex items-center justify-between">
                          <span className="text-sm text-gray-700">{type}</span>
                          <div className="flex items-center gap-2">
                            <div className="w-20 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-600 h-2 rounded-full"
                                style={{ width: `${(count / Math.max(...Object.values(fileStats.uploadsByType))) * 100}%` }}
                              ></div>
                            </div>
                            <span className="text-sm font-medium text-gray-900 w-8">{count}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold text-gray-900">الأمان والحماية</h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center gap-3 mb-2">
                      <CheckCircle className="w-5 h-5 text-green-600" />
                      <span className="font-medium text-green-900">فحوصات الأمان</span>
                    </div>
                    <div className="text-2xl font-bold text-green-600">{fileStats.securityScans}</div>
                    <div className="text-sm text-green-700">فحص مكتمل</div>
                  </div>

                  <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center gap-3 mb-2">
                      <AlertTriangle className="w-5 h-5 text-red-600" />
                      <span className="font-medium text-red-900">تهديدات تم منعها</span>
                    </div>
                    <div className="text-2xl font-bold text-red-600">{fileStats.threatsPrevented}</div>
                    <div className="text-sm text-red-700">ملف مشبوه تم حجبه</div>
                  </div>

                  <div className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">معدل الأمان</span>
                      <span className="text-sm font-medium text-green-600">98.7%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full transition-all duration-500"
                        style={{ width: '98.7%' }}
                      ></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </ProtectedLayout>
  )
} 