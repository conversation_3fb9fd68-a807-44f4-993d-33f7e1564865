import React from 'react'
import { AlertCircle, X } from 'lucide-react'

interface ErrorMessageProps {
  title?: string
  message: string
  type?: 'error' | 'warning' | 'info'
  onClose?: () => void
  showIcon?: boolean
}

export function ErrorMessage({ 
  title = 'خطأ', 
  message, 
  type = 'error', 
  onClose,
  showIcon = true 
}: ErrorMessageProps) {
  const getStyles = () => {
    switch (type) {
      case 'error':
        return {
          container: 'bg-red-50 border-red-200 text-red-800',
          icon: 'text-red-500',
          title: 'text-red-900'
        }
      case 'warning':
        return {
          container: 'bg-yellow-50 border-yellow-200 text-yellow-800',
          icon: 'text-yellow-500',
          title: 'text-yellow-900'
        }
      case 'info':
        return {
          container: 'bg-blue-50 border-blue-200 text-blue-800',
          icon: 'text-blue-500',
          title: 'text-blue-900'
        }
      default:
        return {
          container: 'bg-gray-50 border-gray-200 text-gray-800',
          icon: 'text-gray-500',
          title: 'text-gray-900'
        }
    }
  }

  const styles = getStyles()

  return (
    <div className={`border rounded-lg p-4 ${styles.container}`}>
      <div className="flex items-start">
        {showIcon && (
          <div className="flex-shrink-0 ml-3">
            <AlertCircle className={`w-5 h-5 ${styles.icon}`} />
          </div>
        )}
        
        <div className="flex-1">
          <h3 className={`text-sm font-medium ${styles.title}`}>
            {title}
          </h3>
          <div className="mt-2 text-sm">
            <p>{message}</p>
          </div>
        </div>

        {onClose && (
          <div className="flex-shrink-0 mr-3">
            <button
              onClick={onClose}
              className={`inline-flex rounded-md p-1.5 ${styles.icon} hover:bg-black hover:bg-opacity-10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600`}
            >
              <span className="sr-only">إغلاق</span>
              <X className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

// مكون مبسط لعرض رسائل الخطأ في النماذج
export function FormErrorMessage({ message }: { message: string }) {
  if (!message) return null
  
  return (
    <div className="mt-2 text-sm text-red-600 flex items-center">
      <AlertCircle className="w-4 h-4 ml-1" />
      {message}
    </div>
  )
} 