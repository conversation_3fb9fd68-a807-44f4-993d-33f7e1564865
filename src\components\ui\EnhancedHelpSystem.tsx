'use client'

import React, { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { 
  HelpCircle, 
  X, 
  ChevronRight, 
  ChevronLeft,
  Lightbulb,
  CheckCircle,
  AlertCircle,
  Info,
  FileText,
  Users,
  Target,
  Brain,
  Shield,
  BookOpen,
  Video,
  Download
} from 'lucide-react'
import { DownloadableGuide } from './DownloadableGuide'

interface HelpSystemProps {
  onClose: () => void
  currentStep: number
}

interface GuideSection {
  title: string
  icon: React.ReactNode
  content: string
  tips: string[]
  examples: string[]
  warnings: string[]
}

export function EnhancedHelpSystem({ onClose, currentStep }: HelpSystemProps) {
  const [activeTab, setActiveTab] = useState<'guide' | 'examples' | 'tips' | 'checklist' | 'download'>('guide')
  const [selectedSection, setSelectedSection] = useState<string | null>(null)

  const stepInfo = {
    1: {
      title: 'معلومات أساسية للمشروع',
      icon: <FileText className="w-6 h-6" />,
      color: 'blue',
      description: 'تحديد هوية المشروع والإطار الزمني'
    },
    2: {
      title: 'Find - العثور على المشكلة',
      icon: <Target className="w-6 h-6" />,
      color: 'orange',
      description: 'تحديد وقياس المشكلة بدقة'
    },
    3: {
      title: 'Organize - تنظيم المشروع',
      icon: <Users className="w-6 h-6" />,
      color: 'green',
      description: 'تشكيل الفريق وتخطيط الموارد'
    },
    4: {
      title: 'Clarify - توضيح العمليات',
      icon: <FileText className="w-6 h-6" />,
      color: 'blue',
      description: 'فهم العمليات المتأثرة'
    },
    5: {
      title: 'Understand - فهم الأسباب',
      icon: <Brain className="w-6 h-6" />,
      color: 'yellow',
      description: 'تحليل الأسباب الجذرية'
    },
    6: {
      title: 'Select - اختيار الحل',
      icon: <CheckCircle className="w-6 h-6" />,
      color: 'purple',
      description: 'تحديد الحل الأمثل'
    },
    7: {
      title: 'RAID Matrix - إدارة المخاطر',
      icon: <Shield className="w-6 h-6" />,
      color: 'red',
      description: 'تحديد المخاطر والتحديات'
    }
  }

  const currentStepInfo = stepInfo[currentStep as keyof typeof stepInfo]

  const guides = {
    1: {
      overview: {
        title: 'نظرة عامة على المرحلة الأولى',
        icon: <Info className="w-5 h-5" />,
        content: 'هذه المرحلة تهدف إلى تحديد الهوية الأساسية للمشروع والإطار الزمني للتنفيذ. المعلومات التي تدخلها هنا ستكون الأساس لجميع المراحل التالية.',
        tips: [
          'اختر اسماً واضحاً ومحدداً للمشروع',
          'اكتب وصفاً شاملاً يوضح الهدف والفائدة',
          'حدد تواريخ واقعية قابلة للتحقيق',
          'اختر الأولوية بناءً على تأثير المشكلة'
        ],
        examples: [
          'اسم المشروع: "تحسين وقت استجابة خدمة العملاء"',
          'الوصف: "مشروع يهدف إلى تقليل وقت انتظار العملاء من 10 دقائق إلى 3 دقائق"',
          'المدة: مشروع لمدة 3 أشهر مع مراحل تنفيذ واضحة'
        ],
        warnings: [
          'تجنب الأسماء العامة مثل "تحسين الخدمة"',
          'لا تضع تواريخ غير واقعية',
          'تأكد من توافق الوصف مع الهدف الحقيقي'
        ]
      },
      projectName: {
        title: 'كيفية اختيار اسم مناسب للمشروع',
        icon: <FileText className="w-5 h-5" />,
        content: 'اسم المشروع يجب أن يكون واضحاً ومحدداً ويعبر عن الهدف الأساسي من المشروع.',
        tips: [
          'استخدم كلمات محددة وواضحة',
          'اذكر المجال أو القسم المستهدف',
          'تجنب المصطلحات التقنية المعقدة',
          'اجعله قصيراً وسهل الفهم'
        ],
        examples: [
          'تحسين كفاءة عملية معالجة الطلبات',
          'تقليل وقت انتظار المرضى في العيادات',
          'رفع جودة خدمة الدعم الفني',
          'تطوير نظام إدارة المخزون'
        ],
        warnings: [
          'تجنب الأسماء الطويلة جداً',
          'لا تستخدم اختصارات غير مفهومة',
          'تجنب الأسماء العامة جداً'
        ]
      }
    },
    2: {
      overview: {
        title: 'فهم مرحلة العثور على المشكلة',
        icon: <Target className="w-5 h-5" />,
        content: 'هذه المرحلة الأهم في المشروع حيث نحدد المشكلة بدقة ونقيسها كمياً. بدون تحديد دقيق للمشكلة، لن نتمكن من حلها بفعالية.',
        tips: [
          'كن محدداً في وصف المشكلة',
          'استخدم أرقام وإحصائيات حقيقية',
          'اختر مؤشراً واحداً واضحاً',
          'تأكد من صحة اتجاه التحسن'
        ],
        examples: [
          'مشكلة: تأخير في معالجة الطلبات يؤدي لفقدان العملاء',
          'المؤشر: متوسط وقت معالجة الطلب',
          'القيمة الحالية: 5 أيام، المستهدفة: 2 يوم'
        ],
        warnings: [
          'لا تخلط بين المشكلة والحل',
          'تجنب الوصف العام غير المحدد',
          'لا تستخدم مؤشرات متعددة في نفس الوقت'
        ]
      },
      problemDescription: {
        title: 'كتابة وصف فعال للمشكلة',
        icon: <AlertCircle className="w-5 h-5" />,
        content: 'وصف المشكلة يجب أن يكون واضحاً ومحدداً ويحتوي على معلومات كافية لفهم طبيعة المشكلة وتأثيرها.',
        tips: [
          'ابدأ بوصف الوضع الحالي',
          'اذكر التأثير على العمل أو العملاء',
          'استخدم أرقام محددة عند الإمكان',
          'حدد متى وأين تحدث المشكلة'
        ],
        examples: [
          'يواجه قسم خدمة العملاء تأخيراً في الرد على الاستفسارات، حيث يصل وقت الانتظار إلى 15 دقيقة في المتوسط خلال ساعات الذروة (9-11 صباحاً)، مما يؤدي إلى زيادة الشكاوى بنسبة 30% وانخفاض رضا العملاء.',
          'تحدث أخطاء في إدخال البيانات في نظام المبيعات بمعدل 12 خطأ يومياً، مما يتطلب إعادة المعالجة ويؤخر إنجاز التقارير الشهرية بـ 3 أيام إضافية.'
        ],
        warnings: [
          'تجنب الوصف العام مثل "الخدمة بطيئة"',
          'لا تذكر الحلول في وصف المشكلة',
          'تجنب اللغة العاطفية أو الاتهامات'
        ]
      }
    }
  }

  const renderGuideContent = () => {
    const currentGuides = guides[currentStep as keyof typeof guides] || {}
    
    return (
      <div className="space-y-6">
        {Object.entries(currentGuides).map(([key, guide]) => (
          <Card key={key} className="p-4">
            <div className="flex items-center gap-3 mb-3">
              {guide.icon}
              <h3 className="font-semibold text-gray-900">{guide.title}</h3>
            </div>
            
            <p className="text-gray-700 mb-4">{guide.content}</p>
            
            {guide.tips.length > 0 && (
              <div className="mb-4">
                <h4 className="font-medium text-green-800 mb-2 flex items-center gap-2">
                  <Lightbulb className="w-4 h-4" />
                  نصائح مهمة
                </h4>
                <ul className="space-y-1">
                  {guide.tips.map((tip, index) => (
                    <li key={index} className="text-sm text-green-700 flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      {tip}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            {guide.examples.length > 0 && (
              <div className="mb-4">
                <h4 className="font-medium text-blue-800 mb-2 flex items-center gap-2">
                  <FileText className="w-4 h-4" />
                  أمثلة عملية
                </h4>
                <div className="space-y-2">
                  {guide.examples.map((example, index) => (
                    <div key={index} className="bg-blue-50 border border-blue-200 rounded p-3">
                      <p className="text-sm text-blue-800">{example}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {guide.warnings.length > 0 && (
              <div>
                <h4 className="font-medium text-red-800 mb-2 flex items-center gap-2">
                  <AlertCircle className="w-4 h-4" />
                  تجنب هذه الأخطاء
                </h4>
                <ul className="space-y-1">
                  {guide.warnings.map((warning, index) => (
                    <li key={index} className="text-sm text-red-700 flex items-start gap-2">
                      <X className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      {warning}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </Card>
        ))}
      </div>
    )
  }

  const renderExamples = () => (
    <div className="space-y-4">
      <Card className="p-4">
        <h3 className="font-semibold text-gray-900 mb-3">مثال كامل للمرحلة الحالية</h3>
        <div className="bg-gray-50 border rounded-lg p-4">
          <p className="text-sm text-gray-700">
            سيتم عرض مثال تطبيقي كامل لهذه المرحلة مع جميع الحقول المطلوبة
          </p>
        </div>
      </Card>
    </div>
  )

  const renderChecklist = () => (
    <div className="space-y-4">
      <Card className="p-4">
        <h3 className="font-semibold text-gray-900 mb-3">قائمة التحقق للمرحلة</h3>
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="text-sm">تم ملء جميع الحقول المطلوبة</span>
          </div>
          <div className="flex items-center gap-3">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="text-sm">تم مراجعة البيانات المدخلة</span>
          </div>
          <div className="flex items-center gap-3">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <span className="text-sm">تم التأكد من دقة المعلومات</span>
          </div>
        </div>
      </Card>
    </div>
  )

  const tabs = [
    { id: 'guide', label: 'الدليل', icon: <BookOpen className="w-4 h-4" /> },
    { id: 'examples', label: 'أمثلة', icon: <Lightbulb className="w-4 h-4" /> },
    { id: 'tips', label: 'نصائح', icon: <Info className="w-4 h-4" /> },
    { id: 'checklist', label: 'قائمة التحقق', icon: <CheckCircle className="w-4 h-4" /> },
    { id: 'download', label: 'تحميل', icon: <Download className="w-4 h-4" /> }
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* رأس النافذة */}
        <div className={`bg-${currentStepInfo.color}-50 border-b border-${currentStepInfo.color}-200 p-6`}>
          <div className="flex justify-between items-start">
            <div className="flex items-center gap-4">
              <div className={`p-3 bg-${currentStepInfo.color}-100 rounded-lg`}>
                {currentStepInfo.icon}
              </div>
              <div>
                <h2 className={`text-xl font-bold text-${currentStepInfo.color}-900`}>
                  دليل المساعدة - {currentStepInfo.title}
                </h2>
                <p className={`text-${currentStepInfo.color}-700 mt-1`}>
                  {currentStepInfo.description}
                </p>
              </div>
            </div>
            <Button onClick={onClose} variant="outline" size="sm">
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* علامات التبويب */}
        <div className="border-b border-gray-200">
          <div className="flex">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <div className="flex items-center gap-2">
                  {tab.icon}
                  {tab.label}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* محتوى النافذة */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {activeTab === 'guide' && renderGuideContent()}
          {activeTab === 'examples' && renderExamples()}
          {activeTab === 'tips' && renderExamples()}
          {activeTab === 'checklist' && renderChecklist()}
          {activeTab === 'download' && <DownloadableGuide currentStep={currentStep} />}
        </div>

        {/* تذييل النافذة */}
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              المرحلة {currentStep} من 7
            </div>
            <div className="flex gap-3">
              <Button variant="outline" onClick={onClose}>
                إغلاق
              </Button>
              <Button>
                <Download className="w-4 h-4 mr-2" />
                تحميل الدليل الكامل
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 