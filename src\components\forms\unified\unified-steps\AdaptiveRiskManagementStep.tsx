'use client'

import React, { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { FieldHelp } from '@/components/ui/HelpSystem'
import { StepHeader } from '../../shared/StepHeader'
import { RiskCard } from './risk-management/RiskCard'
import { RiskStatistics } from './risk-management/RiskStatistics'
import { 
  AlertTriangle, 
  Shield, 
  Plus, 
  Info
} from 'lucide-react'
import { FormType, UnifiedFormData, EnhancedImprovementData } from '../UnifiedProjectForm'

interface AdaptiveRiskManagementStepProps {
  formType: FormType
  data: UnifiedFormData
  updateData: (field: string, value: any) => void
  errors: Record<string, string>
}

interface Risk {
  id: string
  description: string
  probability: 'low' | 'medium' | 'high'
  impact: 'low' | 'medium' | 'high'
  mitigation?: string
}

export function AdaptiveRiskManagementStep({ 
  formType, 
  data, 
  updateData, 
  errors 
}: AdaptiveRiskManagementStepProps) {
  
  const enhancedData = data as EnhancedImprovementData
  const [showTips, setShowTips] = useState(false)
  
  // قائمة المخاطر الشائعة
  const commonRisks = [
    { description: 'تأخير في الجدولة الزمنية', probability: 'medium', impact: 'high' },
    { description: 'نقص الموارد البشرية', probability: 'high', impact: 'medium' },
    { description: 'تجاوز الميزانية المحددة', probability: 'medium', impact: 'high' },
    { description: 'مقاومة التغيير من الموظفين', probability: 'high', impact: 'medium' },
    { description: 'عدم توفر التقنيات المطلوبة', probability: 'low', impact: 'high' },
    { description: 'تغيير في أولويات المؤسسة', probability: 'medium', impact: 'medium' },
    { description: 'عدم دعم الإدارة العليا', probability: 'low', impact: 'high' },
    { description: 'صعوبة في التنسيق بين الأقسام', probability: 'medium', impact: 'medium' }
  ]

  const addRisk = (template?: any) => {
    const newRisk: Risk = {
      id: Date.now().toString(),
      description: template?.description || '',
      probability: template?.probability || 'medium',
      impact: template?.impact || 'medium',
      mitigation: ''
    }

    const currentRisks = enhancedData.risks || []
    const updatedRisks = [...currentRisks, newRisk]
    updateData('risks', updatedRisks)
  }

  const removeRisk = (id: string) => {
    const currentRisks = enhancedData.risks || []
    const updatedRisks = currentRisks.filter((risk: any) => risk.id !== id)
    updateData('risks', updatedRisks)
  }

  const updateRisk = (id: string, field: string, value: any) => {
    const currentRisks = enhancedData.risks || []
    const updatedRisks = currentRisks.map((risk: any) => 
      risk.id === id ? { ...risk, [field]: value } : risk
    )
    updateData('risks', updatedRisks)
  }

  const risks = enhancedData.risks || []

  return (
    <div className="space-y-6">
      {/* رأس الخطوة */}
      <StepHeader
        stepNumber={8}
        title="Risk Management - إدارة المخاطر"
        description="تحديد المخاطر المحتملة وخطط التخفيف للمشروع"
        icon={AlertTriangle}
        formType={formType}
      />

      {/* نصائح وإرشادات */}
      <Card className="p-6 bg-blue-50 border-blue-200">
        <div className="flex items-center justify-between mb-4">
          <h4 className="font-semibold text-blue-900">💡 نصائح لتحديد المخاطر</h4>
          <Button
            onClick={() => setShowTips(!showTips)}
            variant="ghost"
            size="sm"
            className="text-blue-600 hover:text-blue-700"
          >
            <Info className="w-4 h-4 mr-2" />
            {showTips ? 'إخفاء النصائح' : 'عرض النصائح'}
          </Button>
        </div>

        {showTips && (
          <div className="space-y-3 text-sm text-blue-800">
            <div>
              <strong>احتمالية الحدوث:</strong>
              <ul className="mt-1 space-y-1 text-xs">
                <li>• <strong>منخفض:</strong> احتمال حدوث أقل من 30%</li>
                <li>• <strong>متوسط:</strong> احتمال حدوث 30-70%</li>
                <li>• <strong>عالي:</strong> احتمال حدوث أكثر من 70%</li>
              </ul>
            </div>
            <div>
              <strong>درجة التأثير:</strong>
              <ul className="mt-1 space-y-1 text-xs">
                <li>• <strong>منخفض:</strong> تأثير طفيف على المشروع</li>
                <li>• <strong>متوسط:</strong> تأثير ملحوظ قد يؤخر المشروع</li>
                <li>• <strong>عالي:</strong> تأثير كبير قد يفشل المشروع</li>
              </ul>
            </div>
            <div>
              <strong>خطة التخفيف:</strong> كيف ستمنع أو تقلل من تأثير المخاطر
            </div>
          </div>
        )}
      </Card>

      {/* إحصائيات المخاطر */}
      <RiskStatistics risks={risks} />

      {/* قائمة المخاطر الشائعة */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h4 className="text-lg font-semibold text-gray-900">المخاطر الشائعة</h4>
          <Button
            onClick={() => addRisk()}
            variant="secondary"
            size="sm"
          >
            <Plus className="w-4 h-4 mr-2" />
            إضافة مخاطر مخصص
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-6">
          {commonRisks.map((risk, index) => (
            <Button
              key={index}
              onClick={() => addRisk(risk)}
              variant="ghost"
              size="sm"
              className="text-left justify-start h-auto p-3 hover:bg-gray-50 border border-gray-200"
            >
              <div>
                <div className="font-medium text-gray-900">{risk.description}</div>
                <div className="text-xs text-gray-500">
                  احتمالية: {risk.probability === 'low' ? 'منخفض' : risk.probability === 'medium' ? 'متوسط' : 'عالي'} | 
                  تأثير: {risk.impact === 'low' ? 'منخفض' : risk.impact === 'medium' ? 'متوسط' : 'عالي'}
                </div>
              </div>
            </Button>
          ))}
        </div>

        {/* المخاطر المضافة */}
        {risks.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <AlertTriangle className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            <p>لم يتم إضافة مخاطر بعد</p>
            <p className="text-xs mt-1">اختر من المخاطر الشائعة أعلاه أو أضف مخاطر مخصص</p>
          </div>
        ) : (
          <div className="space-y-4">
            {risks.map((risk: any, index: number) => (
              <RiskCard
                key={risk.id}
                risk={risk}
                index={index}
                onUpdate={updateRisk}
                onRemove={removeRisk}
              />
            ))}
          </div>
        )}
      </Card>

      {/* نصائح للمبتدئين */}
      <Card className="p-6 bg-green-50 border-green-200">
        <h4 className="font-semibold text-green-900 mb-3">🎯 نصائح للمبتدئين</h4>
        <ul className="text-sm text-green-800 space-y-2">
          <li>• ابدأ بـ 3-5 مخاطر رئيسية، لا تضع قائمة طويلة</li>
          <li>• ركز على المخاطر التي يمكن أن تحدث فعلاً في بيئة عملك</li>
          <li>• ضع خطة تخفيف عملية وقابلة للتنفيذ</li>
          <li>• راجع المخاطر بانتظام أثناء تنفيذ المشروع</li>
          <li>• استشر زملاءك أو مدراءك في تحديد المخاطر</li>
        </ul>
      </Card>
    </div>
  )
} 