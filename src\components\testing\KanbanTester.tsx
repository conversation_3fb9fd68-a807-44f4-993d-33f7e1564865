'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/auth'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/Button'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { CheckCircle, XCircle, Play, Calendar, Users, Target } from 'lucide-react'

interface Project {
  id: string
  title: string
  description: string
  status: string
  created_at: string
  project_manager_id?: string
}

interface KanbanTest {
  id: string
  name: string
  status: 'pending' | 'running' | 'passed' | 'failed'
  message: string
  data?: any
}

const PROJECT_STATUSES = [
  { key: 'planning', name: 'التخطيط', color: 'bg-blue-100 text-blue-800' },
  { key: 'in_progress', name: 'قيد التنفيذ', color: 'bg-yellow-100 text-yellow-800' },
  { key: 'review', name: 'المراجعة', color: 'bg-purple-100 text-purple-800' },
  { key: 'completed', name: 'مكتمل', color: 'bg-green-100 text-green-800' },
  { key: 'on_hold', name: 'متوقف', color: 'bg-gray-100 text-gray-800' }
]

export default function KanbanTester() {
  const { user } = useAuth()
  const [tests, setTests] = useState<KanbanTest[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [testProjects, setTestProjects] = useState<Project[]>([])
  const [projectsByStatus, setProjectsByStatus] = useState<Record<string, Project[]>>({})

  const updateTest = (id: string, status: KanbanTest['status'], message: string, data?: any) => {
    setTests(prev => prev.map(test => 
      test.id === id ? { ...test, status, message, data } : test
    ))
  }

  const initializeTests = () => {
    setTests([
      { id: 'create_projects', name: 'إنشاء مشاريع تجريبية', status: 'pending', message: 'في انتظار التنفيذ' },
      { id: 'fetch_projects', name: 'جلب المشاريع', status: 'pending', message: 'في انتظار التنفيذ' },
      { id: 'organize_kanban', name: 'تنظيم لوحة Kanban', status: 'pending', message: 'في انتظار التنفيذ' },
      { id: 'move_projects', name: 'نقل المشاريع بين الحالات', status: 'pending', message: 'في انتظار التنفيذ' },
      { id: 'update_status', name: 'تحديث حالة المشاريع', status: 'pending', message: 'في انتظار التنفيذ' }
    ])
  }

  const createTestProjects = async () => {
    updateTest('create_projects', 'running', 'جاري إنشاء مشاريع تجريبية...')
    
    try {
      const projectsData = [
        {
          title: 'مشروع اختبار التخطيط',
          description: 'مشروع تجريبي في مرحلة التخطيط',
          status: 'planning',
          project_manager_id: user?.id,
          created_from_request: false,
          start_date: new Date().toISOString().split('T')[0],
          end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          form_data: { test: true, kanban_test: true }
        },
        {
          title: 'مشروع اختبار التنفيذ',
          description: 'مشروع تجريبي قيد التنفيذ',
          status: 'in_progress',
          project_manager_id: user?.id,
          created_from_request: false,
          start_date: new Date().toISOString().split('T')[0],
          end_date: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          form_data: { test: true, kanban_test: true }
        },
        {
          title: 'مشروع اختبار المراجعة',
          description: 'مشروع تجريبي في مرحلة المراجعة',
          status: 'review',
          project_manager_id: user?.id,
          created_from_request: false,
          start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          end_date: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          form_data: { test: true, kanban_test: true }
        },
        {
          title: 'مشروع اختبار مكتمل',
          description: 'مشروع تجريبي مكتمل',
          status: 'completed',
          project_manager_id: user?.id,
          created_from_request: false,
          start_date: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          end_date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          form_data: { test: true, kanban_test: true }
        }
      ]

      const { data, error } = await supabase
        .from('projects')
        .insert(projectsData)
        .select()

      if (error) throw error

      setTestProjects(data)
      updateTest('create_projects', 'passed', `تم إنشاء ${data.length} مشاريع تجريبية`, data)
      return data
    } catch (error) {
      updateTest('create_projects', 'failed', `فشل في إنشاء المشاريع: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
      throw error
    }
  }

  const fetchProjects = async () => {
    updateTest('fetch_projects', 'running', 'جاري جلب المشاريع...')
    
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('form_data->kanban_test', true)
        .order('created_at', { ascending: false })

      if (error) throw error

      updateTest('fetch_projects', 'passed', `تم جلب ${data.length} مشروع`, data)
      return data
    } catch (error) {
      updateTest('fetch_projects', 'failed', `فشل في جلب المشاريع: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
      throw error
    }
  }

  const organizeKanban = async (projects: Project[]) => {
    updateTest('organize_kanban', 'running', 'جاري تنظيم لوحة Kanban...')
    
    try {
      const organized = PROJECT_STATUSES.reduce((acc, status) => {
        acc[status.key] = projects.filter(project => project.status === status.key)
        return acc
      }, {} as Record<string, Project[]>)

      setProjectsByStatus(organized)
      
      const summary = PROJECT_STATUSES.map(status => 
        `${status.name}: ${organized[status.key]?.length || 0}`
      ).join(', ')

      updateTest('organize_kanban', 'passed', `تم تنظيم المشاريع - ${summary}`, organized)
      return organized
    } catch (error) {
      updateTest('organize_kanban', 'failed', `فشل في تنظيم Kanban: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
      throw error
    }
  }

  const moveProjectBetweenStatuses = async () => {
    updateTest('move_projects', 'running', 'جاري اختبار نقل المشاريع...')
    
    try {
      // البحث عن مشروع في مرحلة التخطيط لنقله إلى قيد التنفيذ
      const planningProjects = testProjects.filter(p => p.status === 'planning')
      
      if (planningProjects.length === 0) {
        throw new Error('لا توجد مشاريع في مرحلة التخطيط للاختبار')
      }

      const projectToMove = planningProjects[0]
      
      const { data, error } = await supabase
        .from('projects')
        .update({ status: 'in_progress' })
        .eq('id', projectToMove.id)
        .select()

      if (error) throw error

      updateTest('move_projects', 'passed', `تم نقل المشروع "${projectToMove.title}" من التخطيط إلى قيد التنفيذ`, data)
      return data
    } catch (error) {
      updateTest('move_projects', 'failed', `فشل في نقل المشروع: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
      throw error
    }
  }

  const updateProjectStatus = async () => {
    updateTest('update_status', 'running', 'جاري اختبار تحديث الحالة...')
    
    try {
      // تحديث حالة مشروع من قيد التنفيذ إلى المراجعة
      const inProgressProjects = testProjects.filter(p => p.status === 'in_progress')
      
      if (inProgressProjects.length === 0) {
        updateTest('update_status', 'passed', 'لا توجد مشاريع قيد التنفيذ للاختبار')
        return
      }

      const projectToUpdate = inProgressProjects[0]
      
      const { data, error } = await supabase
        .from('projects')
        .update({ 
          status: 'review',
          updated_at: new Date().toISOString()
        })
        .eq('id', projectToUpdate.id)
        .select()

      if (error) throw error

      updateTest('update_status', 'passed', `تم تحديث حالة المشروع "${projectToUpdate.title}" إلى المراجعة`, data)
      return data
    } catch (error) {
      updateTest('update_status', 'failed', `فشل في تحديث الحالة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
      throw error
    }
  }

  const runFullKanbanTest = async () => {
    setIsRunning(true)
    initializeTests()

    try {
      // 1. إنشاء مشاريع تجريبية
      const projects = await createTestProjects()
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 2. جلب المشاريع
      const fetchedProjects = await fetchProjects()
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 3. تنظيم لوحة Kanban
      await organizeKanban(fetchedProjects || projects)
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 4. نقل المشاريع بين الحالات
      await moveProjectBetweenStatuses()
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 5. تحديث حالة المشاريع
      await updateProjectStatus()

    } catch (error) {
      console.error('خطأ في اختبار Kanban:', error)
    }

    setIsRunning(false)
  }

  const cleanupTestData = async () => {
    try {
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('form_data->kanban_test', true)

      if (error) throw error

      setTestProjects([])
      setProjectsByStatus({})
      
      const cleanupTest: KanbanTest = {
        id: 'cleanup',
        name: 'تنظيف البيانات',
        status: 'passed',
        message: 'تم حذف المشاريع التجريبية بنجاح'
      }
      
      setTests(prev => [...prev, cleanupTest])
    } catch (error) {
      const cleanupTest: KanbanTest = {
        id: 'cleanup',
        name: 'تنظيف البيانات',
        status: 'failed',
        message: `فشل في حذف البيانات: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`
      }
      
      setTests(prev => [...prev, cleanupTest])
    }
  }

  const getStatusIcon = (status: KanbanTest['status']) => {
    switch (status) {
      case 'passed': return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'failed': return <XCircle className="w-5 h-5 text-red-500" />
      case 'running': return <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
      default: return <Calendar className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: KanbanTest['status']) => {
    switch (status) {
      case 'passed': return 'bg-green-50 border-green-200'
      case 'failed': return 'bg-red-50 border-red-200'
      case 'running': return 'bg-blue-50 border-blue-200'
      default: return 'bg-gray-50 border-gray-200'
    }
  }

  const passedTests = tests.filter(t => t.status === 'passed').length
  const failedTests = tests.filter(t => t.status === 'failed').length

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">اختبار لوحة Kanban وإدارة المشاريع</h2>
          <p className="text-gray-600">
            اختبر إنشاء وتنظيم ونقل المشاريع في لوحة Kanban
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <Button 
              onClick={runFullKanbanTest}
              disabled={isRunning}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Play className="w-4 h-4 mr-2" />
              {isRunning ? 'جاري الاختبار...' : 'اختبار Kanban الكامل'}
            </Button>
            
            <Button 
              onClick={cleanupTestData}
              disabled={isRunning}
              variant="danger"
            >
              تنظيف البيانات التجريبية
            </Button>
          </div>

          {/* إحصائيات */}
          {tests.length > 0 && (
            <div className="grid grid-cols-3 gap-4 mb-6">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{tests.length}</div>
                <div className="text-sm text-gray-600">إجمالي الاختبارات</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{passedTests}</div>
                <div className="text-sm text-gray-600">نجح</div>
              </div>
              <div className="text-center p-3 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">{failedTests}</div>
                <div className="text-sm text-gray-600">فشل</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* لوحة Kanban المصغرة */}
      {Object.keys(projectsByStatus).length > 0 && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">معاينة لوحة Kanban</h3>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {PROJECT_STATUSES.map(status => (
                <div key={status.key} className="bg-gray-50 rounded-lg p-4">
                  <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium mb-3 ${status.color}`}>
                    {status.name} ({projectsByStatus[status.key]?.length || 0})
                  </div>
                  <div className="space-y-2">
                    {projectsByStatus[status.key]?.map(project => (
                      <div key={project.id} className="bg-white p-3 rounded-md shadow-sm border">
                        <h4 className="font-medium text-sm">{project.title}</h4>
                        <p className="text-xs text-gray-500 mt-1">{project.description}</p>
                        <div className="flex items-center gap-2 mt-2 text-xs text-gray-400">
                          <Users className="w-3 h-3" />
                          <span>مدير المشروع</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* نتائج الاختبارات */}
      {tests.length > 0 && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">نتائج اختبار Kanban</h3>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {tests.map((test) => (
                <div
                  key={test.id}
                  className={`p-4 rounded-lg border-2 ${getStatusColor(test.status)}`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(test.status)}
                      <div>
                        <h4 className="font-medium">{test.name}</h4>
                        <p className="text-sm text-gray-600">{test.message}</p>
                      </div>
                    </div>
                  </div>
                  
                  {test.data && (
                    <div className="mt-2">
                      <details>
                        <summary className="cursor-pointer text-sm font-medium text-gray-700">
                          عرض التفاصيل
                        </summary>
                        <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                          {JSON.stringify(test.data, null, 2)}
                        </pre>
                      </details>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 