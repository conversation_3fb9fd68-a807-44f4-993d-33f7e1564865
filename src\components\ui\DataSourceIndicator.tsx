'use client'

import { useEffect, useState } from 'react'
import { systemInfo, checkDataSource } from '@/lib/supabase'
import { Card } from './Card'
import { Typography } from './Typography'

interface DataSourceInfo {
  isDevelopmentMode: boolean
  supabaseUrl: string | undefined
  isConnected: boolean
}

export function DataSourceIndicator() {
  const [dataSource, setDataSource] = useState<DataSourceInfo | null>(null)

  useEffect(() => {
    // فحص مصدر البيانات
    checkDataSource()
    setDataSource(systemInfo)
  }, [])

  if (!dataSource) return null

  return (
    <Card className="mb-6 border-l-4 border-l-blue-500">
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <Typography variant="main" className="font-semibold text-gray-900">
              مصدر البيانات الحالي
            </Typography>
            <div className="mt-2 space-y-1">
              {dataSource.isDevelopmentMode ? (
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <Typography variant="body" className="text-yellow-700">
                    وضع التطوير - بيانات محاكاة محلية
                  </Typography>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <Typography variant="body" className="text-green-700">
                    قاعدة البيانات الحقيقية - Supabase
                  </Typography>
                </div>
              )}
              
              {dataSource.supabaseUrl && (
                <Typography variant="body" className="text-gray-600 text-sm">
                  الرابط: {dataSource.supabaseUrl}
                </Typography>
              )}
            </div>
          </div>
          
          <div className="text-right">
            <button
              onClick={() => {
                checkDataSource()
                console.log('تم فحص مصدر البيانات - تحقق من وحدة التحكم')
              }}
              className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
            >
              فحص مصدر البيانات
            </button>
          </div>
        </div>
      </div>
    </Card>
  )
} 