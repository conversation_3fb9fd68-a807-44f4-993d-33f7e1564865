import SimpleLoginTest from '@/components/testing/SimpleLoginTest'
import Link from 'next/link'
import { ArrowLeft, Shield } from 'lucide-react'

export default function AuthTestPage() {
  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto py-8">
        {/* Navigation */}
        <div className="mb-6">
          <Link 
            href="/testing" 
            className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            العودة إلى لوحة الاختبارات
          </Link>
        </div>

        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Shield className="w-8 h-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">
              اختبار تسجيل الدخول
            </h1>
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            اختبار بسيط لتسجيل الدخول مع المستخدمين المُعاد إنشاؤهم
          </p>
        </div>
        
        <SimpleLoginTest />
      </div>
    </div>
  )
}
