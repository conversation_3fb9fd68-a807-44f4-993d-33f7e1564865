'use client'

import React from 'react'
import { Input, Select } from '@/components/ui/Input'
import { Card } from '@/components/ui/Card'
import { FieldHelp } from '@/components/ui/HelpSystem'
import { useFormErrorHandler, commonValidationRules } from '@/lib/errorHandler'
import { FileText, Building, User, Phone, Mail } from 'lucide-react'

interface BasicInfoSectionProps {
  data: any
  updateData: (field: string, value: any) => void
  errors: Record<string, string>
  departments: string[]
}

export function BasicInfoSection({ data, updateData, errors, departments }: BasicInfoSectionProps) {
  const { validateField } = useFormErrorHandler()

  const handleFieldChange = (field: string, value: any) => {
    updateData(field, value)
    
    // التحقق المباشر من الحقول
    if (field === 'projectTitle') {
      validateField(field, value, commonValidationRules.projectTitle)
    } else if (field === 'leadPhone') {
      validateField(field, value, commonValidationRules.phone)
    } else if (field === 'leadEmail') {
      validateField(field, value, commonValidationRules.email)
    }
  }

  return (
    <div className="space-y-6">
      {/* معلومات المشروع الأساسية */}
      <Card className="p-6 border-yellow-200 bg-yellow-50">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center font-bold">
            <FileText className="w-4 h-4" />
          </div>
          <h3 className="text-lg font-bold text-yellow-800">معلومات المشروع الأساسية</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              عنوان المشروع *
            </label>
            <FieldHelp 
              field="projectTitle" 
              step={1} 
              content="اختر عنواناً واضحاً ومحدداً يعبر عن هدف المشروع بشكل مباشر" 
            />
            <Input
              value={data.projectTitle || ''}
              onChange={(e) => updateData('projectTitle', e.target.value)}
              placeholder="مثال: تحسين وقت الاستجابة لخدمة العملاء"
              error={errors.projectTitle}
              className="mt-1"
              maxLength={200}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              القسم *
            </label>
            <FieldHelp 
              field="section" 
              step={1} 
              content="اختر القسم المسؤول عن تنفيذ هذا المشروع" 
            />
            <Select
              value={data.section || ''}
              onChange={(e) => updateData('section', e.target.value)}
              error={errors.section}
              className="mt-1"
            >
              <option value="">اختر القسم</option>
              {departments.map((dept) => (
                <option key={dept} value={dept}>
                  {dept}
                </option>
              ))}
            </Select>
          </div>
        </div>
      </Card>

      {/* معلومات منفذ المشروع */}
      <Card className="p-6 border-yellow-200 bg-yellow-50">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center font-bold">
            <User className="w-4 h-4" />
          </div>
          <h3 className="text-lg font-bold text-yellow-800">معلومات منفذ المشروع</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              اسم منفذ المشروع *
            </label>
            <FieldHelp 
              field="projectLead" 
              step={1} 
              content="الشخص المسؤول عن تنفيذ ومتابعة المشروع" 
            />
            <Input
              value={data.projectExecutor?.name || ''}
              onChange={(e) => updateData('projectExecutor.name', e.target.value)}
              placeholder="اسم المسؤول عن تنفيذ المشروع"
              error={errors['projectExecutor.name']}
              className="mt-1"
              readOnly={false}
              maxLength={100}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              رقم الجوال
            </label>
            <FieldHelp 
              field="leadPhone" 
              step={1} 
              content="رقم جوال منفذ المشروع للتواصل (05XXXXXXXX)" 
            />
            <Input
              value={data.projectExecutor?.phone || ''}
              onChange={(e) => updateData('projectExecutor.phone', e.target.value)}
              placeholder="05XXXXXXXX"
              error={errors['projectExecutor.phone']}
              className="mt-1"
              readOnly={false}
              maxLength={15}
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              البريد الإلكتروني
            </label>
            <FieldHelp 
              field="leadEmail" 
              step={1} 
              content="البريد الإلكتروني لمنفذ المشروع للمتابعة والتواصل" 
            />
            <Input
              type="email"
              value={data.projectExecutor?.email || ''}
              onChange={(e) => updateData('projectExecutor.email', e.target.value)}
              placeholder="<EMAIL>"
              error={errors['projectExecutor.email']}
              className="mt-1"
              readOnly={false}
              maxLength={100}
            />
          </div>
        </div>
      </Card>

      {/* التواريخ والمدة */}
      <Card className="p-6 border-yellow-200 bg-yellow-50">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center font-bold">
            📅
          </div>
          <h3 className="text-lg font-bold text-yellow-800">الجدولة الزمنية</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              تاريخ البداية المتوقع *
            </label>
            <FieldHelp 
              field="startDate" 
              step={1} 
              content="حدد تاريخ بداية المشروع (يفضل خلال أسبوعين من تاريخ اليوم)" 
            />
            <Input
              type="date"
              value={data.projectExecutor?.startDate || ''}
              onChange={(e) => updateData('projectExecutor.startDate', e.target.value)}
              error={errors['projectExecutor.startDate']}
              className="mt-1"
              min={new Date().toISOString().split('T')[0]}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              مدة التنفيذ *
            </label>
            <FieldHelp 
              field="implementationWeeks" 
              step={1} 
              content="اختر مدة تنفيذ المشروع. كويك وين يجب ألا يتجاوز 4 أسابيع" 
            />
            <Select
              value={data.solution?.implementationWeeks?.toString() || ''}
              onChange={(e) => updateData('solution.implementationWeeks', parseInt(e.target.value))}
              error={errors['solution.implementationWeeks']}
              className="mt-1"
            >
              <option value="">اختر المدة</option>
              <option value="1">أسبوع واحد</option>
              <option value="2">أسبوعين</option>
              <option value="3">3 أسابيع</option>
              <option value="4">4 أسابيع</option>
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              التكلفة التقديرية (ريال)
            </label>
            <FieldHelp 
              field="estimatedCost" 
              step={1} 
              content="التكلفة المتوقعة للمشروع بالريال السعودي (اختياري)" 
            />
            <Input
              type="number"
              value={data.solution?.estimatedCost || ''}
              onChange={(e) => updateData('solution.estimatedCost', parseFloat(e.target.value) || 0)}
              placeholder="0"
              min="0"
              error={errors['solution.estimatedCost']}
              className="mt-1"
            />
          </div>
        </div>
      </Card>
    </div>
  )
} 