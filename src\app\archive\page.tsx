'use client'

import { useState, useEffect } from 'react'
import { useAuth, usePermissions } from '@/lib/auth'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  Archive, 
  Search, 
  Filter, 
  Calendar, 
  FileText, 
  BarChart3,
  Eye,
  Download,
  RestoreIcon,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  User,
  Building
} from 'lucide-react'

export default function ArchivePage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [typeFilter, setTypeFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('all')
  const [archivedItems, setArchivedItems] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  
  const { user } = useAuth()
  const permissions = usePermissions()

  useEffect(() => {
    fetchArchivedItems()
  }, [])

  const fetchArchivedItems = async () => {
    try {
      setLoading(true)
      // محاكاة بيانات الأرشيف
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockData = [
        {
          id: 1,
          title: 'تحسين عملية الموافقات',
          type: 'project',
          status: 'completed',
          created_at: '2024-01-15',
          archived_at: '2024-02-20',
          user_name: 'أحمد محمد',
          department: 'تقنية المعلومات',
          description: 'مشروع لتحسين سير عمل الموافقات في النظام'
        },
        {
          id: 2,
          title: 'طلب تطوير نظام جديد',
          type: 'request',
          status: 'rejected',
          created_at: '2024-01-10',
          archived_at: '2024-01-25',
          user_name: 'سارة أحمد',
          department: 'الموارد البشرية',
          description: 'طلب تطوير نظام إدارة الموارد البشرية'
        },
        {
          id: 3,
          title: 'مشروع كويك وين - تحسين الواجهة',
          type: 'project',
          status: 'completed',
          created_at: '2024-01-05',
          archived_at: '2024-02-10',
          user_name: 'محمد علي',
          department: 'التسويق',
          description: 'تحسين واجهة المستخدم للنظام'
        }
      ]
      
      setArchivedItems(mockData)
    } catch (error) {
      console.error('Error fetching archived items:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'rejected': return <XCircle className="w-4 h-4 text-red-500" />
      case 'cancelled': return <Clock className="w-4 h-4 text-gray-500" />
      default: return <FileText className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'مكتمل'
      case 'rejected': return 'مرفوض'
      case 'cancelled': return 'ملغي'
      default: return 'غير محدد'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800'
      case 'rejected': return 'bg-red-100 text-red-800'
      case 'cancelled': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'project': return <BarChart3 className="w-4 h-4 text-blue-500" />
      case 'request': return <FileText className="w-4 h-4 text-purple-500" />
      default: return <FileText className="w-4 h-4 text-gray-500" />
    }
  }

  const getTypeText = (type: string) => {
    switch (type) {
      case 'project': return 'مشروع'
      case 'request': return 'طلب'
      default: return 'غير محدد'
    }
  }

  const filteredItems = archivedItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.user_name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter
    const matchesType = typeFilter === 'all' || item.type === typeFilter
    return matchesSearch && matchesStatus && matchesType
  })

  const stats = {
    total: archivedItems.length,
    completed: archivedItems.filter(item => item.status === 'completed').length,
    rejected: archivedItems.filter(item => item.status === 'rejected').length,
    projects: archivedItems.filter(item => item.type === 'project').length,
    requests: archivedItems.filter(item => item.type === 'request').length
  }

  const handleRestore = async (itemId: number) => {
    if (confirm('هل تريد استعادة هذا العنصر من الأرشيف؟')) {
      try {
        // محاكاة استعادة العنصر
        await new Promise(resolve => setTimeout(resolve, 1000))
        alert('تم استعادة العنصر بنجاح!')
        fetchArchivedItems()
      } catch (error) {
        console.error('Error restoring item:', error)
        alert('حدث خطأ أثناء استعادة العنصر')
      }
    }
  }

  const handlePermanentDelete = async (itemId: number) => {
    if (confirm('هل تريد حذف هذا العنصر نهائياً؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      try {
        // محاكاة حذف العنصر نهائياً
        await new Promise(resolve => setTimeout(resolve, 1000))
        alert('تم حذف العنصر نهائياً!')
        fetchArchivedItems()
      } catch (error) {
        console.error('Error deleting item:', error)
        alert('حدث خطأ أثناء حذف العنصر')
      }
    }
  }

  if (loading) {
    return (
      <ProtectedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل الأرشيف...</p>
          </div>
        </div>
      </ProtectedLayout>
    )
  }

  return (
    <ProtectedLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Page Header */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">الأرشيف</h1>
            <p className="text-gray-600 mt-1">
              عرض وإدارة الطلبات والمشاريع المؤرشفة
            </p>
          </div>
          
          <div className="flex flex-wrap gap-3">
            <Button
              variant="ghost"
              icon={<Download className="w-4 h-4" />}
            >
              تصدير الأرشيف
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Archive className="w-5 h-5 text-blue-600" />
                </div>
                <div className="mr-3">
                  <p className="text-sm font-medium text-gray-600">إجمالي المؤرشف</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                </div>
                <div className="mr-3">
                  <p className="text-sm font-medium text-gray-600">مكتملة</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.completed}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <XCircle className="w-5 h-5 text-red-600" />
                </div>
                <div className="mr-3">
                  <p className="text-sm font-medium text-gray-600">مرفوضة</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.rejected}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <BarChart3 className="w-5 h-5 text-purple-600" />
                </div>
                <div className="mr-3">
                  <p className="text-sm font-medium text-gray-600">مشاريع</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.projects}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <FileText className="w-5 h-5 text-orange-600" />
                </div>
                <div className="mr-3">
                  <p className="text-sm font-medium text-gray-600">طلبات</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.requests}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="البحث في الأرشيف..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              
              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">جميع الحالات</option>
                  <option value="completed">مكتملة</option>
                  <option value="rejected">مرفوضة</option>
                  <option value="cancelled">ملغية</option>
                </select>

                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">جميع الأنواع</option>
                  <option value="project">مشاريع</option>
                  <option value="request">طلبات</option>
                </select>

                <select
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">جميع التواريخ</option>
                  <option value="last_week">آخر أسبوع</option>
                  <option value="last_month">آخر شهر</option>
                  <option value="last_year">آخر سنة</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Archive List */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">
              العناصر المؤرشفة ({filteredItems.length})
            </h3>
          </CardHeader>
          
          <CardContent>
            {filteredItems.length === 0 ? (
              <div className="text-center py-12">
                <Archive className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد عناصر في الأرشيف</h3>
                <p className="text-gray-600">لا توجد عناصر تطابق معايير البحث</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredItems.map((item) => (
                  <div
                    key={item.id}
                    className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          {getTypeIcon(item.type)}
                          <h4 className="font-semibold text-gray-900">{item.title}</h4>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(item.status)}`}>
                            {getStatusIcon(item.status)}
                            <span className="mr-1">{getStatusText(item.status)}</span>
                          </span>
                          <span className="bg-gray-100 text-gray-700 px-2 py-1 text-xs rounded-full">
                            {getTypeText(item.type)}
                          </span>
                        </div>
                        
                        <p className="text-gray-600 text-sm mb-2 line-clamp-2">
                          {item.description}
                        </p>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <div className="flex items-center gap-1">
                            <User className="w-4 h-4" />
                            <span>{item.user_name}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Building className="w-4 h-4" />
                            <span>{item.department}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            <span>أُرشف في: {new Date(item.archived_at).toLocaleDateString('ar-SA')}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          icon={<Eye className="w-4 h-4" />}
                          title="عرض التفاصيل"
                        >
                          عرض
                        </Button>
                        
                        {permissions.canManageArchive && (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              icon={<RestoreIcon className="w-4 h-4" />}
                              onClick={() => handleRestore(item.id)}
                              title="استعادة من الأرشيف"
                            >
                              استعادة
                            </Button>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              icon={<Trash2 className="w-4 h-4" />}
                              onClick={() => handlePermanentDelete(item.id)}
                              className="text-red-600 hover:bg-red-50"
                              title="حذف نهائياً"
                            >
                              حذف
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </ProtectedLayout>
  )
} 