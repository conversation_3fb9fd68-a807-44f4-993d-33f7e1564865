-- إضافة عمود is_read لجدول الإشعارات
ALTER TABLE notifications 
ADD COLUMN IF NOT EXISTS is_read BOOLEAN DEFAULT false;

-- تحديث القيم الموجودة
UPDATE notifications 
SET is_read = (read_at IS NOT NULL)
WHERE is_read IS NULL;

-- إضافة trigger لتحديث is_read تلقائياً عند تحديث read_at
CREATE OR REPLACE FUNCTION update_notification_is_read()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.read_at IS NOT NULL AND OLD.read_at IS NULL THEN
    NEW.is_read = true;
  ELSIF NEW.read_at IS NULL AND OLD.read_at IS NOT NULL THEN
    NEW.is_read = false;
  END IF;
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_notification_is_read_trigger
  BEFORE UPDATE ON notifications
  FOR EACH ROW
  EXECUTE FUNCTION update_notification_is_read();

-- إضافة فهرس للأداء
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);

-- إضافة عمود data لحفظ بيانات إضافية
ALTER TABLE notifications 
ADD COLUMN IF NOT EXISTS data JSONB DEFAULT '{}';

-- تحديث الإشعارات الموجودة
UPDATE notifications 
SET data = '{}'
WHERE data IS NULL; 