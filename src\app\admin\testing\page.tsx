'use client'

import { useState } from 'react'
import { useAuth, usePermissions } from '@/lib/auth'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  TestTube, 
  Database, 
  Users, 
  FileText, 
  Settings, 
  Bell,
  Zap,
  Building,
  Shield,
  Activity,
  ExternalLink,
  Play,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react'

export default function TestingPage() {
  const { user, userRole } = useAuth()
  const permissions = usePermissions()
  const [testResults, setTestResults] = useState<Record<string, 'idle' | 'running' | 'success' | 'error'>>({})

  const testCategories = [
    {
      id: 'system',
      title: 'اختبارات النظام',
      description: 'اختبار المكونات الأساسية للنظام',
      icon: Settings,
      color: 'blue',
      tests: [
        {
          id: 'database-connection',
          name: 'اتصال قاعدة البيانات',
          description: 'اختبار الاتصال بقاعدة البيانات',
          path: '/database'
        },
        {
          id: 'api-endpoints',
          name: 'نقاط النهاية API',
          description: 'اختبار جميع نقاط النهاية',
          path: '/api/test'
        },
        {
          id: 'authentication',
          name: 'نظام المصادقة',
          description: 'اختبار تسجيل الدخول والصلاحيات',
          path: '/auth/test'
        }
      ]
    },
    {
      id: 'forms',
      title: 'اختبارات النماذج',
      description: 'اختبار النماذج والطلبات',
      icon: FileText,
      color: 'green',
      tests: [
        {
          id: 'unified-form',
          name: 'النموذج الموحد',
          description: 'اختبار النموذج الموحد للطلبات',
          path: '/unified-test'
        },
        {
          id: 'form-validation',
          name: 'التحقق من النماذج',
          description: 'اختبار التحقق من صحة البيانات',
          path: '/forms/validation-test'
        }
      ]
    },
    {
      id: 'departments',
      title: 'اختبارات الأقسام',
      description: 'اختبار إدارة الأقسام والمستخدمين',
      icon: Building,
      color: 'purple',
      tests: [
        {
          id: 'departments-management',
          name: 'إدارة الأقسام',
          description: 'اختبار إدارة الأقسام والهيكل التنظيمي',
          path: '/test-departments'
        },
        {
          id: 'users-permissions',
          name: 'صلاحيات المستخدمين',
          description: 'اختبار نظام الصلاحيات والأدوار',
          path: '/permissions/test'
        }
      ]
    },
    {
      id: 'notifications',
      title: 'اختبارات الإشعارات',
      description: 'اختبار نظام الإشعارات',
      icon: Bell,
      color: 'orange',
      tests: [
        {
          id: 'notification-system',
          name: 'نظام الإشعارات',
          description: 'اختبار إرسال واستقبال الإشعارات',
          path: '/notifications-demo'
        },
        {
          id: 'email-notifications',
          name: 'إشعارات البريد الإلكتروني',
          description: 'اختبار إرسال الإشعارات عبر البريد',
          path: '/notifications/email-test'
        }
      ]
    },
    {
      id: 'performance',
      title: 'اختبارات الأداء',
      description: 'اختبار أداء النظام والسرعة',
      icon: Activity,
      color: 'red',
      tests: [
        {
          id: 'load-test',
          name: 'اختبار الحمولة',
          description: 'اختبار أداء النظام تحت الضغط',
          path: '/performance/load-test'
        },
        {
          id: 'database-performance',
          name: 'أداء قاعدة البيانات',
          description: 'اختبار سرعة الاستعلامات',
          path: '/performance/database-test'
        }
      ]
    }
  ]

  const runTest = async (testId: string) => {
    setTestResults(prev => ({ ...prev, [testId]: 'running' }))
    
    try {
      // محاكاة تشغيل الاختبار
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // محاكاة نتيجة عشوائية
      const success = Math.random() > 0.3
      setTestResults(prev => ({ ...prev, [testId]: success ? 'success' : 'error' }))
    } catch (error) {
      setTestResults(prev => ({ ...prev, [testId]: 'error' }))
    }
  }

  const runAllTests = async () => {
    const allTests = testCategories.flatMap(category => category.tests.map(test => test.id))
    
    for (const testId of allTests) {
      await runTest(testId)
      // انتظار قصير بين الاختبارات
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  }

  const getTestStatusIcon = (testId: string) => {
    const status = testResults[testId] || 'idle'
    switch (status) {
      case 'running':
        return <Clock className="w-4 h-4 text-blue-500 animate-spin" />
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />
      default:
        return <TestTube className="w-4 h-4 text-gray-400" />
    }
  }

  const getTestStatusColor = (testId: string) => {
    const status = testResults[testId] || 'idle'
    switch (status) {
      case 'running':
        return 'border-blue-200 bg-blue-50'
      case 'success':
        return 'border-green-200 bg-green-50'
      case 'error':
        return 'border-red-200 bg-red-50'
      default:
        return 'border-gray-200 bg-white'
    }
  }

  // التحقق من الصلاحيات
  if (!permissions.isAdmin()) {
    return (
      <ProtectedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <Shield className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">غير مصرح</h2>
            <p className="text-gray-600">ليس لديك صلاحية للوصول إلى صفحة الاختبارات</p>
          </div>
        </div>
      </ProtectedLayout>
    )
  }

  return (
    <ProtectedLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Page Header */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">مركز الاختبارات</h1>
            <p className="text-gray-600 mt-1">
              اختبار جميع مكونات النظام والتأكد من سلامة العمل
            </p>
          </div>
          
          <div className="flex flex-wrap gap-3">
            <Button
              variant="primary"
              icon={<Play className="w-4 h-4" />}
              onClick={runAllTests}
            >
              تشغيل جميع الاختبارات
            </Button>
          </div>
        </div>

        {/* Test Categories */}
        <div className="space-y-8">
          {testCategories.map((category) => {
            const Icon = category.icon
            return (
              <div key={category.id}>
                <div className="flex items-center gap-3 mb-4">
                  <div className={`p-2 bg-${category.color}-100 rounded-lg`}>
                    <Icon className={`w-6 h-6 text-${category.color}-600`} />
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">{category.title}</h2>
                    <p className="text-gray-600 text-sm">{category.description}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {category.tests.map((test) => (
                    <Card key={test.id} className={`transition-all ${getTestStatusColor(test.id)}`}>
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-2">
                            {getTestStatusIcon(test.id)}
                            <h3 className="font-medium text-gray-900">{test.name}</h3>
                          </div>
                          <div className="flex gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              icon={<Play className="w-3 h-3" />}
                              onClick={() => runTest(test.id)}
                              disabled={testResults[test.id] === 'running'}
                            />
                            <Button
                              variant="ghost"
                              size="sm"
                              icon={<ExternalLink className="w-3 h-3" />}
                              onClick={() => window.open(test.path, '_blank')}
                            />
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-3">{test.description}</p>
                        
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>المسار: {test.path}</span>
                          <span>
                            {testResults[test.id] === 'running' && 'جاري التشغيل...'}
                            {testResults[test.id] === 'success' && 'نجح'}
                            {testResults[test.id] === 'error' && 'فشل'}
                            {!testResults[test.id] && 'جاهز'}
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )
          })}
        </div>

        {/* Test Results Summary */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">ملخص النتائج</h3>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <TestTube className="w-8 h-8 text-gray-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-gray-900">
                  {testCategories.reduce((total, category) => total + category.tests.length, 0)}
                </p>
                <p className="text-sm text-gray-600">إجمالي الاختبارات</p>
              </div>
              
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <CheckCircle className="w-8 h-8 text-green-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-green-900">
                  {Object.values(testResults).filter(status => status === 'success').length}
                </p>
                <p className="text-sm text-green-600">نجح</p>
              </div>
              
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <XCircle className="w-8 h-8 text-red-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-red-900">
                  {Object.values(testResults).filter(status => status === 'error').length}
                </p>
                <p className="text-sm text-red-600">فشل</p>
              </div>
              
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <Clock className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-blue-900">
                  {Object.values(testResults).filter(status => status === 'running').length}
                </p>
                <p className="text-sm text-blue-600">جاري التشغيل</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ProtectedLayout>
  )
} 