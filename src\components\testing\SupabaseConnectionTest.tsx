'use client'

import { useState, useEffect } from 'react'
import { supabase, auth, database, systemInfo } from '@/lib/supabase'
import { DEFAULT_PASSWORDS } from '@/lib/core/auth'
import { setupDefaultUsers, checkUsersStatus } from '@/lib/setup-users'
import { CheckCircle, XCircle, AlertCircle, Loader2, Database, Users, Key, UserPlus } from 'lucide-react'

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'error'
  message: string
  details?: any
}

export default function SupabaseConnectionTest() {
  const [tests, setTests] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [currentUser, setCurrentUser] = useState<any>(null)

  const updateTest = (name: string, status: TestResult['status'], message: string, details?: any) => {
    setTests(prev => {
      const existing = prev.find(t => t.name === name)
      if (existing) {
        existing.status = status
        existing.message = message
        existing.details = details
        return [...prev]
      } else {
        return [...prev, { name, status, message, details }]
      }
    })
  }

  const runTests = async () => {
    setIsRunning(true)
    setTests([])

    // Test 1: Basic Connection
    updateTest('اتصال أساسي', 'pending', 'جاري الاختبار...')
    try {
      const { count, error } = await supabase.from('users').select('*', { count: 'exact', head: true })
      if (error) throw error
      updateTest('اتصال أساسي', 'success', `متصل بنجاح - ${count || 0} مستخدم موجود`)
    } catch (error: any) {
      updateTest('اتصال أساسي', 'error', `فشل الاتصال: ${error.message}`)
    }

    // Test 2: Authentication System
    updateTest('نظام المصادقة', 'pending', 'جاري اختبار المصادقة...')
    try {
      const { data, error } = await auth.getCurrentSession()
      if (error && error.message !== 'فشل في الحصول على الجلسة') throw error
      updateTest('نظام المصادقة', 'success', 'نظام المصادقة يعمل بشكل صحيح')
    } catch (error: any) {
      updateTest('نظام المصادقة', 'error', `خطأ في المصادقة: ${error.message}`)
    }

    // Test 3: Database Tables
    updateTest('جداول قاعدة البيانات', 'pending', 'جاري فحص الجداول...')
    try {
      const tables = ['users', 'roles', 'departments', 'project_requests']
      const results = []

      for (const table of tables) {
        const { count, error } = await supabase.from(table).select('*', { count: 'exact', head: true })
        if (error) throw new Error(`جدول ${table}: ${error.message}`)
        results.push(`${table}: ${count || 0} سجل`)
      }

      updateTest('جداول قاعدة البيانات', 'success', 'جميع الجداول متاحة', results)
    } catch (error: any) {
      updateTest('جداول قاعدة البيانات', 'error', `خطأ في الجداول: ${error.message}`)
    }

    // Test 4: User Data
    updateTest('بيانات المستخدمين', 'pending', 'جاري فحص بيانات المستخدمين...')
    try {
      // اختبار بسيط أولاً
      const { data: simpleUsers, error: simpleError } = await supabase
        .from('users')
        .select('id, email, name')
        .limit(3)

      if (simpleError) throw simpleError

      // ثم اختبار الدالة المعقدة
      const { data, error } = await database.getUsers()
      if (error) {
        updateTest('بيانات المستخدمين', 'success', `جلب بسيط نجح: ${simpleUsers?.length || 0} مستخدم. خطأ في الدالة المعقدة: ${error.message}`, simpleUsers)
      } else {
        updateTest('بيانات المستخدمين', 'success', `تم جلب ${data?.length || 0} مستخدم بنجاح`, data?.slice(0, 3))
      }
    } catch (error: any) {
      updateTest('بيانات المستخدمين', 'error', `خطأ في بيانات المستخدمين: ${error.message}`)
    }

    // Test 5: Login Test
    updateTest('اختبار تسجيل الدخول', 'pending', 'جاري اختبار تسجيل الدخول...')
    try {
      const testEmail = '<EMAIL>'
      const testPassword = DEFAULT_PASSWORDS[testEmail]
      
      const { data, error } = await auth.signIn(testEmail, testPassword)
      if (error) throw error
      
      if (data.user) {
        setCurrentUser(data.user)
        updateTest('اختبار تسجيل الدخول', 'success', `تم تسجيل الدخول بنجاح للمستخدم: ${data.user.email}`)
      } else {
        throw new Error('لم يتم إرجاع بيانات المستخدم')
      }
    } catch (error: any) {
      updateTest('اختبار تسجيل الدخول', 'error', `فشل تسجيل الدخول: ${error.message}`)
    }

    setIsRunning(false)
  }

  const logout = async () => {
    try {
      await auth.signOut()
      setCurrentUser(null)
      updateTest('تسجيل الخروج', 'success', 'تم تسجيل الخروج بنجاح')
    } catch (error: any) {
      updateTest('تسجيل الخروج', 'error', `فشل تسجيل الخروج: ${error.message}`)
    }
  }

  const setupUsers = async () => {
    updateTest('إعداد المستخدمين', 'pending', 'جاري إعداد المستخدمين الافتراضيين...')
    try {
      const results = await setupDefaultUsers()
      const successCount = results.filter(r => r.status === 'created' || r.status === 'exists').length
      updateTest('إعداد المستخدمين', 'success', `تم إعداد ${successCount} مستخدم`, results)
    } catch (error: any) {
      updateTest('إعداد المستخدمين', 'error', `فشل في إعداد المستخدمين: ${error.message}`)
    }
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center gap-3 mb-6">
          <Database className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-900">اختبار اتصال Supabase</h2>
        </div>

        {/* معلومات النظام */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-gray-900 mb-3">معلومات النظام</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">رابط Supabase:</span>
              <span className="ml-2 font-mono text-xs">{systemInfo.supabaseUrl}</span>
            </div>
            <div>
              <span className="text-gray-600">حالة الاتصال:</span>
              <span className={`ml-2 ${systemInfo.isConnected ? 'text-green-600' : 'text-red-600'}`}>
                {systemInfo.isConnected ? 'متصل' : 'غير متصل'}
              </span>
            </div>
          </div>
        </div>

        {/* المستخدم الحالي */}
        {currentUser && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Users className="h-5 w-5 text-green-600" />
                <div>
                  <h4 className="font-semibold text-green-900">مستخدم مسجل الدخول</h4>
                  <p className="text-sm text-green-700">{currentUser.email}</p>
                  {currentUser.user_metadata?.name && (
                    <p className="text-sm text-green-600">{currentUser.user_metadata.name}</p>
                  )}
                </div>
              </div>
              <button
                onClick={logout}
                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              >
                تسجيل الخروج
              </button>
            </div>
          </div>
        )}

        {/* كلمات المرور الافتراضية */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-2 mb-3">
            <Key className="h-5 w-5 text-blue-600" />
            <h4 className="font-semibold text-blue-900">كلمات المرور الافتراضية</h4>
          </div>
          <div className="space-y-2 text-sm">
            {Object.entries(DEFAULT_PASSWORDS).map(([email, password]) => (
              <div key={email} className="flex justify-between items-center">
                <span className="text-blue-700">{email}</span>
                <span className="font-mono bg-blue-100 px-2 py-1 rounded text-blue-800">{password}</span>
              </div>
            ))}
          </div>
          <div className="mt-3 p-2 bg-blue-100 rounded text-xs text-blue-800">
            <strong>ملاحظة:</strong> تم إعادة تعيين كلمات المرور في قاعدة البيانات. إذا فشل تسجيل الدخول، قد تحتاج لإعادة إنشاء المستخدمين.
          </div>
        </div>

        {/* أزرار التحكم */}
        <div className="flex gap-4 mb-6">
          <button
            onClick={runTests}
            disabled={isRunning}
            className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isRunning ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <CheckCircle className="h-4 w-4" />
            )}
            {isRunning ? 'جاري الاختبار...' : 'تشغيل الاختبارات'}
          </button>

          <button
            onClick={setupUsers}
            disabled={isRunning}
            className="flex items-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <UserPlus className="h-4 w-4" />
            إعداد المستخدمين
          </button>
        </div>

        {/* نتائج الاختبارات */}
        {tests.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">نتائج الاختبارات</h3>
            {tests.map((test, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center gap-3 mb-2">
                  {getStatusIcon(test.status)}
                  <h4 className="font-medium text-gray-900">{test.name}</h4>
                </div>
                <p className={`text-sm ${
                  test.status === 'success' ? 'text-green-700' :
                  test.status === 'error' ? 'text-red-700' : 'text-gray-600'
                }`}>
                  {test.message}
                </p>
                {test.details && (
                  <div className="mt-2 p-3 bg-gray-50 rounded text-xs font-mono">
                    <pre>{JSON.stringify(test.details, null, 2)}</pre>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
