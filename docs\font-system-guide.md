# دليل نظام أحجام الخطوط الموحد

## نظرة عامة

تم إعادة تصميم نظام أحجام الخطوط في النظام بالكامل لتكون أكثر بساطة وتناسقاً. النظام الجديد يعتمد على 5 أحجام فقط:

- **8px** - للتفاصيل الدقيقة والملاحظات الصغيرة
- **12px** - للنصوص الثانوية والتسميات
- **16px** - للنصوص الأساسية والمحتوى
- **20px** - للعناوين الفرعية والمهمة
- **24px** - للعناوين الرئيسية والمهمة جداً

## فئات CSS المتاحة

### فئات Tailwind المحدثة
```css
text-xs     /* 8px */
text-sm     /* 12px */
text-base   /* 16px */
text-lg     /* 20px */
text-xl     /* 24px */
```

### فئات CSS مخصصة
```css
/* الأحجام الأساسية */
.text-micro    /* 8px */
.text-small    /* 12px */
.text-normal   /* 16px */
.text-medium   /* 20px */
.text-large    /* 24px */

/* العناوين */
.heading-main     /* 24px - العناوين الرئيسية */
.heading-sub      /* 20px - العناوين الفرعية */
.heading-section  /* 16px - عناوين الأقسام */

/* النصوص */
.text-body     /* 16px - النصوص الأساسية */
.text-caption  /* 12px - النصوص الثانوية */
.text-label    /* 8px - التسميات الصغيرة */
```

## مكونات Typography

### استيراد المكونات
```tsx
import { 
  MainHeading, 
  SubHeading, 
  SectionHeading,
  BodyText,
  CaptionText,
  LabelText,
  Typography,
  PageTitle,
  PageSubtitle,
  CardTitle,
  CardDescription,
  FormLabel,
  ErrorText,
  HelperText,
  Badge
} from '@/components/ui/Typography'
```

### أمثلة الاستخدام

#### العناوين
```tsx
{/* العنوان الرئيسي للصفحة */}
<PageTitle>إدارة المشاريع</PageTitle>

{/* العنوان الفرعي */}
<PageSubtitle>نظام إدارة طلبات المشاريع</PageSubtitle>

{/* عنوان القسم */}
<SectionHeading>المشاريع الحالية</SectionHeading>
```

#### النصوص
```tsx
{/* النص الأساسي */}
<BodyText>هذا نص أساسي للمحتوى العام</BodyText>

{/* النص الثانوي */}
<CaptionText>معلومات إضافية أو تفاصيل ثانوية</CaptionText>

{/* النص الصغير */}
<LabelText>تسمية صغيرة أو رقم</LabelText>
```

#### الكروت
```tsx
<Card>
  <CardTitle>عنوان الكرت</CardTitle>
  <CardDescription>وصف مختصر للكرت</CardDescription>
</Card>
```

#### النماذج
```tsx
<FormLabel>اسم الحقل</FormLabel>
<Input />
<HelperText>نص مساعد للحقل</HelperText>
<ErrorText>رسالة خطأ</ErrorText>
```

#### الشارات
```tsx
<Badge className="bg-blue-100 text-blue-800">جديد</Badge>
<Badge className="bg-green-100 text-green-800">مكتمل</Badge>
```

## إرشادات الاستخدام

### العناوين
- **24px (heading-main)**: العناوين الرئيسية للصفحات
- **20px (heading-sub)**: العناوين الفرعية والأقسام المهمة
- **16px (heading-section)**: عناوين الأقسام والكروت

### النصوص
- **16px (text-body)**: النصوص الأساسية والمحتوى
- **12px (text-caption)**: النصوص الثانوية والتسميات
- **8px (text-label)**: التفاصيل الدقيقة والأرقام الصغيرة

### الاستخدامات الشائعة

#### 8px (text-xs / text-label)
- أيقونات صغيرة
- تسميات الحقول الصغيرة
- أرقام الصفحات
- التواريخ الصغيرة
- الشارات الصغيرة

#### 12px (text-sm / text-caption)
- تسميات الحقول
- النصوص المساعدة
- رسائل الأخطاء
- التواريخ والأوقات
- النصوص الثانوية

#### 16px (text-base / text-body)
- النصوص الأساسية
- محتوى الصفحات
- النماذج
- الأزرار
- القوائم

#### 20px (text-lg / heading-sub)
- العناوين الفرعية
- أسماء الأقسام
- عناوين الكروت
- النصوص المهمة
- عناوين الجداول

#### 24px (text-xl / heading-main)
- العناوين الرئيسية
- عناوين الصفحات
- رسائل الحالة المهمة
- العناوين الكبيرة
- نصوص الترحيب

## أمثلة عملية

### صفحة نموذجية
```tsx
export default function ProjectPage() {
  return (
    <div>
      <PageTitle>إدارة المشاريع</PageTitle>
      <PageSubtitle>عرض وإدارة جميع المشاريع الحالية</PageSubtitle>
      
      <SectionHeading>المشاريع النشطة</SectionHeading>
      
      <Card>
        <CardTitle>مشروع التحسين الأول</CardTitle>
        <CardDescription>وصف مختصر للمشروع</CardDescription>
        
        <div className="mt-4">
          <BodyText>تفاصيل المشروع والمعلومات الأساسية</BodyText>
          <CaptionText>معلومات إضافية</CaptionText>
          <LabelText>آخر تحديث: 2024</LabelText>
        </div>
      </Card>
    </div>
  )
}
```

### نموذج نموذجي
```tsx
export default function ProjectForm() {
  return (
    <form>
      <FormLabel>اسم المشروع</FormLabel>
      <Input />
      <HelperText>أدخل اسم المشروع بوضوح</HelperText>
      
      <FormLabel>الوصف</FormLabel>
      <Textarea />
      <ErrorText>هذا الحقل مطلوب</ErrorText>
    </form>
  )
}
```

## ملاحظات مهمة

1. **التناسق**: استخدم نفس الحجم لنفس النوع من المحتوى في جميع أنحاء النظام
2. **التدرج**: اتبع التدرج الطبيعي من الأكبر إلى الأصغر
3. **الوضوح**: تأكد من أن النص واضح ومقروء على جميع الأجهزة
4. **الاستثناءات**: تجنب استخدام أحجام خارج النظام المحدد
5. **الاختبار**: اختبر النصوص على شاشات مختلفة للتأكد من الوضوح

## التحديثات المطلوبة

عند العمل على مكونات جديدة أو تحديث مكونات موجودة:

1. استخدم المكونات من `Typography.tsx` بدلاً من فئات CSS مباشرة
2. تأكد من اتباع نظام الأحجام الجديد
3. اختبر النصوص على أجهزة مختلفة
4. راجع التناسق مع باقي النظام

## الدعم

للمساعدة أو الاستفسارات حول نظام الأحجام الجديد، يرجى مراجعة:
- ملف `src/lib/fontSizes.ts` للأحجام والاستخدامات
- مكونات `src/components/ui/Typography.tsx` للمكونات الجاهزة
- ملف `src/app/globals.css` للفئات المخصصة 