'use client'

import * as React from 'react'
import { cn } from '@/lib/utils'

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  icon?: React.ReactNode
  helperText?: string
  showCharCount?: boolean
  showMaxLengthText?: boolean
}

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string
  error?: string
  helperText?: string
  showCharCount?: boolean
}

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string
  error?: string
  helperText?: string
  children: React.ReactNode
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, label, error, icon, helperText, showCharCount = true, showMaxLengthText = true, type = 'text', ...props }, ref) => {
    const inputId = React.useId()
    const [valueLength, setValueLength] = React.useState<number>(0)
    
    // تحديث طول القيمة عند تغييرها
    React.useEffect(() => {
      if (props.value !== undefined) {
        setValueLength(String(props.value).length)
      }
    }, [props.value])
    
    // التعامل مع تغيير القيمة
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value
      
      // منع الكتابة أكثر من الحد الأقصى
      if (props.maxLength && newValue.length > props.maxLength) {
        e.preventDefault()
        return
      }
      
      // تحديث طول القيمة
      setValueLength(newValue.length)
      
      // استدعاء دالة onChange الأصلية إذا كانت موجودة
      if (props.onChange) {
        props.onChange(e)
      }
    }
    
    return (
      <div className="w-full">
        {label && (
          <label 
            htmlFor={inputId}
            className="form-label"
          >
            {label}
            {props.required && <span className="text-red-500 mr-1">*</span>}
          </label>
        )}
        
        <div className="relative">
          {icon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <div className="text-gray-500">
                {icon}
              </div>
            </div>
          )}
          
          <input
            id={inputId}
            type={type}
            className={cn(
              'form-input',
              error && 'error',
              icon && 'pr-10',
              className
            )}
            ref={ref}
            onChange={handleChange}
            {...props}
          />
        </div>
        
        <div className="flex justify-between items-start mt-1">
          <div className="flex-1">
            {error && (
              <p className="form-error">
                {error}
              </p>
            )}
            
            {helperText && !error && (
              <p className="form-helper">
                {helperText}
              </p>
            )}
          </div>
          
          {showCharCount && props.maxLength && (
            <span className={cn(
              "text-caption mr-2 flex-shrink-0 font-arabic",
              valueLength > (props.maxLength * 0.8) ? "text-amber-500" : "text-gray-500",
              valueLength >= props.maxLength ? "text-red-500 font-bold" : ""
            )}>
              {valueLength}/{props.maxLength}
            </span>
          )}
        </div>
      </div>
    )
  }
)

export const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, label, error, helperText, showCharCount = true, ...props }, ref) => {
    const textareaId = React.useId()
    const [valueLength, setValueLength] = React.useState<number>(0)
    
    // تحديث طول القيمة عند تغييرها
    React.useEffect(() => {
      if (props.value !== undefined) {
        setValueLength(String(props.value).length)
      }
    }, [props.value])
    
    // التعامل مع تغيير القيمة
    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value
      
      // تحديث طول القيمة
      setValueLength(newValue.length)
      
      // استدعاء دالة onChange الأصلية إذا كانت موجودة
      if (props.onChange) {
        props.onChange(e)
      }
    }
    
    return (
      <div className="w-full">
        {label && (
          <label 
            htmlFor={textareaId}
            className="form-label"
          >
            {label}
            {props.required && <span className="text-red-500 mr-1">*</span>}
          </label>
        )}
        
        <textarea
          id={textareaId}
          className={cn(
            'form-input resize-vertical min-h-[100px]',
            error && 'error',
            className
          )}
          ref={ref}
          onChange={handleChange}
          {...props}
        />
        
        <div className="flex justify-between items-start mt-1">
          <div className="flex-1">
            {error && (
              <p className="form-error">
                {error}
              </p>
            )}
            
            {helperText && !error && (
              <p className="form-helper">
                {helperText}
              </p>
            )}
          </div>
          
          {showCharCount && props.maxLength && (
            <span className={cn(
              "text-caption mr-2 flex-shrink-0 font-arabic",
              valueLength > (props.maxLength * 0.8) ? "text-amber-500" : "text-gray-500",
              valueLength >= props.maxLength ? "text-red-500 font-bold" : ""
            )}>
              {valueLength}/{props.maxLength}
            </span>
          )}
        </div>
      </div>
    )
  }
)

export const Select = React.forwardRef<HTMLSelectElement, SelectProps>(
  ({ className, label, error, helperText, children, ...props }, ref) => {
    const selectId = React.useId()
    
    return (
      <div className="w-full">
        {label && (
          <label 
            htmlFor={selectId}
            className="form-label"
          >
            {label}
            {props.required && <span className="text-red-500 mr-1">*</span>}
          </label>
        )}
        
        <select
          id={selectId}
          className={cn(
            'form-input cursor-pointer',
            error && 'error',
            className
          )}
          ref={ref}
          {...props}
        >
          {children}
        </select>
        
        {(error || helperText) && (
          <div className="mt-1">
            {error && (
              <p className="form-error">
                {error}
              </p>
            )}
            
            {helperText && !error && (
              <p className="form-helper">
                {helperText}
              </p>
            )}
          </div>
        )}
      </div>
    )
  }
)

Input.displayName = 'Input'
Textarea.displayName = 'Textarea'
Select.displayName = 'Select' 