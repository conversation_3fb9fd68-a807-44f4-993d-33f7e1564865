'use client'

import React from 'react'
import { Input, Textarea, Select } from '@/components/ui/Input'
import { Button } from '@/components/ui/Button'
import { FieldHelp } from '@/components/ui/HelpSystem'
import { Lightbulb, Plus, Trash2, Star, DollarSign, Clock } from 'lucide-react'
import { FormType, UnifiedFormData, EnhancedImprovementData, SuggestionData, QuickWinData } from '../UnifiedProjectForm'

interface AdaptiveSelectStepProps {
  formType: FormType
  data: UnifiedFormData
  updateData: (field: string, value: any) => void
  errors: Record<string, string>
}

export function AdaptiveSelectStep({ formType, data, updateData, errors }: AdaptiveSelectStepProps) {
  const getStepTitle = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'Select - اختيار الحل الأمثل'
      case 'suggestion':
        return 'Select - اقتراح الحلول'
      case 'quick_win':
        return 'Select - تحديد الحل السريع'
      default:
        return 'Select - اختيار الحل'
    }
  }

  const getStepDescription = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'حدد الحل الأمثل مع تفاصيل التنفيذ والموارد المطلوبة'
      case 'suggestion':
        return 'اقترح حلول متعددة مع تقييم كل حل وأولويته'
      case 'quick_win':
        return 'حدد الحل السريع الذي يمكن تنفيذه في 4 أسابيع أو أقل'
      default:
        return 'حدد الحل المناسب'
    }
  }

  const getStepColor = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'emerald'
      case 'suggestion':
        return 'indigo'
      case 'quick_win':
        return 'yellow'
      default:
        return 'emerald'
    }
  }

  const colorClasses = {
    emerald: {
      bg: 'bg-emerald-50',
      border: 'border-emerald-200',
      text: 'text-emerald-900',
      icon: 'text-emerald-600'
    },
    indigo: {
      bg: 'bg-indigo-50',
      border: 'border-indigo-200',
      text: 'text-indigo-900',
      icon: 'text-indigo-600'
    },
    yellow: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      text: 'text-yellow-900',
      icon: 'text-yellow-600'
    }
  }

  const stepColor = getStepColor()
  const colors = colorClasses[stepColor]

  // دوال للمقترحات (حلول متعددة)
  const addSuggestedSolution = () => {
    if (formType === 'suggestion') {
      const suggestionData = data as SuggestionData
      const newSolution = {
        id: Date.now().toString(),
        title: '',
        description: '',
        justification: '',
        expectedBenefits: '',
        feasibilityScore: 5,
        impactScore: 5,
        priority: 'medium' as const
      }
      updateData('suggestedSolutions', [...suggestionData.suggestedSolutions, newSolution])
    }
  }

  const removeSuggestedSolution = (id: string) => {
    if (formType === 'suggestion') {
      const suggestionData = data as SuggestionData
      const updated = suggestionData.suggestedSolutions.filter(sol => sol.id !== id)
      updateData('suggestedSolutions', updated)
    }
  }

  const updateSuggestedSolution = (id: string, field: string, value: any) => {
    if (formType === 'suggestion') {
      const suggestionData = data as SuggestionData
      const updated = suggestionData.suggestedSolutions.map(sol => 
        sol.id === id ? { ...sol, [field]: value } : sol
      )
      updateData('suggestedSolutions', updated)
    }
  }

  // دوال لكويك وين
  const addQuickWinTask = () => {
    if (formType === 'quick_win') {
      const quickWinData = data as QuickWinData
      updateData('solution', {
        ...quickWinData.solution,
        tasks: [...quickWinData.solution.tasks, '']
      })
    }
  }

  const removeQuickWinTask = (index: number) => {
    if (formType === 'quick_win') {
      const quickWinData = data as QuickWinData
      const updated = quickWinData.solution.tasks.filter((_, i) => i !== index)
      updateData('solution', { ...quickWinData.solution, tasks: updated })
    }
  }

  const updateQuickWinTask = (index: number, value: string) => {
    if (formType === 'quick_win') {
      const quickWinData = data as QuickWinData
      const updated = quickWinData.solution.tasks.map((task, i) => 
        i === index ? value : task
      )
      updateData('solution', { ...quickWinData.solution, tasks: updated })
    }
  }

  const renderEnhancedImprovementSelect = () => {
    const enhancedData = data as EnhancedImprovementData

    return (
      <div className="space-y-6">
        <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
          <h4 className="font-semibold text-emerald-900 mb-4">الحل المختار</h4>
          
          <div className="space-y-4">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  وصف الحل *
                </label>
                <FieldHelp 
                  content="اشرح الحل المختار بالتفصيل وكيفية تطبيقه"
                  field="selectedSolution"
                  step={6}
                />
              </div>
              <Textarea
                value={enhancedData.selectedSolution.description}
                onChange={(e) => updateData('selectedSolution', {
                  ...enhancedData.selectedSolution,
                  description: e.target.value
                })}
                placeholder="اشرح الحل المختار بالتفصيل..."
                rows={4}
                maxLength={1000}
                error={errors['selectedSolution.description']}
              />
            </div>

            <div>
              <div className="flex items-center gap-2 mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  مبررات الاختيار *
                </label>
                <FieldHelp 
                  content="لماذا تم اختيار هذا الحل دون غيره؟"
                  field="justification"
                  step={6}
                />
              </div>
              <Textarea
                value={enhancedData.selectedSolution.justification}
                onChange={(e) => updateData('selectedSolution', {
                  ...enhancedData.selectedSolution,
                  justification: e.target.value
                })}
                placeholder="لماذا تم اختيار هذا الحل؟"
                rows={3}
                maxLength={500}
                error={errors['selectedSolution.justification']}
              />
            </div>



            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  التكلفة المقدرة (ريال)
                </label>
                <Input
                  type="number"
                  value={enhancedData.selectedSolution.estimatedCost}
                  onChange={(e) => updateData('selectedSolution', {
                    ...enhancedData.selectedSolution,
                    estimatedCost: parseFloat(e.target.value) || 0
                  })}
                  min="0"
                  step="0.01"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وقت التنفيذ المتوقع
                </label>
                <Input
                  value={enhancedData.selectedSolution.implementationTime}
                  onChange={(e) => updateData('selectedSolution', {
                    ...enhancedData.selectedSolution,
                    implementationTime: e.target.value
                  })}
                  placeholder="مثال: 3 أشهر"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const renderSuggestionSelect = () => {
    const suggestionData = data as SuggestionData

    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h4 className="font-semibold text-gray-900">الحلول المقترحة</h4>
          <Button onClick={addSuggestedSolution} variant="secondary" size="sm">
            <Plus className="w-4 h-4 mr-2" />
            إضافة حل
          </Button>
        </div>

        {suggestionData.suggestedSolutions.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Lightbulb className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            <p>لم يتم إضافة حلول بعد</p>
            <p className="text-sm">انقر على &quot;إضافة حل&quot; لبدء اقتراح الحلول</p>
          </div>
        )}

        {suggestionData.suggestedSolutions.map((solution, index) => (
          <div key={solution.id} className="bg-white border rounded-lg p-4">
            <div className="flex justify-between items-start mb-4">
              <h5 className="font-medium text-gray-900">الحل {index + 1}</h5>
              <Button 
                onClick={() => removeSuggestedSolution(solution.id)}
                variant="danger" 
                size="sm"
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>

            <div className="space-y-4">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    عنوان الحل *
                  </label>
                  <FieldHelp 
                    content="عنوان مختصر وواضح للحل المقترح"
                    field="solutionTitle"
                    step={6}
                  />
                </div>
                <Input
                  value={solution.title}
                  onChange={(e) => updateSuggestedSolution(solution.id, 'title', e.target.value)}
                  placeholder="عنوان مختصر للحل"
                />
              </div>

              <div>
                <div className="flex items-center gap-2 mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    وصف الحل *
                  </label>
                  <FieldHelp 
                    content="شرح مفصل لكيفية تطبيق الحل"
                    field="solutionDescription"
                    step={6}
                  />
                </div>
                <Textarea
                  value={solution.description}
                  onChange={(e) => updateSuggestedSolution(solution.id, 'description', e.target.value)}
                  placeholder="اشرح الحل بالتفصيل..."
                  rows={3}
                />
              </div>

              <div>
                <div className="flex items-center gap-2 mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    مبررات الحل *
                  </label>
                  <FieldHelp 
                    content="لماذا هذا الحل مناسب لحل المشكلة؟"
                    field="solutionJustification"
                    step={6}
                  />
                </div>
                <Textarea
                  value={solution.justification}
                  onChange={(e) => updateSuggestedSolution(solution.id, 'justification', e.target.value)}
                  placeholder="لماذا هذا الحل مناسب؟"
                  rows={2}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      درجة الجدوى (1-10) *
                    </label>
                    <FieldHelp 
                      content="مدى سهولة تطبيق الحل (1 = صعب جداً، 10 = سهل جداً)"
                      field="feasibilityScore"
                      step={6}
                    />
                  </div>
                  <div className="space-y-2">
                    <input
                      type="range"
                      min="1"
                      max="10"
                      value={solution.feasibilityScore}
                      onChange={(e) => updateSuggestedSolution(solution.id, 'feasibilityScore', parseInt(e.target.value))}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>1 - صعب جداً</span>
                      <span className="font-semibold text-indigo-600">{solution.feasibilityScore}/10</span>
                      <span>10 - سهل جداً</span>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الأولوية
                  </label>
                  <Select
                    value={solution.priority}
                    onChange={(e) => updateSuggestedSolution(solution.id, 'priority', e.target.value)}
                  >
                    <option value="low">منخفضة</option>
                    <option value="medium">متوسطة</option>
                    <option value="high">عالية</option>
                  </Select>
                </div>
              </div>

              {/* مؤشر الجدوى */}
              <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-indigo-700">تقييم الجدوى:</span>
                  <div className="flex items-center gap-2">
                    <Star className="w-4 h-4 text-indigo-500" />
                    <span className="font-bold text-indigo-900">
                      {solution.feasibilityScore >= 8 ? 'عالية جداً' : 
                       solution.feasibilityScore >= 6 ? 'عالية' :
                       solution.feasibilityScore >= 4 ? 'متوسطة' : 'منخفضة'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  const renderQuickWinSelect = () => {
    const quickWinData = data as QuickWinData

    return (
      <div className="space-y-6">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-semibold text-yellow-900 mb-4 flex items-center gap-2">
            <Clock className="w-5 h-5" />
            الحل السريع (حد أقصى 4 أسابيع)
          </h4>

          <div className="space-y-4">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  وصف الحل *
                </label>
                <FieldHelp 
                  content="اشرح الحل السريع الذي يمكن تنفيذه في 4 أسابيع أو أقل"
                  field="quickWinSolution"
                  step={6}
                />
              </div>
              <Textarea
                value={quickWinData.solution.description}
                onChange={(e) => updateData('solution', {
                  ...quickWinData.solution,
                  description: e.target.value
                })}
                placeholder="اشرح الحل السريع الذي يمكن تنفيذه بسهولة..."
                rows={3}
                maxLength={500}
                error={errors['solution.description']}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                المهام المطلوبة
              </label>
              <div className="space-y-2">
                {quickWinData.solution.tasks.map((task, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={task}
                      onChange={(e) => updateQuickWinTask(index, e.target.value)}
                      placeholder={`المهمة ${index + 1}`}
                      className="flex-1"
                    />
                    <Button
                      onClick={() => removeQuickWinTask(index)}
                      variant="danger"
                      size="sm"
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
                
                <Button 
                  onClick={addQuickWinTask}
                  variant="secondary" 
                  size="sm" 
                  className="w-full"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  إضافة مهمة
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    مدة التنفيذ (أسابيع) *
                  </label>
                  <FieldHelp 
                    content="المدة المطلوبة لتنفيذ الحل (حد أقصى 4 أسابيع)"
                    field="implementationWeeks"
                    step={6}
                  />
                </div>
                <Input
                  type="number"
                  value={quickWinData.solution.implementationWeeks}
                  onChange={(e) => updateData('solution', {
                    ...quickWinData.solution,
                    implementationWeeks: Math.min(4, parseInt(e.target.value) || 1)
                  })}
                  min="1"
                  max="4"
                  error={errors['solution.implementationWeeks']}
                />
                <div className="text-xs text-gray-500 mt-1">
                  حد أقصى 4 أسابيع للحلول السريعة
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  التكلفة المقدرة (ريال)
                </label>
                <Input
                  type="number"
                  value={quickWinData.solution.estimatedCost}
                  onChange={(e) => updateData('solution', {
                    ...quickWinData.solution,
                    estimatedCost: parseFloat(e.target.value) || 0
                  })}
                  min="0"
                  step="0.01"
                />
              </div>
            </div>

            {/* تحذير إذا تجاوزت المدة 4 أسابيع */}
            {quickWinData.solution.implementationWeeks > 4 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-red-800 text-sm">
                  ⚠️ مدة التنفيذ تتجاوز 4 أسابيع. يجب أن تكون الحلول السريعة قابلة للتنفيذ في 4 أسابيع أو أقل.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className={`${colors.bg} border ${colors.border} rounded-lg p-4 mb-6`}>
        <div className="flex items-center gap-2 mb-2">
          <Lightbulb className={`w-5 h-5 ${colors.icon}`} />
          <h3 className={`font-semibold ${colors.text}`}>{getStepTitle()}</h3>
        </div>
        <p className={`${colors.text} text-sm`}>
          {getStepDescription()}
        </p>
      </div>

      {formType === 'enhanced_improvement' && renderEnhancedImprovementSelect()}
      {formType === 'suggestion' && renderSuggestionSelect()}
      {formType === 'quick_win' && renderQuickWinSelect()}
    </div>
  )
} 