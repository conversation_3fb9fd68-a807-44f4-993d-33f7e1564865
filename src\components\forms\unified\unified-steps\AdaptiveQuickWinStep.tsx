'use client'

import React from 'react'
import { Zap } from 'lucide-react'
import { StepHeader } from '../../shared/StepHeader'
import { IndicatorSection } from '../../shared/IndicatorSection'
import { BasicInfoSection } from './quick-win/BasicInfoSection'
import { ProblemSolutionSection } from './quick-win/ProblemSolutionSection'
import { useFormErrorHandler, commonValidationRules } from '@/lib/errorHandler'
import { FormType, QuickWinData } from '../UnifiedProjectForm'

interface AdaptiveQuickWinStepProps {
  data: QuickWinData
  updateData: (field: string, value: any) => void
  errors: Record<string, string>
  formType?: FormType
}

export function AdaptiveQuickWinStep({ data, updateData, errors, formType = 'quick_win' }: AdaptiveQuickWinStepProps) {
  const { validateField } = useFormErrorHandler()

  // قائمة الأقسام المتاحة
  const departments = [
    'إدارة الموارد البشرية',
    'إدارة تقنية المعلومات',
    'إدارة الجودة',
    'إدارة العمليات',
    'إدارة المالية',
    'إدارة التسويق',
    'إدارة خدمة العملاء',
    'إدارة المشتريات',
    'إدارة الصيانة',
    'الإدارة العامة'
  ]

  // التحقق من صحة البيانات عند التغيير
  const handleDataUpdate = (field: string, value: any) => {
    updateData(field, value)
    
    // التحقق المباشر من بعض الحقول المهمة
    if (field === 'projectTitle') {
      validateField(field, value, commonValidationRules.projectTitle)
    } else if (field === 'problemDescription') {
      validateField(field, value, commonValidationRules.projectDescription)
    } else if (field === 'projectExecutor.phone') {
      validateField(field, value, commonValidationRules.phone)
    } else if (field === 'projectExecutor.email') {
      validateField(field, value, commonValidationRules.email)
    }
  }

  return (
    <div className="space-y-8">
      {/* رأس الخطوة */}
      <StepHeader
        stepNumber={1}
        title="QuickWin - نموذج التحسين السريع"
        description="نموذج مبسط لمشاريع التحسين السريعة - حدد تاريخ البداية واختر مدة التنفيذ (1-4 أسابيع)"
        icon={Zap}
        formType={formType}
      />

      {/* النموذج الكامل بدون تبويبات */}
      <div className="space-y-8">
        {/* قسم المعلومات الأساسية */}
        <BasicInfoSection
          data={data}
          updateData={handleDataUpdate}
          errors={errors}
          departments={departments}
        />

        {/* قسم المشكلة والحل */}
        <ProblemSolutionSection
          data={data}
          updateData={handleDataUpdate}
          errors={errors}
        />

        {/* قسم المؤشرات */}
        <IndicatorSection
          data={{
            indicatorName: data.indicatorName,
            currentValue: data.currentValue,
            targetValue: data.targetValue,
            improvementDirection: data.improvementDirection,
            unit: data.unit
          }}
          onUpdate={handleDataUpdate}
          errors={errors}
          formType={formType}
        />
      </div>



      {/* نصائح سريعة */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="font-bold text-blue-800 mb-2">💡 نصائح للنجاح:</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• ركز على مشكلة واحدة محددة وقابلة للحل</li>
          <li>• اختر حلاً بسيطاً وقابلاً للتنفيذ خلال المدة المحددة</li>
          <li>• حدد تاريخ بداية واقعي يمكن الالتزام به</li>
          <li>• اختر مدة تنفيذ مناسبة (1-4 أسابيع كحد أقصى)</li>
          <li>• حدد مؤشراً واضحاً وقابلاً للقياس</li>
          <li>• قدر التكلفة بدقة لتجنب المفاجآت</li>
        </ul>
      </div>
    </div>
  )
} 