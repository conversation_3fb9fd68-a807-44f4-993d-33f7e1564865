export interface HelpContent {
  title: string
  description: string
  tips: string[]
  examples: string[]
  warnings: string[]
  bestPractices: string[]
}

export interface StepGuide {
  overview: HelpContent
  fields: Record<string, HelpContent>
}

export const helpDatabase: Record<number, StepGuide> = {
  1: {
    overview: {
      title: 'المعلومات الأساسية للمشروع',
      description: 'هذه المرحلة تحدد الهوية الأساسية للمشروع والإطار الزمني. المعلومات هنا ستكون الأساس لجميع المراحل التالية.',
      tips: [
        'اختر اسماً واضحاً ومحدداً للمشروع',
        'اكتب وصفاً شاملاً يوضح الهدف والفائدة',
        'حدد تواريخ واقعية قابلة للتحقيق',
        'اختر الأولوية بناءً على تأثير المشكلة وإلحاحها'
      ],
      examples: [
        'مشروع تحسين وقت استجابة خدمة العملاء من 10 دقائق إلى 3 دقائق خلال 3 أشهر',
        'مشروع تقليل أخطاء إدخال البيانات من 15 خطأ يومياً إلى أقل من 5 أخطاء',
        'مشروع رفع مستوى رضا المرضى في قسم الطوارئ من 60% إلى 85%'
      ],
      warnings: [
        'تجنب الأسماء العامة مثل "تحسين الخدمة"',
        'لا تضع تواريخ غير واقعية أو ضيقة جداً',
        'تأكد من توافق الوصف مع الهدف الحقيقي للمشروع'
      ],
      bestPractices: [
        'راجع المشاريع المشابهة السابقة لتقدير المدة المناسبة',
        'تأكد من توفر الموارد اللازمة خلال فترة المشروع',
        'حدد معايير النجاح منذ البداية'
      ]
    },
    fields: {
      projectName: {
        title: 'اسم المشروع',
        description: 'اسم واضح ومحدد يعبر عن هدف المشروع الأساسي',
        tips: [
          'استخدم كلمات محددة وواضحة',
          'اذكر المجال أو القسم المستهدف',
          'تجنب المصطلحات التقنية المعقدة',
          'اجعله قصيراً وسهل الفهم (أقل من 60 حرف)'
        ],
        examples: [
          'تحسين كفاءة عملية معالجة الطلبات',
          'تقليل وقت انتظار المرضى في العيادات',
          'رفع جودة خدمة الدعم الفني',
          'تطوير نظام إدارة المخزون الذكي'
        ],
        warnings: [
          'تجنب الأسماء الطويلة جداً أو المعقدة',
          'لا تستخدم اختصارات غير مفهومة',
          'تجنب الأسماء العامة مثل "مشروع التحسين"'
        ],
        bestPractices: [
          'اختبر الاسم مع زملائك للتأكد من وضوحه',
          'تأكد من أن الاسم يعكس النتيجة المرجوة',
          'استخدم أفعال إيجابية مثل "تحسين" و"تطوير"'
        ]
      },
      projectDescription: {
        title: 'وصف المشروع',
        description: 'وصف شامل يوضح هدف المشروع والفائدة المتوقعة والنتائج المرجوة',
        tips: [
          'ابدأ بوصف الوضع الحالي والمشكلة',
          'اذكر الهدف المحدد والنتيجة المرجوة',
          'وضح الفائدة للمؤسسة والعملاء',
          'استخدم لغة واضحة ومباشرة'
        ],
        examples: [
          'يهدف هذا المشروع إلى تحسين تجربة العملاء من خلال تقليل وقت انتظار الرد على الاستفسارات من 10 دقائق حالياً إلى 3 دقائق كحد أقصى. سيتم تحقيق ذلك من خلال تحسين توزيع المكالمات وتدريب الموظفين على الرد السريع.',
          'مشروع يهدف إلى تقليل الأخطاء في إدخال البيانات في نظام المبيعات من 15 خطأ يومياً إلى أقل من 5 أخطاء، مما سيوفر 3 ساعات عمل يومياً ويحسن دقة التقارير.'
        ],
        warnings: [
          'تجنب الوصف العام أو الغامض',
          'لا تذكر تفاصيل تقنية معقدة',
          'تجنب الوعود غير الواقعية'
        ],
        bestPractices: [
          'اربط الوصف بأهداف المؤسسة الاستراتيجية',
          'استخدم أرقام محددة عند الإمكان',
          'اذكر الأثر الإيجابي على أصحاب المصلحة'
        ]
      }
    }
  },
  2: {
    overview: {
      title: 'العثور على المشكلة وقياسها',
      description: 'المرحلة الأهم في المشروع حيث نحدد المشكلة بدقة ونقيسها كمياً. بدون تحديد دقيق للمشكلة، لن نتمكن من حلها بفعالية.',
      tips: [
        'كن محدداً جداً في وصف المشكلة',
        'استخدم أرقام وإحصائيات حقيقية وموثقة',
        'اختر مؤشراً واحداً واضحاً وقابلاً للقياس',
        'تأكد من صحة اتجاه التحسن للمؤشر المختار'
      ],
      examples: [
        'مشكلة: تأخير في معالجة طلبات العملاء يؤدي لفقدان 20% منهم شهرياً',
        'المؤشر: متوسط وقت معالجة الطلب الواحد',
        'القيمة الحالية: 5 أيام عمل، المستهدفة: 2 يوم عمل'
      ],
      warnings: [
        'لا تخلط بين المشكلة والحل أو الأعراض',
        'تجنب الوصف العام غير المحدد',
        'لا تستخدم مؤشرات متعددة في نفس الوقت'
      ],
      bestPractices: [
        'اجمع البيانات من مصادر متعددة للتأكد',
        'احسب المتوسطات لفترة زمنية مناسبة',
        'وثق مصادر البيانات وطريقة الحساب'
      ]
    },
    fields: {
      problemDescription: {
        title: 'وصف المشكلة',
        description: 'وصف واضح ومحدد للمشكلة الحالية مع ذكر التأثير والأسباب الظاهرة',
        tips: [
          'ابدأ بوصف الوضع الحالي بدقة',
          'اذكر التأثير على العمل أو العملاء أو الجودة',
          'استخدم أرقام محددة وإحصائيات موثقة',
          'حدد متى وأين وكيف تحدث المشكلة'
        ],
        examples: [
          'يواجه قسم خدمة العملاء تأخيراً في الرد على الاستفسارات، حيث يصل وقت الانتظار إلى 15 دقيقة في المتوسط خلال ساعات الذروة (9-11 صباحاً و 2-4 عصراً)، مما أدى إلى زيادة الشكاوى بنسبة 30% خلال الربع الأخير وانخفاض رضا العملاء من 85% إلى 65%.',
          'تحدث أخطاء في إدخال البيانات في نظام المبيعات بمعدل 12-15 خطأ يومياً، معظمها في أرقام الهواتف والعناوين، مما يتطلب إعادة المعالجة ويؤخر إنجاز التقارير الشهرية بـ 3-4 أيام إضافية ويكلف المؤسسة 8 ساعات عمل إضافية أسبوعياً.'
        ],
        warnings: [
          'تجنب الوصف العام مثل "الخدمة بطيئة" أو "هناك مشاكل"',
          'لا تذكر الحلول أو الاقتراحات في وصف المشكلة',
          'تجنب اللغة العاطفية أو توجيه الاتهامات لأشخاص'
        ],
        bestPractices: [
          'استخدم نموذج "ماذا، متى، أين، كيف، لماذا"',
          'اجمع شهادات من المتأثرين بالمشكلة',
          'راجع البيانات التاريخية لفهم تطور المشكلة'
        ]
      },
      indicatorName: {
        title: 'اسم المؤشر',
        description: 'المؤشر الكمي الذي ستستخدمه لقياس المشكلة وتتبع التحسن',
        tips: [
          'اختر مؤشراً واحداً واضحاً وقابلاً للقياس',
          'يجب أن يكون مرتبطاً مباشرة بالمشكلة المحددة',
          'استخدم مؤشرات معروفة ومقبولة في مجال عملك',
          'تأكد من إمكانية الحصول على البيانات بانتظام'
        ],
        examples: [
          'متوسط وقت انتظار العميل (بالدقائق)',
          'معدل رضا العملاء (نسبة مئوية)',
          'عدد الأخطاء في إدخال البيانات (يومياً)',
          'وقت الاستجابة للطلبات (بالساعات)',
          'معدل إنجاز المهام في الوقت المحدد (نسبة مئوية)'
        ],
        warnings: [
          'تجنب المؤشرات الغامضة أو غير القابلة للقياس',
          'لا تستخدم مؤشرات متعددة في نفس الوقت',
          'تجنب المؤشرات التي لا ترتبط مباشرة بالمشكلة'
        ],
        bestPractices: [
          'اختر مؤشرات SMART (محددة، قابلة للقياس، قابلة للتحقيق، ذات صلة، محددة زمنياً)',
          'تأكد من أن المؤشر يعكس تجربة المستفيد النهائي',
          'استشر الخبراء في المجال لاختيار أفضل المؤشرات'
        ]
      }
    }
  },
  3: {
    overview: {
      title: 'تنظيم المشروع والموارد',
      description: 'هذه المرحلة تهدف إلى تشكيل فريق المشروع وتحديد الموارد والمهام اللازمة للنجاح',
      tips: [
        'اختر فريقاً متنوعاً من التخصصات المختلفة',
        'حدد الأدوار والمسؤوليات بوضوح',
        'تأكد من توفر الوقت والموارد اللازمة',
        'اشرك أصحاب المصلحة المهمين'
      ],
      examples: [
        'فريق مكون من قائد المشروع، خبير تقني، مختص جودة، وممثل عن المستخدمين',
        'موارد تشمل برامج تحليل البيانات، قاعة اجتماعات، وميزانية للتدريب',
        'مهام محددة بمواعيد نهائية واضحة ومسؤوليات محددة'
      ],
      warnings: [
        'تجنب الفرق الكبيرة جداً أو الصغيرة جداً',
        'لا تتجاهل أهمية تمثيل المستخدمين النهائيين',
        'تجنب تحديد مهام غامضة أو غير واضحة'
      ],
      bestPractices: [
        'اعقد اجتماعاً تعريفياً لتوضيح الأهداف والتوقعات',
        'استخدم أدوات إدارة المشاريع لتتبع التقدم',
        'راجع التقدم بانتظام واضبط الخطة حسب الحاجة'
      ]
    },
    fields: {
      teamLeader: {
        title: 'قائد الفريق',
        description: 'الشخص المسؤول عن قيادة مشروع التحسين وضمان تحقيق الأهداف',
        tips: [
          'اختر شخصاً لديه خبرة في المجال المحدد',
          'يجب أن يكون متفرغاً جزئياً على الأقل للمشروع',
          'يفضل أن يكون من نفس القسم أو قريباً من المشكلة',
          'لديه مهارات قيادة وتواصل وحل المشكلات'
        ],
        examples: [
          'مدير القسم المختص أو نائبه',
          'مشرف العمليات ذو الخبرة',
          'خبير في المجال المحدد مع خبرة إدارية',
          'شخص نجح في قيادة مشاريع تحسين سابقة'
        ],
        warnings: [
          'تجنب اختيار شخص غير متفرغ أو مشغول جداً',
          'لا تختار شخصاً ليس له صلاحيات كافية',
          'تجنب اختيار شخص بعيد عن المشكلة أو المجال'
        ],
        bestPractices: [
          'تأكد من التزام الإدارة العليا بدعم قائد الفريق',
          'حدد الصلاحيات والمسؤوليات بوضوح',
          'وفر التدريب اللازم إذا كان يحتاج مهارات إضافية'
        ]
      }
    }
  },
  4: {
    overview: {
      title: 'توضيح العمليات المتأثرة',
      description: 'فهم العمليات الحالية التي تحتوي على المشكلة وتحديد نطاق التأثير بدقة',
      tips: [
        'ارسم خريطة العملية بالتفصيل',
        'حدد جميع المدخلات والمخرجات',
        'اشرك الأشخاص المعنيين في الوصف',
        'وثق نقاط القرار والتحويل'
      ],
      examples: [
        'عملية معالجة طلبات العملاء من الاستلام حتى التسليم',
        'سير عمل الموافقات الإدارية مع تحديد المسؤوليات',
        'عملية إنتاج التقارير الشهرية مع مصادر البيانات'
      ],
      warnings: [
        'تجنب الوصف المبسط الذي يفوت تفاصيل مهمة',
        'لا تفترض معرفة القارئ بالعملية',
        'تجنب التركيز على الأعراض بدلاً من العملية الأساسية'
      ],
      bestPractices: [
        'استخدم مخططات بصرية لتوضيح العملية',
        'اجمع معلومات من مصادر متعددة',
        'تحقق من دقة الوصف مع المختصين'
      ]
    },
    fields: {}
  },
  5: {
    overview: {
      title: 'فهم الأسباب الجذرية',
      description: 'تحليل عميق لاكتشاف الأسباب الحقيقية وراء المشكلة باستخدام أدوات التحليل المناسبة',
      tips: [
        'لا تتوقف عند الأعراض الظاهرة',
        'استخدم أكثر من طريقة تحليل إذا لزم الأمر',
        'اشرك فريق متنوع في التحليل',
        'اسأل "لماذا" بشكل متكرر'
      ],
      examples: [
        'استخدام خمسة لماذا لتحليل تأخير الشحنات',
        'مخطط عظمة السمكة لتحليل أخطاء الإنتاج',
        'تحليل السبب الجذري لمشاكل جودة الخدمة'
      ],
      warnings: [
        'تجنب القفز للاستنتاجات السريعة',
        'لا تلوم الأشخاص بل ركز على العمليات',
        'تجنب التحليل السطحي غير المكتمل'
      ],
      bestPractices: [
        'استخدم البيانات والحقائق في التحليل',
        'وثق جميع خطوات التحليل',
        'راجع النتائج مع خبراء آخرين'
      ]
    },
    fields: {}
  },
  6: {
    overview: {
      title: 'اختيار الحل الأمثل',
      description: 'تحديد حل واحد فعال وقابل للتنفيذ مع خطة تنفيذ واضحة',
      tips: [
        'اختر حلاً واحداً فقط للتركيز',
        'تأكد من قابلية التنفيذ بالموارد المتاحة',
        'ربط الحل بالسبب الجذري المحدد',
        'حدد مؤشرات نجاح واضحة'
      ],
      examples: [
        'تطبيق نظام إلكتروني لتسريع المعالجة',
        'برنامج تدريب مكثف لتحسين المهارات',
        'إعادة تصميم العملية لتقليل الخطوات'
      ],
      warnings: [
        'تجنب اختيار حلول متعددة معقدة',
        'لا تختار حلولاً مكلفة جداً أو صعبة التنفيذ',
        'تجنب الحلول التي لا تعالج السبب الجذري'
      ],
      bestPractices: [
        'اختبر الحل على نطاق صغير أولاً',
        'احسب العائد على الاستثمار',
        'ضع خطة للمخاطر المحتملة'
      ]
    },
    fields: {}
  },
  7: {
    overview: {
      title: 'إدارة المخاطر - RAID Matrix',
      description: 'تحديد وإدارة المخاطر والتحديات التي قد تواجه المشروع',
      tips: [
        'كن صادقاً في تحديد المخاطر المحتملة',
        'ضع خطط تخفيف واقعية لكل مخاطرة',
        'راجع المصفوفة بانتظام أثناء التنفيذ',
        'اشرك الفريق في تحديد المخاطر'
      ],
      examples: [
        'مخاطر: نقص الموارد البشرية - التخفيف: تدريب فريق احتياطي',
        'افتراض: توفر الميزانية - التحقق: تأكيد رسمي من الإدارة المالية',
        'مشكلة: مقاومة التغيير - الحل: برنامج توعية وتواصل'
      ],
      warnings: [
        'لا تتجاهل المخاطر الصغيرة التي قد تتراكم',
        'تجنب وضع خطط تخفيف غير واقعية',
        'لا تفترض أن كل شيء سيسير وفق الخطة'
      ],
      bestPractices: [
        'صنف المخاطر حسب الاحتمالية والتأثير',
        'حدد مسؤولاً عن كل عنصر في المصفوفة',
        'راجع وحدث المصفوفة أسبوعياً'
      ]
    },
    fields: {}
  }
}

export const getHelpContent = (step: number, field?: string): HelpContent | null => {
  const stepGuide = helpDatabase[step]
  if (!stepGuide) return null
  
  if (field && stepGuide.fields[field]) {
    return stepGuide.fields[field]
  }
  
  return stepGuide.overview
}

export const getStepOverview = (step: number): HelpContent | null => {
  const stepGuide = helpDatabase[step]
  return stepGuide ? stepGuide.overview : null
} 