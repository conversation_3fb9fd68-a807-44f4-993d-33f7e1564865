import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

// GET - التحقق من الجلسة
export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get('session_token')?.value

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'لا توجد جلسة نشطة' },
        { status: 401 }
      )
    }

    // TODO: التحقق من الجلسة في قاعدة البيانات
    // مؤقتاً سنتحقق من وجود المستخدم في localStorage
    
    // للآن سنرجع بيانات مؤقتة
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      name: 'مدير النظام',
      department: { id: '1', name: 'مكتب إدارة المشاريع' },
      role: { 
        id: '1', 
        name: 'admin', 
        display_name: 'مدير النظام',
        permissions: { all: true }
      }
    }

    return NextResponse.json({
      success: true,
      user: mockUser
    })

  } catch (error) {
    console.error('Verify API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
} 