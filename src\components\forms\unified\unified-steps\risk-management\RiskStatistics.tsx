'use client'

import React from 'react'
import { Card } from '@/components/ui/Card'
import { AlertTriangle, Shield, TrendingUp, CheckCircle } from 'lucide-react'

interface Risk {
  id: string
  description: string
  probability: 'low' | 'medium' | 'high'
  impact: 'low' | 'medium' | 'high'
  mitigation?: string
}

interface RiskStatisticsProps {
  risks: Risk[]
}

export function RiskStatistics({ risks }: RiskStatisticsProps) {
  // حساب مستوى المخاطرة
  const getRiskLevel = (probability: string, impact: string) => {
    const levels = {
      'low-low': 'low',
      'low-medium': 'low',
      'low-high': 'medium',
      'medium-low': 'low',
      'medium-medium': 'medium',
      'medium-high': 'high',
      'high-low': 'medium',
      'high-medium': 'high',
      'high-high': 'high'
    }
    return levels[`${probability}-${impact}` as keyof typeof levels] || 'medium'
  }

  // حساب الإحصائيات
  const stats = {
    total: risks.length,
    high: risks.filter(r => getRiskLevel(r.probability, r.impact) === 'high').length,
    medium: risks.filter(r => getRiskLevel(r.probability, r.impact) === 'medium').length,
    low: risks.filter(r => getRiskLevel(r.probability, r.impact) === 'low').length,
    withMitigation: risks.filter(r => r.mitigation && r.mitigation.trim().length > 0).length
  }

  const mitigationPercentage = stats.total > 0 ? Math.round((stats.withMitigation / stats.total) * 100) : 0

  if (stats.total === 0) {
    return null
  }

  return (
    <Card className="p-6 bg-gray-50">
      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
        <TrendingUp className="w-5 h-5" />
        إحصائيات المخاطر
      </h4>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {/* إجمالي المخاطر */}
        <div className="text-center p-3 bg-white rounded-lg border">
          <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
          <div className="text-sm text-gray-600">إجمالي المخاطر</div>
        </div>

        {/* مخاطر عالية */}
        <div className="text-center p-3 bg-white rounded-lg border">
          <div className="text-2xl font-bold text-red-600">{stats.high}</div>
          <div className="text-sm text-gray-600">مخاطر عالية</div>
          <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
            <div 
              className="bg-red-500 h-1 rounded-full" 
              style={{ width: `${stats.total > 0 ? (stats.high / stats.total) * 100 : 0}%` }}
            />
          </div>
        </div>

        {/* مخاطر متوسطة */}
        <div className="text-center p-3 bg-white rounded-lg border">
          <div className="text-2xl font-bold text-yellow-600">{stats.medium}</div>
          <div className="text-sm text-gray-600">مخاطر متوسطة</div>
          <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
            <div 
              className="bg-yellow-500 h-1 rounded-full" 
              style={{ width: `${stats.total > 0 ? (stats.medium / stats.total) * 100 : 0}%` }}
            />
          </div>
        </div>

        {/* مخاطر منخفضة */}
        <div className="text-center p-3 bg-white rounded-lg border">
          <div className="text-2xl font-bold text-green-600">{stats.low}</div>
          <div className="text-sm text-gray-600">مخاطر منخفضة</div>
          <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
            <div 
              className="bg-green-500 h-1 rounded-full" 
              style={{ width: `${stats.total > 0 ? (stats.low / stats.total) * 100 : 0}%` }}
            />
          </div>
        </div>
      </div>

      {/* نسبة التخفيف */}
      <div className="mt-4 p-3 bg-white rounded-lg border">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">خطط التخفيف</span>
          <span className="text-sm text-gray-600">{stats.withMitigation} من {stats.total}</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-500 h-2 rounded-full transition-all duration-300" 
            style={{ width: `${mitigationPercentage}%` }}
          />
        </div>
        <div className="text-xs text-gray-500 mt-1">
          {mitigationPercentage}% من المخاطر لديها خطط تخفيف
        </div>
      </div>

      {/* تنبيهات */}
      {stats.high > 0 && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 text-red-800">
            <AlertTriangle className="w-4 h-4" />
            <span className="font-medium">تنبيه: يوجد {stats.high} مخاطر عالية</span>
          </div>
          <p className="text-xs text-red-700 mt-1">
            يُنصح بوضع خطط تخفيف مفصلة للمخاطر عالية المستوى
          </p>
        </div>
      )}

      {mitigationPercentage === 100 && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center gap-2 text-green-800">
            <CheckCircle className="w-4 h-4" />
            <span className="font-medium">ممتاز! جميع المخاطر لديها خطط تخفيف</span>
          </div>
        </div>
      )}
    </Card>
  )
} 