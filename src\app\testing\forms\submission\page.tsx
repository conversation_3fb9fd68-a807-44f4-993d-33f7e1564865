'use client'

import Link from 'next/link'
import { ArrowLeft, Send, CheckCircle, AlertCircle, Clock } from 'lucide-react'
import { useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

export default function SubmissionTestPage() {
  const [testResults, setTestResults] = useState<{[key: string]: any}>({})
  const [loading, setLoading] = useState(false)

  const runSubmissionTest = async (testType: string) => {
    setLoading(true)
    try {
      // محاكاة اختبار الإرسال
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      setTestResults(prev => ({
        ...prev,
        [testType]: {
          success: Math.random() > 0.3, // 70% نجاح
          timestamp: new Date().toISOString(),
          responseTime: Math.floor(Math.random() * 1000) + 500
        }
      }))
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [testType]: { success: false, error: 'فشل الاختبار' }
      }))
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (testType: string) => {
    const result = testResults[testType]
    if (!result) return <Clock className="w-5 h-5 text-gray-400" />
    return result.success 
      ? <CheckCircle className="w-5 h-5 text-green-500" />
      : <AlertCircle className="w-5 h-5 text-red-500" />
  }

  const submissionTests = [
    {
      id: 'form_validation',
      title: 'اختبار التحقق من النموذج',
      description: 'اختبار التحقق من صحة البيانات قبل الإرسال'
    },
    {
      id: 'api_submission',
      title: 'اختبار إرسال API',
      description: 'اختبار إرسال البيانات إلى الخادم'
    },
    {
      id: 'database_save',
      title: 'اختبار حفظ قاعدة البيانات',
      description: 'اختبار حفظ البيانات في قاعدة البيانات'
    },
    {
      id: 'notification_send',
      title: 'اختبار إرسال الإشعارات',
      description: 'اختبار إرسال الإشعارات للمعتمدين'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Navigation */}
        <div className="mb-6">
          <Link 
            href="/testing/forms" 
            className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            العودة إلى اختبارات النماذج
          </Link>
        </div>

        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Send className="w-8 h-8 text-orange-600" />
            <h1 className="text-3xl font-bold text-gray-900">
              اختبار إرسال النماذج
            </h1>
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            اختبار شامل لعملية إرسال النماذج والتحقق من جميع المراحل
          </p>
        </div>

        {/* Test Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {submissionTests.map((test) => (
            <Card key={test.id} className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  {test.title}
                </h3>
                {getStatusIcon(test.id)}
              </div>
              
              <p className="text-gray-600 mb-4">
                {test.description}
              </p>

              {testResults[test.id] && (
                <div className={`mb-4 p-3 rounded-lg ${
                  testResults[test.id].success 
                    ? 'bg-green-50 border border-green-200' 
                    : 'bg-red-50 border border-red-200'
                }`}>
                  <p className={`font-medium ${
                    testResults[test.id].success ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {testResults[test.id].success ? 'نجح الاختبار' : 'فشل الاختبار'}
                  </p>
                  {testResults[test.id].responseTime && (
                    <p className="text-sm text-gray-600">
                      وقت الاستجابة: {testResults[test.id].responseTime}ms
                    </p>
                  )}
                </div>
              )}

              <Button
                onClick={() => runSubmissionTest(test.id)}
                disabled={loading}
                className="w-full"
                variant={testResults[test.id]?.success ? 'secondary' : 'primary'}
              >
                {loading ? 'جاري الاختبار...' : 'تشغيل الاختبار'}
                <Send className="w-4 h-4 mr-2" />
              </Button>
            </Card>
          ))}
        </div>

        {/* Summary */}
        <Card className="mt-8 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            ملخص الاختبارات
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600">
                {Object.keys(testResults).length}
              </p>
              <p className="text-sm text-gray-600">اختبارات مكتملة</p>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">
                {Object.values(testResults).filter((r: any) => r.success).length}
              </p>
              <p className="text-sm text-gray-600">اختبارات ناجحة</p>
            </div>
            
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <p className="text-2xl font-bold text-red-600">
                {Object.values(testResults).filter((r: any) => !r.success).length}
              </p>
              <p className="text-sm text-gray-600">اختبارات فاشلة</p>
            </div>
          </div>

          <div className="mt-6 flex justify-center">
            <Button
              onClick={() => {
                submissionTests.forEach(test => runSubmissionTest(test.id))
              }}
              disabled={loading}
              className="px-8"
            >
              تشغيل جميع الاختبارات
            </Button>
          </div>
        </Card>
      </div>
    </div>
  )
}
