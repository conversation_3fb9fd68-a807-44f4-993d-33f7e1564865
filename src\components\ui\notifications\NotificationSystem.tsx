'use client'

import React, { useState, useEffect, createContext, useContext, ReactNode } from 'react'
import { X, CheckCircle, AlertTriangle, AlertCircle, Info, Loader2, Bell } from 'lucide-react'
import { 
  BaseNotification, 
  ToastNotification, 
  SystemNotification, 
  PopupNotification,
  NotificationSettings,
  NotificationType,
  NotificationPriority,
  NotificationPosition
} from './NotificationTypes'

// واجهة السياق
interface NotificationContextType {
  // Toast notifications
  toasts: ToastNotification[]
  addToast: (toast: Omit<ToastNotification, 'id'>) => string
  removeToast: (id: string) => void
  updateToast: (id: string, updates: Partial<ToastNotification>) => void
  
  // System notifications
  systemNotifications: SystemNotification[]
  addSystemNotification: (notification: Omit<SystemNotification, 'id' | 'timestamp'>) => string
  markAsRead: (id: string) => void
  markAllAsRead: () => void
  
  // Popup notifications
  popupNotifications: PopupNotification[]
  addPopupNotification: (notification: Omit<PopupNotification, 'id'>) => string
  
  // Common
  clearAll: () => void
  settings: NotificationSettings
  updateSettings: (settings: Partial<NotificationSettings>) => void
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

// مزود السياق الموحد
export function NotificationProvider({ 
  children, 
  defaultSettings = {} 
}: { 
  children: ReactNode
  defaultSettings?: Partial<NotificationSettings>
}) {
  const [toasts, setToasts] = useState<ToastNotification[]>([])
  const [systemNotifications, setSystemNotifications] = useState<SystemNotification[]>([])
  const [popupNotifications, setPopupNotifications] = useState<PopupNotification[]>([])
  const [settings, setSettings] = useState<NotificationSettings>({
    maxVisible: 5,
    defaultDuration: 5000,
    autoRefresh: true,
    refreshInterval: 30000,
    showFilters: true,
    position: 'bottom-right',
    enableSound: false,
    enableVibration: false,
    ...defaultSettings
  })

  // إضافة Toast
  const addToast = (toast: Omit<ToastNotification, 'id'>) => {
    const id = generateId()
    const newToast: ToastNotification = {
      ...toast,
      id,
      duration: toast.duration ?? settings.defaultDuration!,
      timestamp: new Date().toISOString()
    }

    setToasts(prev => [...prev.slice(-(settings.maxVisible! - 1)), newToast])

    // إزالة تلقائية
    if (newToast.duration > 0 && !newToast.persistent) {
      setTimeout(() => removeToast(id), newToast.duration)
    }

    return id
  }

  // إضافة إشعار نظام
  const addSystemNotification = (notification: Omit<SystemNotification, 'id' | 'timestamp'>) => {
    const id = generateId()
    const newNotification: SystemNotification = {
      ...notification,
      id,
      timestamp: new Date().toISOString(),
      isRead: false
    }

    setSystemNotifications(prev => [newNotification, ...prev.slice(0, settings.maxVisible! - 1)])
    return id
  }

  // إضافة إشعار منبثق
  const addPopupNotification = (notification: Omit<PopupNotification, 'id'>) => {
    const id = generateId()
    const newNotification: PopupNotification = {
      ...notification,
      id,
      timestamp: new Date().toISOString(),
      position: notification.position ?? settings.position
    }

    setPopupNotifications(prev => [...prev.slice(-(settings.maxVisible! - 1)), newNotification])

    // إزالة تلقائية
    if (newNotification.duration && newNotification.duration > 0) {
      setTimeout(() => removePopupNotification(id), newNotification.duration)
    }

    return id
  }

  // وظائف الإزالة
  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(t => t.id !== id))
  }

  const removeSystemNotification = (id: string) => {
    setSystemNotifications(prev => prev.filter(n => n.id !== id))
  }

  const removePopupNotification = (id: string) => {
    setPopupNotifications(prev => prev.filter(n => n.id !== id))
  }

  // تحديث Toast
  const updateToast = (id: string, updates: Partial<ToastNotification>) => {
    setToasts(prev => prev.map(t => t.id === id ? { ...t, ...updates } : t))
  }

  // تحديد كمقروء
  const markAsRead = (id: string) => {
    setSystemNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, isRead: true } : n)
    )
  }

  // تحديد الكل كمقروء
  const markAllAsRead = () => {
    setSystemNotifications(prev => prev.map(n => ({ ...n, isRead: true })))
  }

  // مسح الكل
  const clearAll = () => {
    setToasts([])
    setSystemNotifications([])
    setPopupNotifications([])
  }

  // تحديث الإعدادات
  const updateSettings = (newSettings: Partial<NotificationSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }))
  }

  // توليد ID
  const generateId = () => Math.random().toString(36).substring(2, 9)

  return (
    <NotificationContext.Provider value={{
      toasts,
      addToast,
      removeToast,
      updateToast,
      systemNotifications,
      addSystemNotification,
      markAsRead,
      markAllAsRead,
      popupNotifications,
      addPopupNotification,
      clearAll,
      settings,
      updateSettings
    }}>
      {children}
      <ToastContainer />
      <PopupContainer />
    </NotificationContext.Provider>
  )
}

// مكون عرض Toast
function ToastContainer() {
  const { toasts, removeToast } = useNotifications()

  return (
    <div className="fixed bottom-4 left-4 z-50 space-y-2 pointer-events-none">
      {toasts.map(toast => (
        <ToastItem key={toast.id} toast={toast} onRemove={() => removeToast(toast.id)} />
      ))}
    </div>
  )
}

// مكون عرض Popup
function PopupContainer() {
  const { popupNotifications, settings } = useNotifications()

  const getPositionClasses = (position: NotificationPosition) => {
    switch (position) {
      case 'top-right': return 'top-4 left-4'
      case 'top-left': return 'top-4 right-4'
      case 'bottom-right': return 'bottom-4 left-4'
      case 'bottom-left': return 'bottom-4 right-4'
      default: return 'bottom-4 left-4'
    }
  }

  return (
    <div className={`fixed ${getPositionClasses(settings.position!)} z-50 space-y-2 pointer-events-none`}>
      {popupNotifications.map(notification => (
        <PopupItem key={notification.id} notification={notification} />
      ))}
    </div>
  )
}

// مكون Toast فردي
function ToastItem({ toast, onRemove }: { toast: ToastNotification; onRemove: () => void }) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 10)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div
      className={`transform transition-all duration-300 ease-out pointer-events-auto ${
        isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
      }`}
    >
      <NotificationCard notification={toast} onRemove={onRemove} />
    </div>
  )
}

// مكون Popup فردي
function PopupItem({ notification }: { notification: PopupNotification }) {
  return (
    <div className="transform transition-all duration-300 ease-out pointer-events-auto">
      <NotificationCard notification={notification} />
    </div>
  )
}

// مكون البطاقة الموحد
function NotificationCard({ 
  notification, 
  onRemove 
}: { 
  notification: BaseNotification
  onRemove?: () => void 
}) {
  const getStyles = (type: NotificationType) => {
    const baseStyles = 'flex items-start gap-3 p-4 rounded-lg shadow-lg border-2 backdrop-blur-sm min-w-80 max-w-md'
    
    switch (type) {
      case 'success':
        return `${baseStyles} bg-green-50 border-green-200 text-green-800`
      case 'error':
        return `${baseStyles} bg-red-50 border-red-200 text-red-800`
      case 'warning':
        return `${baseStyles} bg-yellow-50 border-yellow-200 text-yellow-800`
      case 'info':
        return `${baseStyles} bg-blue-50 border-blue-200 text-blue-800`
      case 'loading':
        return `${baseStyles} bg-gray-50 border-gray-200 text-gray-800`
      default:
        return `${baseStyles} bg-white border-gray-200 text-gray-800`
    }
  }

  const getIcon = (type: NotificationType) => {
    const iconClass = 'w-5 h-5 flex-shrink-0 mt-0.5'
    
    switch (type) {
      case 'success':
        return <CheckCircle className={`${iconClass} text-green-600`} />
      case 'error':
        return <AlertCircle className={`${iconClass} text-red-600`} />
      case 'warning':
        return <AlertTriangle className={`${iconClass} text-yellow-600`} />
      case 'info':
        return <Info className={`${iconClass} text-blue-600`} />
      case 'loading':
        return <Loader2 className={`${iconClass} text-gray-600 animate-spin`} />
      default:
        return <Bell className={`${iconClass} text-gray-600`} />
    }
  }

  return (
    <div className={getStyles(notification.type)}>
      {getIcon(notification.type)}
      
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h4 className="font-medium text-sm leading-tight">
              {notification.title}
            </h4>
            {notification.message && (
              <p className="text-sm mt-1 opacity-90">
                {notification.message}
              </p>
            )}
          </div>
          
          {onRemove && notification.type !== 'loading' && (
            <button
              onClick={onRemove}
              className="mr-2 p-1 rounded-full hover:bg-black hover:bg-opacity-10 transition-colors"
              aria-label="إغلاق الإخطار"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
        
        {notification.action && (
          <button
            onClick={notification.action.onClick}
            className="mt-2 text-sm font-medium underline hover:no-underline transition-all"
          >
            {notification.action.label}
          </button>
        )}
      </div>
    </div>
  )
}

// هوك الاستخدام
export function useNotifications() {
  const context = useContext(NotificationContext)
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider')
  }
  return context
} 