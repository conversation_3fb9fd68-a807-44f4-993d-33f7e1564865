'use client'

import React, { useState } from 'react'
import { HelpCircle, X, Lightbulb, AlertTriangle, CheckCircle, Info } from 'lucide-react'
import { getHelpContent } from './HelpContent'

interface InteractiveTooltipProps {
  step: number
  field: string
  children: React.ReactNode
}

export function InteractiveTooltip({ step, field, children }: InteractiveTooltipProps) {
  const [isOpen, setIsOpen] = useState(false)
  const helpContent = getHelpContent(step, field)

  if (!helpContent) return <>{children}</>

  return (
    <div className="relative">
      {children}
      
      {/* أيقونة المساعدة */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors shadow-lg"
        title="عرض المساعدة"
      >
        <HelpCircle className="w-4 h-4" />
      </button>

      {/* نافذة المساعدة المنبثقة */}
      {isOpen && (
        <>
          {/* خلفية شفافة */}
          <div 
            className="fixed inset-0 bg-black bg-opacity-25 z-40"
            onClick={() => setIsOpen(false)}
          />
          
          {/* محتوى المساعدة */}
          <div className="absolute top-8 right-0 w-96 bg-white border border-gray-200 rounded-lg shadow-xl z-50 p-4">
            {/* رأس النافذة */}
            <div className="flex justify-between items-start mb-3">
              <h3 className="font-semibold text-gray-900 text-sm">
                {helpContent.title}
              </h3>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-4 h-4" />
              </button>
            </div>

            {/* الوصف */}
            <p className="text-sm text-gray-700 mb-4">
              {helpContent.description}
            </p>

            {/* النصائح */}
            {helpContent.tips.length > 0 && (
              <div className="mb-4">
                <h4 className="font-medium text-green-800 text-sm mb-2 flex items-center gap-1">
                  <Lightbulb className="w-4 h-4" />
                  نصائح مهمة
                </h4>
                <ul className="space-y-1">
                  {helpContent.tips.slice(0, 3).map((tip, index) => (
                    <li key={index} className="text-xs text-green-700 flex items-start gap-1">
                      <CheckCircle className="w-3 h-3 mt-0.5 flex-shrink-0" />
                      {tip}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* مثال سريع */}
            {helpContent.examples.length > 0 && (
              <div className="mb-4">
                <h4 className="font-medium text-blue-800 text-sm mb-2 flex items-center gap-1">
                  <Info className="w-4 h-4" />
                  مثال
                </h4>
                <div className="bg-blue-50 border border-blue-200 rounded p-2">
                  <p className="text-xs text-blue-800">
                    {helpContent.examples[0]}
                  </p>
                </div>
              </div>
            )}

            {/* تحذير سريع */}
            {helpContent.warnings.length > 0 && (
              <div className="mb-3">
                <h4 className="font-medium text-red-800 text-sm mb-2 flex items-center gap-1">
                  <AlertTriangle className="w-4 h-4" />
                  تجنب
                </h4>
                <p className="text-xs text-red-700">
                  {helpContent.warnings[0]}
                </p>
              </div>
            )}

            {/* رابط للمزيد */}
            <button
              className="w-full text-center text-xs text-blue-600 hover:text-blue-800 font-medium border-t border-gray-200 pt-2"
              onClick={() => {
                setIsOpen(false)
                // يمكن إضافة فتح نافذة المساعدة الكاملة هنا
              }}
            >
              عرض الدليل الكامل
            </button>
          </div>
        </>
      )}
    </div>
  )
}

// مكون مبسط للنصائح السريعة
interface QuickTipProps {
  tip: string
  type?: 'info' | 'warning' | 'success'
}

export function QuickTip({ tip, type = 'info' }: QuickTipProps) {
  const colors = {
    info: 'bg-blue-50 border-blue-200 text-blue-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    success: 'bg-green-50 border-green-200 text-green-800'
  }

  const icons = {
    info: <Info className="w-4 h-4" />,
    warning: <AlertTriangle className="w-4 h-4" />,
    success: <CheckCircle className="w-4 h-4" />
  }

  return (
    <div className={`flex items-start gap-2 p-3 rounded-lg border ${colors[type]} text-sm`}>
      {icons[type]}
      <p>{tip}</p>
    </div>
  )
}

// مكون لعرض أمثلة سريعة
interface ExampleBoxProps {
  title: string
  example: string
  type?: 'good' | 'bad'
}

export function ExampleBox({ title, example, type = 'good' }: ExampleBoxProps) {
  const colors = type === 'good' 
    ? 'bg-green-50 border-green-200' 
    : 'bg-red-50 border-red-200'
  
  const textColors = type === 'good'
    ? 'text-green-800'
    : 'text-red-800'

  return (
    <div className={`border rounded-lg p-3 ${colors}`}>
      <div className="flex items-center gap-2 mb-2">
        {type === 'good' ? (
          <CheckCircle className="w-4 h-4 text-green-600" />
        ) : (
          <X className="w-4 h-4 text-red-600" />
        )}
        <span className={`font-medium text-sm ${textColors}`}>
          {title}
        </span>
      </div>
      <p className={`text-sm ${textColors}`}>
        {example}
      </p>
    </div>
  )
} 