import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

// واجهة بيانات الإشعار
interface NotificationData {
  user_id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  action_url?: string
  data?: any
}

// POST - إنشاء إشعار جديد
export async function POST(request: NextRequest) {
  try {
    const body: NotificationData = await request.json()
    
    // التحقق من صحة البيانات
    if (!body.user_id || !body.title || !body.message || !body.type) {
      return NextResponse.json(
        { error: 'البيانات المطلوبة غير مكتملة' },
        { status: 400 }
      )
    }

    // إنشاء الإشعار
    const { data, error } = await supabase
      .from('notifications')
      .insert({
        user_id: body.user_id,
        title: body.title,
        message: body.message,
        type: body.type,
        action_url: body.action_url,
        data: body.data
      })
      .select()
      .single()

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في إنشاء الإشعار' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء الإشعار بنجاح',
      data
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// GET - استرجاع الإشعارات
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    const isRead = searchParams.get('is_read')
    const type = searchParams.get('type')
    const limit = searchParams.get('limit')

    if (!userId) {
      return NextResponse.json(
        { error: 'معرف المستخدم مطلوب' },
        { status: 400 }
      )
    }

    let query = supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)

    // تطبيق الفلاتر
    if (isRead !== null) {
      query = query.eq('is_read', isRead === 'true')
    }
    if (type) {
      query = query.eq('type', type)
    }

    // ترتيب وتحديد العدد
    query = query.order('created_at', { ascending: false })
    if (limit) {
      query = query.limit(parseInt(limit))
    }

    const { data, error } = await query

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في استرجاع الإشعارات' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: data || []
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// PUT - تحديث حالة الإشعار (قراءة/عدم قراءة)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { notification_id, is_read, user_id } = body

    if (!notification_id || is_read === undefined) {
      return NextResponse.json(
        { error: 'معرف الإشعار وحالة القراءة مطلوبان' },
        { status: 400 }
      )
    }

    // تحديث حالة الإشعار
    let query = supabase
      .from('notifications')
      .update({ 
        is_read,
        read_at: is_read ? new Date().toISOString() : null 
      })
      .eq('id', notification_id)

    // إضافة شرط المستخدم للأمان
    if (user_id) {
      query = query.eq('user_id', user_id)
    }

    const { data, error } = await query.select().single()

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في تحديث الإشعار' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث حالة الإشعار بنجاح',
      data
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// DELETE - حذف إشعار
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const notificationId = searchParams.get('notification_id')
    const userId = searchParams.get('user_id')

    if (!notificationId) {
      return NextResponse.json(
        { error: 'معرف الإشعار مطلوب' },
        { status: 400 }
      )
    }

    // حذف الإشعار
    let query = supabase
      .from('notifications')
      .delete()
      .eq('id', notificationId)

    // إضافة شرط المستخدم للأمان
    if (userId) {
      query = query.eq('user_id', userId)
    }

    const { error } = await query

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في حذف الإشعار' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الإشعار بنجاح'
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
} 