-- Migration: إنشاء نظام مؤشرات الأداء (KPIs)
-- التاريخ: 2024-01-26
-- الوصف: إضافة جداول المؤشرات وربطها بالنظام الحالي

-- =============================================
-- جدول أنواع المؤشرات
-- =============================================
CREATE TABLE IF NOT EXISTS kpi_types (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(50) UNIQUE NOT NULL,
  display_name VARCHAR(100) NOT NULL,
  description TEXT,
  default_unit VARCHAR(20), -- 'minutes', 'hours', 'days', 'count', 'percentage', 'currency'
  icon VARCHAR(50), -- اسم الأيقونة للواجهة
  color VARCHAR(20), -- لون المؤشر في الواجهة
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- جدول المؤشرات الرئيسي
-- =============================================
CREATE TABLE IF NOT EXISTS project_kpis (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_request_id UUID REFERENCES project_requests(id) ON DELETE CASCADE NOT NULL,
  kpi_type_id UUID REFERENCES kpi_types(id) NOT NULL,
  
  -- معلومات المؤشر
  name VARCHAR(200) NOT NULL,
  description TEXT,
  measurement_method TEXT, -- كيفية قياس المؤشر
  data_source VARCHAR(200), -- مصدر البيانات
  
  -- القيم
  baseline_value DECIMAL(15,2), -- القيمة الأساسية (قبل المشروع)
  target_value DECIMAL(15,2) NOT NULL, -- القيمة المستهدفة
  current_value DECIMAL(15,2), -- القيمة الحالية
  unit VARCHAR(20), -- وحدة القياس المحددة
  
  -- حالة المؤشر
  status VARCHAR(20) CHECK (status IN ('draft', 'pending_review', 'approved', 'rejected', 'active', 'completed')) DEFAULT 'draft',
  priority VARCHAR(10) CHECK (priority IN ('low', 'medium', 'high')) DEFAULT 'medium',
  
  -- الموافقة
  is_approved BOOLEAN DEFAULT FALSE,
  approved_by UUID REFERENCES users(id),
  approved_at TIMESTAMPTZ,
  rejection_reason TEXT,
  
  -- معلومات إضافية
  is_primary BOOLEAN DEFAULT FALSE, -- هل هو المؤشر الرئيسي للمشروع
  calculation_formula TEXT, -- معادلة الحساب إذا كان مؤشر محسوب
  target_date DATE, -- تاريخ تحقيق الهدف
  
  -- تواريخ
  created_by UUID REFERENCES users(id) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- جدول قياسات المؤشرات
-- =============================================
CREATE TABLE IF NOT EXISTS kpi_measurements (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  kpi_id UUID REFERENCES project_kpis(id) ON DELETE CASCADE NOT NULL,
  
  -- القياس
  measured_value DECIMAL(15,2) NOT NULL,
  measurement_date DATE NOT NULL,
  measurement_period VARCHAR(20), -- 'daily', 'weekly', 'monthly', 'quarterly'
  
  -- معلومات القياس
  notes TEXT,
  data_quality VARCHAR(20) CHECK (data_quality IN ('excellent', 'good', 'fair', 'poor')) DEFAULT 'good',
  confidence_level VARCHAR(20) CHECK (confidence_level IN ('high', 'medium', 'low')) DEFAULT 'medium',
  
  -- التحقق
  is_verified BOOLEAN DEFAULT FALSE,
  verified_by UUID REFERENCES users(id),
  verified_at TIMESTAMPTZ,
  
  -- القائم بالقياس
  measured_by UUID REFERENCES users(id) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- جدول قوالب المؤشرات
-- =============================================
CREATE TABLE IF NOT EXISTS kpi_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  kpi_type_id UUID REFERENCES kpi_types(id) NOT NULL,
  
  -- معلومات القالب
  name VARCHAR(200) NOT NULL,
  description TEXT,
  category VARCHAR(50), -- 'healthcare', 'finance', 'operations', 'hr', 'it'
  
  -- القالب
  template_data JSONB NOT NULL DEFAULT '{}',
  measurement_method TEXT,
  typical_baseline DECIMAL(15,2),
  typical_target DECIMAL(15,2),
  
  -- حالة القالب
  is_active BOOLEAN DEFAULT TRUE,
  usage_count INTEGER DEFAULT 0,
  
  -- تواريخ
  created_by UUID REFERENCES users(id) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- جدول تحليل المؤشرات
-- =============================================
CREATE TABLE IF NOT EXISTS kpi_analysis (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  kpi_id UUID REFERENCES project_kpis(id) ON DELETE CASCADE NOT NULL,
  
  -- فترة التحليل
  analysis_period_start DATE NOT NULL,
  analysis_period_end DATE NOT NULL,
  
  -- النتائج
  improvement_percentage DECIMAL(5,2), -- نسبة التحسن
  trend VARCHAR(20) CHECK (trend IN ('improving', 'stable', 'declining')) DEFAULT 'stable',
  target_achievement_percentage DECIMAL(5,2), -- نسبة تحقيق الهدف
  
  -- التحليل
  analysis_notes TEXT,
  recommendations TEXT,
  
  -- القائم بالتحليل
  analyzed_by UUID REFERENCES users(id) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- الفهارس للأداء
-- =============================================
CREATE INDEX IF NOT EXISTS idx_kpi_types_name ON kpi_types(name);
CREATE INDEX IF NOT EXISTS idx_kpi_types_active ON kpi_types(is_active);

CREATE INDEX IF NOT EXISTS idx_project_kpis_request ON project_kpis(project_request_id);
CREATE INDEX IF NOT EXISTS idx_project_kpis_type ON project_kpis(kpi_type_id);
CREATE INDEX IF NOT EXISTS idx_project_kpis_status ON project_kpis(status);
CREATE INDEX IF NOT EXISTS idx_project_kpis_approved ON project_kpis(is_approved);
CREATE INDEX IF NOT EXISTS idx_project_kpis_primary ON project_kpis(is_primary);

CREATE INDEX IF NOT EXISTS idx_kpi_measurements_kpi ON kpi_measurements(kpi_id);
CREATE INDEX IF NOT EXISTS idx_kpi_measurements_date ON kpi_measurements(measurement_date);
CREATE INDEX IF NOT EXISTS idx_kpi_measurements_verified ON kpi_measurements(is_verified);

CREATE INDEX IF NOT EXISTS idx_kpi_templates_type ON kpi_templates(kpi_type_id);
CREATE INDEX IF NOT EXISTS idx_kpi_templates_category ON kpi_templates(category);
CREATE INDEX IF NOT EXISTS idx_kpi_templates_active ON kpi_templates(is_active);

CREATE INDEX IF NOT EXISTS idx_kpi_analysis_kpi ON kpi_analysis(kpi_id);
CREATE INDEX IF NOT EXISTS idx_kpi_analysis_period ON kpi_analysis(analysis_period_start, analysis_period_end);

-- =============================================
-- Triggers للتحديث التلقائي
-- =============================================
CREATE TRIGGER update_kpi_types_updated_at BEFORE UPDATE ON kpi_types
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_project_kpis_updated_at BEFORE UPDATE ON project_kpis
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_kpi_templates_updated_at BEFORE UPDATE ON kpi_templates
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- إدراج البيانات الأساسية
-- =============================================

-- أنواع المؤشرات الأساسية
INSERT INTO kpi_types (name, display_name, description, default_unit, icon, color) VALUES
('time', 'مؤشرات الوقت', 'قياس الزمن والمدة', 'minutes', 'Clock', '#3B82F6'),
('count', 'مؤشرات العدد', 'قياس الكمية والعدد', 'count', 'Hash', '#10B981'),
('percentage', 'مؤشرات النسبة', 'قياس النسب المئوية', 'percentage', 'Percent', '#F59E0B'),
('cost', 'مؤشرات التكلفة', 'قياس التكلفة والوفورات', 'currency', 'DollarSign', '#EF4444'),
('quality', 'مؤشرات الجودة', 'قياس مستوى الجودة', 'score', 'Star', '#8B5CF6'),
('satisfaction', 'مؤشرات الرضا', 'قياس مستوى الرضا', 'percentage', 'Heart', '#EC4899'),
('efficiency', 'مؤشرات الكفاءة', 'قياس الكفاءة والإنتاجية', 'ratio', 'Zap', '#06B6D4'),
('compliance', 'مؤشرات الامتثال', 'قياس مستوى الامتثال', 'percentage', 'Shield', '#84CC16')
ON CONFLICT (name) DO NOTHING;

-- =============================================
-- تعليقات الجداول
-- =============================================
COMMENT ON TABLE kpi_types IS 'أنواع مؤشرات الأداء المختلفة';
COMMENT ON TABLE project_kpis IS 'المؤشرات المرتبطة بكل مشروع';
COMMENT ON TABLE kpi_measurements IS 'قياسات المؤشرات عبر الزمن';
COMMENT ON TABLE kpi_templates IS 'قوالب المؤشرات الجاهزة';
COMMENT ON TABLE kpi_analysis IS 'تحليل أداء المؤشرات';

-- =============================================
-- إضافة أعمدة جديدة لجدول المشاريع
-- =============================================
ALTER TABLE project_requests 
ADD COLUMN IF NOT EXISTS kpi_status VARCHAR(20) CHECK (kpi_status IN ('not_set', 'draft', 'pending_review', 'approved', 'active')) DEFAULT 'not_set';

ALTER TABLE project_requests 
ADD COLUMN IF NOT EXISTS primary_kpi_id UUID REFERENCES project_kpis(id);

-- فهرس للمؤشر الرئيسي
CREATE INDEX IF NOT EXISTS idx_project_requests_primary_kpi ON project_requests(primary_kpi_id);
CREATE INDEX IF NOT EXISTS idx_project_requests_kpi_status ON project_requests(kpi_status);

-- تعليق على الأعمدة الجديدة
COMMENT ON COLUMN project_requests.kpi_status IS 'حالة المؤشرات للمشروع';
COMMENT ON COLUMN project_requests.primary_kpi_id IS 'المؤشر الرئيسي للمشروع'; 