'use client'

import React from 'react'
import { Input, Textarea } from '@/components/ui/Input'
import { FieldHelp } from '@/components/ui/HelpSystem'
import { ClipboardList, Upload, Target } from 'lucide-react'
import { FormType, UnifiedFormData } from '../UnifiedProjectForm'
import { StepHeader } from '../../shared/StepHeader'

interface AdaptiveClarifyStepProps {
  formType: FormType
  data: UnifiedFormData
  updateData: (field: string, value: any) => void
  errors: Record<string, string>
}

export function AdaptiveClarifyStep({ formType, data, updateData, errors }: AdaptiveClarifyStepProps) {
  const getStepTitle = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'Clarify - توضيح العمليات'
      case 'suggestion':
        return 'Clarify - توضيح العمليات'
      case 'quick_win':
        return 'Clarify - توضيح العملية'
      default:
        return 'Clarify - توضيح العمليات'
    }
  }

  const getStepDescription = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'وضح العملية الحالية بالتفصيل وحدد نطاق المشكلة والمخرجات المتأثرة'
      case 'suggestion':
        return 'وضح العملية التي تتضمن المشكلة أو الفرصة التحسينية'
      case 'quick_win':
        return 'وضح العملية البسيطة التي تحتاج تحسين سريع'
      default:
        return 'وضح العملية الحالية'
    }
  }

  const getStepColor = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'blue'
      case 'suggestion':
        return 'indigo'
      case 'quick_win':
        return 'yellow'
      default:
        return 'blue'
    }
  }

  const colorClasses = {
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      text: 'text-blue-900',
      icon: 'text-blue-600'
    },
    indigo: {
      bg: 'bg-indigo-50',
      border: 'border-indigo-200',
      text: 'text-indigo-900',
      icon: 'text-indigo-600'
    },
    yellow: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      text: 'text-yellow-900',
      icon: 'text-yellow-600'
    }
  }

  const stepColor = getStepColor()
  const colors = colorClasses[stepColor]

  const addAffectedOutput = () => {
    const newOutput = ''
    updateData('affectedOutputs', [...data.affectedOutputs, newOutput])
  }

  const removeAffectedOutput = (index: number) => {
    const updated = data.affectedOutputs.filter((_, i) => i !== index)
    updateData('affectedOutputs', updated)
  }

  const updateAffectedOutput = (index: number, value: string) => {
    const updated = data.affectedOutputs.map((output, i) => 
      i === index ? value : output
    )
    updateData('affectedOutputs', updated)
  }

  return (
    <div className="space-y-6">
      <StepHeader
        icon={ClipboardList}
        title={getStepTitle()}
        description={getStepDescription()}
        formType={formType}
        stepNumber={4}
      />

      <div className="space-y-6">
        {/* وصف العملية */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <label className="block text-sm font-medium text-gray-700">
              وصف العملية الحالية *
            </label>
            <FieldHelp 
              content={formType === 'quick_win' 
                ? "اشرح العملية الحالية بشكل مختصر وواضح"
                : "اشرح العملية الحالية خطوة بخطوة مع تحديد المسؤوليات"
              }
              field="processDescription"
              step={4}
            />
          </div>
          <Textarea
            value={data.processDescription}
            onChange={(e) => updateData('processDescription', e.target.value)}
            placeholder={
              formType === 'quick_win'
                ? "مثال: 1. استلام المكالمة 2. البحث عن المعلومات 3. تقديم الإجابة"
                : "اكتب خطوات العملية بالتفصيل مع ذكر المسؤول عن كل خطوة..."
            }
            rows={formType === 'quick_win' ? 4 : 6}
            error={errors.processDescription}
            maxLength={formType === 'quick_win' ? 800 : 1500}
          />
          <div className="text-xs text-gray-500 mt-1">
            {data.processDescription.length}/{formType === 'quick_win' ? 800 : 1500} حرف
          </div>
        </div>

        {/* خريطة العملية */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Upload className="w-4 h-4 text-gray-500" />
            <label className="block text-sm font-medium text-gray-700">
              خريطة العملية (اختياري)
            </label>
            <FieldHelp 
              content="أرفق مخطط أو رسم بياني يوضح العملية الحالية"
              field="processMap"
              step={4}
            />
          </div>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
            <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-600">
              اسحب ملف خريطة العملية هنا أو انقر للاختيار
            </p>
            <p className="text-xs text-gray-500 mt-1">
              PDF, JPG, PNG, Visio (حد أقصى 10 ميجابايت)
            </p>
          </div>
        </div>

        {/* نطاق المشكلة */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Target className="w-4 h-4 text-gray-500" />
            <label className="block text-sm font-medium text-gray-700">
              نطاق المشكلة *
            </label>
            <FieldHelp 
              content="حدد بدقة ما يدخل في نطاق المشكلة وما لا يدخل"
              field="problemScope"
              step={4}
            />
          </div>
          <Textarea
            value={data.problemScope}
            onChange={(e) => updateData('problemScope', e.target.value)}
            placeholder={
              formType === 'quick_win'
                ? "مثال: مكالمات خدمة العملاء في أوقات الذروة (9-11 صباحاً)"
                : "مثال: مرضى الطوارئ فقط، لا يشمل العيادات الخارجية، خلال ساعات العمل الرسمية..."
            }
            rows={3}
            error={errors.problemScope}
            maxLength={500}
          />
          <div className="text-xs text-gray-500 mt-1">
            {data.problemScope.length}/500 حرف
          </div>
        </div>

        {/* المخرجات المتأثرة */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <label className="block text-sm font-medium text-gray-700">
              المخرجات المتأثرة *
            </label>
            <FieldHelp 
              content="ما هي النتائج أو المخرجات التي تتأثر بهذه المشكلة؟"
              field="affectedOutputs"
              step={4}
            />
          </div>

          <div className="space-y-3">
            {data.affectedOutputs.map((output, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  value={output}
                  onChange={(e) => updateAffectedOutput(index, e.target.value)}
                  placeholder="مثال: تقارير التحليل، رضا المرضى، سرعة الخدمة"
                  className="flex-1"
                />
                <button
                  type="button"
                  onClick={() => removeAffectedOutput(index)}
                  className="px-3 py-2 text-red-600 hover:text-red-700 border border-red-300 rounded-lg hover:bg-red-50"
                >
                  حذف
                </button>
              </div>
            ))}

            <button
              type="button"
              onClick={addAffectedOutput}
              className="w-full p-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-700 transition-colors"
            >
              + إضافة مخرج متأثر
            </button>

            {data.affectedOutputs.length === 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <p className="text-yellow-800 text-sm">
                  أضف على الأقل مخرج واحد متأثر بالمشكلة
                </p>
              </div>
            )}
          </div>
        </div>

        {/* أمثلة للمخرجات حسب نوع النموذج */}
        <div className="bg-gray-50 border rounded-lg p-4">
          <h4 className="font-semibold text-gray-900 mb-2">أمثلة على المخرجات المتأثرة:</h4>
          <div className="text-sm text-gray-700 space-y-1">
            {formType === 'quick_win' ? (
              <>
                <p>• سرعة الاستجابة للعملاء</p>
                <p>• جودة المعلومات المقدمة</p>
                <p>• رضا العملاء</p>
              </>
            ) : (
              <>
                <p>• تقارير نتائج التحاليل</p>
                <p>• مستوى رضا المرضى</p>
                <p>• كفاءة استخدام الموارد</p>
                <p>• جودة الخدمة المقدمة</p>
                <p>• سرعة إنجاز المعاملات</p>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
} 