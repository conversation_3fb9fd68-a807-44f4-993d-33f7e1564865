import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import type { Database } from '@/types/database.types'
import { auth as supabaseAuth, database } from './supabase'

// نوع المستخدم الأساسي
export interface User {
  id: string
  email: string
  user_metadata: {
    name?: string
    role?: string
    department?: string
  }
}

// نوع الجلسة
export interface Session {
  user: User
  access_token: string
  expires_at: number
}

// نوع المستخدم مع البيانات الإضافية
export interface ExtendedUser extends User {
  user_metadata: {
    name?: string
    department_id?: string
    role_id?: string
    phone?: string
    role?: string
    department?: string
  }
}

// نوع الدور
export interface UserRole {
  id: string
  name: string
  display_name: string
  description: string | null
  permissions: Record<string, any>
}

// نوع القسم
export interface Department {
  id: string
  name: string
  description: string | null
  parent_id: string | null
  manager_id: string | null
}

// خطاف للمصادقة
export function useAuth() {
  const [user, setUser] = useState<ExtendedUser | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [userRole, setUserRole] = useState<UserRole | null>(null)
  const [userDepartment, setUserDepartment] = useState<Department | null>(null)
  
  const router = useRouter()

  useEffect(() => {
    // الحصول على الجلسة الحالية
    const getInitialSession = async () => {
      try {
        const { session } = await supabaseAuth.getCurrentSession()
        
        if (session?.user) {
          setSession(session)
          setUser(session.user as ExtendedUser)
          await fetchUserDetails(session.user.id)
        } else {
          // إذا لم نجد جلسة، تحقق من الجلسة المحفوظة محلياً
          const storedSession = localStorage.getItem('demo_session')
          if (storedSession) {
            try {
              const parsedSession = JSON.parse(storedSession)
              if (parsedSession && parsedSession.user && parsedSession.expires_at > Date.now()) {
                setSession(parsedSession)
                setUser(parsedSession.user as ExtendedUser)
                await fetchUserDetails(parsedSession.user.id)
              } else {
                // الجلسة منتهية الصلاحية
                localStorage.removeItem('demo_session')
              }
            } catch (error) {
              console.error('Error parsing stored session:', error)
              localStorage.removeItem('demo_session')
            }
          }
        }
      } catch (error) {
        console.error('Error getting initial session:', error)
      }
      
      setLoading(false)
    }

    getInitialSession()

    // فحص الجلسة كل 30 ثانية
    const interval = setInterval(async () => {
      const { session } = await supabaseAuth.getCurrentSession()
      setSession(session)
      setUser(session?.user as ExtendedUser || null)
      
      if (!session) {
        setUserRole(null)
        setUserDepartment(null)
      }
    }, 30000)

    return () => clearInterval(interval)
  }, [router])

  // جلب تفاصيل المستخدم من قاعدة البيانات
  const fetchUserDetails = async (userId: string) => {
    try {
      console.log('fetchUserDetails called with userId:', userId)
      // في وضع التطوير، نستخدم البيانات المحلية
      const { data: userData, error: userError } = await database.getUserById(userId)

      if (userError) {
        console.error('Error fetching user details:', userError)
        return
      }

      if (userData) {
        // تحديد الدور بناءً على البيانات
        const userRoleName = typeof userData.role === 'string' ? userData.role : userData.role?.name || 'موظف'
        const isAdmin = userRoleName.includes('مدير') || userRoleName === 'مدير النظام'
        
        const mockRole: UserRole = {
          id: userData.id || '1',
          name: isAdmin ? 'admin' : 'employee',
          display_name: userRoleName,
          description: `دور ${userRoleName}`,
          permissions: { 
            all: isAdmin,
            view_projects: true,
            create_projects: isAdmin,
            approve_projects: isAdmin,
            manage_users: isAdmin
          }
        }
        
        const departmentName = typeof userData.department === 'string' ? userData.department : userData.department?.name || 'قسم عام'
        
        const mockDepartment: Department = {
          id: '1',
          name: departmentName,
          description: `قسم ${departmentName}`,
          parent_id: null,
          manager_id: isAdmin ? userData.id : null
        }
        
        setUserRole(mockRole)
        setUserDepartment(mockDepartment)
        
        console.log('User details loaded:', {
          user: userData,
          role: mockRole,
          department: mockDepartment
        })
      }
    } catch (error) {
      console.error('Error in fetchUserDetails:', error)
      // في حالة الخطأ، نضع بيانات افتراضية
      setUserRole({
        id: '1',
        name: 'employee',
        display_name: 'موظف',
        description: 'دور افتراضي',
        permissions: { view_projects: true }
      })
      setUserDepartment({
        id: '1',
        name: 'قسم عام',
        description: 'قسم افتراضي',
        parent_id: null,
        manager_id: null
      })
    }
  }

  // تسجيل الدخول
  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabaseAuth.signIn(email, password)
      
      if (data?.user && !error) {
        setUser(data.user as ExtendedUser)
        setSession(data.session as Session)
        await fetchUserDetails(data.user.id)
        return { data, error: null }
      }
      
      return { data, error }
    } catch (error) {
      console.error('Sign in error:', error)
      return { data: null, error: { message: 'حدث خطأ أثناء تسجيل الدخول' } }
    }
  }

  // تسجيل الخروج
  const signOut = async () => {
    const { error } = await supabaseAuth.signOut()
    
    if (!error) {
      setUser(null)
      setSession(null)
      setUserRole(null)
      setUserDepartment(null)
      router.push('/auth/login')
    }
    
    return { error }
  }

  // تسجيل مستخدم جديد
  const signUp = async (email: string, password: string, userData: any) => {
    const { data, error } = await supabaseAuth.signUp(email, password, userData)
    return { data, error }
  }

  // إعادة تعيين كلمة المرور
  const resetPassword = async (email: string) => {
    // في وضع التطوير، نرجع نجاح وهمي
    return { data: null, error: null }
  }

  // تحديث كلمة المرور
  const updatePassword = async (password: string) => {
    // في وضع التطوير، نرجع نجاح وهمي
    return { data: null, error: null }
  }

  return {
    user,
    session,
    userRole,
    userDepartment,
    loading,
    signIn,
    signOut,
    signUp,
    resetPassword,
    updatePassword,
    fetchUserDetails,
  }
}

// دوال التحقق من الصلاحيات
export class PermissionChecker {
  private userRole: UserRole | null
  
  constructor(userRole: UserRole | null) {
    this.userRole = userRole
  }

  // التحقق من صلاحية عامة
  hasPermission(resource: string, action: string): boolean {
    if (!this.userRole) return false
    
    // المدير العام له صلاحيات كاملة
    if (this.userRole.name === 'admin') return true
    if (this.userRole.permissions?.all === true) return true
    
    const resourcePermissions = this.userRole.permissions?.[resource]
    if (!resourcePermissions) return false
    
    // إذا كانت الصلاحية مصفوفة من الأعمال
    if (Array.isArray(resourcePermissions)) {
      return resourcePermissions.includes(action)
    }
    
    // إذا كانت الصلاحية boolean
    if (typeof resourcePermissions === 'boolean') {
      return resourcePermissions
    }
    
    return false
  }

  // التحقق من صلاحيات المشاريع
  canViewProjects(): boolean {
    if (this.userRole?.name === 'admin') return true
    return this.hasPermission('projects', 'read')
  }

  canCreateProjects(): boolean {
    if (this.userRole?.name === 'admin') return true
    return this.hasPermission('projects', 'write')
  }

  canApproveProjects(): boolean {
    if (this.userRole?.name === 'admin') return true
    return this.hasPermission('projects', 'approve')
  }

  canFinalApproveProjects(): boolean {
    if (this.userRole?.name === 'admin') return true
    return this.hasPermission('projects', 'final_approve')
  }

  // التحقق من صلاحيات الطلبات
  canViewRequests(): boolean {
    if (this.userRole?.name === 'admin') return true
    return this.hasPermission('requests', 'read')
  }

  canCreateRequests(): boolean {
    if (this.userRole?.name === 'admin') return true
    return this.hasPermission('requests', 'write')
  }

  canApproveRequests(): boolean {
    if (this.userRole?.name === 'admin') return true
    return this.hasPermission('requests', 'approve')
  }

  // التحقق من صلاحيات المهام
  canViewTasks(): boolean {
    return this.hasPermission('tasks', 'read')
  }

  canManageTasks(): boolean {
    return this.hasPermission('tasks', 'write')
  }

  // التحقق من الأدوار
  isAdmin(): boolean {
    return this.userRole?.name === 'admin'
  }

  isPMOManager(): boolean {
    return this.userRole?.name === 'pmo_manager'
  }

  isPlanningManager(): boolean {
    return this.userRole?.name === 'planning_manager'
  }

  isExecutiveManager(): boolean {
    return this.userRole?.name === 'executive_manager'
  }

  isProjectManager(): boolean {
    return this.userRole?.name === 'project_manager'
  }

  isEmployee(): boolean {
    return this.userRole?.name === 'employee'
  }

  // التحقق من إمكانية الاعتماد حسب نوع الطلب
  canApproveRequestType(mainType: string, subType: string | null, currentApprovalLevel: number): boolean {
    // للمشاريع العامة - تحتاج اعتماد ثلاث مستويات
    if (mainType === 'general_project') {
      if (currentApprovalLevel === 1) return this.isPMOManager()
      if (currentApprovalLevel === 2) return this.isPlanningManager()
      if (currentApprovalLevel === 3) return this.isExecutiveManager()
      return false
    }
    
    // لمشاريع التحسين - يعتمد على النوع الفرعي
    if (mainType === 'improvement_project') {
      switch (subType) {
        case 'quick_win':
          // مشاريع الكويك وين تحتاج موافقة مدير المكتب فقط
          return currentApprovalLevel === 1 && this.isPMOManager()
        
        case 'suggestion':
          // مقترحات المشاريع تحتاج موافقة مدير المكتب ثم مدير التخطيط
          if (currentApprovalLevel === 1) return this.isPMOManager()
          if (currentApprovalLevel === 2) return this.isPlanningManager()
          return false
        
        case 'improvement_full':
          // مشاريع التحسين الشاملة تحتاج موافقة ثلاث مستويات
          if (currentApprovalLevel === 1) return this.isPMOManager()
          if (currentApprovalLevel === 2) return this.isPlanningManager()
          if (currentApprovalLevel === 3) return this.isExecutiveManager()
          return false
        
        default:
          return false
      }
    }
    
    return false
  }

  // تحديد عدد مستويات الاعتماد المطلوبة
  getRequiredApprovalLevels(mainType: string, subType: string | null): number {
    if (mainType === 'general_project') {
      return 3 // PMO → Planning → Executive
    }
    
    if (mainType === 'improvement_project') {
      switch (subType) {
        case 'quick_win':
          return 1 // PMO فقط
        case 'suggestion':
          return 2 // PMO → Planning
        case 'improvement_full':
          return 3 // PMO → Planning → Executive
        default:
          return 1
      }
    }
    
    return 1
  }

  // تحديد المعتمد للمستوى التالي
  getNextApprover(mainType: string, subType: string | null, currentLevel: number): string | null {
    const requiredLevels = this.getRequiredApprovalLevels(mainType, subType)
    
    if (currentLevel >= requiredLevels) {
      return null // لا يوجد معتمد آخر
    }
    
    const nextLevel = currentLevel + 1
    
    switch (nextLevel) {
      case 1:
        return 'pmo_manager'
      case 2:
        return 'planning_manager'
      case 3:
        return 'executive_manager'
      default:
        return null
    }
  }
}

// خطاف للتحقق من الصلاحيات
export function usePermissions() {
  const { userRole } = useAuth()
  return new PermissionChecker(userRole)
} 