'use client'

import React, { useEffect, useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { WorkflowManager, WorkflowStep } from '@/lib/workflow'
import { useAuth, usePermissions } from '@/lib/auth'
import { 
  CheckCircle, 
  Clock, 
  XCircle, 
  User, 
  Calendar,
  MessageSquare,
  ArrowRight,
  AlertCircle
} from 'lucide-react'

interface WorkflowTrackerProps {
  requestId: string
  mainType: string
  subType: string | null
  currentStatus: string
  onApprovalUpdate?: () => void
}

export function WorkflowTracker({ 
  requestId, 
  mainType, 
  subType, 
  currentStatus,
  onApprovalUpdate 
}: WorkflowTrackerProps) {
  const [steps, setSteps] = useState<WorkflowStep[]>([])
  const [loading, setLoading] = useState(true)
  const [processingApproval, setProcessingApproval] = useState(false)
  const [showNotesModal, setShowNotesModal] = useState(false)
  const [approvalNotes, setApprovalNotes] = useState('')
  const [selectedDecision, setSelectedDecision] = useState<'approved' | 'rejected'>('approved')

  const { user } = useAuth()
  const permissions = usePermissions()

  useEffect(() => {
    loadWorkflowSteps()
  }, [requestId])

  const loadWorkflowSteps = async () => {
    setLoading(true)
    try {
      const workflowSteps = await WorkflowManager.getWorkflowSteps(requestId)
      setSteps(workflowSteps)
    } catch (error) {
      console.error('Error loading workflow steps:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleApproval = async (decision: 'approved' | 'rejected') => {
    if (!user) return

    setProcessingApproval(true)
    try {
      const result = await WorkflowManager.processApproval(
        requestId,
        user.id,
        decision,
        approvalNotes
      )

      if (result.success) {
        await loadWorkflowSteps()
        onApprovalUpdate?.()
        setShowNotesModal(false)
        setApprovalNotes('')
      } else {
        console.error('Error processing approval:', result.error)
      }
    } catch (error) {
      console.error('Error processing approval:', error)
    } finally {
      setProcessingApproval(false)
    }
  }

  const canApproveCurrentStep = (step: WorkflowStep): boolean => {
    if (!user || step.status !== 'pending') return false
    
    return permissions.canApproveRequestType(
      mainType, 
      subType, 
      step.level
    )
  }

  const getStepIcon = (step: WorkflowStep) => {
    switch (step.status) {
      case 'approved':
        return <CheckCircle className="w-6 h-6 text-green-500" />
      case 'rejected':
        return <XCircle className="w-6 h-6 text-red-500" />
      case 'pending':
        return <Clock className="w-6 h-6 text-yellow-500" />
      default:
        return <AlertCircle className="w-6 h-6 text-gray-400" />
    }
  }

  const getStepColor = (step: WorkflowStep) => {
    switch (step.status) {
      case 'approved':
        return 'border-green-500 bg-green-50'
      case 'rejected':
        return 'border-red-500 bg-red-50'
      case 'pending':
        return 'border-yellow-500 bg-yellow-50'
      default:
        return 'border-gray-300 bg-gray-50'
    }
  }

  if (loading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </Card>
    )
  }

  return (
    <Card className="p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">سير العمل والاعتماد</h3>
        <p className="text-sm text-gray-600">
          تتبع مراحل اعتماد الطلب حسب النوع والأولوية
        </p>
      </div>

      <div className="space-y-4">
        {steps.map((step, index) => (
          <div key={step.level} className="relative">
            {/* خط الربط */}
            {index < steps.length - 1 && (
              <div className="absolute right-3 top-12 w-0.5 h-8 bg-gray-300"></div>
            )}

            <div className={`border-2 rounded-lg p-4 ${getStepColor(step)}`}>
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3">
                  {getStepIcon(step)}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-gray-900">
                        المستوى {step.level}: {step.roleName}
                      </h4>
                      {step.status === 'pending' && canApproveCurrentStep(step) && (
                        <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                          في انتظار موافقتك
                        </span>
                      )}
                    </div>

                    {step.approvedAt && (
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                        <Calendar className="w-4 h-4" />
                        <span>
                          {step.status === 'approved' ? 'تم الاعتماد في:' : 'تم الرفض في:'} 
                          {new Date(step.approvedAt).toLocaleDateString('ar-SA')}
                        </span>
                      </div>
                    )}

                    {step.notes && (
                      <div className="flex items-start gap-2 text-sm text-gray-700 bg-white bg-opacity-50 p-2 rounded">
                        <MessageSquare className="w-4 h-4 mt-0.5" />
                        <span>{step.notes}</span>
                      </div>
                    )}
                  </div>
                </div>

                {step.status === 'pending' && canApproveCurrentStep(step) && (
                  <div className="flex gap-2">
                    <Button
                      variant="success"
                      size="sm"
                      onClick={() => {
                        setSelectedDecision('approved')
                        setShowNotesModal(true)
                      }}
                      disabled={processingApproval}
                    >
                      اعتماد
                    </Button>
                    <Button
                      variant="danger"
                      size="sm"
                      onClick={() => {
                        setSelectedDecision('rejected')
                        setShowNotesModal(true)
                      }}
                      disabled={processingApproval}
                    >
                      رفض
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* نافذة الملاحظات */}
      {showNotesModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {selectedDecision === 'approved' ? 'اعتماد الطلب' : 'رفض الطلب'}
            </h3>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ملاحظات (اختيارية)
              </label>
              <textarea
                value={approvalNotes}
                onChange={(e) => setApprovalNotes(e.target.value)}
                placeholder="أضف ملاحظات حول قرارك..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div className="flex gap-3 justify-end">
              <Button
                variant="ghost"
                onClick={() => {
                  setShowNotesModal(false)
                  setApprovalNotes('')
                }}
                disabled={processingApproval}
              >
                إلغاء
              </Button>
              <Button
                variant={selectedDecision === 'approved' ? 'success' : 'danger'}
                onClick={() => handleApproval(selectedDecision)}
                disabled={processingApproval}
              >
                {processingApproval ? 'جاري المعالجة...' : 
                 selectedDecision === 'approved' ? 'تأكيد الاعتماد' : 'تأكيد الرفض'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </Card>
  )
}