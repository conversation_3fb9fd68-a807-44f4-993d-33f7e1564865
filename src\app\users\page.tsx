'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth, usePermissions } from '@/lib/core/auth'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit,
  Trash2,
  User,
  Users,
  Shield,
  Mail,
  Building,
  Calendar
} from 'lucide-react'
import { Pagination, usePagination } from '@/components/ui/Pagination'

export default function UsersPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('all')
  const [departmentFilter, setDepartmentFilter] = useState('all')
  const [showAddModal, setShowAddModal] = useState(false)
  
  const { } = useAuth()
  const permissions = usePermissions()
  const router = useRouter()

  // بيانات وهمية للمستخدمين
  const mockUsers = [
    {
      id: '1',
      name: 'أحمد محمد',
      email: '<EMAIL>',
      role: 'admin',
      roleName: 'مدير النظام',
      department: 'تقنية المعلومات',
      status: 'active',
      lastLogin: '2024-01-25',
      createdAt: '2023-12-01'
    },
    {
      id: '2',
      name: 'فاطمة أحمد',
      email: '<EMAIL>',
      role: 'pmo_manager',
      roleName: 'مدير مكتب المشاريع',
      department: 'مكتب إدارة المشاريع',
      status: 'active',
      lastLogin: '2024-01-24',
      createdAt: '2023-11-15'
    },
    {
      id: '3',
      name: 'محمد علي',
      email: '<EMAIL>',
      role: 'planning_manager',
      roleName: 'مدير إدارة التخطيط',
      department: 'التخطيط الاستراتيجي',
      status: 'active',
      lastLogin: '2024-01-23',
      createdAt: '2023-10-20'
    },
    {
      id: '4',
      name: 'سارة خالد',
      email: '<EMAIL>',
      role: 'executive_manager',
      roleName: 'المدير التنفيذي',
      department: 'الإدارة التنفيذية',
      status: 'active',
      lastLogin: '2024-01-22',
      createdAt: '2023-09-10'
    },
    {
      id: '5',
      name: 'علي حسن',
      email: '<EMAIL>',
      role: 'project_manager',
      roleName: 'مدير مشروع',
      department: 'تقنية المعلومات',
      status: 'active',
      lastLogin: '2024-01-25',
      createdAt: '2024-01-01'
    },
    {
      id: '6',
      name: 'نورا أحمد',
      email: '<EMAIL>',
      role: 'employee',
      roleName: 'موظف',
      department: 'الموارد البشرية',
      status: 'inactive',
      lastLogin: '2024-01-15',
      createdAt: '2023-08-15'
    }
  ]

  const roleOptions = [
    { value: 'all', label: 'جميع الأدوار' },
    { value: 'admin', label: 'مدير النظام' },
    { value: 'pmo_manager', label: 'مدير مكتب المشاريع' },
    { value: 'planning_manager', label: 'مدير إدارة التخطيط' },
    { value: 'executive_manager', label: 'المدير التنفيذي' },
    { value: 'project_manager', label: 'مدير مشروع' },
    { value: 'employee', label: 'موظف' }
  ]

  const departmentOptions = [
    { value: 'all', label: 'جميع الأقسام' },
    { value: 'تقنية المعلومات', label: 'تقنية المعلومات' },
    { value: 'مكتب إدارة المشاريع', label: 'مكتب إدارة المشاريع' },
    { value: 'التخطيط الاستراتيجي', label: 'التخطيط الاستراتيجي' },
    { value: 'الإدارة التنفيذية', label: 'الإدارة التنفيذية' },
    { value: 'الموارد البشرية', label: 'الموارد البشرية' },
    { value: 'العمليات', label: 'العمليات' }
  ]

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-purple-100 text-purple-800'
      case 'pmo_manager':
        return 'bg-blue-100 text-blue-800'
      case 'planning_manager':
        return 'bg-green-100 text-green-800'
      case 'executive_manager':
        return 'bg-red-100 text-red-800'
      case 'project_manager':
        return 'bg-orange-100 text-orange-800'
      case 'employee':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    return status === 'active' 
      ? 'bg-green-100 text-green-800' 
      : 'bg-red-100 text-red-800'
  }

  const filteredUsers = mockUsers.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRole = roleFilter === 'all' || user.role === roleFilter
    const matchesDepartment = departmentFilter === 'all' || user.department === departmentFilter
    
    return matchesSearch && matchesRole && matchesDepartment
  })

  // استخدام نظام الترقيم
  const {
    paginatedData: paginatedUsers,
    currentPage,
    totalPages,
    showingFrom,
    showingTo,
    totalItems,
    goToPage
  } = usePagination(filteredUsers, 8) // 8 مستخدمين في كل صفحة

  const handleDeleteUser = async (userId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      try {
        console.log('Deleting user:', userId)
        // TODO: API call to delete user
      } catch (error) {
        console.error('Error deleting user:', error)
      }
    }
  }

  const AddUserModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <h2 className="text-xl font-semibold">إضافة مستخدم جديد</h2>
        </CardHeader>
        <CardContent className="space-y-4">
          <Input
            label="الاسم الكامل"
            placeholder="أدخل الاسم الكامل"
            required
          />
          
          <Input
            label="البريد الإلكتروني"
            type="email"
            placeholder="أدخل البريد الإلكتروني"
            required
          />
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الدور
            </label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              {roleOptions.slice(1).map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              القسم
            </label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              {departmentOptions.slice(1).map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex gap-3 pt-4">
            <Button
              variant="secondary"
              onClick={() => setShowAddModal(false)}
              className="flex-1"
            >
              إلغاء
            </Button>
            <Button
              variant="primary"
              onClick={() => {
                // TODO: Handle add user
                setShowAddModal(false)
              }}
              className="flex-1"
            >
              إضافة
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  return (
    <ProtectedLayout>
      <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">إدارة المستخدمين</h1>
              <p className="text-gray-600 mt-1">إدارة المستخدمين والصلاحيات</p>
            </div>
            
            {permissions.isAdmin() && (
              <Button
                variant="primary"
                onClick={() => setShowAddModal(true)}
                icon={<Plus className="w-4 h-4" />}
              >
                إضافة مستخدم
              </Button>
            )}
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">إجمالي المستخدمين</p>
                    <p className="text-3xl font-bold text-gray-900">{mockUsers.length}</p>
                  </div>
                  <Users className="w-12 h-12 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">المستخدمين النشطين</p>
                    <p className="text-3xl font-bold text-green-600">
                      {mockUsers.filter(u => u.status === 'active').length}
                    </p>
                  </div>
                  <User className="w-12 h-12 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">المدراء</p>
                    <p className="text-3xl font-bold text-purple-600">
                      {mockUsers.filter(u => u.role.includes('manager') || u.role === 'admin').length}
                    </p>
                  </div>
                  <Shield className="w-12 h-12 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">الأقسام</p>
                    <p className="text-3xl font-bold text-orange-600">
                      {new Set(mockUsers.map(u => u.department)).size}
                    </p>
                  </div>
                  <Building className="w-12 h-12 text-orange-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="البحث في المستخدمين..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <select
                  value={roleFilter}
                  onChange={(e) => setRoleFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {roleOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>

                <select
                  value={departmentFilter}
                  onChange={(e) => setDepartmentFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {departmentOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>

                <Button
                  variant="ghost"
                  onClick={() => {
                    setSearchTerm('')
                    setRoleFilter('all')
                    setDepartmentFilter('all')
                  }}
                  icon={<Filter className="w-4 h-4" />}
                >
                  مسح الفلاتر
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Users Table */}
          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المستخدم
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الدور
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        القسم
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الحالة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        آخر دخول
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {paginatedUsers.map((user) => (
                      <tr key={user.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                              <User className="w-5 h-5 text-blue-600" />
                            </div>
                            <div className="mr-4">
                              <div className="text-sm font-medium text-gray-900">
                                {user.name}
                              </div>
                              <div className="text-sm text-gray-500 flex items-center gap-1">
                                <Mail className="w-3 h-3" />
                                {user.email}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                            {user.roleName}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center gap-1 text-sm text-gray-900">
                            <Building className="w-4 h-4 text-gray-400" />
                            {user.department}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
                            {user.status === 'active' ? 'نشط' : 'غير نشط'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center gap-1 text-sm text-gray-500">
                            <Calendar className="w-4 h-4" />
                            {user.lastLogin}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => router.push(`/users/${user.id}`)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              <Eye className="w-4 h-4" />
                            </button>
                            
                            {permissions.isAdmin() && (
                              <>
                                <button
                                  onClick={() => router.push(`/users/${user.id}/edit`)}
                                  className="text-green-600 hover:text-green-900"
                                >
                                  <Edit className="w-4 h-4" />
                                </button>
                                
                                <button
                                  onClick={() => handleDeleteUser(user.id)}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Pagination */}
          {totalPages > 1 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={goToPage}
              showingFrom={showingFrom}
              showingTo={showingTo}
              totalItems={totalItems}
            />
          )}

          {/* Summary */}
          {totalPages <= 1 && (
            <div className="mt-6 text-center text-sm text-gray-500">
              عرض {totalItems} مستخدم
            </div>
          )}
        {/* Add User Modal */}
        {showAddModal && <AddUserModal />}
        </div>
    </ProtectedLayout>
  )
} 