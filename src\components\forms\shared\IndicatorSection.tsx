'use client'

import React, { useEffect } from 'react'
import { Input, Select } from '@/components/ui/Input'
import { FieldHelp } from '@/components/ui/HelpSystem'
import { Target, TrendingUp, TrendingDown, Calculator, AlertCircle } from 'lucide-react'
import { FormSection } from './FormSection'

interface IndicatorData {
  indicatorName: string
  improvementDirection: 'increase' | 'decrease' | ''
  currentValue: number
  targetValue: number
  unit: string
  calculatedGap?: number
  isValidDirection?: boolean
}

interface IndicatorSectionProps {
  data: IndicatorData
  onUpdate: (field: string, value: any) => void
  errors: Record<string, string>
  formType?: 'enhanced_improvement' | 'suggestion' | 'quick_win'
  className?: string
}

const units = [
  'عدد', 'نسبة مئوية (%)', 'ساعة', 'يوم', 'ريال سعودي',
  'كيلوجرام', 'متر', 'لتر', 'درجة', 'نقطة'
]

export function IndicatorSection({ 
  data, 
  onUpdate, 
  errors, 
  formType = 'enhanced_improvement',
  className = '' 
}: IndicatorSectionProps) {
  
  // حساب الفجوة تلقائياً
  useEffect(() => {
    if (data.currentValue !== undefined && data.targetValue !== undefined && data.improvementDirection) {
      const gap = Math.abs(data.targetValue - data.currentValue)
      const isValidDirection = data.improvementDirection === 'increase' 
        ? data.targetValue > data.currentValue 
        : data.targetValue < data.currentValue
      
      // تحديث فقط إذا كانت القيم مختلفة لتجنب الحلقة اللا نهائية
      if (data.calculatedGap !== gap) {
        onUpdate('calculatedGap', gap)
      }
      if (data.isValidDirection !== isValidDirection) {
        onUpdate('isValidDirection', isValidDirection)
      }
    }
  }, [data.currentValue, data.targetValue, data.improvementDirection])

  const calculateImprovementPercentage = () => {
    if (data.currentValue && data.targetValue && data.currentValue !== 0) {
      return Math.abs(((data.targetValue - data.currentValue) / data.currentValue) * 100).toFixed(1)
    }
    return '0'
  }

  return (
    <FormSection
      icon={Target}
      title="بيانات المؤشر"
      variant="highlighted"
      className={className}
    >
      <div className="space-y-4">
        {/* اسم المؤشر */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <label className="block text-sm font-medium text-gray-700">
              اسم المؤشر *
            </label>
            <FieldHelp 
              content="اسم المؤشر الذي سيتم قياسه لتتبع التحسن"
              field="indicatorName"
              step={2}
            />
          </div>
          <Input
            value={data.indicatorName}
            onChange={(e) => onUpdate('indicatorName', e.target.value)}
            placeholder="مثال: متوسط وقت معالجة الطلب"
            error={errors.indicatorName}
          />
        </div>

        {/* اتجاه التحسن */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <label className="block text-sm font-medium text-gray-700">
              اتجاه التحسن *
            </label>
            <FieldHelp 
              content="حدد ما إذا كان التحسن يتطلب زيادة أم تقليل الرقم"
              field="improvementDirection"
              step={2}
            />
          </div>
          <div className="grid grid-cols-2 gap-3">
            <button
              type="button"
              onClick={() => onUpdate('improvementDirection', 'increase')}
              className={`p-3 border rounded-lg flex items-center gap-2 transition-colors ${
                data.improvementDirection === 'increase'
                  ? 'bg-green-50 border-green-200 text-green-700'
                  : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
              }`}
            >
              <TrendingUp className="w-4 h-4" />
              <span className="font-medium">زيادة</span>
            </button>
            <button
              type="button"
              onClick={() => onUpdate('improvementDirection', 'decrease')}
              className={`p-3 border rounded-lg flex items-center gap-2 transition-colors ${
                data.improvementDirection === 'decrease'
                  ? 'bg-red-50 border-red-200 text-red-700'
                  : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
              }`}
            >
              <TrendingDown className="w-4 h-4" />
              <span className="font-medium">تقليل</span>
            </button>
          </div>
          {errors.improvementDirection && (
            <p className="text-red-600 text-sm mt-1">{errors.improvementDirection}</p>
          )}
        </div>

        {/* القيم والوحدة */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <label className="block text-sm font-medium text-gray-700">
                القيمة الحالية *
              </label>
              <FieldHelp 
                content="القيمة الحالية للمؤشر قبل التحسين"
                field="currentValue"
                step={2}
              />
            </div>
            <Input
              type="number"
              value={data.currentValue || ''}
              onChange={(e) => onUpdate('currentValue', parseFloat(e.target.value) || 0)}
              placeholder="0"
              error={errors.currentValue}
            />
          </div>

          <div>
            <div className="flex items-center gap-2 mb-2">
              <label className="block text-sm font-medium text-gray-700">
                القيمة المستهدفة *
              </label>
              <FieldHelp 
                content="القيمة المطلوب الوصول إليها بعد التحسين"
                field="targetValue"
                step={2}
              />
            </div>
            <Input
              type="number"
              value={data.targetValue || ''}
              onChange={(e) => onUpdate('targetValue', parseFloat(e.target.value) || 0)}
              placeholder="0"
              error={errors.targetValue}
            />
          </div>

          <div>
            <div className="flex items-center gap-2 mb-2">
              <label className="block text-sm font-medium text-gray-700">
                وحدة القياس *
              </label>
              <FieldHelp 
                content="الوحدة المستخدمة لقياس المؤشر"
                field="unit"
                step={2}
              />
            </div>
            <Select
              value={data.unit}
              onChange={(e) => onUpdate('unit', e.target.value)}
              error={errors.unit}
            >
              <option value="">اختر الوحدة</option>
              {units.map(unit => (
                <option key={unit} value={unit}>{unit}</option>
              ))}
            </Select>
          </div>
        </div>

        {/* النتائج المحسوبة */}
        {data.currentValue && data.targetValue && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <Calculator className="w-5 h-5 text-blue-600" />
              <h5 className="font-semibold text-blue-900">النتائج المحسوبة</h5>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-600">الفجوة:</span>
                <span className="font-semibold text-gray-900 mr-2">
                  {data.calculatedGap?.toFixed(2)} {data.unit}
                </span>
              </div>
              
              <div>
                <span className="text-gray-600">نسبة التحسن:</span>
                <span className="font-semibold text-gray-900 mr-2">
                  {calculateImprovementPercentage()}%
                </span>
              </div>
              
              <div className="flex items-center gap-2">
                {data.isValidDirection ? (
                  <>
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-green-700 font-medium">اتجاه صحيح</span>
                  </>
                ) : (
                  <>
                    <AlertCircle className="w-4 h-4 text-red-500" />
                    <span className="text-red-700 font-medium">تحقق من الاتجاه</span>
                  </>
                )}
              </div>
            </div>
            
            {!data.isValidDirection && (
              <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-yellow-800 text-sm">
                  <AlertCircle className="w-4 h-4 inline ml-1" />
                  تأكد من أن القيمة المستهدفة تتماشى مع اتجاه التحسن المحدد
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </FormSection>
  )
} 