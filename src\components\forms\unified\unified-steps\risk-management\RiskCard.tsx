'use client'

import React from 'react'
import { Button } from '@/components/ui/Button'
import { Input, Textarea, Select } from '@/components/ui/Input'
import { Trash2 } from 'lucide-react'

interface Risk {
  id: string
  description: string
  probability: 'low' | 'medium' | 'high'
  impact: 'low' | 'medium' | 'high'
  mitigation?: string
}

interface RiskCardProps {
  risk: Risk
  index: number
  onUpdate: (id: string, field: string, value: any) => void
  onRemove: (id: string) => void
}

export function RiskCard({ risk, index, onUpdate, onRemove }: RiskCardProps) {
  // حساب مستوى المخاطرة
  const getRiskLevel = (probability: string, impact: string) => {
    const levels = {
      'low-low': { level: 'منخفض', color: 'text-green-600 bg-green-50 border-green-200' },
      'low-medium': { level: 'منخفض', color: 'text-green-600 bg-green-50 border-green-200' },
      'low-high': { level: 'متوسط', color: 'text-yellow-600 bg-yellow-50 border-yellow-200' },
      'medium-low': { level: 'منخفض', color: 'text-green-600 bg-green-50 border-green-200' },
      'medium-medium': { level: 'متوسط', color: 'text-yellow-600 bg-yellow-50 border-yellow-200' },
      'medium-high': { level: 'عالي', color: 'text-red-600 bg-red-50 border-red-200' },
      'high-low': { level: 'متوسط', color: 'text-yellow-600 bg-yellow-50 border-yellow-200' },
      'high-medium': { level: 'عالي', color: 'text-red-600 bg-red-50 border-red-200' },
      'high-high': { level: 'عالي', color: 'text-red-600 bg-red-50 border-red-200' }
    }
    return levels[`${probability}-${impact}` as keyof typeof levels] || { level: 'غير محدد', color: 'text-gray-600 bg-gray-50 border-gray-200' }
  }

  const riskLevel = getRiskLevel(risk.probability, risk.impact)

  return (
    <div className="border rounded-lg p-4 bg-white">
      <div className="flex justify-between items-start mb-4">
        <h5 className="font-medium text-gray-900">مخاطر {index + 1}</h5>
        <Button
          onClick={() => onRemove(risk.id)}
          variant="ghost"
          size="sm"
          className="text-red-600 hover:text-red-700"
        >
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            وصف المخاطر *
          </label>
          <Textarea
            value={risk.description}
            onChange={(e) => onUpdate(risk.id, 'description', e.target.value)}
            placeholder="اكتب وصفاً واضحاً للمخاطر المحتمل"
            rows={2}
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            احتمالية الحدوث *
          </label>
          <Select
            value={risk.probability}
            onChange={(e) => onUpdate(risk.id, 'probability', e.target.value)}
            className="w-full"
          >
            <option value="low">منخفض</option>
            <option value="medium">متوسط</option>
            <option value="high">عالي</option>
          </Select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            درجة التأثير *
          </label>
          <Select
            value={risk.impact}
            onChange={(e) => onUpdate(risk.id, 'impact', e.target.value)}
            className="w-full"
          >
            <option value="low">منخفض</option>
            <option value="medium">متوسط</option>
            <option value="high">عالي</option>
          </Select>
        </div>
      </div>

      {/* مستوى المخاطرة المحسوب */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          مستوى المخاطرة
        </label>
        <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium border ${riskLevel.color}`}>
          {riskLevel.level}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          خطة التخفيف (اختياري)
        </label>
        <Textarea
          value={risk.mitigation || ''}
          onChange={(e) => onUpdate(risk.id, 'mitigation', e.target.value)}
          placeholder="كيف ستتعامل مع هذا المخاطر أو تقلل من تأثيره؟"
          rows={2}
          className="w-full"
        />
      </div>
    </div>
  )
} 