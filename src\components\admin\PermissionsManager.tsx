'use client'

import React, { useState, useEffect } from 'react'
import { Shield, Users, Settings, Plus, Edit, Trash2, Save, X } from 'lucide-react'
import { Button } from '../ui/Button'
import { Card } from '../ui/Card'
import { Input } from '../ui/Input'
import { Role, User, Department } from '../shared/SharedTypes'
import { useNotificationHelpers } from '../ui/notifications/NotificationHooks'

interface PermissionsManagerProps {
  className?: string
}

export function PermissionsManager({ className = '' }: PermissionsManagerProps) {
  const [activeTab, setActiveTab] = useState<'roles' | 'users' | 'permissions'>('roles')
  const [roles, setRoles] = useState<Role[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [departments, setDepartments] = useState<Department[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [editingRole, setEditingRole] = useState<Role | null>(null)
  const [editingUser, setEditingUser] = useState<User | null>(null)

  const { showSuccess, showError } = useNotificationHelpers()

  // تحميل البيانات
  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setIsLoading(true)
    try {
      // هنا يمكن استدعاء API للحصول على البيانات الفعلية
      // مؤقتاً سنستخدم بيانات وهمية
      const mockRoles: Role[] = [
        {
          id: '1',
          name: 'admin',
          displayName: 'مدير النظام',
          description: 'صلاحيات كاملة للنظام',
          permissions: { all: true },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '2',
          name: 'pmo_manager',
          displayName: 'مدير مكتب المشاريع',
          description: 'إدارة المشاريع والطلبات',
          permissions: { projects: true, requests: true, approvals: true },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ]

      const mockUsers: User[] = [
        {
          id: '1',
          email: '<EMAIL>',
          name: 'مدير النظام',
          departmentId: '1',
          roleId: '1',
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ]

      const mockDepartments: Department[] = [
        {
          id: '1',
          name: 'إدارة التقنية',
          description: 'قسم تقنية المعلومات',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ]

      setRoles(mockRoles)
      setUsers(mockUsers)
      setDepartments(mockDepartments)
    } catch (error) {
      showError('خطأ في تحميل البيانات', 'حدث خطأ أثناء تحميل بيانات الصلاحيات')
    } finally {
      setIsLoading(false)
    }
  }

  // حفظ الدور
  const saveRole = async (role: Role) => {
    try {
      if (editingRole) {
        setRoles(prev => prev.map(r => r.id === role.id ? role : r))
        showSuccess('تم التحديث', 'تم تحديث الدور بنجاح')
      } else {
        setRoles(prev => [...prev, { ...role, id: Date.now().toString() }])
        showSuccess('تم الإضافة', 'تم إضافة الدور بنجاح')
      }
      setEditingRole(null)
    } catch (error) {
      showError('خطأ في الحفظ', 'حدث خطأ أثناء حفظ الدور')
    }
  }

  // حذف الدور
  const deleteRole = async (roleId: string) => {
    try {
      setRoles(prev => prev.filter(r => r.id !== roleId))
      showSuccess('تم الحذف', 'تم حذف الدور بنجاح')
    } catch (error) {
      showError('خطأ في الحذف', 'حدث خطأ أثناء حذف الدور')
    }
  }

  // تحديث دور المستخدم
  const updateUserRole = async (userId: string, roleId: string) => {
    try {
      setUsers(prev => prev.map(u => u.id === userId ? { ...u, roleId } : u))
      showSuccess('تم التحديث', 'تم تحديث دور المستخدم بنجاح')
    } catch (error) {
      showError('خطأ في التحديث', 'حدث خطأ أثناء تحديث دور المستخدم')
    }
  }

  if (isLoading) {
    return (
      <div className={`${className} flex items-center justify-center h-64`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`${className} space-y-6`}>
      {/* التبويبات */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('roles')}
          className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
            activeTab === 'roles'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Shield className="w-4 h-4" />
          الأدوار
        </button>
        <button
          onClick={() => setActiveTab('users')}
          className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
            activeTab === 'users'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Users className="w-4 h-4" />
          المستخدمون
        </button>
        <button
          onClick={() => setActiveTab('permissions')}
          className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
            activeTab === 'permissions'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Settings className="w-4 h-4" />
          الصلاحيات
        </button>
      </div>

      {/* محتوى التبويبات */}
      {activeTab === 'roles' && (
        <RolesTab
          roles={roles}
          onEdit={setEditingRole}
          onDelete={deleteRole}
          onSave={saveRole}
          editingRole={editingRole}
          onCancelEdit={() => setEditingRole(null)}
        />
      )}

      {activeTab === 'users' && (
        <UsersTab
          users={users}
          roles={roles}
          departments={departments}
          onUpdateUserRole={updateUserRole}
        />
      )}

      {activeTab === 'permissions' && (
        <PermissionsTab roles={roles} />
      )}
    </div>
  )
}

// تبويب الأدوار
function RolesTab({
  roles,
  onEdit,
  onDelete,
  onSave,
  editingRole,
  onCancelEdit
}: {
  roles: Role[]
  onEdit: (role: Role) => void
  onDelete: (roleId: string) => void
  onSave: (role: Role) => void
  editingRole: Role | null
  onCancelEdit: () => void
}) {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">إدارة الأدوار</h3>
        <Button
          onClick={() => onEdit({
            id: '',
            name: '',
            displayName: '',
            description: '',
            permissions: {},
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          })}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          إضافة دور جديد
        </Button>
      </div>

      <div className="grid gap-4">
        {roles.map(role => (
          <Card key={role.id} className="p-4">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-medium">{role.displayName}</h4>
                <p className="text-sm text-gray-600">{role.description}</p>
                <div className="mt-2 flex flex-wrap gap-1">
                  {Object.keys(role.permissions).map(permission => (
                    <span
                      key={permission}
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                    >
                      {permission}
                    </span>
                  ))}
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => onEdit(role)}
                >
                  <Edit className="w-4 h-4" />
                </Button>
                <Button
                  variant="danger"
                  size="sm"
                  onClick={() => onDelete(role.id)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* نموذج التحرير */}
      {editingRole && (
        <RoleEditForm
          role={editingRole}
          onSave={onSave}
          onCancel={onCancelEdit}
        />
      )}
    </div>
  )
}

// تبويب المستخدمين
function UsersTab({
  users,
  roles,
  departments,
  onUpdateUserRole
}: {
  users: User[]
  roles: Role[]
  departments: Department[]
  onUpdateUserRole: (userId: string, roleId: string) => void
}) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">إدارة المستخدمين</h3>
      
      <div className="grid gap-4">
        {users.map(user => {
          const userRole = roles.find(r => r.id === user.roleId)
          const userDepartment = departments.find(d => d.id === user.departmentId)
          
          return (
            <Card key={user.id} className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <h4 className="font-medium">{user.name}</h4>
                  <p className="text-sm text-gray-600">{user.email}</p>
                  <p className="text-sm text-gray-500">{userDepartment?.name}</p>
                </div>
                <div className="flex items-center gap-2">
                  <select
                    value={user.roleId}
                    onChange={(e) => onUpdateUserRole(user.id, e.target.value)}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm"
                  >
                    {roles.map(role => (
                      <option key={role.id} value={role.id}>
                        {role.displayName}
                      </option>
                    ))}
                  </select>
                  <span className={`px-2 py-1 rounded text-xs ${
                    user.isActive 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {user.isActive ? 'نشط' : 'غير نشط'}
                  </span>
                </div>
              </div>
            </Card>
          )
        })}
      </div>
    </div>
  )
}

// تبويب الصلاحيات
function PermissionsTab({ roles }: { roles: Role[] }) {
  const allPermissions = [
    'projects.read',
    'projects.write',
    'projects.delete',
    'requests.read',
    'requests.write',
    'requests.approve',
    'users.read',
    'users.write',
    'system.admin'
  ]

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">مصفوفة الصلاحيات</h3>
      
      <div className="overflow-x-auto">
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-50">
              <th className="border border-gray-300 px-4 py-2 text-right">الدور</th>
              {allPermissions.map(permission => (
                <th key={permission} className="border border-gray-300 px-2 py-2 text-xs">
                  {permission}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {roles.map(role => (
              <tr key={role.id}>
                <td className="border border-gray-300 px-4 py-2 font-medium">
                  {role.displayName}
                </td>
                {allPermissions.map(permission => (
                  <td key={permission} className="border border-gray-300 px-2 py-2 text-center">
                    {role.permissions[permission] || role.permissions.all ? (
                      <span className="text-green-600">✓</span>
                    ) : (
                      <span className="text-red-600">✗</span>
                    )}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

// نموذج تحرير الدور
function RoleEditForm({
  role,
  onSave,
  onCancel
}: {
  role: Role
  onSave: (role: Role) => void
  onCancel: () => void
}) {
  const [formData, setFormData] = useState(role)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave(formData)
  }

  return (
    <Card className="p-6">
      <h4 className="text-lg font-semibold mb-4">
        {role.id ? 'تحرير الدور' : 'إضافة دور جديد'}
      </h4>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            اسم الدور
          </label>
          <Input
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="admin, manager, employee"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            الاسم المعروض
          </label>
          <Input
            value={formData.displayName}
            onChange={(e) => setFormData(prev => ({ ...prev, displayName: e.target.value }))}
            placeholder="مدير النظام"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            الوصف
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={3}
            placeholder="وصف الدور والصلاحيات"
          />
        </div>

        <div className="flex gap-2">
          <Button type="submit" className="flex items-center gap-2">
            <Save className="w-4 h-4" />
            حفظ
          </Button>
          <Button
            type="button"
            variant="secondary"
            onClick={onCancel}
            className="flex items-center gap-2"
          >
            <X className="w-4 h-4" />
            إلغاء
          </Button>
        </div>
      </form>
    </Card>
  )
} 