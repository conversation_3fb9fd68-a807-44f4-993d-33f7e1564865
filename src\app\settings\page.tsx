'use client'

import { useState } from 'react'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  Settings, 
  Bell, 
  Shield, 
  Palette, 
  Globe, 
  Key, 
  Mail,
  Moon,
  Sun,
  Volume2,
  VolumeX,
  Eye,
  EyeOff,
  Download,
  Upload,
  Trash2,
  Save,
  RefreshCw
} from 'lucide-react'

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('general')
  const [isLoading, setIsLoading] = useState(false)
  
  // إعدادات الإشعارات
  const [notifications, setNotifications] = useState({
    email: true,
    push: true,
    sms: false,
    newRequests: true,
    approvals: true,
    projectUpdates: true,
    systemAlerts: true
  })

  // إعدادات المظهر
  const [appearance, setAppearance] = useState({
    theme: 'light',
    language: 'ar',
    fontSize: 'medium',
    compactMode: false
  })

  // إعدادات الأمان
  const [security, setSecurity] = useState({
    twoFactorAuth: false,
    sessionTimeout: 30,
    loginNotifications: true,
    passwordReminders: true
  })

  const tabs = [
    { id: 'general', name: 'عام', icon: Settings },
    { id: 'notifications', name: 'الإشعارات', icon: Bell },
    { id: 'appearance', name: 'المظهر', icon: Palette },
    { id: 'security', name: 'الأمان', icon: Shield },
    { id: 'data', name: 'البيانات', icon: Download }
  ]

  const handleSave = async () => {
    setIsLoading(true)
    try {
      // محاكاة حفظ الإعدادات
      await new Promise(resolve => setTimeout(resolve, 1000))
      alert('تم حفظ الإعدادات بنجاح!')
    } catch (error) {
      console.error('Error saving settings:', error)
      alert('حدث خطأ أثناء حفظ الإعدادات')
    } finally {
      setIsLoading(false)
    }
  }

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900">الإعدادات العامة</h3>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">اللغة</label>
            <select 
              value={appearance.language}
              onChange={(e) => setAppearance(prev => ({ ...prev, language: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="ar">العربية</option>
              <option value="en">English</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">المنطقة الزمنية</label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              <option value="Asia/Riyadh">الرياض (GMT+3)</option>
              <option value="Asia/Dubai">دبي (GMT+4)</option>
              <option value="Asia/Kuwait">الكويت (GMT+3)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">تنسيق التاريخ</label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              <option value="dd/mm/yyyy">يوم/شهر/سنة</option>
              <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
              <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
            </select>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900">طرق الإشعار</h3>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Mail className="w-5 h-5 text-gray-400" />
              <div>
                <p className="font-medium text-gray-900">البريد الإلكتروني</p>
                <p className="text-sm text-gray-500">تلقي الإشعارات عبر البريد الإلكتروني</p>
              </div>
            </div>
            <button
              onClick={() => setNotifications(prev => ({ ...prev, email: !prev.email }))}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                notifications.email ? 'bg-blue-600' : 'bg-gray-200'
              }`}
            >
              <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                notifications.email ? 'translate-x-6' : 'translate-x-1'
              }`} />
            </button>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Bell className="w-5 h-5 text-gray-400" />
              <div>
                <p className="font-medium text-gray-900">الإشعارات المنبثقة</p>
                <p className="text-sm text-gray-500">إشعارات فورية في المتصفح</p>
              </div>
            </div>
            <button
              onClick={() => setNotifications(prev => ({ ...prev, push: !prev.push }))}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                notifications.push ? 'bg-blue-600' : 'bg-gray-200'
              }`}
            >
              <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                notifications.push ? 'translate-x-6' : 'translate-x-1'
              }`} />
            </button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900">أنواع الإشعارات</h3>
        </CardHeader>
        <CardContent className="space-y-4">
          {[
            { key: 'newRequests', label: 'طلبات جديدة', description: 'عند تقديم طلب جديد' },
            { key: 'approvals', label: 'الموافقات', description: 'عند الحاجة للموافقة أو تغيير الحالة' },
            { key: 'projectUpdates', label: 'تحديثات المشاريع', description: 'عند تحديث حالة المشروع' },
            { key: 'systemAlerts', label: 'تنبيهات النظام', description: 'تنبيهات مهمة من النظام' }
          ].map((item) => (
            <div key={item.key} className="flex items-center justify-between">
              <div>
                <p className="font-medium text-gray-900">{item.label}</p>
                <p className="text-sm text-gray-500">{item.description}</p>
              </div>
              <button
                onClick={() => setNotifications(prev => ({ ...prev, [item.key]: !prev[item.key as keyof typeof prev] }))}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  notifications[item.key as keyof typeof notifications] ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              >
                <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  notifications[item.key as keyof typeof notifications] ? 'translate-x-6' : 'translate-x-1'
                }`} />
              </button>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  )

  const renderAppearanceSettings = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900">المظهر والعرض</h3>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">المظهر</label>
            <div className="grid grid-cols-2 gap-4">
              <button
                onClick={() => setAppearance(prev => ({ ...prev, theme: 'light' }))}
                className={`p-4 border-2 rounded-lg flex items-center gap-3 transition-colors ${
                  appearance.theme === 'light' 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <Sun className="w-5 h-5" />
                <span>المظهر الفاتح</span>
              </button>
              <button
                onClick={() => setAppearance(prev => ({ ...prev, theme: 'dark' }))}
                className={`p-4 border-2 rounded-lg flex items-center gap-3 transition-colors ${
                  appearance.theme === 'dark' 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <Moon className="w-5 h-5" />
                <span>المظهر الداكن</span>
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">حجم الخط</label>
            <select 
              value={appearance.fontSize}
              onChange={(e) => setAppearance(prev => ({ ...prev, fontSize: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="small">صغير</option>
              <option value="medium">متوسط</option>
              <option value="large">كبير</option>
            </select>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900">الوضع المضغوط</p>
              <p className="text-sm text-gray-500">عرض أكثر المحتوى في مساحة أقل</p>
            </div>
            <button
              onClick={() => setAppearance(prev => ({ ...prev, compactMode: !prev.compactMode }))}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                appearance.compactMode ? 'bg-blue-600' : 'bg-gray-200'
              }`}
            >
              <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                appearance.compactMode ? 'translate-x-6' : 'translate-x-1'
              }`} />
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900">الأمان والخصوصية</h3>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Shield className="w-5 h-5 text-gray-400" />
              <div>
                <p className="font-medium text-gray-900">المصادقة الثنائية</p>
                <p className="text-sm text-gray-500">حماية إضافية لحسابك</p>
              </div>
            </div>
            <button
              onClick={() => setSecurity(prev => ({ ...prev, twoFactorAuth: !prev.twoFactorAuth }))}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                security.twoFactorAuth ? 'bg-blue-600' : 'bg-gray-200'
              }`}
            >
              <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                security.twoFactorAuth ? 'translate-x-6' : 'translate-x-1'
              }`} />
            </button>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">انتهاء الجلسة (دقيقة)</label>
            <select 
              value={security.sessionTimeout}
              onChange={(e) => setSecurity(prev => ({ ...prev, sessionTimeout: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value={15}>15 دقيقة</option>
              <option value={30}>30 دقيقة</option>
              <option value={60}>ساعة واحدة</option>
              <option value={120}>ساعتان</option>
            </select>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900">إشعارات تسجيل الدخول</p>
              <p className="text-sm text-gray-500">تنبيه عند تسجيل دخول جديد</p>
            </div>
            <button
              onClick={() => setSecurity(prev => ({ ...prev, loginNotifications: !prev.loginNotifications }))}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                security.loginNotifications ? 'bg-blue-600' : 'bg-gray-200'
              }`}
            >
              <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                security.loginNotifications ? 'translate-x-6' : 'translate-x-1'
              }`} />
            </button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900">كلمة المرور</h3>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button variant="ghost" icon={<Key className="w-4 h-4" />}>
            تغيير كلمة المرور
          </Button>
          <Button variant="ghost" icon={<RefreshCw className="w-4 h-4" />}>
            إعادة تعيين كلمة المرور
          </Button>
        </CardContent>
      </Card>
    </div>
  )

  const renderDataSettings = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900">إدارة البيانات</h3>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-3">
              <Download className="w-5 h-5 text-blue-600" />
              <div>
                <p className="font-medium text-gray-900">تصدير البيانات</p>
                <p className="text-sm text-gray-500">تحميل نسخة من بياناتك</p>
              </div>
            </div>
            <Button variant="ghost" size="sm">
              تصدير
            </Button>
          </div>

          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-3">
              <Upload className="w-5 h-5 text-green-600" />
              <div>
                <p className="font-medium text-gray-900">استيراد البيانات</p>
                <p className="text-sm text-gray-500">رفع بيانات من ملف</p>
              </div>
            </div>
            <Button variant="ghost" size="sm">
              استيراد
            </Button>
          </div>

          <div className="flex items-center justify-between p-4 bg-red-50 rounded-lg">
            <div className="flex items-center gap-3">
              <Trash2 className="w-5 h-5 text-red-600" />
              <div>
                <p className="font-medium text-gray-900">حذف الحساب</p>
                <p className="text-sm text-gray-500">حذف الحساب وجميع البيانات نهائياً</p>
              </div>
            </div>
            <Button variant="ghost" size="sm" className="text-red-600 hover:bg-red-100">
              حذف
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general': return renderGeneralSettings()
      case 'notifications': return renderNotificationSettings()
      case 'appearance': return renderAppearanceSettings()
      case 'security': return renderSecuritySettings()
      case 'data': return renderDataSettings()
      default: return renderGeneralSettings()
    }
  }

  return (
    <ProtectedLayout>
      <div className="max-w-6xl mx-auto">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Sidebar */}
          <div className="lg:w-64 flex-shrink-0">
            <Card>
              <CardHeader>
                <h2 className="text-xl font-semibold text-gray-900">الإعدادات</h2>
              </CardHeader>
              <CardContent className="p-0">
                <nav className="space-y-1">
                  {tabs.map((tab) => {
                    const Icon = tab.icon
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`w-full flex items-center gap-3 px-4 py-3 text-right transition-colors ${
                          activeTab === tab.id
                            ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-600'
                            : 'text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        <Icon className="w-5 h-5" />
                        {tab.name}
                      </button>
                    )
                  })}
                </nav>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {renderTabContent()}
            
            {/* Save Button */}
            <div className="mt-6 flex justify-end">
              <Button
                variant="primary"
                icon={<Save className="w-4 h-4" />}
                onClick={handleSave}
                disabled={isLoading}
              >
                {isLoading ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  )
} 