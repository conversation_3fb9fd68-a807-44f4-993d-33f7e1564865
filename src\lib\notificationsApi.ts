import { supabase } from './supabase'

// واجهات البيانات
export interface NotificationData {
  user_id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  action_url?: string
  data?: any
}

export interface BulkNotificationData {
  user_ids: string[]
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  action_url?: string
  data?: any
}

export interface NotificationFilters {
  user_id: string
  is_read?: boolean
  type?: 'info' | 'success' | 'warning' | 'error'
  limit?: number
}

export interface Notification {
  id: string
  user_id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  action_url?: string
  data?: any
  is_read: boolean
  read_at?: string
  created_at: string
}

// دوال API للإشعارات
export class NotificationsAPI {
  
  // إنشاء إشعار واحد
  static async createNotification(data: NotificationData) {
    try {
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في إنشاء الإشعار')
      }

      return await response.json()
    } catch (error) {
      console.error('Error creating notification:', error)
      throw error
    }
  }

  // إرسال إشعارات جماعية
  static async createBulkNotifications(data: BulkNotificationData) {
    try {
      const response = await fetch('/api/notifications/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في إرسال الإشعارات')
      }

      return await response.json()
    } catch (error) {
      console.error('Error creating bulk notifications:', error)
      throw error
    }
  }

  // استرجاع الإشعارات
  static async getNotifications(filters: NotificationFilters) {
    try {
      const params = new URLSearchParams()
      params.append('user_id', filters.user_id)
      
      if (filters.is_read !== undefined) {
        params.append('is_read', filters.is_read.toString())
      }
      if (filters.type) {
        params.append('type', filters.type)
      }
      if (filters.limit) {
        params.append('limit', filters.limit.toString())
      }

      const response = await fetch(`/api/notifications?${params.toString()}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في استرجاع الإشعارات')
      }

      return await response.json()
    } catch (error) {
      console.error('Error fetching notifications:', error)
      throw error
    }
  }

  // تحديث حالة إشعار واحد
  static async updateNotification(notificationId: string, isRead: boolean, userId?: string) {
    try {
      const response = await fetch('/api/notifications', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notification_id: notificationId,
          is_read: isRead,
          user_id: userId
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في تحديث الإشعار')
      }

      return await response.json()
    } catch (error) {
      console.error('Error updating notification:', error)
      throw error
    }
  }

  // تحديث حالة إشعارات متعددة
  static async updateBulkNotifications(notificationIds: string[], isRead: boolean, userId?: string) {
    try {
      const response = await fetch('/api/notifications/bulk', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notification_ids: notificationIds,
          is_read: isRead,
          user_id: userId
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في تحديث الإشعارات')
      }

      return await response.json()
    } catch (error) {
      console.error('Error updating bulk notifications:', error)
      throw error
    }
  }

  // حذف إشعار واحد
  static async deleteNotification(notificationId: string, userId?: string) {
    try {
      const params = new URLSearchParams()
      params.append('notification_id', notificationId)
      if (userId) {
        params.append('user_id', userId)
      }

      const response = await fetch(`/api/notifications?${params.toString()}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في حذف الإشعار')
      }

      return await response.json()
    } catch (error) {
      console.error('Error deleting notification:', error)
      throw error
    }
  }

  // حذف إشعارات متعددة
  static async deleteBulkNotifications(notificationIds: string[], userId?: string) {
    try {
      const response = await fetch('/api/notifications/bulk', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notification_ids: notificationIds,
          user_id: userId
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في حذف الإشعارات')
      }

      return await response.json()
    } catch (error) {
      console.error('Error deleting bulk notifications:', error)
      throw error
    }
  }

  // إحصائيات الإشعارات
  static async getNotificationStats(userId: string) {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('type, read_at, created_at')
        .eq('user_id', userId)

      if (error) {
        throw new Error('حدث خطأ في استرجاع إحصائيات الإشعارات')
      }

      const stats = {
        total: data?.length || 0,
        unread: data?.filter(n => !n.read_at).length || 0,
        read: data?.filter(n => n.read_at).length || 0,
        byType: {
          info: data?.filter(n => n.type === 'info').length || 0,
          success: data?.filter(n => n.type === 'success').length || 0,
          warning: data?.filter(n => n.type === 'warning').length || 0,
          error: data?.filter(n => n.type === 'error').length || 0
        },
        today: data?.filter(n => 
          n.created_at && new Date(n.created_at).toDateString() === new Date().toDateString()
        ).length || 0
      }

      return { success: true, data: stats }
    } catch (error) {
      console.error('Error fetching notification stats:', error)
      throw error
    }
  }

  // تحديد المستخدمين المعنيين بالطلب
  static async getStakeholders(requestId: string, requestType: string): Promise<string[]> {
    try {
      const { data: request, error } = await supabase
        .from('project_requests')
        .select(`
          *,
          requester:users!requester_id(id, name, email),
          department:departments!department_id(id, name)
        `)
        .eq('id', requestId)
        .single()

      if (error || !request) {
        throw new Error('الطلب غير موجود')
      }

      const stakeholders: string[] = []

      // المقدم
      stakeholders.push(request.requester_id)

      // المعتمدون حسب نوع الطلب
      const approvers = await this.getApproversByRequestType(request.main_type, request.sub_type)
      stakeholders.push(...approvers)

      // مدير القسم المسؤول
      if (request.department?.id) {
        const departmentManager = await this.getDepartmentManager(request.department.id)
        if (departmentManager) {
          stakeholders.push(departmentManager)
        }
      }

      // إزالة التكرارات
      return [...new Set(stakeholders)]
    } catch (error) {
      console.error('Error getting stakeholders:', error)
      return []
    }
  }

  // الحصول على المعتمدين حسب نوع الطلب
  private static async getApproversByRequestType(mainType: string, subType: string | null): Promise<string[]> {
    try {
      const roles = []
      
      // تحديد الأدوار المطلوبة
      if (mainType === 'improvement_project') {
        switch (subType) {
          case 'quick_win':
            roles.push('pmo_manager')
            break
          case 'suggestion':
            roles.push('pmo_manager', 'planning_manager')
            break
          case 'improvement_full':
            roles.push('pmo_manager', 'planning_manager', 'executive_manager')
            break
        }
      } else if (mainType === 'general_project') {
        roles.push('pmo_manager', 'planning_manager', 'executive_manager')
      }

      // الحصول على المستخدمين بهذه الأدوار
      const { data: users, error } = await supabase
        .from('users')
        .select('id, role:roles!role_id(name)')
        .in('role.name', roles)

      if (error) {
        console.error('Error fetching approvers:', error)
        return []
      }

      return users?.map(user => user.id) || []
    } catch (error) {
      console.error('Error getting approvers by request type:', error)
      return []
    }
  }

  // الحصول على مدير القسم
  private static async getDepartmentManager(departmentId: string): Promise<string | null> {
    try {
      const { data: manager, error } = await supabase
        .from('users')
        .select('id')
        .eq('department_id', departmentId)
        .eq('role.name', 'department_manager')
        .single()

      if (error || !manager) {
        return null
      }

      return manager.id
    } catch (error) {
      console.error('Error getting department manager:', error)
      return null
    }
  }
}

// دوال مساعدة للإشعارات
export class NotificationHelpers {
  
  // تحديد لون نوع الإشعار
  static getTypeColor(type: string): string {
    switch (type) {
      case 'info': return 'text-blue-600 bg-blue-100'
      case 'success': return 'text-green-600 bg-green-100'
      case 'warning': return 'text-yellow-600 bg-yellow-100'
      case 'error': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  // تحديد أيقونة نوع الإشعار
  static getTypeIcon(type: string): string {
    switch (type) {
      case 'info': return '📢'
      case 'success': return '✅'
      case 'warning': return '⚠️'
      case 'error': return '❌'
      default: return '📋'
    }
  }

  // تحديد نص نوع الإشعار
  static getTypeText(type: string): string {
    switch (type) {
      case 'info': return 'معلومات'
      case 'success': return 'نجح'
      case 'warning': return 'تحذير'
      case 'error': return 'خطأ'
      default: return 'غير محدد'
    }
  }

  // حساب الوقت المنقضي
  static getTimeElapsed(createdAt: string): string {
    const now = new Date()
    const created = new Date(createdAt)
    const diffInMinutes = Math.floor((now.getTime() - created.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) {
      return 'الآن'
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} دقيقة`
    } else if (diffInMinutes < 1440) {
      const diffInHours = Math.floor(diffInMinutes / 60)
      return `${diffInHours} ساعة`
    } else {
      const diffInDays = Math.floor(diffInMinutes / 1440)
      return `${diffInDays} يوم`
    }
  }

  // تنسيق تاريخ الإشعار
  static formatDate(dateString: string): string {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // تقصير النص
  static truncateText(text: string, maxLength: number = 100): string {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
  }
} 