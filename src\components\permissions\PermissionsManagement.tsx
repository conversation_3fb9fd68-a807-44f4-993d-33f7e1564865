'use client'

import React, { useState, useEffect } from 'react'
import { Save, RotateCcw, Shield, Check, X, Info } from 'lucide-react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

interface Role {
  id: string
  name: string
  display_name: string
  permissions: Record<string, any>
}

interface Permission {
  id: string
  name: string
  display_name: string
  description: string
  category: string
  actions: string[]
}

interface PermissionState {
  [roleId: string]: {
    [permissionName: string]: string[]
  }
}

export default function PermissionsManagement() {
  const [roles, setRoles] = useState<Role[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [permissionStates, setPermissionStates] = useState<PermissionState>({})
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [selectedRole, setSelectedRole] = useState<string>('')

  // بيانات تجريبية للأدوار
  const mockRoles: Role[] = [
    {
      id: '1',
      name: 'admin',
      display_name: 'مدير النظام',
      permissions: { all: true }
    },
    {
      id: '2',
      name: 'pmo_manager',
      display_name: 'مدير مكتب المشاريع',
      permissions: { 
        projects: ['read', 'write', 'approve'], 
        requests: ['read', 'write', 'approve'],
        users: ['read'],
        reports: ['read', 'write']
      }
    },
    {
      id: '3',
      name: 'planning_manager',
      display_name: 'مدير إدارة التخطيط',
      permissions: { 
        projects: ['read', 'approve'], 
        requests: ['read', 'approve'],
        reports: ['read']
      }
    },
    {
      id: '4',
      name: 'project_manager',
      display_name: 'مدير مشروع',
      permissions: { 
        projects: ['read', 'write'], 
        tasks: ['read', 'write'],
        reports: ['read']
      }
    },
    {
      id: '5',
      name: 'employee',
      display_name: 'موظف',
      permissions: { 
        requests: ['read', 'write'], 
        projects: ['read']
      }
    }
  ]

  // بيانات تجريبية للصلاحيات
  const mockPermissions: Permission[] = [
    {
      id: 'projects',
      name: 'projects',
      display_name: 'إدارة المشاريع',
      description: 'التحكم في المشاريع ومراحلها',
      category: 'المشاريع',
      actions: ['read', 'write', 'approve', 'delete']
    },
    {
      id: 'requests',
      name: 'requests',
      display_name: 'طلبات المشاريع',
      description: 'إدارة طلبات المشاريع الجديدة',
      category: 'الطلبات',
      actions: ['read', 'write', 'approve', 'delete']
    },
    {
      id: 'users',
      name: 'users',
      display_name: 'إدارة المستخدمين',
      description: 'التحكم في المستخدمين وصلاحياتهم',
      category: 'المستخدمين',
      actions: ['read', 'write', 'delete']
    },
    {
      id: 'tasks',
      name: 'tasks',
      display_name: 'إدارة المهام',
      description: 'التحكم في مهام المشاريع',
      category: 'المهام',
      actions: ['read', 'write', 'assign', 'delete']
    },
    {
      id: 'reports',
      name: 'reports',
      display_name: 'التقارير',
      description: 'الوصول للتقارير والإحصائيات',
      category: 'التقارير',
      actions: ['read', 'write', 'export']
    },
    {
      id: 'departments',
      name: 'departments',
      display_name: 'إدارة الأقسام',
      description: 'التحكم في الأقسام والهيكل التنظيمي',
      category: 'التنظيم',
      actions: ['read', 'write', 'delete']
    }
  ]

  const actionLabels: Record<string, string> = {
    read: 'عرض',
    write: 'تعديل',
    approve: 'اعتماد',
    delete: 'حذف',
    assign: 'تعيين',
    export: 'تصدير'
  }

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setRoles(mockRoles)
      setPermissions(mockPermissions)
      
      // تحويل صلاحيات الأدوار إلى الشكل المطلوب
      const initialStates: PermissionState = {}
      mockRoles.forEach(role => {
        initialStates[role.id] = {}
        mockPermissions.forEach(permission => {
          const rolePermissions = role.permissions[permission.name] || []
          initialStates[role.id][permission.name] = Array.isArray(rolePermissions) 
            ? rolePermissions 
            : []
        })
      })
      
      setPermissionStates(initialStates)
      setSelectedRole(mockRoles[0]?.id || '')
      setLoading(false)
    }, 1000)
  }, [])

  const togglePermission = (roleId: string, permissionName: string, action: string) => {
    setPermissionStates(prev => {
      const newState = { ...prev }
      if (!newState[roleId]) {
        newState[roleId] = {}
      }
      if (!newState[roleId][permissionName]) {
        newState[roleId][permissionName] = []
      }

      const currentActions = newState[roleId][permissionName]
      const hasAction = currentActions.includes(action)

      if (hasAction) {
        newState[roleId][permissionName] = currentActions.filter(a => a !== action)
      } else {
        newState[roleId][permissionName] = [...currentActions, action]
      }

      return newState
    })
  }

  const hasPermission = (roleId: string, permissionName: string, action: string): boolean => {
    const role = roles.find(r => r.id === roleId)
    if (role?.name === 'admin') return true // المدير له صلاحيات كاملة
    
    return permissionStates[roleId]?.[permissionName]?.includes(action) || false
  }

  const resetPermissions = () => {
    const role = roles.find(r => r.id === selectedRole)
    if (!role) return

    const resetState: PermissionState = { ...permissionStates }
    resetState[selectedRole] = {}
    
    permissions.forEach(permission => {
      const rolePermissions = role.permissions[permission.name] || []
      resetState[selectedRole][permission.name] = Array.isArray(rolePermissions) 
        ? rolePermissions 
        : []
    })
    
    setPermissionStates(resetState)
  }

  const savePermissions = async () => {
    setSaving(true)
    try {
      // محاكاة حفظ البيانات
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // تحديث صلاحيات الدور
      const updatedRoles = roles.map(role => {
        if (role.id === selectedRole) {
          const newPermissions: Record<string, string[]> = {}
          permissions.forEach(permission => {
            const actions = permissionStates[selectedRole]?.[permission.name] || []
            if (actions.length > 0) {
              newPermissions[permission.name] = actions
            }
          })
          return { ...role, permissions: newPermissions }
        }
        return role
      })
      
      setRoles(updatedRoles)
      alert('تم حفظ الصلاحيات بنجاح')
    } catch (error) {
      alert('حدث خطأ أثناء حفظ الصلاحيات')
    } finally {
      setSaving(false)
    }
  }

  const selectedRoleData = roles.find(r => r.id === selectedRole)
  const isAdminRole = selectedRoleData?.name === 'admin'

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">إدارة الصلاحيات</h2>
          <p className="text-gray-600 mt-1">
            تحديد صلاحيات كل دور في النظام
          </p>
        </div>
        
        <div className="flex gap-3">
          <Button
            onClick={resetPermissions}
            variant="secondary"
            className="flex items-center gap-2"
            disabled={!selectedRole}
          >
            <RotateCcw className="w-4 h-4" />
            إعادة تعيين
          </Button>
          <Button
            onClick={savePermissions}
            className="flex items-center gap-2"
            disabled={saving || !selectedRole}
          >
            <Save className="w-4 h-4" />
            {saving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
          </Button>
        </div>
      </div>

      {/* Role Selection */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">اختر الدور</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
          {roles.map((role) => (
            <button
              key={role.id}
              onClick={() => setSelectedRole(role.id)}
              className={`p-3 text-center border rounded-lg transition-colors ${
                selectedRole === role.id
                  ? 'border-blue-500 bg-blue-50 text-blue-900'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <Shield className="w-6 h-6 mx-auto mb-2" />
              <div className="font-medium text-sm">{role.display_name}</div>
            </button>
          ))}
        </div>
      </Card>

      {/* Permissions Matrix */}
      {selectedRole && (
        <Card className="p-6">
          <div className="flex items-center gap-3 mb-6">
            <Shield className="w-6 h-6 text-blue-600" />
            <h3 className="text-lg font-semibold">
              صلاحيات: {selectedRoleData?.display_name}
            </h3>
            {isAdminRole && (
              <div className="flex items-center gap-2 bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">
                <Info className="w-4 h-4" />
                صلاحيات كاملة (غير قابلة للتعديل)
              </div>
            )}
          </div>

          <div className="space-y-6">
            {permissions.reduce((categories, permission) => {
              if (!categories[permission.category]) {
                categories[permission.category] = []
              }
              categories[permission.category].push(permission)
              return categories
            }, {} as Record<string, Permission[]>).map = Object.entries}
            
            {Object.entries(
              permissions.reduce((categories, permission) => {
                if (!categories[permission.category]) {
                  categories[permission.category] = []
                }
                categories[permission.category].push(permission)
                return categories
              }, {} as Record<string, Permission[]>)
            ).map(([category, categoryPermissions]) => (
              <div key={category} className="border border-gray-200 rounded-lg overflow-hidden">
                <div className="bg-gray-50 px-4 py-3 border-b">
                  <h4 className="font-semibold text-gray-900">{category}</h4>
                </div>
                
                <div className="p-4 space-y-4">
                  {categoryPermissions.map((permission) => (
                    <div key={permission.id} className="space-y-3">
                      <div>
                        <h5 className="font-medium text-gray-900">{permission.display_name}</h5>
                        <p className="text-sm text-gray-600">{permission.description}</p>
                      </div>
                      
                      <div className="flex flex-wrap gap-3">
                        {permission.actions.map((action) => {
                          const isChecked = hasPermission(selectedRole, permission.name, action)
                          
                          return (
                            <label
                              key={action}
                              className={`flex items-center gap-2 px-3 py-2 border rounded-lg cursor-pointer transition-colors ${
                                isChecked
                                  ? 'border-green-500 bg-green-50 text-green-900'
                                  : 'border-gray-300 hover:border-gray-400'
                              } ${isAdminRole ? 'opacity-50 cursor-not-allowed' : ''}`}
                            >
                              <input
                                type="checkbox"
                                checked={isChecked}
                                onChange={() => togglePermission(selectedRole, permission.name, action)}
                                disabled={isAdminRole}
                                className="sr-only"
                              />
                              <div className={`w-4 h-4 border rounded flex items-center justify-center ${
                                isChecked ? 'border-green-500 bg-green-500' : 'border-gray-300'
                              }`}>
                                {isChecked && <Check className="w-3 h-3 text-white" />}
                              </div>
                              <span className="text-sm font-medium">
                                {actionLabels[action] || action}
                              </span>
                            </label>
                          )
                        })}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {isAdminRole && (
            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center gap-2 text-yellow-800">
                <Info className="w-5 h-5" />
                <p className="font-medium">ملاحظة</p>
              </div>
              <p className="text-yellow-700 mt-2 text-sm">
                دور مدير النظام له صلاحيات كاملة في جميع أجزاء النظام ولا يمكن تعديل صلاحياته.
              </p>
            </div>
          )}
        </Card>
      )}

      {!selectedRole && (
        <div className="text-center py-12">
          <Shield className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            اختر دوراً لإدارة صلاحياته
          </h3>
          <p className="text-gray-600">
            حدد أحد الأدوار أعلاه لعرض وتعديل صلاحياته
          </p>
        </div>
      )}
    </div>
  )
} 