'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { WorkflowTracker } from '@/components/workflow/WorkflowTracker'
import { 
  ArrowLeft, 
  Edit, 
  Send, 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertCircle,
  FileText,
  Lightbulb,
  Target,
  Briefcase,
  Settings,
  Zap,
  User,
  Calendar,
  DollarSign,
  Building,
  Flag
} from 'lucide-react'

interface RequestData {
  id: string
  title: string
  description: string
  type: 'general_project' | 'improvement_project'
  subType: 'comprehensive' | 'suggestion' | 'quick_win'
  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected' | 'completed'
  createdAt: string
  updatedAt: string
  submittedBy: string
  assignedTo?: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  expectedDuration: string
  estimatedCost: number
  department: string
  formData: any
  workflow: any[]
  comments: any[]
}

const statusConfig = {
  draft: { icon: Edit, color: 'text-gray-500', bg: 'bg-gray-100', label: 'مسودة' },
  submitted: { icon: Send, color: 'text-blue-500', bg: 'bg-blue-100', label: 'مُرسل' },
  under_review: { icon: Clock, color: 'text-yellow-500', bg: 'bg-yellow-100', label: 'قيد المراجعة' },
  approved: { icon: CheckCircle, color: 'text-green-500', bg: 'bg-green-100', label: 'معتمد' },
  rejected: { icon: XCircle, color: 'text-red-500', bg: 'bg-red-100', label: 'مرفوض' },
  completed: { icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-200', label: 'مكتمل' }
}

const typeConfig = {
  general_project: { icon: Briefcase, label: 'مشروع عام', color: 'text-blue-600' },
  improvement_project: { icon: Target, label: 'مشروع تحسين', color: 'text-green-600' }
}

const subTypeConfig = {
  comprehensive: { icon: Settings, label: 'تحسين شامل', color: 'text-purple-600' },
  suggestion: { icon: Lightbulb, label: 'مقترح تحسين', color: 'text-yellow-600' },
  quick_win: { icon: Zap, label: 'كويك وين', color: 'text-orange-600' }
}

const priorityConfig = {
  low: { color: 'text-green-600', bg: 'bg-green-100', label: 'منخفض' },
  medium: { color: 'text-yellow-600', bg: 'bg-yellow-100', label: 'متوسط' },
  high: { color: 'text-orange-600', bg: 'bg-orange-100', label: 'عالي' },
  urgent: { color: 'text-red-600', bg: 'bg-red-100', label: 'عاجل' }
}

export default function RequestDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { user } = useAuth()
  
  const [request, setRequest] = useState<RequestData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('details')

  useEffect(() => {
    loadRequest()
  }, [params.id])

  const loadRequest = async () => {
    try {
      setLoading(true)
      // TODO: Replace with actual API call
      const mockRequest: RequestData = {
        id: params.id as string,
        title: 'تحسين عملية إدارة المخزون',
        description: 'مقترح لتحسين كفاءة إدارة المخزون وتقليل التكاليف',
        type: 'improvement_project',
        subType: 'suggestion',
        status: 'under_review',
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-16T14:20:00Z',
        submittedBy: 'أحمد محمد',
        assignedTo: 'مدير مكتب المشاريع',
        priority: 'high',
        expectedDuration: '3 أشهر',
        estimatedCost: 150000,
        department: 'العمليات',
        formData: {
          problemDescription: 'تأخير في عمليات المخزون',
          proposedSolution: 'تطبيق نظام إدارة مخزون متقدم',
          expectedBenefits: 'تحسين الكفاءة بنسبة 30%'
        },
        workflow: [
          { step: 'submitted', date: '2024-01-15T10:30:00Z', user: 'أحمد محمد', status: 'completed' },
          { step: 'review_level_1', date: '2024-01-16T09:00:00Z', user: 'مدير مكتب المشاريع', status: 'in_progress' }
        ],
        comments: [
          { id: 1, user: 'مدير مكتب المشاريع', text: 'مقترح جيد، يحتاج تفاصيل أكثر', date: '2024-01-16T14:20:00Z' }
        ]
      }
      setRequest(mockRequest)
    } catch (err) {
      setError('فشل في تحميل بيانات الطلب')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async () => {
    try {
      // TODO: Submit request for approval
      console.log('Submitting request for approval')
      loadRequest()
    } catch (err) {
      setError('فشل في إرسال الطلب')
    }
  }

  const canEdit = () => {
    return request?.status === 'draft' && user?.id === request?.submittedBy
  }

  const canSubmit = () => {
    return request?.status === 'draft' && canEdit()
  }

  if (loading) {
    return (
      <ProtectedLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </ProtectedLayout>
    )
  }

  if (error || !request) {
    return (
      <ProtectedLayout>
        <div className="flex flex-col items-center justify-center min-h-screen">
          <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
          <p className="text-red-600 text-lg mb-4">{error || 'لم يتم العثور على الطلب'}</p>
          <Button onClick={() => router.push('/requests')} variant="ghost">
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة للطلبات
          </Button>
        </div>
      </ProtectedLayout>
    )
  }

  const StatusIcon = statusConfig[request.status].icon
  const TypeIcon = typeConfig[request.type].icon
  const SubTypeIcon = subTypeConfig[request.subType].icon

  return (
    <ProtectedLayout>
      <div className="max-w-6xl mx-auto p-6">
        {/* Header */}
        <div className="mb-6">
          <Button 
            onClick={() => router.push('/requests')} 
            variant="ghost"
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة للطلبات
          </Button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">{request.title}</h1>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <span>رقم الطلب: {request.id.slice(0, 8)}</span>
                <span>•</span>
                <span>تاريخ الإنشاء: {new Date(request.createdAt).toLocaleDateString('ar-SA')}</span>
                <span>•</span>
                <span>آخر تحديث: {new Date(request.updatedAt).toLocaleDateString('ar-SA')}</span>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              {canEdit() && (
                <Button onClick={() => router.push(`/requests/${request.id}/edit`)} variant="ghost">
                  <Edit className="h-4 w-4 mr-2" />
                  تعديل
                </Button>
              )}
              {canSubmit() && (
                <Button onClick={handleSubmit} variant="primary">
                  <Send className="h-4 w-4 mr-2" />
                  إرسال للاعتماد
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Status and Type Badges */}
        <div className="flex items-center gap-4 mb-6">
          <div className={`flex items-center gap-2 px-3 py-1 rounded-full ${statusConfig[request.status].bg}`}>
            <StatusIcon className={`h-4 w-4 ${statusConfig[request.status].color}`} />
            <span className={`text-sm font-medium ${statusConfig[request.status].color}`}>
              {statusConfig[request.status].label}
            </span>
          </div>
          
          <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-gray-100">
            <TypeIcon className={`h-4 w-4 ${typeConfig[request.type].color}`} />
            <span className={`text-sm font-medium ${typeConfig[request.type].color}`}>
              {typeConfig[request.type].label}
            </span>
          </div>
          
          <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-gray-100">
            <SubTypeIcon className={`h-4 w-4 ${subTypeConfig[request.subType].color}`} />
            <span className={`text-sm font-medium ${subTypeConfig[request.subType].color}`}>
              {subTypeConfig[request.subType].label}
            </span>
          </div>
          
          <div className={`flex items-center gap-2 px-3 py-1 rounded-full ${priorityConfig[request.priority].bg}`}>
            <Flag className={`h-4 w-4 ${priorityConfig[request.priority].color}`} />
            <span className={`text-sm font-medium ${priorityConfig[request.priority].color}`}>
              {priorityConfig[request.priority].label}
            </span>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="flex space-x-8">
            {[
              { id: 'details', label: 'تفاصيل الطلب', icon: FileText },
              { id: 'workflow', label: 'سير العمل', icon: Clock },
              { id: 'comments', label: 'التعليقات', icon: User }
            ].map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  {tab.label}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {activeTab === 'details' && (
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold">تفاصيل الطلب</h3>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">وصف المشكلة</h4>
                    <p className="text-gray-700">{request.description}</p>
                  </div>
                  
                  {request.formData.problemDescription && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">تفاصيل المشكلة</h4>
                      <p className="text-gray-700">{request.formData.problemDescription}</p>
                    </div>
                  )}
                  
                  {request.formData.proposedSolution && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">الحل المقترح</h4>
                      <p className="text-gray-700">{request.formData.proposedSolution}</p>
                    </div>
                  )}
                  
                  {request.formData.expectedBenefits && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">الفوائد المتوقعة</h4>
                      <p className="text-gray-700">{request.formData.expectedBenefits}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {activeTab === 'workflow' && (
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold">سير العمل</h3>
                </CardHeader>
                <CardContent>
                  <WorkflowTracker 
                    requestId={request.id}
                    mainType={request.type}
                    subType={request.subType}
                    currentStatus={request.status}
                    onApprovalUpdate={loadRequest}
                  />
                </CardContent>
              </Card>
            )}

            {activeTab === 'comments' && (
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold">التعليقات</h3>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {request.comments.map((comment) => (
                      <div key={comment.id} className="border-l-4 border-blue-200 pl-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-gray-900">{comment.user}</span>
                          <span className="text-sm text-gray-500">
                            {new Date(comment.date).toLocaleDateString('ar-SA')}
                          </span>
                        </div>
                        <p className="text-gray-700">{comment.text}</p>
                      </div>
                    ))}
                    
                    {request.comments.length === 0 && (
                      <p className="text-gray-500 text-center py-8">لا توجد تعليقات</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Request Info */}
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">معلومات الطلب</h3>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <User className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">مقدم الطلب</p>
                    <p className="font-medium">{request.submittedBy}</p>
                  </div>
                </div>
                
                {request.assignedTo && (
                  <div className="flex items-center gap-3">
                    <User className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">مُكلف إلى</p>
                      <p className="font-medium">{request.assignedTo}</p>
                    </div>
                  </div>
                )}
                
                <div className="flex items-center gap-3">
                  <Building className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">القسم</p>
                    <p className="font-medium">{request.department}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">المدة المتوقعة</p>
                    <p className="font-medium">{request.expectedDuration}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <DollarSign className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">التكلفة المقدرة</p>
                    <p className="font-medium">{request.estimatedCost.toLocaleString()} ريال</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">إجراءات سريعة</h3>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  variant="ghost" 
                  className="w-full justify-start"
                  onClick={() => window.print()}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  طباعة الطلب
                </Button>
                
                <Button 
                  variant="ghost" 
                  className="w-full justify-start"
                  onClick={() => {
                    const url = `/requests/${request.id}`
                    navigator.clipboard.writeText(window.location.origin + url)
                  }}
                >
                  <Send className="h-4 w-4 mr-2" />
                  نسخ الرابط
                </Button>
                
                {request.status === 'draft' && (
                  <Button 
                    variant="danger" 
                    className="w-full justify-start"
                    onClick={() => {
                      if (confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
                        // TODO: Delete request
                        router.push('/requests')
                      }
                    }}
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    حذف الطلب
                  </Button>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </ProtectedLayout>
  )
} 