'use client'

import React from 'react'
import { Input, Textarea, Select } from '@/components/ui/Input'
import { Button } from '@/components/ui/Button'
import { FieldHelp } from '@/components/ui/HelpSystem'
import { CheckCircle, Plus, Trash2, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>Check, Lightbulb, Target } from 'lucide-react'
import { FormType, UnifiedFormData, EnhancedImprovementData, SuggestionData } from '../UnifiedProjectForm'

interface AdaptiveFinalizeStepProps {
  formType: FormType
  data: UnifiedFormData
  updateData: (field: string, value: any) => void
  errors: Record<string, string>
}

export function AdaptiveFinalizeStep({ formType, data, updateData, errors }: AdaptiveFinalizeStepProps) {
  const getStepTitle = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'Finalize - RAID Matrix والمراجعة'
      case 'suggestion':
        return 'Finalize - التوصيات والخطوات التالية'
      case 'quick_win':
        return 'Finalize - المراجعة النهائية'
      default:
        return 'Finalize - الإنهاء'
    }
  }

  const getStepDescription = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'حدد المخاطر والافتراضات والقضايا والتبعيات للمشروع'
      case 'suggestion':
        return 'اكتب التوصيات النهائية والخطوات التالية المقترحة'
      case 'quick_win':
        return 'راجع جميع المعلومات وتأكد من اكتمال الحل السريع'
      default:
        return 'المراجعة النهائية'
    }
  }

  const getStepColor = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'red'
      case 'suggestion':
        return 'indigo'
      case 'quick_win':
        return 'yellow'
      default:
        return 'gray'
    }
  }

  const colorClasses = {
    red: {
      bg: 'bg-red-50',
      border: 'border-red-200',
      text: 'text-red-900',
      icon: 'text-red-600'
    },
    indigo: {
      bg: 'bg-indigo-50',
      border: 'border-indigo-200',
      text: 'text-indigo-900',
      icon: 'text-indigo-600'
    },
    yellow: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      text: 'text-yellow-900',
      icon: 'text-yellow-600'
    },
    gray: {
      bg: 'bg-gray-50',
      border: 'border-gray-200',
      text: 'text-gray-900',
      icon: 'text-gray-600'
    }
  }

  const stepColor = getStepColor()
  const colors = colorClasses[stepColor]

  // دوال إدارة RAID Matrix للتحسين الشامل
  const addRaidItem = (type: 'risks' | 'assumptions' | 'issues' | 'dependencies') => {
    if (formType === 'enhanced_improvement') {
      const enhancedData = data as EnhancedImprovementData
      const newItem = type === 'risks' 
        ? { description: '', severity: 'medium' as const, mitigation: '' }
        : type === 'assumptions'
        ? { description: '', validation: '' }
        : type === 'issues'
        ? { description: '', solution: '' }
        : { description: '', responsible: '', deadline: '' }

      updateData(type, [...enhancedData[type], newItem])
    }
  }

  const removeRaidItem = (type: 'risks' | 'assumptions' | 'issues' | 'dependencies', index: number) => {
    if (formType === 'enhanced_improvement') {
      const enhancedData = data as EnhancedImprovementData
      const updated = enhancedData[type].filter((_, i) => i !== index)
      updateData(type, updated)
    }
  }

  const updateRaidItem = (type: 'risks' | 'assumptions' | 'issues' | 'dependencies', index: number, field: string, value: any) => {
    if (formType === 'enhanced_improvement') {
      const enhancedData = data as EnhancedImprovementData
      const updated = enhancedData[type].map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
      updateData(type, updated)
    }
  }

  // دوال للمقترحات
  const addNextStep = () => {
    if (formType === 'suggestion') {
      const suggestionData = data as SuggestionData
      updateData('nextSteps', [...suggestionData.nextSteps, ''])
    }
  }

  const removeNextStep = (index: number) => {
    if (formType === 'suggestion') {
      const suggestionData = data as SuggestionData
      const updated = suggestionData.nextSteps.filter((_, i) => i !== index)
      updateData('nextSteps', updated)
    }
  }

  const updateNextStep = (index: number, value: string) => {
    if (formType === 'suggestion') {
      const suggestionData = data as SuggestionData
      const updated = suggestionData.nextSteps.map((step, i) => 
        i === index ? value : step
      )
      updateData('nextSteps', updated)
    }
  }

  const renderEnhancedImprovementFinalize = () => {
    const enhancedData = data as EnhancedImprovementData

    return (
      <div className="space-y-6">
        {/* RAID Matrix */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h4 className="font-semibold text-red-900 mb-4 flex items-center gap-2">
            <AlertTriangle className="w-5 h-5" />
            RAID Matrix
          </h4>

          {/* المخاطر - Risks */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-3">
              <h5 className="font-medium text-gray-900">المخاطر (Risks)</h5>
              <Button onClick={() => addRaidItem('risks')} variant="outline" size="sm">
                <Plus className="w-4 h-4 mr-2" />
                إضافة مخاطرة
              </Button>
            </div>

            {enhancedData.risks.map((risk, index) => (
              <div key={index} className="bg-white border rounded-lg p-4 mb-3">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      وصف المخاطرة
                    </label>
                    <Input
                      value={risk.description}
                      onChange={(e) => updateRaidItem('risks', index, 'description', e.target.value)}
                      placeholder="اكتب وصف المخاطرة..."
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        درجة الخطورة
                      </label>
                      <Select
                        value={risk.severity}
                        onChange={(e) => updateRaidItem('risks', index, 'severity', e.target.value)}
                      >
                        <option value="low">منخفضة</option>
                        <option value="medium">متوسطة</option>
                        <option value="high">عالية</option>
                      </Select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        خطة التخفيف
                      </label>
                      <Input
                        value={risk.mitigation}
                        onChange={(e) => updateRaidItem('risks', index, 'mitigation', e.target.value)}
                        placeholder="كيف سيتم التعامل مع هذه المخاطرة؟"
                      />
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button 
                      onClick={() => removeRaidItem('risks', index)}
                      variant="outline" 
                      size="sm"
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      حذف
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* الافتراضات - Assumptions */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-3">
              <h5 className="font-medium text-gray-900">الافتراضات (Assumptions)</h5>
              <Button onClick={() => addRaidItem('assumptions')} variant="outline" size="sm">
                <Plus className="w-4 h-4 mr-2" />
                إضافة افتراض
              </Button>
            </div>

            {enhancedData.assumptions.map((assumption, index) => (
              <div key={index} className="bg-white border rounded-lg p-4 mb-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      وصف الافتراض
                    </label>
                    <Input
                      value={assumption.description}
                      onChange={(e) => updateRaidItem('assumptions', index, 'description', e.target.value)}
                      placeholder="اكتب الافتراض..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      طريقة التحقق
                    </label>
                    <Input
                      value={assumption.validation}
                      onChange={(e) => updateRaidItem('assumptions', index, 'validation', e.target.value)}
                      placeholder="كيف سيتم التحقق من صحة هذا الافتراض؟"
                    />
                  </div>
                </div>

                <div className="flex justify-end mt-4">
                  <Button 
                    onClick={() => removeRaidItem('assumptions', index)}
                    variant="outline" 
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    حذف
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {/* القضايا - Issues */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-3">
              <h5 className="font-medium text-gray-900">القضايا (Issues)</h5>
              <Button onClick={() => addRaidItem('issues')} variant="outline" size="sm">
                <Plus className="w-4 h-4 mr-2" />
                إضافة قضية
              </Button>
            </div>

            {enhancedData.issues.map((issue, index) => (
              <div key={index} className="bg-white border rounded-lg p-4 mb-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      وصف القضية
                    </label>
                    <Input
                      value={issue.description}
                      onChange={(e) => updateRaidItem('issues', index, 'description', e.target.value)}
                      placeholder="اكتب وصف القضية..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الحل المقترح
                    </label>
                    <Input
                      value={issue.solution}
                      onChange={(e) => updateRaidItem('issues', index, 'solution', e.target.value)}
                      placeholder="ما هو الحل المقترح؟"
                    />
                  </div>
                </div>

                <div className="flex justify-end mt-4">
                  <Button 
                    onClick={() => removeRaidItem('issues', index)}
                    variant="outline" 
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    حذف
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {/* التبعيات - Dependencies */}
          <div>
            <div className="flex justify-between items-center mb-3">
              <h5 className="font-medium text-gray-900">التبعيات (Dependencies)</h5>
              <Button onClick={() => addRaidItem('dependencies')} variant="outline" size="sm">
                <Plus className="w-4 h-4 mr-2" />
                إضافة تبعية
              </Button>
            </div>

            {enhancedData.dependencies.map((dependency, index) => (
              <div key={index} className="bg-white border rounded-lg p-4 mb-3">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      وصف التبعية
                    </label>
                    <Input
                      value={dependency.description}
                      onChange={(e) => updateRaidItem('dependencies', index, 'description', e.target.value)}
                      placeholder="اكتب وصف التبعية..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      المسؤول
                    </label>
                    <Input
                      value={dependency.responsible}
                      onChange={(e) => updateRaidItem('dependencies', index, 'responsible', e.target.value)}
                      placeholder="من المسؤول؟"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الموعد المطلوب
                    </label>
                    <Input
                      type="date"
                      value={dependency.deadline}
                      onChange={(e) => updateRaidItem('dependencies', index, 'deadline', e.target.value)}
                    />
                  </div>
                </div>

                <div className="flex justify-end mt-4">
                  <Button 
                    onClick={() => removeRaidItem('dependencies', index)}
                    variant="outline" 
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    حذف
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const renderSuggestionFinalize = () => {
    const suggestionData = data as SuggestionData

    return (
      <div className="space-y-6">
        {/* التوصيات */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Lightbulb className="w-4 h-4 text-gray-500" />
            <label className="block text-sm font-medium text-gray-700">
              التوصيات النهائية *
            </label>
            <FieldHelp 
              content="اكتب توصياتك النهائية بناءً على الحلول المقترحة"
              field="recommendations"
              step={7}
            />
          </div>
          <Textarea
            value={suggestionData.recommendations}
            onChange={(e) => updateData('recommendations', e.target.value)}
            placeholder="بناءً على التحليل والحلول المقترحة، أوصي بـ..."
            rows={4}
            error={errors.recommendations}
            maxLength={1000}
          />
          <div className="text-xs text-gray-500 mt-1">
            {suggestionData.recommendations.length}/1000 حرف
          </div>
        </div>

        {/* الخطوات التالية */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Target className="w-4 h-4 text-gray-500" />
            <label className="block text-sm font-medium text-gray-700">
              الخطوات التالية المقترحة *
            </label>
            <FieldHelp 
              content="حدد الخطوات العملية التي يجب اتخاذها بعد قبول المقترحات"
              field="nextSteps"
              step={7}
            />
          </div>

          <div className="space-y-3">
            {suggestionData.nextSteps.map((step, index) => (
              <div key={index} className="flex gap-2">
                <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center text-sm font-medium text-indigo-600">
                  {index + 1}
                </div>
                <Input
                  value={step}
                  onChange={(e) => updateNextStep(index, e.target.value)}
                  placeholder={`الخطوة ${index + 1}`}
                  className="flex-1"
                />
                <Button
                  onClick={() => removeNextStep(index)}
                  variant="outline"
                  size="sm"
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            ))}

            <Button 
              onClick={addNextStep}
              variant="outline" 
              size="sm" 
              className="w-full"
            >
              <Plus className="w-4 h-4 mr-2" />
              إضافة خطوة
            </Button>

            {suggestionData.nextSteps.length === 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <p className="text-yellow-800 text-sm">
                  أضف على الأقل خطوة واحدة من الخطوات التالية المقترحة
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  const renderQuickWinFinalize = () => {
    return (
      <div className="space-y-6">
        {/* ملخص المراجعة */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-4">
            <CheckCircle className="w-5 h-5 text-green-600" />
            <h4 className="font-semibold text-green-900">مراجعة الحل السريع</h4>
          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white border rounded-lg p-3">
                <h5 className="font-medium text-gray-900 mb-2">المشكلة المحددة</h5>
                <p className="text-sm text-gray-700">{data.problemDescription || 'لم يتم تحديد المشكلة'}</p>
              </div>

              <div className="bg-white border rounded-lg p-3">
                <h5 className="font-medium text-gray-900 mb-2">المؤشر المستهدف</h5>
                <p className="text-sm text-gray-700">
                  {data.indicatorName}: {data.currentValue} → {data.targetValue} {data.unit}
                </p>
              </div>
            </div>

            <div className="bg-white border rounded-lg p-3">
              <h5 className="font-medium text-gray-900 mb-2">الحل المقترح</h5>
              <p className="text-sm text-gray-700">
                {formType === 'quick_win' && (data as any).solution?.description || 'لم يتم تحديد الحل'}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white border rounded-lg p-3">
                <h5 className="font-medium text-gray-900 mb-2">مدة التنفيذ</h5>
                <p className="text-sm text-gray-700">
                  {formType === 'quick_win' && (data as any).solution?.implementationWeeks || 0} أسابيع
                </p>
              </div>

              <div className="bg-white border rounded-lg p-3">
                <h5 className="font-medium text-gray-900 mb-2">التكلفة المقدرة</h5>
                <p className="text-sm text-gray-700">
                  {formType === 'quick_win' && (data as any).solution?.estimatedCost?.toLocaleString() || 0} ريال
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* تأكيد الجاهزية */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-semibold text-blue-900 mb-3">تأكيد الجاهزية للتنفيذ</h4>
          <div className="space-y-2">
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="rounded border-gray-300" />
              <span className="text-sm text-gray-700">تم تحديد المشكلة بوضوح</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="rounded border-gray-300" />
              <span className="text-sm text-gray-700">الحل قابل للتنفيذ في المدة المحددة</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="rounded border-gray-300" />
              <span className="text-sm text-gray-700">الفريق جاهز للبدء</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="rounded border-gray-300" />
              <span className="text-sm text-gray-700">الموارد متوفرة</span>
            </label>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className={`${colors.bg} border ${colors.border} rounded-lg p-4 mb-6`}>
        <div className="flex items-center gap-2 mb-2">
          <FileCheck className={`w-5 h-5 ${colors.icon}`} />
          <h3 className={`font-semibold ${colors.text}`}>{getStepTitle()}</h3>
        </div>
        <p className={`${colors.text} text-sm`}>
          {getStepDescription()}
        </p>
      </div>

      {formType === 'enhanced_improvement' && renderEnhancedImprovementFinalize()}
      {formType === 'suggestion' && renderSuggestionFinalize()}
      {formType === 'quick_win' && renderQuickWinFinalize()}
    </div>
  )
} 