import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'
import { createHash } from 'crypto'

// استخدام admin client للتجاوز RLS
const supabase = supabaseAdmin

// واجهة بيانات المستخدم
interface UserData {
  email: string
  name: string
  department_id: string
  role_id: string
  phone?: string
  password?: string
  is_active?: boolean
}

// POST - إنشاء مستخدم جديد
export async function POST(request: NextRequest) {
  try {
    const body: UserData = await request.json()
    
    // التحقق من صحة البيانات
    if (!body.email || !body.name || !body.department_id || !body.role_id) {
      return NextResponse.json(
        { error: 'البريد الإلكتروني والاسم والقسم والدور مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من صحة تنسيق البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { error: 'تنسيق البريد الإلكتروني غير صحيح' },
        { status: 400 }
      )
    }

    // التحقق من عدم وجود مستخدم بنفس البريد الإلكتروني
    const { data: existingUser } = await supabase
      .from('users')
      .select('id')
      .eq('email', body.email.toLowerCase())
      .single()

    if (existingUser) {
      return NextResponse.json(
        { error: 'يوجد مستخدم بنفس البريد الإلكتروني' },
        { status: 409 }
      )
    }

    // التحقق من وجود القسم
    const { data: department, error: deptError } = await supabase
      .from('departments')
      .select('id, name')
      .eq('id', body.department_id)
      .single()

    if (deptError || !department) {
      return NextResponse.json(
        { error: 'القسم المحدد غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من وجود الدور
    const { data: role, error: roleError } = await supabase
      .from('roles')
      .select('id, name, display_name')
      .eq('id', body.role_id)
      .single()

    if (roleError || !role) {
      return NextResponse.json(
        { error: 'الدور المحدد غير موجود' },
        { status: 404 }
      )
    }

    // إنشاء المستخدم (بدون كلمة مرور لأن الجدول لا يحتوي على password_hash)
    const { data, error } = await supabase
      .from('users')
      .insert({
        email: body.email.toLowerCase(),
        name: body.name,
        department_id: body.department_id,
        role_id: body.role_id,
        phone: body.phone,
        is_active: body.is_active !== undefined ? body.is_active : true
      })
      .select('*')
      .single()

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في إنشاء المستخدم' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء المستخدم بنجاح',
      data
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// GET - استرجاع المستخدمين
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const departmentId = searchParams.get('department_id')
    const roleId = searchParams.get('role_id')
    const isActive = searchParams.get('is_active')
    const search = searchParams.get('search')
    const includeHierarchy = searchParams.get('include_hierarchy') === 'true'

    let query = supabase
      .from('users')
      .select(`
        *,
        department:departments!department_id(id, name),
        role:roles!role_id(id, name, display_name, permissions)
      `)

    // تطبيق الفلاتر
    if (departmentId) {
      if (includeHierarchy) {
        // الحصول على جميع الأقسام الفرعية
        const childDepartments = await getDepartmentHierarchy(departmentId)
        const allDepartmentIds = [departmentId, ...childDepartments.map(d => d.id)]
        query = query.in('department_id', allDepartmentIds)
      } else {
        query = query.eq('department_id', departmentId)
      }
    }

    if (roleId) {
      query = query.eq('role_id', roleId)
    }

    if (isActive !== null) {
      query = query.eq('is_active', isActive === 'true')
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%`)
    }

    // ترتيب النتائج
    query = query.order('name')

    const { data, error } = await query

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في استرجاع المستخدمين' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: data || []
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// PUT - تحديث مستخدم
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, password, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        { error: 'معرف المستخدم مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود المستخدم
    const { data: existingUser, error: existingError } = await supabase
      .from('users')
      .select('id, email')
      .eq('id', id)
      .single()

    if (existingError || !existingUser) {
      return NextResponse.json(
        { error: 'المستخدم غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من البريد الإلكتروني إذا تم تحديثه
    if (updateData.email && updateData.email !== existingUser.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(updateData.email)) {
        return NextResponse.json(
          { error: 'تنسيق البريد الإلكتروني غير صحيح' },
          { status: 400 }
        )
      }

      // التحقق من عدم وجود مستخدم آخر بنفس البريد الإلكتروني
      const { data: duplicateUser } = await supabase
        .from('users')
        .select('id')
        .eq('email', updateData.email.toLowerCase())
        .neq('id', id)
        .single()

      if (duplicateUser) {
        return NextResponse.json(
          { error: 'يوجد مستخدم آخر بنفس البريد الإلكتروني' },
          { status: 409 }
        )
      }

      updateData.email = updateData.email.toLowerCase()
    }

    // تشفير كلمة المرور الجديدة إذا تم تحديثها
    if (password) {
      updateData.password_hash = createHash('sha256').update(password).digest('hex')
    }

    // تحديث المستخدم
    const { data, error } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        department:departments!department_id(id, name),
        role:roles!role_id(id, name, display_name, permissions)
      `)
      .single()

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في تحديث المستخدم' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث المستخدم بنجاح',
      data
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// DELETE - حذف مستخدم (تعطيل بدلاً من الحذف)
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    const permanent = searchParams.get('permanent') === 'true'

    if (!id) {
      return NextResponse.json(
        { error: 'معرف المستخدم مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود المستخدم
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, name, email')
      .eq('id', id)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'المستخدم غير موجود' },
        { status: 404 }
      )
    }

    if (permanent) {
      // حذف نهائي (يجب التأكد من عدم وجود مراجع)
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Database error:', error)
        return NextResponse.json(
          { error: 'حدث خطأ في حذف المستخدم' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'تم حذف المستخدم نهائياً'
      })
    } else {
      // تعطيل المستخدم
      const { data, error } = await supabase
        .from('users')
        .update({ is_active: false })
        .eq('id', id)
        .select(`
          *,
          department:departments!department_id(id, name),
          role:roles!role_id(id, name, display_name)
        `)
        .single()

      if (error) {
        console.error('Database error:', error)
        return NextResponse.json(
          { error: 'حدث خطأ في تعطيل المستخدم' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'تم تعطيل المستخدم بنجاح',
        data
      })
    }

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// دالة الحصول على الهيكل الهرمي للأقسام
async function getDepartmentHierarchy(departmentId: string): Promise<any[]> {
  const allDepartments: any[] = []
  
  const getChildren = async (parentId: string) => {
    const { data: children } = await supabase
      .from('departments')
      .select('id, name, parent_id')
      .eq('parent_id', parentId)

    if (children && children.length > 0) {
      allDepartments.push(...children)
      
      // البحث عن الأقسام الفرعية لكل قسم
      for (const child of children) {
        await getChildren(child.id)
      }
    }
  }

  await getChildren(departmentId)
  return allDepartments
} 