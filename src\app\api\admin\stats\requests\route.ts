import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'

export async function GET(request: NextRequest) {
  try {
    // جلب إحصائيات الطلبات
    const { data: requests, error: requestsError } = await supabaseAdmin
      .from('project_requests')
      .select('id, status, created_at')

    if (requestsError) {
      console.error('Error fetching requests:', requestsError)
      return NextResponse.json(
        { success: false, error: 'فشل في جلب إحصائيات الطلبات' },
        { status: 500 }
      )
    }

    // حساب الإحصائيات
    const total = requests?.length || 0
    const pending = requests?.filter(req => req.status === 'pending').length || 0
    const approved = requests?.filter(req => req.status === 'approved').length || 0
    const rejected = requests?.filter(req => req.status === 'rejected').length || 0

    // حساب الطلبات هذا الشهر
    const currentMonth = new Date()
    currentMonth.setDate(1)
    currentMonth.setHours(0, 0, 0, 0)
    
    const thisMonth = requests?.filter(req => {
      if (!req.created_at) return false
      const createdAt = new Date(req.created_at)
      return createdAt >= currentMonth
    }).length || 0

    // حساب معدل النمو (مقارنة بالشهر الماضي)
    const lastMonth = new Date()
    lastMonth.setMonth(lastMonth.getMonth() - 1)
    lastMonth.setDate(1)
    lastMonth.setHours(0, 0, 0, 0)
    
    const lastMonthEnd = new Date(currentMonth)
    lastMonthEnd.setSeconds(-1)
    
    const lastMonthCount = requests?.filter(req => {
      if (!req.created_at) return false
      const createdAt = new Date(req.created_at)
      return createdAt >= lastMonth && createdAt <= lastMonthEnd
    }).length || 0

    const growthRate = lastMonthCount > 0 ? ((thisMonth - lastMonthCount) / lastMonthCount) * 100 : 0

    const stats = {
      total,
      pending,
      approved,
      rejected,
      this_month: thisMonth,
      growth_rate: Math.round(growthRate * 10) / 10
    }

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('Error in requests stats API:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في الخادم' },
      { status: 500 }
    )
  }
} 