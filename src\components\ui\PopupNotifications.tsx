'use client';

import React, { createContext, useContext, useCallback, useState, useEffect } from 'react';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle, Clock } from 'lucide-react';

// أنواع الإشعارات المنبثقة
export type PopupNotificationType = 'success' | 'error' | 'warning' | 'info' | 'loading';

// بيانات الإشعار المنبثق
export interface PopupNotificationData {
  id: string;
  type: PopupNotificationType;
  title: string;
  message?: string;
  duration?: number;
  persistent?: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// سياق الإشعارات المنبثقة
interface PopupNotificationContextType {
  notifications: PopupNotificationData[];
  addNotification: (notification: Omit<PopupNotificationData, 'id'>) => string;
  removeNotification: (id: string) => void;
  updateNotification: (id: string, updates: Partial<PopupNotificationData>) => void;
  clearAllNotifications: () => void;
}

const PopupNotificationContext = createContext<PopupNotificationContextType | undefined>(undefined);

// خطاف لاستخدام الإشعارات المنبثقة
export const usePopupNotifications = () => {
  const context = useContext(PopupNotificationContext);
  if (!context) {
    throw new Error('usePopupNotifications must be used within PopupNotificationProvider');
  }
  return context;
};

// مكون الإشعار المنبثق الواحد
const PopupNotification: React.FC<{ 
  notification: PopupNotificationData; 
  onRemove: (id: string) => void; 
}> = ({ notification, onRemove }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    // إظهار الإشعار
    const showTimer = setTimeout(() => setIsVisible(true), 50);
    
    // إخفاء الإشعار تلقائياً إذا لم يكن دائماً
    let hideTimer: NodeJS.Timeout;
    if (!notification.persistent) {
      hideTimer = setTimeout(() => {
        setIsExiting(true);
        setTimeout(() => onRemove(notification.id), 300);
      }, notification.duration || 5000);
    }

    return () => {
      clearTimeout(showTimer);
      if (hideTimer) clearTimeout(hideTimer);
    };
  }, [notification.id, notification.duration, notification.persistent, onRemove]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => onRemove(notification.id), 300);
  };

  // أيقونات الإشعارات
  const getIcon = () => {
    switch (notification.type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'info':
        return <Info className="w-5 h-5 text-blue-500" />;
      case 'loading':
        return <Clock className="w-5 h-5 text-blue-500 animate-spin" />;
      default:
        return <Info className="w-5 h-5 text-blue-500" />;
    }
  };

  // ألوان الإشعارات
  const getNotificationStyles = () => {
    const baseStyles = "relative flex items-start gap-3 p-4 rounded-lg shadow-lg border backdrop-blur-sm transition-all duration-300 ease-in-out";
    
    switch (notification.type) {
      case 'success':
        return `${baseStyles} bg-green-50/95 border-green-200 text-green-800`;
      case 'error':
        return `${baseStyles} bg-red-50/95 border-red-200 text-red-800`;
      case 'warning':
        return `${baseStyles} bg-yellow-50/95 border-yellow-200 text-yellow-800`;
      case 'info':
        return `${baseStyles} bg-blue-50/95 border-blue-200 text-blue-800`;
      case 'loading':
        return `${baseStyles} bg-blue-50/95 border-blue-200 text-blue-800`;
      default:
        return `${baseStyles} bg-gray-50/95 border-gray-200 text-gray-800`;
    }
  };

  return (
    <div
      className={`
        ${getNotificationStyles()}
        ${isVisible ? 'transform translate-x-0 opacity-100 scale-100' : 'transform translate-x-full opacity-0 scale-95'}
        ${isExiting ? 'transform translate-x-full opacity-0 scale-95' : ''}
        max-w-sm w-full
      `}
    >
      {/* أيقونة الإشعار */}
      <div className="flex-shrink-0 mt-0.5">
        {getIcon()}
      </div>

      {/* محتوى الإشعار */}
      <div className="flex-1 min-w-0">
        <h4 className="font-semibold text-sm leading-5">
          {notification.title}
        </h4>
        {notification.message && (
          <p className="mt-1 text-sm opacity-90 leading-5">
            {notification.message}
          </p>
        )}
        
        {/* زر الإجراء */}
        {notification.action && (
          <div className="mt-3">
            <button
              onClick={notification.action.onClick}
              className="text-sm font-medium underline hover:no-underline focus:outline-none"
            >
              {notification.action.label}
            </button>
          </div>
        )}
      </div>

      {/* زر الإغلاق */}
      {notification.type !== 'loading' && (
        <button
          onClick={handleClose}
          className="flex-shrink-0 p-1 rounded-md hover:bg-black/10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current transition-colors"
          aria-label="إغلاق الإشعار"
        >
          <X className="w-4 h-4" />
        </button>
      )}
    </div>
  );
};

// مكون حاوي الإشعارات المنبثقة
export const PopupNotifications: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<PopupNotificationData[]>([]);

  const addNotification = useCallback((notification: Omit<PopupNotificationData, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    setNotifications(prev => [...prev, { ...notification, id }]);
    return id;
  }, []);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  const updateNotification = useCallback((id: string, updates: Partial<PopupNotificationData>) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id ? { ...notification, ...updates } : notification
      )
    );
  }, []);

  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  return (
    <PopupNotificationContext.Provider value={{ 
      notifications, 
      addNotification, 
      removeNotification, 
      updateNotification, 
      clearAllNotifications 
    }}>
      {children}
      
      {/* حاوي الإشعارات المنبثقة */}
      <div className="fixed top-4 left-4 z-50 space-y-2 pointer-events-none">
        <div className="flex flex-col gap-2 pointer-events-auto">
          {notifications.map(notification => (
            <PopupNotification
              key={notification.id}
              notification={notification}
              onRemove={removeNotification}
            />
          ))}
        </div>
      </div>
    </PopupNotificationContext.Provider>
  );
}; 