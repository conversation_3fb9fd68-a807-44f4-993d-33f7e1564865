'use client'

import { useState, useEffect } from 'react'
import { Card } from '../ui/Card'
import { Button } from '../ui/Button'
import { Typography } from '../ui/Typography'
import { supabase } from '../../lib/supabase'

interface SecurityMetrics {
  totalUsers: number
  activeUsers: number
  suspiciousActivity: number
  recentLogins: number
  failedAttempts: number
  activeSessions: number
}

interface LoginAttempt {
  id: string
  email: string
  success: boolean
  attempted_at: string
  failure_reason?: string
}

interface UserSession {
  id: string
  user_id: string
  login_at: string
  is_active: boolean
  ip_address?: string
}

export default function SecurityDashboard() {
  const [metrics, setMetrics] = useState<SecurityMetrics>({
    totalUsers: 0,
    activeUsers: 0,
    suspiciousActivity: 0,
    recentLogins: 0,
    failedAttempts: 0,
    activeSessions: 0
  })
  
  const [loginAttempts, setLoginAttempts] = useState<LoginAttempt[]>([])
  const [userSessions, setUserSessions] = useState<UserSession[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    loadSecurityData()
    
    // تحديث البيانات كل دقيقة
    const interval = setInterval(loadSecurityData, 60000)
    return () => clearInterval(interval)
  }, [])

  const loadSecurityData = async () => {
    try {
      setRefreshing(true)
      
      // تحميل إحصائيات المستخدمين
      const { data: users } = await supabase
        .from('users')
        .select('id, is_active, created_at')
      
      // تحميل محاولات تسجيل الدخول الأخيرة
      const { data: attempts } = await supabase
        .from('login_attempts')
        .select('*')
        .order('attempted_at', { ascending: false })
        .limit(20)
      
      // تحميل الجلسات النشطة
      const { data: sessions } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('is_active', true)
        .order('login_at', { ascending: false })
        .limit(20)
      
      // حساب الإحصائيات
      const totalUsers = users?.length || 0
      const activeUsers = users?.filter(u => u.is_active)?.length || 0
      const recentLogins = attempts?.filter(a => a.success)?.length || 0
      const failedAttempts = attempts?.filter(a => !a.success)?.length || 0
      const activeSessions = sessions?.length || 0
      
      // تحديد النشاط المشبوه (أكثر من 5 محاولات فاشلة في آخر ساعة)
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString()
      const recentFailures = attempts?.filter(a => 
        !a.success && a.attempted_at > oneHourAgo
      )?.length || 0
      
      setMetrics({
        totalUsers,
        activeUsers,
        suspiciousActivity: recentFailures > 5 ? 1 : 0,
        recentLogins,
        failedAttempts,
        activeSessions
      })
      
      setLoginAttempts(attempts || [])
      setUserSessions(sessions || [])
    } catch (error) {
      console.error('Error loading security data:', error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const terminateSession = async (sessionId: string) => {
    try {
      const { error } = await supabase
        .from('user_sessions')
        .update({ is_active: false, logout_at: new Date().toISOString() })
        .eq('id', sessionId)
      
      if (error) {
        console.error('Error terminating session:', error)
        return
      }
      
      // إعادة تحميل البيانات
      await loadSecurityData()
    } catch (error) {
      console.error('Error terminating session:', error)
    }
  }

  const cleanupExpiredSessions = async () => {
    try {
      const { error } = await supabase.rpc('cleanup_expired_sessions')
      
      if (error) {
        console.error('Error cleaning up sessions:', error)
        return
      }
      
      // إعادة تحميل البيانات
      await loadSecurityData()
    } catch (error) {
      console.error('Error cleaning up sessions:', error)
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* العنوان */}
      <div className="flex justify-between items-center">
        <Typography variant="main" className="text-2xl font-bold text-gray-900">
          لوحة الأمان والمراقبة
        </Typography>
        <Button
          onClick={loadSecurityData}
          disabled={refreshing}
          className="flex items-center gap-2"
        >
          {refreshing ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          ) : (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          )}
          تحديث
        </Button>
      </div>

      {/* الإحصائيات الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="body" className="text-sm font-medium text-gray-600">
                إجمالي المستخدمين
              </Typography>
              <Typography variant="main" className="text-2xl font-bold text-gray-900">
                {metrics.totalUsers}
              </Typography>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="body" className="text-sm font-medium text-gray-600">
                المستخدمون النشطون
              </Typography>
              <Typography variant="main" className="text-2xl font-bold text-green-600">
                {metrics.activeUsers}
              </Typography>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="body" className="text-sm font-medium text-gray-600">
                النشاط المشبوه
              </Typography>
              <Typography variant="main" className={`text-2xl font-bold ${metrics.suspiciousActivity > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {metrics.suspiciousActivity}
              </Typography>
            </div>
            <div className={`p-3 rounded-full ${metrics.suspiciousActivity > 0 ? 'bg-red-100' : 'bg-green-100'}`}>
              <svg className={`w-6 h-6 ${metrics.suspiciousActivity > 0 ? 'text-red-600' : 'text-green-600'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="body" className="text-sm font-medium text-gray-600">
                تسجيلات الدخول الأخيرة
              </Typography>
              <Typography variant="main" className="text-2xl font-bold text-blue-600">
                {metrics.recentLogins}
              </Typography>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
              </svg>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="body" className="text-sm font-medium text-gray-600">
                المحاولات الفاشلة
              </Typography>
              <Typography variant="main" className="text-2xl font-bold text-red-600">
                {metrics.failedAttempts}
              </Typography>
            </div>
            <div className="p-3 bg-red-100 rounded-full">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="body" className="text-sm font-medium text-gray-600">
                الجلسات النشطة
              </Typography>
              <Typography variant="main" className="text-2xl font-bold text-green-600">
                {metrics.activeSessions}
              </Typography>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
        </Card>
      </div>

      {/* محاولات تسجيل الدخول الأخيرة */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="main" className="text-lg font-semibold">
            محاولات تسجيل الدخول الأخيرة
          </Typography>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  البريد الإلكتروني
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الوقت
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  سبب الفشل
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loginAttempts.slice(0, 10).map((attempt) => (
                <tr key={attempt.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {attempt.email}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      attempt.success 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {attempt.success ? 'نجح' : 'فشل'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(attempt.attempted_at).toLocaleString('ar-SA')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {attempt.failure_reason || '-'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* الجلسات النشطة */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="main" className="text-lg font-semibold">
            الجلسات النشطة
          </Typography>
          <Button
            onClick={cleanupExpiredSessions}
            variant="default"
            className="text-sm"
          >
            تنظيف الجلسات المنتهية
          </Button>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المستخدم
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  وقت تسجيل الدخول
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  عنوان IP
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {userSessions.slice(0, 10).map((session) => (
                <tr key={session.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {session.user_id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(session.login_at).toLocaleString('ar-SA')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {session.ip_address || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <Button
                      onClick={() => terminateSession(session.id)}
                      variant="default"
                      className="text-red-600 hover:text-red-900 text-sm"
                    >
                      إنهاء الجلسة
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  )
} 