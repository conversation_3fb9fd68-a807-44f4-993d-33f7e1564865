'use client'

import React, { useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { UnifiedProjectForm, FormType, UnifiedFormData } from '@/components/forms/unified/UnifiedProjectForm'
import { 
  Zap, 
  Target, 
  Lightbulb,
  ArrowRight,
  CheckCircle
} from 'lucide-react'

export default function UnifiedTestPage() {
  const [selectedType, setSelectedType] = useState<FormType | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [submittedData, setSubmittedData] = useState<UnifiedFormData | null>(null)

  const formTypes = [
    {
      id: 'enhanced_improvement' as FormType,
      title: 'مشروع التحسين الشامل',
      description: 'للمشاريع الكبيرة والمعقدة التي تحتاج تحليل شامل',
      icon: <Target className="w-8 h-8 text-blue-600" />,
      color: 'border-blue-200 bg-blue-50',
      features: [
        '9 مراحل شاملة',
        'تخطيط مفصل للمشروع',
        'إدارة المخاطر RAID Matrix',
        'مراجعة نهائية شاملة'
      ]
    },
    {
      id: 'suggestion' as FormType,
      title: 'مقترح تحسيني',
      description: 'لاقتراح حلول متعددة دون التزام بالتنفيذ',
      icon: <Lightbulb className="w-8 h-8 text-purple-600" />,
      color: 'border-purple-200 bg-purple-50',
      features: [
        '7 مراحل مبسطة',
        'حلول متعددة مقترحة',
        'تقييم الجدوى فقط',
        'توصيات نهائية'
      ]
    },
    {
      id: 'quick_win' as FormType,
      title: 'كويك وين',
      description: 'للتحسينات السريعة (حد أقصى 4 أسابيع)',
      icon: <Zap className="w-8 h-8 text-orange-600" />,
      color: 'border-orange-200 bg-orange-50',
      features: [
        'نموذج موحد مبسط',
        'حل واحد سريع',
        'مؤشرات متقدمة',
        'تنفيذ فوري'
      ]
    }
  ]

  const handleSubmit = async (data: UnifiedFormData) => {
    setIsLoading(true)
    
    // محاكاة إرسال البيانات
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    setSubmittedData(data)
    setIsLoading(false)
  }

  const handleSaveDraft = async (data: UnifiedFormData) => {
    // حفظ المسودة في localStorage للاختبار
    localStorage.setItem('test_draft', JSON.stringify(data))
    alert('تم حفظ المسودة في localStorage!')
  }

  const resetTest = () => {
    setSelectedType(null)
    setSubmittedData(null)
  }

  if (submittedData) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-4xl mx-auto">
          <Card className="p-8 text-center">
            <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              تم إرسال النموذج بنجاح!
            </h1>
            <p className="text-gray-600 mb-6">
              تم استلام {formTypes.find(t => t.id === selectedType)?.title} وسيتم مراجعته قريباً
            </p>
            
            <div className="bg-gray-50 border rounded-lg p-4 mb-6 text-right">
              <h3 className="font-semibold text-gray-900 mb-2">ملخص البيانات المرسلة:</h3>
              <pre className="text-sm text-gray-700 overflow-auto max-h-96">
                {JSON.stringify(submittedData, null, 2)}
              </pre>
            </div>

            <Button onClick={resetTest} className="bg-blue-600 hover:bg-blue-700">
              اختبار نموذج آخر
            </Button>
          </Card>
        </div>
      </div>
    )
  }

  if (selectedType) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center gap-4 mb-6">
            <Button
              onClick={() => setSelectedType(null)}
              variant="secondary"
            >
              ← العودة للاختيار
            </Button>
            <h1 className="text-2xl font-bold text-gray-900">
              اختبار: {formTypes.find(t => t.id === selectedType)?.title}
            </h1>
          </div>
          
          <UnifiedProjectForm
            formType={selectedType}
            onSubmit={handleSubmit}
            onSaveDraft={handleSaveDraft}
            isLoading={isLoading}
          />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            اختبار النموذج الموحد
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            اختر نوع النموذج الذي تريد اختباره. تم تحديث جميع النماذج لتعمل بنظام موحد مع مراحل محسنة
          </p>
        </div>

        {/* المقارنة السريعة */}
        <div className="bg-white border rounded-lg p-6 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">مقارنة سريعة</h2>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-right p-3 border">النموذج</th>
                  <th className="text-center p-3 border">عدد المراحل</th>
                  <th className="text-center p-3 border">مدة التعبئة</th>
                  <th className="text-center p-3 border">التعقيد</th>
                  <th className="text-right p-3 border">الهدف</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="p-3 border font-medium">التحسين الشامل</td>
                  <td className="p-3 border text-center">9 مراحل</td>
                  <td className="p-3 border text-center">30-45 دقيقة</td>
                  <td className="p-3 border text-center">
                    <span className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">معقد</span>
                  </td>
                  <td className="p-3 border">مشاريع كبيرة ومعقدة</td>
                </tr>
                <tr>
                  <td className="p-3 border font-medium">المقترحات</td>
                  <td className="p-3 border text-center">7 مراحل</td>
                  <td className="p-3 border text-center">20-30 دقيقة</td>
                  <td className="p-3 border text-center">
                    <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">متوسط</span>
                  </td>
                  <td className="p-3 border">اقتراح حلول متعددة</td>
                </tr>
                <tr>
                  <td className="p-3 border font-medium">كويك وين</td>
                  <td className="p-3 border text-center">مرحلة واحدة</td>
                  <td className="p-3 border text-center">10-15 دقيقة</td>
                  <td className="p-3 border text-center">
                    <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">بسيط</span>
                  </td>
                  <td className="p-3 border">تحسينات سريعة</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* اختيار النموذج */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {formTypes.map((formType) => (
            <Card key={formType.id} className={`p-6 cursor-pointer transition-all hover:shadow-lg ${formType.color}`}>
              <div className="text-center mb-4">
                {formType.icon}
                <h3 className="text-xl font-bold text-gray-900 mt-2">
                  {formType.title}
                </h3>
                <p className="text-gray-600 mt-2">
                  {formType.description}
                </p>
              </div>

              <div className="space-y-2 mb-6">
                {formType.features.map((feature, index) => (
                  <div key={index} className="flex items-center text-sm text-gray-700">
                    <CheckCircle className="w-4 h-4 text-green-600 ml-2 flex-shrink-0" />
                    {feature}
                  </div>
                ))}
              </div>

              <Button
                onClick={() => setSelectedType(formType.id)}
                className="w-full"
                variant="primary"
              >
                اختبار هذا النموذج
                <ArrowRight className="w-4 h-4 mr-2" />
              </Button>
            </Card>
          ))}
        </div>

        {/* معلومات إضافية */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">
            ✨ التحسينات الجديدة في النموذج الموحد
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
            <div>
              <h4 className="font-medium mb-2">التحسين الشامل:</h4>
              <ul className="space-y-1 mr-4">
                <li>• إزالة التكرار بين المراحل</li>
                <li>• مرحلة تخطيط منفصلة</li>
                <li>• إدارة مخاطر كاملة (RAID Matrix)</li>
                <li>• مراجعة نهائية شاملة</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">المقترحات وكويك وين:</h4>
              <ul className="space-y-1 mr-4">
                <li>• إزالة الفوائد المتوقعة من الحلول</li>
                <li>• تحسين مؤشر الجدوى (slider)</li>
                <li>• كويك وين مبسط في صفحة واحدة</li>
                <li>• مؤشرات متقدمة اختيارية</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 