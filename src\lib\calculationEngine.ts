// محرك الحسابات التلقائية لمشاريع التحسين
export interface CalculationResult {
  gap: number
  gapPercentage: number
  improvementPotential: number
  priority: 'low' | 'medium' | 'high' | 'urgent'
  estimatedImpact: string
  recommendations: string[]
}

export interface ROICalculation {
  estimatedSavings: number
  implementationCost: number
  roi: number
  paybackPeriod: number
  npv: number
}

export class CalculationEngine {
  // حساب الفجوة والمؤشرات الأساسية
  static calculateGap(
    currentValue: number, 
    targetValue: number, 
    unit: string, 
    improvementDirection: 'increase' | 'decrease' = 'increase'
  ): CalculationResult {
    const gap = Math.abs(targetValue - currentValue)
    const gapPercentage = currentValue > 0 ? (gap / currentValue) * 100 : 0
    
    // التحقق من صحة الاتجاه
    const isValidDirection = improvementDirection === 'increase' 
      ? targetValue > currentValue 
      : targetValue < currentValue
    
    if (!isValidDirection) {
      console.warn('تحذير: اتجاه التحسن لا يتطابق مع القيم المدخلة')
    }
    
    // تحديد إمكانية التحسين
    const improvementPotential = this.calculateImprovementPotential(currentValue, targetValue, unit)
    
    // تحديد الأولوية
    const priority = this.determinePriority(gap, gapPercentage, unit)
    
    // تقدير التأثير
    const estimatedImpact = this.estimateImpact(gap, gapPercentage, unit)
    
    // توصيات
    const recommendations = this.generateRecommendations(gap, gapPercentage, unit, priority)
    
    return {
      gap,
      gapPercentage,
      improvementPotential,
      priority,
      estimatedImpact,
      recommendations
    }
  }

  // حساب إمكانية التحسين
  private static calculateImprovementPotential(current: number, target: number, unit: string): number {
    const unitMultipliers: Record<string, number> = {
      'ساعة': 1,
      'يوم': 24,
      'ريال سعودي': 0.1,
      'نسبة مئوية (%)': 1,
      'عدد': 1,
      'كيلوجرام': 1,
      'متر': 1,
      'لتر': 1,
      'درجة': 1,
      'نقطة': 10
    }
    
    const multiplier = unitMultipliers[unit] || 1
    const normalizedGap = Math.abs(target - current) * multiplier
    
    // تحديد إمكانية التحسين كنسبة مئوية
    if (normalizedGap > 100) return 95
    if (normalizedGap > 50) return 80
    if (normalizedGap > 20) return 65
    if (normalizedGap > 10) return 50
    if (normalizedGap > 5) return 35
    return 20
  }

  // تحديد الأولوية
  private static determinePriority(gap: number, gapPercentage: number, unit: string): 'low' | 'medium' | 'high' | 'urgent' {
    // قواعد خاصة بكل وحدة
    if (unit === 'ساعة') {
      if (gap > 48) return 'urgent'
      if (gap > 24) return 'high'
      if (gap > 8) return 'medium'
      return 'low'
    }
    
    if (unit === 'ريال سعودي') {
      if (gap > 100000) return 'urgent'
      if (gap > 50000) return 'high'
      if (gap > 10000) return 'medium'
      return 'low'
    }
    
    if (unit === 'نسبة مئوية (%)') {
      if (gap > 30) return 'urgent'
      if (gap > 15) return 'high'
      if (gap > 5) return 'medium'
      return 'low'
    }
    
    // قواعد عامة بناءً على النسبة المئوية
    if (gapPercentage > 50) return 'urgent'
    if (gapPercentage > 25) return 'high'
    if (gapPercentage > 10) return 'medium'
    return 'low'
  }

  // تقدير التأثير
  private static estimateImpact(gap: number, gapPercentage: number, unit: string): string {
    const priority = this.determinePriority(gap, gapPercentage, unit)
    
    const impactMessages = {
      urgent: 'تأثير كبير جداً - يتطلب تدخل فوري',
      high: 'تأثير كبير - يحتاج أولوية عالية',
      medium: 'تأثير متوسط - يمكن التعامل معه ضمن الخطة',
      low: 'تأثير منخفض - يمكن تأجيله'
    }
    
    return impactMessages[priority]
  }

  // توليد التوصيات
  private static generateRecommendations(gap: number, gapPercentage: number, unit: string, priority: 'low' | 'medium' | 'high' | 'urgent'): string[] {
    const recommendations: string[] = []
    
    // توصيات عامة
    if (priority === 'urgent') {
      recommendations.push('تشكيل فريق طوارئ للتعامل مع المشكلة')
      recommendations.push('تخصيص موارد إضافية للمشروع')
      recommendations.push('تطبيق حلول مؤقتة سريعة')
    }
    
    if (priority === 'high') {
      recommendations.push('إشراك الإدارة العليا في المشروع')
      recommendations.push('تسريع جدولة المشروع')
      recommendations.push('تخصيص ميزانية كافية')
    }
    
    // توصيات خاصة بالوحدة
    if (unit === 'ساعة') {
      recommendations.push('تحليل نقاط الاختناق في العملية')
      recommendations.push('تطبيق تقنيات تحسين الوقت')
      recommendations.push('أتمتة الخطوات الروتينية')
    }
    
    if (unit === 'ريال سعودي') {
      recommendations.push('تحليل التكلفة والفائدة')
      recommendations.push('البحث عن مصادر توفير إضافية')
      recommendations.push('تطبيق مبادئ الاقتصاد في الموارد')
    }
    
    if (unit === 'نسبة مئوية (%)') {
      recommendations.push('تحليل العوامل المؤثرة على النسبة')
      recommendations.push('وضع خطة تحسين تدريجية')
      recommendations.push('مراقبة دورية للنسبة')
    }
    
    // توصيات بناءً على حجم الفجوة
    if (gapPercentage > 50) {
      recommendations.push('مراجعة شاملة للعملية الحالية')
      recommendations.push('تطبيق تغييرات جذرية')
    } else if (gapPercentage > 25) {
      recommendations.push('تحسينات متوسطة المدى')
      recommendations.push('تدريب الفريق على أفضل الممارسات')
    } else {
      recommendations.push('تحسينات تدريجية')
      recommendations.push('تطبيق التحسين المستمر')
    }
    
    return recommendations
  }

  // حساب العائد على الاستثمار (ROI)
  static calculateROI(
    currentValue: number,
    targetValue: number,
    unit: string,
    implementationCost: number,
    improvementDirection: 'increase' | 'decrease' = 'increase',
    timeHorizon: number = 12 // بالأشهر
  ): ROICalculation {
    const gap = Math.abs(targetValue - currentValue)
    
    // تقدير التوفير السنوي
    const estimatedSavings = this.estimateAnnualSavings(gap, unit)
    
    // حساب ROI
    const roi = implementationCost > 0 ? ((estimatedSavings - implementationCost) / implementationCost) * 100 : 0
    
    // حساب فترة الاسترداد
    const paybackPeriod = estimatedSavings > 0 ? implementationCost / (estimatedSavings / 12) : 0
    
    // حساب القيمة الحالية الصافية (NPV) - افتراض معدل خصم 8%
    const discountRate = 0.08
    const npv = this.calculateNPV(estimatedSavings, implementationCost, timeHorizon, discountRate)
    
    return {
      estimatedSavings,
      implementationCost,
      roi,
      paybackPeriod,
      npv
    }
  }

  // تقدير التوفير السنوي
  private static estimateAnnualSavings(gap: number, unit: string): number {
    const savingsFactors: Record<string, number> = {
      'ساعة': 200, // 200 ريال لكل ساعة توفير
      'يوم': 1600, // 1600 ريال لكل يوم توفير
      'ريال سعودي': 1, // التوفير المباشر
      'نسبة مئوية (%)': 1000, // 1000 ريال لكل نسبة مئوية تحسن
      'عدد': 100, // 100 ريال لكل وحدة تحسن
      'كيلوجرام': 50,
      'متر': 30,
      'لتر': 20,
      'درجة': 500,
      'نقطة': 2000
    }
    
    const factor = savingsFactors[unit] || 100
    return gap * factor * 12 // التوفير السنوي
  }

  // حساب القيمة الحالية الصافية
  private static calculateNPV(
    annualSavings: number,
    initialCost: number,
    timeHorizon: number,
    discountRate: number
  ): number {
    let npv = -initialCost
    
    for (let year = 1; year <= Math.ceil(timeHorizon / 12); year++) {
      const yearlyBenefit = annualSavings / Math.pow(1 + discountRate, year)
      npv += yearlyBenefit
    }
    
    return npv
  }

  // حساب مؤشرات الأداء الرئيسية
  static calculateKPIs(formData: any): Record<string, any> {
    const kpis: Record<string, any> = {}
    
    // مؤشر الفجوة
    if (formData.currentValue && formData.targetValue) {
      const gapResult = this.calculateGap(formData.currentValue, formData.targetValue, formData.unit)
      kpis.gap = gapResult
    }
    
    // مؤشر جودة البيانات
    kpis.dataQuality = this.calculateDataQuality(formData)
    
    // مؤشر اكتمال المشروع
    kpis.completeness = this.calculateCompleteness(formData)
    
    // مؤشر تعقيد المشروع
    kpis.complexity = this.calculateComplexity(formData)
    
    // مؤشر احتمالية النجاح
    kpis.successProbability = this.calculateSuccessProbability(formData)
    
    return kpis
  }

  // حساب جودة البيانات
  private static calculateDataQuality(formData: any): number {
    let score = 0
    let maxScore = 0
    
    // جودة وصف المشكلة
    maxScore += 20
    if (formData.problemDescription) {
      if (formData.problemDescription.length > 100) score += 20
      else if (formData.problemDescription.length > 50) score += 15
      else score += 10
    }
    
    // جودة البيانات الكمية
    maxScore += 30
    if (formData.currentValue && formData.targetValue && formData.unit) {
      score += 30
    }
    
    // جودة مصدر البيانات
    maxScore += 20
    if (formData.dataSource && formData.measurementMethod) {
      score += 20
    }
    
    // جودة وصف العملية
    maxScore += 30
    if (formData.processDescription) {
      if (formData.processDescription.length > 200) score += 30
      else if (formData.processDescription.length > 100) score += 20
      else score += 10
    }
    
    return maxScore > 0 ? (score / maxScore) * 100 : 0
  }

  // حساب اكتمال المشروع
  private static calculateCompleteness(formData: any): number {
    const requiredFields = [
      'problemDescription',
      'indicatorName',
      'currentValue',
      'targetValue',
      'unit',
      'responsibleDepartment',
      'teamLeader.name',
      'processDescription',
      'problemScope',
      'rootCause',
      'proposedSolutions'
    ]
    
    let completedFields = 0
    
    requiredFields.forEach(field => {
      const value = this.getFieldValue(formData, field)
      if (value !== null && value !== undefined && value !== '' && 
          (!Array.isArray(value) || value.length > 0)) {
        completedFields++
      }
    })
    
    return (completedFields / requiredFields.length) * 100
  }

  // حساب تعقيد المشروع
  private static calculateComplexity(formData: any): 'بسيط' | 'متوسط' | 'معقد' | 'معقد جداً' {
    let complexityScore = 0
    
    // تعقيد المشكلة
    if (formData.problemDescription && formData.problemDescription.includes('متعدد')) {
      complexityScore += 2
    }
    
    // عدد الأقسام المشاركة
    if (formData.participatingDepartments) {
      complexityScore += Math.min(formData.participatingDepartments.length, 3)
    }
    
    // تعقيد العملية
    if (formData.processDescription) {
      const steps = formData.processDescription.split(/[.،؛\n]/).filter((s: string) => s.trim().length > 0)
      if (steps.length > 10) complexityScore += 3
      else if (steps.length > 5) complexityScore += 2
      else complexityScore += 1
    }
    
    // عدد الحلول المقترحة
    if (formData.proposedSolutions) {
      if (formData.proposedSolutions.length > 3) complexityScore += 2
      else if (formData.proposedSolutions.length > 1) complexityScore += 1
    }
    
    // تحديد مستوى التعقيد
    if (complexityScore <= 3) return 'بسيط'
    if (complexityScore <= 6) return 'متوسط'
    if (complexityScore <= 9) return 'معقد'
    return 'معقد جداً'
  }

  // حساب احتمالية النجاح
  private static calculateSuccessProbability(formData: any): number {
    let probability = 50 // نقطة البداية
    
    // عوامل إيجابية
    if (formData.teamLeader.name) probability += 10
    if (formData.teamMembers && formData.teamMembers.length > 0) probability += 10
    if (formData.dataSource && formData.measurementMethod) probability += 10
    if (formData.rootCause && formData.rootCause.length > 30) probability += 10
    if (formData.proposedSolutions && formData.proposedSolutions.length > 1) probability += 10
    
    // عوامل سلبية
    const complexity = this.calculateComplexity(formData)
    if (complexity === 'معقد جداً') probability -= 20
    else if (complexity === 'معقد') probability -= 10
    
    if (formData.failedSolutions && formData.failedSolutions.length > 2) {
      probability -= 15
    }
    
    // تحديد الحد الأدنى والأقصى
    return Math.max(10, Math.min(90, probability))
  }

  // دالة مساعدة للحصول على قيمة الحقل
  private static getFieldValue(formData: any, fieldPath: string): any {
    const keys = fieldPath.split('.')
    let value = formData
    
    for (const key of keys) {
      value = value?.[key]
    }
    
    return value
  }

  // حساب التوقيت المثالي للتنفيذ
  static calculateOptimalTiming(formData: any): {
    recommendedStartDate: Date
    estimatedDuration: number
    criticalPath: string[]
    milestones: Array<{ name: string; date: Date; description: string }>
  } {
    const now = new Date()
    const complexity = this.calculateComplexity(formData)
    
    // تحديد مدة التنفيذ المتوقعة (بالأسابيع)
    const durationWeeks = {
      'بسيط': 4,
      'متوسط': 8,
      'معقد': 16,
      'معقد جداً': 24
    }[complexity]
    
    // تحديد تاريخ البدء المثالي (بعد أسبوعين للتحضير)
    const recommendedStartDate = new Date(now.getTime() + (14 * 24 * 60 * 60 * 1000))
    
    // المسار الحرج
    const criticalPath = [
      'تحليل المشكلة',
      'تشكيل الفريق',
      'تطوير الحلول',
      'تنفيذ الحل المختار',
      'قياس النتائج'
    ]
    
    // المعالم الرئيسية
    const milestones = [
      {
        name: 'بدء المشروع',
        date: recommendedStartDate,
        description: 'انطلاق مشروع التحسين'
      },
      {
        name: 'اكتمال التحليل',
        date: new Date(recommendedStartDate.getTime() + (durationWeeks * 0.3 * 7 * 24 * 60 * 60 * 1000)),
        description: 'انتهاء مرحلة التحليل والتخطيط'
      },
      {
        name: 'بدء التنفيذ',
        date: new Date(recommendedStartDate.getTime() + (durationWeeks * 0.5 * 7 * 24 * 60 * 60 * 1000)),
        description: 'بدء تطبيق الحلول'
      },
      {
        name: 'اكتمال المشروع',
        date: new Date(recommendedStartDate.getTime() + (durationWeeks * 7 * 24 * 60 * 60 * 1000)),
        description: 'انتهاء المشروع وتحقيق الأهداف'
      }
    ]
    
    return {
      recommendedStartDate,
      estimatedDuration: durationWeeks,
      criticalPath,
      milestones
    }
  }
} 