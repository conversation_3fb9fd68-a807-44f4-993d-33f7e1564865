'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth, usePermissions } from '@/lib/auth'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Eye, 
  FileText,
  User,
  AlertTriangle,
  Filter,
  Lightbulb,
  Target
} from 'lucide-react'
import { Pagination, usePagination } from '@/components/ui/Pagination'
import { CommentModal, useCommentModal } from '@/components/ui/CommentModal'

export default function ApprovalsPage() {
  const [statusFilter, setStatusFilter] = useState('pending')
  const [typeFilter, setTypeFilter] = useState('all')
  const [approvals, setApprovals] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  
  const { user } = useAuth()
  const permissions = usePermissions()
  const router = useRouter()

  // تحميل الموافقات من API
  useEffect(() => {
    if (user) {
      loadApprovals()
    }
  }, [user])

  const loadApprovals = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/approvals?approver_id=${user?.id}`)
      if (response.ok) {
        const result = await response.json()
        setApprovals(result.data || [])
      } else {
        console.error('Error loading approvals:', response.statusText)
      }
    } catch (error) {
      console.error('Error loading approvals:', error)
    } finally {
      setIsLoading(false)
    }
  }
  
  // نظام التعليقات
  const {
    isOpen: isCommentModalOpen,
    modalConfig,
    isLoading: isCommentLoading,
    openApprovalModal,
    openRejectionModal,
    closeModal: closeCommentModal,
    handleSubmit: handleCommentSubmit
  } = useCommentModal()

  // بيانات وهمية للموافقات
  const mockApprovals = [
    {
      id: '1',
      requestId: 'REQ-001',
      title: 'تطوير نظام إدارة المستندات',
      type: 'request',
      priority: 'high',
      requester: 'أحمد محمد',
      department: 'تقنية المعلومات',
      submittedAt: '2024-01-15',
      status: 'pending',
      currentLevel: 'pmo_manager',
      approvalFlow: [
        { level: 'pmo_manager', approver: 'مدير مكتب المشاريع', status: 'pending', date: null },
        { level: 'planning_manager', approver: 'مدير إدارة التخطيط', status: 'pending', date: null },
        { level: 'executive_manager', approver: 'المدير التنفيذي', status: 'pending', date: null }
      ],
      estimatedBudget: 150000,
      description: 'تطوير نظام متكامل لإدارة وأرشفة المستندات الإلكترونية'
    },
    {
      id: '2',
      requestId: 'REQ-002',
      title: 'تحسين واجهة تطبيق الموظفين',
      type: 'suggestion',
      priority: 'medium',
      requester: 'فاطمة أحمد',
      department: 'الموارد البشرية',
      submittedAt: '2024-01-10',
      status: 'approved',
      currentLevel: 'completed',
      approvalFlow: [
        { level: 'pmo_manager', approver: 'مدير مكتب المشاريع', status: 'approved', date: '2024-01-12' },
        { level: 'planning_manager', approver: 'مدير إدارة التخطيط', status: 'approved', date: '2024-01-14' }
      ],
      estimatedBudget: 75000,
      description: 'تحديث وتحسين تجربة المستخدم في تطبيق الموظفين'
    },
    {
      id: '3',
      requestId: 'REQ-003',
      title: 'أتمتة عملية الحضور والانصراف',
      type: 'quick_win',
      priority: 'low',
      requester: 'محمد علي',
      department: 'العمليات',
      submittedAt: '2024-01-05',
      status: 'approved',
      currentLevel: 'completed',
      approvalFlow: [
        { level: 'pmo_manager', approver: 'مدير مكتب المشاريع', status: 'approved', date: '2024-01-06' }
      ],
      estimatedBudget: 25000,
      description: 'تطبيق نظام أتمتة لتسجيل الحضور والانصراف'
    },
    {
      id: '4',
      requestId: 'REQ-004',
      title: 'تطوير منصة التدريب الإلكتروني',
      type: 'request',
      priority: 'urgent',
      requester: 'سارة خالد',
      department: 'التطوير',
      submittedAt: '2024-01-20',
      status: 'rejected',
      currentLevel: 'planning_manager',
      approvalFlow: [
        { level: 'pmo_manager', approver: 'مدير مكتب المشاريع', status: 'approved', date: '2024-01-21' },
        { level: 'planning_manager', approver: 'مدير إدارة التخطيط', status: 'rejected', date: '2024-01-22' }
      ],
      estimatedBudget: 200000,
      description: 'إنشاء منصة شاملة للتدريب والتطوير المهني',
      rejectionReason: 'الميزانية المطلوبة تتجاوز الحد المسموح للربع الحالي'
    }
  ]

  const statusOptions = [
    { value: 'all', label: 'جميع الحالات' },
    { value: 'pending', label: 'معلقة' },
    { value: 'approved', label: 'معتمدة' },
    { value: 'rejected', label: 'مرفوضة' }
  ]

  const typeOptions = [
    { value: 'all', label: 'جميع الأنواع' },
    { value: 'suggestion', label: 'مقترح مشروع' },
    { value: 'request', label: 'طلب مشروع' },
    { value: 'quick_win', label: 'كويك وين' }
  ]

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'suggestion':
        return <Lightbulb className="w-4 h-4 text-blue-500" />
      case 'request':
        return <FileText className="w-4 h-4 text-green-500" />
      case 'quick_win':
        return <Target className="w-4 h-4 text-purple-500" />
      default:
        return <FileText className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />
      case 'approved':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'rejected':
        return <XCircle className="w-4 h-4 text-red-500" />
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-500" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'bg-gray-100 text-gray-800'
      case 'medium':
        return 'bg-blue-100 text-blue-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'urgent':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const canApproveAtLevel = (approval: typeof mockApprovals[0]) => {
    if (!permissions.canApproveRequests()) return false
    
    const currentStep = approval.approvalFlow.find(step => step.level === approval.currentLevel)
    if (!currentStep || currentStep.status !== 'pending') return false
    
    // تحقق من صلاحية المستخدم للمستوى الحالي
    if (approval.currentLevel === 'pmo_manager' && permissions.isPMOManager()) return true
    if (approval.currentLevel === 'planning_manager' && permissions.isPlanningManager()) return true
    if (approval.currentLevel === 'executive_manager' && permissions.isExecutiveManager()) return true
    
    return false
  }

  const handleApprove = async (approval: any) => {
    openApprovalModal(
      approval.request?.title || approval.title,
      approval.request?.id || approval.requestId,
      async (comment: string) => {
        try {
          const response = await fetch('/api/approvals', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              request_id: approval.request_id || approval.request?.id,
              approver_id: user?.id,
              decision: 'approved',
              notes: comment
            })
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error || 'حدث خطأ في اعتماد الطلب')
          }

          // إعادة تحميل البيانات
          await loadApprovals()
          
          alert(`تم اعتماد الطلب بنجاح\nالتعليق: ${comment || 'بدون تعليق'}`)
        } catch (error) {
          console.error('Error approving:', error)
          alert('حدث خطأ في اعتماد الطلب')
          throw error
        }
      }
    )
  }

  const handleReject = async (approval: any) => {
    openRejectionModal(
      approval.request?.title || approval.title,
      approval.request?.id || approval.requestId,
      async (reason: string) => {
        try {
          const response = await fetch('/api/approvals', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              request_id: approval.request_id || approval.request?.id,
              approver_id: user?.id,
              decision: 'rejected',
              notes: reason
            })
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error || 'حدث خطأ في رفض الطلب')
          }

          // إعادة تحميل البيانات
          await loadApprovals()
          
          alert(`تم رفض الطلب\nالسبب: ${reason}`)
        } catch (error) {
          console.error('Error rejecting:', error)
          alert('حدث خطأ في رفض الطلب')
          throw error
        }
      }
    )
  }

  const filteredApprovals = mockApprovals.filter(approval => {
    const matchesStatus = statusFilter === 'all' || approval.status === statusFilter
    const matchesType = typeFilter === 'all' || approval.type === typeFilter
    
    return matchesStatus && matchesType
  })

  // استخدام نظام الترقيم
  const {
    paginatedData: paginatedApprovals,
    currentPage,
    totalPages,
    showingFrom,
    showingTo,
    totalItems,
    goToPage
  } = usePagination(filteredApprovals, 4) // 4 موافقات في كل صفحة

  const pendingCount = mockApprovals.filter(a => a.status === 'pending').length
  const myPendingCount = mockApprovals.filter(a => canApproveAtLevel(a)).length

  return (
    <ProtectedLayout>
      <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">الموافقات والاعتماد</h1>
            <p className="text-gray-600 mt-1">إدارة ومراجعة طلبات الموافقة</p>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">الموافقات المعلقة</p>
                    <p className="text-3xl font-bold text-yellow-600">{pendingCount}</p>
                  </div>
                  <Clock className="w-12 h-12 text-yellow-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">تحتاج موافقتي</p>
                    <p className="text-3xl font-bold text-blue-600">{myPendingCount}</p>
                  </div>
                  <User className="w-12 h-12 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                    <p className="text-3xl font-bold text-gray-900">{mockApprovals.length}</p>
                  </div>
                  <FileText className="w-12 h-12 text-gray-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {statusOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>

                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {typeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>

                <Button
                  variant="ghost"
                  onClick={() => {
                    setStatusFilter('all')
                    setTypeFilter('all')
                  }}
                  icon={<Filter className="w-4 h-4" />}
                >
                  مسح الفلاتر
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Approvals List */}
          <div className="space-y-6">
            {paginatedApprovals.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <CheckCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    لا توجد موافقات
                  </h3>
                  <p className="text-gray-600">
                    لم يتم العثور على موافقات تطابق معايير البحث
                  </p>
                </CardContent>
              </Card>
            ) : (
              paginatedApprovals.map((approval) => (
                <Card key={approval.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          {getTypeIcon(approval.type)}
                          <h3 className="text-lg font-semibold text-gray-900">
                            {approval.title}
                          </h3>
                          <span className="text-sm text-gray-500">
                            #{approval.requestId}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(approval.priority)}`}>
                            {approval.priority === 'low' && 'منخفضة'}
                            {approval.priority === 'medium' && 'متوسطة'}
                            {approval.priority === 'high' && 'عالية'}
                            {approval.priority === 'urgent' && 'عاجلة'}
                          </span>
                        </div>
                        
                        <p className="text-gray-600 mb-3">{approval.description}</p>
                        
                        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 mb-4">
                          <div className="flex items-center gap-1">
                            {getStatusIcon(approval.status)}
                            <span>
                              {approval.status === 'pending' && 'معلقة'}
                              {approval.status === 'approved' && 'معتمدة'}
                              {approval.status === 'rejected' && 'مرفوضة'}
                            </span>
                          </div>
                          
                          <span>المقدم: {approval.requester}</span>
                          <span>القسم: {approval.department}</span>
                          <span>التاريخ: {approval.submittedAt}</span>
                          <span>الميزانية: {approval.estimatedBudget.toLocaleString()} ريال</span>
                        </div>

                        {/* Approval Flow */}
                        <div className="bg-gray-50 rounded-lg p-4">
                          <h4 className="font-medium text-gray-900 mb-3">مسار الموافقة:</h4>
                          <div className="flex items-center gap-2 overflow-x-auto">
                            {approval.approvalFlow.map((step, index) => (
                              <div key={step.level} className="flex items-center gap-2">
                                <div className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm whitespace-nowrap ${
                                  step.status === 'approved' ? 'bg-green-100 text-green-800' :
                                  step.status === 'rejected' ? 'bg-red-100 text-red-800' :
                                  step.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-gray-100 text-gray-600'
                                }`}>
                                  {step.status === 'approved' && <CheckCircle className="w-4 h-4" />}
                                  {step.status === 'rejected' && <XCircle className="w-4 h-4" />}
                                  {step.status === 'pending' && <Clock className="w-4 h-4" />}
                                  
                                  <span>{step.approver}</span>
                                  {step.date && <span className="text-xs">({step.date})</span>}
                                </div>
                                
                                {index < approval.approvalFlow.length - 1 && (
                                  <div className="w-4 h-0.5 bg-gray-300" />
                                )}
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Rejection Reason */}
                        {approval.status === 'rejected' && approval.rejectionReason && (
                          <div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-3">
                            <div className="flex items-center gap-2">
                              <XCircle className="w-5 h-5 text-red-500" />
                              <span className="font-medium text-red-800">سبب الرفض:</span>
                            </div>
                            <p className="text-red-700 mt-1">{approval.rejectionReason}</p>
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-2 mr-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => router.push(`/requests/${approval.requestId}`)}
                          icon={<Eye className="w-4 h-4" />}
                        >
                          عرض التفاصيل
                        </Button>
                        
                        {canApproveAtLevel(approval) && (
                          <div className="flex gap-2">
                            <Button
                              variant="success"
                              size="sm"
                              onClick={() => handleApprove(approval)}
                              icon={<CheckCircle className="w-4 h-4" />}
                            >
                              موافقة
                            </Button>
                            
                            <Button
                              variant="danger"
                              size="sm"
                              onClick={() => handleReject(approval)}
                              icon={<XCircle className="w-4 h-4" />}
                            >
                              رفض
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={goToPage}
              showingFrom={showingFrom}
              showingTo={showingTo}
              totalItems={totalItems}
            />
          )}

          {/* Summary */}
          {totalPages <= 1 && (
            <div className="mt-8 text-center text-sm text-gray-500">
              عرض {totalItems} موافقة
            </div>
          )}
        {/* Comment Modal */}
        {modalConfig && (
          <CommentModal
            isOpen={isCommentModalOpen}
            onClose={closeCommentModal}
            onSubmit={handleCommentSubmit}
            title={modalConfig.title}
            requestTitle={modalConfig.requestTitle}
            requestId={modalConfig.requestId}
            action={modalConfig.action}
            isLoading={isCommentLoading}
          />
        )}
        </div>
    </ProtectedLayout>
  )
} 