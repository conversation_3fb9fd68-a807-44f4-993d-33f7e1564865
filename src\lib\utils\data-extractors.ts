/**
 * أدوات استخراج ومعالجة البيانات
 * يحتوي على فئات وأدوات لاستخراج المشاركين والبيانات من النماذج
 */

import { 
  ParticipantInfo, 
  StakeholderInfo, 
  ReviewerType, 
  ParticipatingDepartment 
} from '@/types/feedback.types';

// ==================== STAKEHOLDER EXTRACTOR ====================

export class StakeholderExtractor {
  /**
   * استخراج المشاركين في المراجعة (بدون أعضاء الفريق)
   */
  static extractParticipants(formData: any): ParticipantInfo[] {
    const participants: ParticipantInfo[] = [];
    
    try {
      // استخراج قائد الفريق
      if (formData.teamLeader?.name) {
        participants.push({
          id: `team_leader_${formData.teamLeader.name.replace(/\s+/g, '_')}`,
          name: formData.teamLeader.name,
          phone: formData.teamLeader.phone || '',
          email: formData.teamLeader.email,
          role: 'team_leader' as ReviewerType,
          department: formData.teamLeader.department || formData.responsibleDepartment?.name || 'غير محدد',
          position: 'قائد الفريق'
        });
      }

      // استخراج مدير القسم المسؤول
      if (formData.responsibleDepartment?.manager) {
        participants.push({
          id: `dept_manager_${formData.responsibleDepartment.manager.replace(/\s+/g, '_')}`,
          name: formData.responsibleDepartment.manager,
          phone: formData.responsibleDepartment.managerPhone || '',
          email: formData.responsibleDepartment.managerEmail,
          role: 'department_manager' as ReviewerType,
          department: formData.responsibleDepartment.name,
          position: 'مدير القسم المسؤول'
        });
      }

      // استخراج مدراء الأقسام المشاركة
      if (Array.isArray(formData.participatingDepartments)) {
        formData.participatingDepartments.forEach((dept: any, index: number) => {
          if (dept.manager?.name) {
            participants.push({
              id: `participating_manager_${index}_${dept.manager.name.replace(/\s+/g, '_')}`,
              name: dept.manager.name,
              phone: dept.manager.phone || '',
              email: dept.manager.email,
              role: 'department_manager' as ReviewerType,
              department: dept.name,
              position: 'مدير قسم مشارك'
            });
          }
        });
      }

      // استخراج المراجعين الخارجيين
      if (Array.isArray(formData.externalReviewers)) {
        formData.externalReviewers.forEach((reviewer: any, index: number) => {
          if (reviewer.name) {
            participants.push({
              id: `external_reviewer_${index}_${reviewer.name.replace(/\s+/g, '_')}`,
              name: reviewer.name,
              phone: reviewer.phone || '',
              email: reviewer.email,
              role: 'external_reviewer' as ReviewerType,
              department: reviewer.organization || 'خارجي',
              position: reviewer.position || 'مراجع خارجي'
            });
          }
        });
      }

    } catch (error) {
      console.error('Error extracting participants:', error);
    }

    return participants;
  }

  /**
   * استخراج أصحاب المصلحة (جميع المشاركين)
   */
  static extractStakeholders(formData: any): StakeholderInfo[] {
    const stakeholders: StakeholderInfo[] = [];
    
    try {
      // الحصول على المشاركين الأساسيين
      const participants = this.extractParticipants(formData);
      
      // تحويل المشاركين إلى أصحاب مصلحة
      participants.forEach(participant => {
        stakeholders.push({
          id: participant.id,
          name: participant.name,
          role: participant.role,
          department: participant.department,
          contact: {
            phone: participant.phone,
            email: participant.email
          },
          involvement_level: this.getInvolvementLevel(participant.role),
          responsibilities: this.getResponsibilities(participant.role)
        });
      });

      // إضافة أعضاء الفريق
      if (Array.isArray(formData.teamMembers)) {
        formData.teamMembers.forEach((member: any, index: number) => {
          if (member.name) {
            stakeholders.push({
              id: `team_member_${index}_${member.name.replace(/\s+/g, '_')}`,
              name: member.name,
              role: 'team_member' as ReviewerType,
              department: member.department || formData.responsibleDepartment?.name || 'غير محدد',
              contact: {
                phone: member.phone || '',
                email: member.email || ''
              },
              involvement_level: 'high',
              responsibilities: ['تنفيذ المهام', 'تقديم التقارير', 'المشاركة في الاجتماعات']
            });
          }
        });
      }

    } catch (error) {
      console.error('Error extracting stakeholders:', error);
    }

    return stakeholders;
  }

  /**
   * استخراج الأقسام المشاركة
   */
  static extractParticipatingDepartments(formData: any): ParticipatingDepartment[] {
    const departments: ParticipatingDepartment[] = [];
    
    try {
      // إضافة القسم المسؤول
      if (formData.responsibleDepartment?.name) {
        departments.push({
          id: `responsible_${formData.responsibleDepartment.name.replace(/\s+/g, '_')}`,
          name: formData.responsibleDepartment.name,
          type: 'responsible',
          manager: {
            name: formData.responsibleDepartment.manager || '',
            phone: formData.responsibleDepartment.managerPhone || '',
            email: formData.responsibleDepartment.managerEmail || ''
          },
          involvement_level: 'primary',
          resources_allocated: formData.responsibleDepartment.resources || []
        });
      }

      // إضافة الأقسام المشاركة
      if (Array.isArray(formData.participatingDepartments)) {
        formData.participatingDepartments.forEach((dept: any, index: number) => {
          if (dept.name) {
            departments.push({
              id: `participating_${index}_${dept.name.replace(/\s+/g, '_')}`,
              name: dept.name,
              type: 'participating',
              manager: {
                name: dept.manager?.name || '',
                phone: dept.manager?.phone || '',
                email: dept.manager?.email || ''
              },
              involvement_level: dept.involvementLevel || 'secondary',
              resources_allocated: dept.resources || []
            });
          }
        });
      }

    } catch (error) {
      console.error('Error extracting participating departments:', error);
    }

    return departments;
  }

  /**
   * تحديد مستوى المشاركة حسب الدور
   */
  private static getInvolvementLevel(role: ReviewerType): 'high' | 'medium' | 'low' {
    switch (role) {
      case 'team_leader':
      case 'department_manager':
        return 'high';
      case 'team_member':
        return 'medium';
      case 'external_reviewer':
        return 'low';
      default:
        return 'medium';
    }
  }

  /**
   * تحديد المسؤوليات حسب الدور
   */
  private static getResponsibilities(role: ReviewerType): string[] {
    switch (role) {
      case 'team_leader':
        return [
          'قيادة الفريق',
          'تنسيق المهام',
          'متابعة التقدم',
          'التواصل مع الإدارة',
          'ضمان جودة التنفيذ'
        ];
      case 'department_manager':
        return [
          'الإشراف العام',
          'اتخاذ القرارات',
          'توفير الموارد',
          'المراجعة والاعتماد',
          'حل المشاكل'
        ];
      case 'team_member':
        return [
          'تنفيذ المهام المحددة',
          'تقديم التقارير',
          'المشاركة في الاجتماعات',
          'التعاون مع الفريق'
        ];
      case 'external_reviewer':
        return [
          'المراجعة الخارجية',
          'تقديم الاستشارة',
          'التقييم المستقل',
          'تقديم التوصيات'
        ];
      default:
        return ['المشاركة في المشروع'];
    }
  }
}

// ==================== DATA EXTRACTORS ====================

export class DataExtractors {
  /**
   * استخراج معلومات الاتصال من البيانات
   */
  static extractContactInfo(data: any): { phone?: string; email?: string } {
    return {
      phone: data.phone || data.phoneNumber || data.mobile || '',
      email: data.email || data.emailAddress || ''
    };
  }

  /**
   * استخراج التواريخ المهمة من المشروع
   */
  static extractProjectDates(formData: any): {
    startDate?: string;
    endDate?: string;
    milestones?: Array<{ name: string; date: string }>;
  } {
    return {
      startDate: formData.startDate || formData.plannedStartDate,
      endDate: formData.endDate || formData.plannedEndDate,
      milestones: formData.milestones || []
    };
  }

  /**
   * استخراج الموارد المطلوبة
   */
  static extractRequiredResources(formData: any): {
    human?: string[];
    financial?: number;
    technical?: string[];
    other?: string[];
  } {
    return {
      human: formData.humanResources || [],
      financial: formData.budget || formData.estimatedCost,
      technical: formData.technicalResources || [],
      other: formData.otherResources || []
    };
  }

  /**
   * استخراج المخاطر والتحديات
   */
  static extractRisksAndChallenges(formData: any): {
    risks?: Array<{ description: string; impact: string; mitigation: string }>;
    challenges?: string[];
  } {
    return {
      risks: formData.risks || [],
      challenges: formData.challenges || formData.expectedChallenges || []
    };
  }

  /**
   * تنظيف وتنسيق البيانات النصية
   */
  static sanitizeTextData(data: any): any {
    if (typeof data === 'string') {
      return data.trim().replace(/\s+/g, ' ');
    }
    
    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeTextData(item));
    }
    
    if (typeof data === 'object' && data !== null) {
      const sanitized: any = {};
      Object.keys(data).forEach(key => {
        sanitized[key] = this.sanitizeTextData(data[key]);
      });
      return sanitized;
    }
    
    return data;
  }

  /**
   * استخراج الكلمات المفتاحية من النص
   */
  static extractKeywords(text: string, minLength: number = 3): string[] {
    if (!text) return [];
    
    const words = text
      .toLowerCase()
      .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length >= minLength);
    
    // إزالة التكرارات
    return [...new Set(words)];
  }
}
