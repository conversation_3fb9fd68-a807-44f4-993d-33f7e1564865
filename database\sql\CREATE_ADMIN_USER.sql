-- إنشاء مستخدم المدير الرئيسي لنظام إرادة PMO 2025
-- يجب تشغيل هذا الملف في Supabase SQL Editor

-- إنشاء مستخدم المدير الرئيسي
INSERT INTO auth.users (
  id,
  email,
  encrypted_password,
  email_confirmed_at,
  created_at,
  updated_at,
  raw_app_meta_data,
  raw_user_meta_data,
  is_super_admin,
  role
) VALUES (
  gen_random_uuid(),
  '<EMAIL>',
  crypt('Admin@2025', gen_salt('bf')),
  now(),
  now(),
  now(),
  '{"provider": "email", "providers": ["email"]}',
  '{"name": "مدير النظام الرئيسي", "role": "super_admin"}',
  true,
  'authenticated'
);

-- إنشاء بيانات المستخدم في جدول users
INSERT INTO public.users (
  id,
  email,
  name,
  role,
  department,
  phone,
  is_active,
  created_at,
  updated_at
) VALUES (
  (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
  '<EMAIL>',
  'مدير النظام الرئيسي',
  'super_admin',
  'إدارة النظم',
  '**********',
  true,
  now(),
  now()
);

-- إنشاء دور المدير الرئيسي
INSERT INTO public.user_roles (
  user_id,
  role_name,
  permissions,
  created_at,
  updated_at
) VALUES (
  (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
  'super_admin',
  ARRAY[
    'users:create',
    'users:read',
    'users:update',
    'users:delete',
    'projects:create',
    'projects:read',
    'projects:update',
    'projects:delete',
    'departments:create',
    'departments:read',
    'departments:update',
    'departments:delete',
    'system:admin',
    'reports:all'
  ],
  now(),
  now()
);

-- تسجيل العملية في سجل المراجعة
INSERT INTO public.audit_logs (
  user_id,
  action,
  table_name,
  record_id,
  old_values,
  new_values,
  created_at
) VALUES (
  (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
  'CREATE',
  'users',
  (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
  '{}',
  jsonb_build_object(
    'email', '<EMAIL>',
    'name', 'مدير النظام الرئيسي',
    'role', 'super_admin'
  ),
  now()
);

-- عرض النتيجة
SELECT 
  u.id,
  u.email,
  u.name,
  u.role,
  u.department,
  u.is_active,
  ur.permissions
FROM public.users u
LEFT JOIN public.user_roles ur ON u.id = ur.user_id
WHERE u.email = '<EMAIL>';
