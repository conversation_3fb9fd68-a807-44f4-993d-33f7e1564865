'use client'

import { Bell, Search, Menu } from 'lucide-react'
import { SystemNotifications } from '@/components/ui/SystemNotifications'

interface TopHeaderProps {
  sidebarCollapsed: boolean
  onToggleSidebar: () => void
}

export function TopHeader({ sidebarCollapsed, onToggleSidebar }: TopHeaderProps) {
  return (
    <header className={`
      fixed top-0 left-0 right-0 h-16 bg-white border-b border-gray-200 z-30 transition-all duration-300
      ${sidebarCollapsed ? 'pr-16' : 'pr-64'}
    `}>
      <div className="flex items-center justify-between h-full px-6">
        {/* Left side - Mobile menu button and search */}
        <div className="flex items-center gap-4">
          <button
            onClick={onToggleSidebar}
            className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <Menu className="w-5 h-5 text-gray-600" />
          </button>
          
          {/* Search Bar */}
          <div className="hidden sm:flex items-center">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="البحث في النظام..."
                className="w-64 pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>
          </div>
        </div>

        {/* Right side - Notifications and actions */}
        <div className="flex items-center gap-4">
          {/* Quick Actions */}
          <div className="hidden md:flex items-center gap-2">
            <button className="px-3 py-2 text-sm font-medium text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
              طلب جديد
            </button>
            <button className="px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
              تقرير سريع
            </button>
          </div>
          
          {/* Notifications */}
          <SystemNotifications />
        </div>
      </div>
    </header>
  )
} 