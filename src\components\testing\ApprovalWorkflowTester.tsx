'use client'

import { useState } from 'react'
import { useAuth } from '@/lib/auth'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/Button'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { CheckCircle, XCircle, Clock, User, ArrowRight } from 'lucide-react'

interface ApprovalTest {
  id: string
  name: string
  status: 'pending' | 'running' | 'passed' | 'failed'
  message: string
  data?: any
}

interface ApprovalStep {
  level: number
  approver_type: string
  approver_id?: string
  status: 'pending' | 'approved' | 'rejected'
  approved_at?: string
  comments?: string
}

export default function ApprovalWorkflowTester() {
  const { user, userRole } = useAuth()
  const [tests, setTests] = useState<ApprovalTest[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [testRequestId, setTestRequestId] = useState<string | null>(null)

  const updateTest = (id: string, status: ApprovalTest['status'], message: string, data?: any) => {
    setTests(prev => prev.map(test => 
      test.id === id ? { ...test, status, message, data } : test
    ))
  }

  const addTest = (test: ApprovalTest) => {
    setTests(prev => [...prev, test])
  }

  const initializeTests = () => {
    setTests([
      { id: 'create_request', name: 'إنشاء طلب للاختبار', status: 'pending', message: 'في انتظار التنفيذ' },
      { id: 'create_approvals', name: 'إنشاء سلسلة الموافقات', status: 'pending', message: 'في انتظار التنفيذ' },
      { id: 'test_team_leader', name: 'اختبار موافقة قائد الفريق', status: 'pending', message: 'في انتظار التنفيذ' },
      { id: 'test_dept_manager', name: 'اختبار موافقة مدير القسم', status: 'pending', message: 'في انتظار التنفيذ' },
      { id: 'test_pmo_manager', name: 'اختبار موافقة مدير PMO', status: 'pending', message: 'في انتظار التنفيذ' },
      { id: 'test_status_update', name: 'اختبار تحديث حالة الطلب', status: 'pending', message: 'في انتظار التنفيذ' }
    ])
  }

  const createTestRequest = async () => {
    updateTest('create_request', 'running', 'جاري إنشاء طلب اختبار...')
    
    try {
      const requestData = {
        title: 'طلب اختبار نظام الموافقات',
        description: 'هذا طلب تجريبي لاختبار نظام الموافقات المتدرج',
        main_type: 'improvement_project',
        sub_type: 'suggestion',
        priority: 'medium',
        estimated_budget: 10000,
        expected_start_date: new Date().toISOString().split('T')[0],
        expected_end_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        requester_id: user?.id,
        department_id: '1', // assuming department 1 exists
        status: 'pending_approval',
        form_data: { test: true, approval_test: true }
      }

      const { data, error } = await supabase
        .from('project_requests')
        .insert(requestData)
        .select()
        .single()

      if (error) throw error

      setTestRequestId(data.id)
      updateTest('create_request', 'passed', 'تم إنشاء طلب الاختبار بنجاح', data)
      return data.id
    } catch (error) {
      updateTest('create_request', 'failed', `فشل في إنشاء الطلب: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
      throw error
    }
  }

  const createApprovalChain = async (requestId: string) => {
    updateTest('create_approvals', 'running', 'جاري إنشاء سلسلة الموافقات...')
    
    try {
      // إنشاء موافقات متدرجة
      const approvals = [
        {
          request_id: requestId,
          approver_type: 'team_leader',
          approval_level: 1,
          status: 'pending',
          required: true
        },
        {
          request_id: requestId,
          approver_type: 'department_manager',
          approval_level: 2,
          status: 'pending',
          required: true
        },
        {
          request_id: requestId,
          approver_type: 'pmo_manager',
          approval_level: 3,
          status: 'pending',
          required: true
        }
      ]

      const { data, error } = await supabase
        .from('approvals')
        .insert(approvals)
        .select()

      if (error) throw error

      updateTest('create_approvals', 'passed', `تم إنشاء ${data.length} موافقة`, data)
      return data
    } catch (error) {
      updateTest('create_approvals', 'failed', `فشل في إنشاء الموافقات: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
      throw error
    }
  }

  const testApprovalLevel = async (requestId: string, level: number, approverType: string, testId: string) => {
    updateTest(testId, 'running', `جاري اختبار موافقة المستوى ${level}...`)
    
    try {
      // محاكاة الموافقة
      const { data, error } = await supabase
        .from('approvals')
        .update({
          status: 'approved',
          approved_at: new Date().toISOString(),
          comments: `موافقة تجريبية من ${approverType}`,
          approver_id: user?.id
        })
        .eq('request_id', requestId)
        .eq('approval_level', level)
        .select()

      if (error) throw error

      updateTest(testId, 'passed', `تمت الموافقة على المستوى ${level}`, data)
      return data
    } catch (error) {
      updateTest(testId, 'failed', `فشل في الموافقة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
      throw error
    }
  }

  const testStatusUpdate = async (requestId: string) => {
    updateTest('test_status_update', 'running', 'جاري اختبار تحديث حالة الطلب...')
    
    try {
      // التحقق من حالة جميع الموافقات
      const { data: approvals, error: approvalsError } = await supabase
        .from('approvals')
        .select('*')
        .eq('request_id', requestId)

      if (approvalsError) throw approvalsError

      const allApproved = approvals?.every(approval => approval.status === 'approved')

      if (allApproved) {
        // تحديث حالة الطلب إلى موافق عليه
        const { data, error } = await supabase
          .from('project_requests')
          .update({ status: 'approved' })
          .eq('id', requestId)
          .select()

        if (error) throw error

        updateTest('test_status_update', 'passed', 'تم تحديث حالة الطلب إلى موافق عليه', data)
      } else {
        updateTest('test_status_update', 'passed', 'الطلب ما زال في انتظار موافقات أخرى', approvals)
      }
    } catch (error) {
      updateTest('test_status_update', 'failed', `فشل في تحديث الحالة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`)
    }
  }

  const runFullWorkflowTest = async () => {
    setIsRunning(true)
    initializeTests()

    try {
      // 1. إنشاء طلب
      const requestId = await createTestRequest()
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 2. إنشاء سلسلة الموافقات
      await createApprovalChain(requestId)
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 3. اختبار موافقة قائد الفريق
      await testApprovalLevel(requestId, 1, 'team_leader', 'test_team_leader')
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 4. اختبار موافقة مدير القسم
      await testApprovalLevel(requestId, 2, 'department_manager', 'test_dept_manager')
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 5. اختبار موافقة مدير PMO
      await testApprovalLevel(requestId, 3, 'pmo_manager', 'test_pmo_manager')
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 6. اختبار تحديث حالة الطلب
      await testStatusUpdate(requestId)

    } catch (error) {
      console.error('خطأ في اختبار نظام الموافقات:', error)
    }

    setIsRunning(false)
  }

  const cleanupTestData = async () => {
    try {
      if (testRequestId) {
        // حذف الموافقات
        await supabase
          .from('approvals')
          .delete()
          .eq('request_id', testRequestId)

        // حذف الطلب
        await supabase
          .from('project_requests')
          .delete()
          .eq('id', testRequestId)

        setTestRequestId(null)
        addTest({
          id: 'cleanup',
          name: 'تنظيف البيانات',
          status: 'passed',
          message: 'تم حذف بيانات الاختبار بنجاح'
        })
      }
    } catch (error) {
      addTest({
        id: 'cleanup',
        name: 'تنظيف البيانات',
        status: 'failed',
        message: `فشل في حذف البيانات: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`
      })
    }
  }

  const getStatusIcon = (status: ApprovalTest['status']) => {
    switch (status) {
      case 'passed': return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'failed': return <XCircle className="w-5 h-5 text-red-500" />
      case 'running': return <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
      default: return <Clock className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: ApprovalTest['status']) => {
    switch (status) {
      case 'passed': return 'bg-green-50 border-green-200'
      case 'failed': return 'bg-red-50 border-red-200'
      case 'running': return 'bg-blue-50 border-blue-200'
      default: return 'bg-gray-50 border-gray-200'
    }
  }

  const passedTests = tests.filter(t => t.status === 'passed').length
  const failedTests = tests.filter(t => t.status === 'failed').length

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">اختبار نظام الموافقات المتدرج</h2>
          <p className="text-gray-600">
            اختبر سير عمل الموافقات من قائد الفريق إلى مدير القسم إلى مدير PMO
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <Button 
              onClick={runFullWorkflowTest}
              disabled={isRunning}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isRunning ? 'جاري الاختبار...' : 'اختبار سير العمل الكامل'}
            </Button>
            
            <Button 
              onClick={cleanupTestData}
              disabled={isRunning}
              variant="danger"
            >
              تنظيف بيانات الاختبار
            </Button>
          </div>

          {/* إحصائيات */}
          {tests.length > 0 && (
            <div className="grid grid-cols-3 gap-4 mb-6">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{tests.length}</div>
                <div className="text-sm text-gray-600">إجمالي الاختبارات</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{passedTests}</div>
                <div className="text-sm text-gray-600">نجح</div>
              </div>
              <div className="text-center p-3 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">{failedTests}</div>
                <div className="text-sm text-gray-600">فشل</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* نتائج الاختبارات */}
      {tests.length > 0 && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">نتائج اختبار سير العمل</h3>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {tests.map((test, index) => (
                <div key={test.id} className="flex items-center">
                  <div className={`flex-1 p-4 rounded-lg border-2 ${getStatusColor(test.status)}`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(test.status)}
                        <div>
                          <h4 className="font-medium">{test.name}</h4>
                          <p className="text-sm text-gray-600">{test.message}</p>
                        </div>
                      </div>
                      <div className="text-sm font-medium text-gray-500">
                        {index + 1}
                      </div>
                    </div>
                    
                    {test.data && (
                      <div className="mt-2">
                        <details>
                          <summary className="cursor-pointer text-sm font-medium text-gray-700">
                            عرض التفاصيل
                          </summary>
                          <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                            {JSON.stringify(test.data, null, 2)}
                          </pre>
                        </details>
                      </div>
                    )}
                  </div>
                  
                  {index < tests.length - 1 && (
                    <ArrowRight className="w-4 h-4 text-gray-400 mx-2" />
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* معلومات المستخدم */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">معلومات الاختبار</h3>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">المستخدم الحالي:</span>
              <span className="mr-2">{user?.email || 'غير محدد'}</span>
            </div>
            <div>
              <span className="font-medium">الدور:</span>
              <span className="mr-2">{userRole?.display_name || 'غير محدد'}</span>
            </div>
            <div>
              <span className="font-medium">معرف الطلب التجريبي:</span>
              <span className="mr-2">{testRequestId || 'لم يتم إنشاؤه بعد'}</span>
            </div>
            <div>
              <span className="font-medium">حالة الاختبار:</span>
              <span className="mr-2">{isRunning ? 'قيد التشغيل' : 'متوقف'}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 