'use client'

import React from 'react'
import { Card } from '@/components/ui/Card'
import { 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  Clock, 
  Target,
  CheckCircle,
  BarChart3,
  Activity
} from 'lucide-react'

interface ProgressMetrics {
  totalTasks: number
  completedTasks: number
  overdueTasks: number
  averageProgress: number
  onTrackTasks: number
  atRiskTasks: number
  estimatedHours: number
  actualHours: number
  efficiency: number
}

interface PhaseProgress {
  phase: string
  title: string
  color: string
  tasks: number
  progress: number
  icon: React.ReactNode
}

interface ProgressTrackerProps {
  metrics: ProgressMetrics
  phaseProgress: PhaseProgress[]
  showDetailed?: boolean
}

export function ProgressTracker({ 
  metrics, 
  phaseProgress, 
  showDetailed = true 
}: ProgressTrackerProps) {
  
  const getEfficiencyColor = (efficiency: number) => {
    if (efficiency >= 90) return 'text-green-600 bg-green-100'
    if (efficiency >= 70) return 'text-blue-600 bg-blue-100'
    if (efficiency >= 50) return 'text-orange-600 bg-orange-100'
    return 'text-red-600 bg-red-100'
  }

  const getEfficiencyIcon = (efficiency: number) => {
    if (efficiency >= 70) return <TrendingUp className="w-4 h-4" />
    return <TrendingDown className="w-4 h-4" />
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500'
    if (progress >= 60) return 'bg-blue-500'
    if (progress >= 40) return 'bg-orange-500'
    return 'bg-red-500'
  }

  const completionRate = metrics.totalTasks > 0 ? (metrics.completedTasks / metrics.totalTasks) * 100 : 0
  const overdueRate = metrics.totalTasks > 0 ? (metrics.overdueTasks / metrics.totalTasks) * 100 : 0
  const onTrackRate = metrics.totalTasks > 0 ? (metrics.onTrackTasks / metrics.totalTasks) * 100 : 0

  return (
    <div className="space-y-6">
      {/* Overall Progress Summary */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <BarChart3 className="w-5 h-5 text-blue-600" />
            ملخص التقدم العام
          </h3>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${getEfficiencyColor(metrics.efficiency)}`}>
            {getEfficiencyIcon(metrics.efficiency)}
            <span className="mr-1">كفاءة {metrics.efficiency.toFixed(1)}%</span>
          </div>
        </div>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {/* Total Tasks */}
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{metrics.totalTasks}</div>
            <div className="text-sm text-gray-600">إجمالي المهام</div>
          </div>

          {/* Completed */}
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{metrics.completedTasks}</div>
            <div className="text-sm text-gray-600">مكتملة</div>
            <div className="text-xs text-green-600">{completionRate.toFixed(1)}%</div>
          </div>

          {/* On Track */}
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{metrics.onTrackTasks}</div>
            <div className="text-sm text-gray-600">في المسار</div>
            <div className="text-xs text-blue-600">{onTrackRate.toFixed(1)}%</div>
          </div>

          {/* Overdue */}
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{metrics.overdueTasks}</div>
            <div className="text-sm text-gray-600">متأخرة</div>
            <div className="text-xs text-red-600">{overdueRate.toFixed(1)}%</div>
          </div>
        </div>

        {/* Overall Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">التقدم الإجمالي</span>
            <span className="text-sm font-bold text-gray-900">{metrics.averageProgress.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className={`h-3 rounded-full transition-all duration-500 ${getProgressColor(metrics.averageProgress)}`}
              style={{ width: `${metrics.averageProgress}%` }}
            />
          </div>
        </div>

        {/* Time Tracking */}
        {showDetailed && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <Target className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <div className="text-sm text-gray-600">ساعات مخططة</div>
                <div className="font-semibold text-gray-900">{metrics.estimatedHours.toLocaleString()}</div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                <Clock className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <div className="text-sm text-gray-600">ساعات فعلية</div>
                <div className="font-semibold text-gray-900">{metrics.actualHours.toLocaleString()}</div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <Activity className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <div className="text-sm text-gray-600">نسبة الإنجاز</div>
                <div className="font-semibold text-gray-900">
                  {metrics.estimatedHours > 0 ? ((metrics.actualHours / metrics.estimatedHours) * 100).toFixed(1) : 0}%
                </div>
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* PDCA Phase Progress */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <CheckCircle className="w-5 h-5 text-green-600" />
          تقدم مراحل PDCA
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {phaseProgress.map((phase) => (
            <div key={phase.phase} className="space-y-3">
              <div className="flex items-center gap-2">
                <div className={`text-${phase.color}-600`}>
                  {phase.icon}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">{phase.title}</h4>
                  <p className="text-sm text-gray-600">{phase.tasks} مهام</p>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">التقدم</span>
                  <span className="text-xs font-medium text-gray-700">{phase.progress.toFixed(1)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-500 bg-${phase.color}-500`}
                    style={{ width: `${phase.progress}%` }}
                  />
                </div>
              </div>

              {/* Phase Status Indicator */}
              <div className="flex items-center gap-1">
                {phase.progress >= 80 ? (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                ) : phase.progress >= 50 ? (
                  <Clock className="w-4 h-4 text-blue-500" />
                ) : (
                  <AlertTriangle className="w-4 h-4 text-orange-500" />
                )}
                <span className="text-xs text-gray-600">
                  {phase.progress >= 80 ? 'متقدم' : phase.progress >= 50 ? 'جيد' : 'يحتاج متابعة'}
                </span>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Alerts and Notifications */}
      {(metrics.overdueTasks > 0 || metrics.atRiskTasks > 0 || metrics.efficiency < 70) && (
        <Card className="p-6 border-orange-200 bg-orange-50">
          <h3 className="text-lg font-semibold text-orange-900 mb-4 flex items-center gap-2">
            <AlertTriangle className="w-5 h-5" />
            تنبيهات ومتابعة
          </h3>

          <div className="space-y-3">
            {metrics.overdueTasks > 0 && (
              <div className="flex items-center gap-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                <AlertTriangle className="w-5 h-5 text-red-600 flex-shrink-0" />
                <div>
                  <div className="font-medium text-red-900">
                    {metrics.overdueTasks} مهام متأخرة عن الموعد
                  </div>
                  <div className="text-sm text-red-700">
                    تحتاج إلى متابعة فورية ومراجعة الجداول الزمنية
                  </div>
                </div>
              </div>
            )}

            {metrics.atRiskTasks > 0 && (
              <div className="flex items-center gap-3 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <Clock className="w-5 h-5 text-orange-600 flex-shrink-0" />
                <div>
                  <div className="font-medium text-orange-900">
                    {metrics.atRiskTasks} مهام معرضة للتأخير
                  </div>
                  <div className="text-sm text-orange-700">
                    قد تحتاج إلى موارد إضافية أو إعادة جدولة
                  </div>
                </div>
              </div>
            )}

            {metrics.efficiency < 70 && (
              <div className="flex items-center gap-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <TrendingDown className="w-5 h-5 text-yellow-600 flex-shrink-0" />
                <div>
                  <div className="font-medium text-yellow-900">
                    كفاءة الأداء أقل من المتوقع ({metrics.efficiency.toFixed(1)}%)
                  </div>
                  <div className="text-sm text-yellow-700">
                    يُنصح بمراجعة العمليات وتحسين توزيع المهام
                  </div>
                </div>
              </div>
            )}
          </div>
        </Card>
      )}
    </div>
  )
} 