'use client'

import React from 'react'
import { Button } from '@/components/ui/Button'
import { Download, FileText, CheckCircle } from 'lucide-react'

interface DownloadableGuideProps {
  currentStep?: number
}

export function DownloadableGuide({ currentStep }: DownloadableGuideProps) {
  
  const generateGuideContent = () => {
    return `
# دليل مشروع التحسين الشامل - منهجية FOCUS-PDCA

## نظرة عامة
هذا الدليل يساعدك في إكمال مشروع التحسين الشامل باستخدام منهجية FOCUS-PDCA المكونة من 9 مراحل.

## المراحل السبع

### 1. المعلومات الأساسية
**الهدف:** تحديد هوية المشروع والإطار الزمني
**المدة المتوقعة:** 10-15 دقيقة

**الحقول المطلوبة:**
- اسم المشروع (أقل من 60 حرف)
- وصف المشروع (أقل من 500 حرف)
- تاريخ البداية والنهاية
- مستوى الأولوية

**نصائح مهمة:**
✓ اختر اسماً واضحاً ومحدداً للمشروع
✓ اكتب وصفاً شاملاً يوضح الهدف والفائدة
✓ حدد تواريخ واقعية قابلة للتحقيق
✓ اختر الأولوية بناءً على تأثير المشكلة

**أمثلة جيدة:**
- "تحسين وقت استجابة خدمة العملاء من 10 دقائق إلى 3 دقائق"
- "تقليل أخطاء إدخال البيانات من 15 خطأ يومياً إلى أقل من 5"

### 2. Find - العثور على المشكلة
**الهدف:** تحديد وقياس المشكلة بدقة
**المدة المتوقعة:** 20-30 دقيقة

**الحقول المطلوبة:**
- وصف تفصيلي للمشكلة
- اسم المؤشر
- القيمة الحالية والمستهدفة
- اتجاه التحسن
- وحدة القياس ومصدر البيانات

**نصائح مهمة:**
✓ كن محدداً في وصف المشكلة
✓ استخدم أرقام وإحصائيات حقيقية
✓ اختر مؤشراً واحداً واضحاً
✓ تأكد من صحة اتجاه التحسن

**تجنب هذه الأخطاء:**
✗ الوصف العام مثل "الخدمة بطيئة"
✗ الخلط بين المشكلة والحل
✗ استخدام مؤشرات متعددة

### 3. Organize - تنظيم المشروع
**الهدف:** تشكيل الفريق وتحديد الموارد
**المدة المتوقعة:** 15-25 دقيقة

**الحقول المطلوبة:**
- القسم المسؤول
- قائد الفريق (الاسم والتواصل)
- أعضاء الفريق (3-7 أعضاء)
- المهام والموارد المطلوبة

**نصائح مهمة:**
✓ اختر فريقاً متنوعاً من التخصصات
✓ حدد الأدوار والمسؤوليات بوضوح
✓ تأكد من توفر الوقت الكافي
✓ اشرك ممثلين عن المستخدمين

### 4. Clarify - توضيح العمليات
**الهدف:** فهم العمليات المتأثرة
**المدة المتوقعة:** 20-25 دقيقة

**الحقول المطلوبة:**
- وصف العملية الحالية
- خريطة العملية (مرفق)
- نطاق المشكلة
- المخرجات المتأثرة

**نصائح مهمة:**
✓ اكتب الخطوات بالترتيب الزمني
✓ حدد المسؤوليات في كل خطوة
✓ اذكر المدخلات والمخرجات
✓ حدد نقاط القرار المهمة

### 5. Understand - فهم الأسباب
**الهدف:** تحليل الأسباب الجذرية
**المدة المتوقعة:** 25-35 دقيقة

**طرق التحليل المتاحة:**
- خمسة لماذا (Five Whys)
- عظمة السمكة (Fishbone)
- تحليل السبب الجذري

**نصائح مهمة:**
✓ اختر طريقة التحليل المناسبة
✓ احفر عميقاً للوصول للسبب الحقيقي
✓ اشرك الفريق في التحليل
✓ وثق جميع النتائج

### 6. Select - اختيار الحل
**الهدف:** تحديد الحل الأمثل
**المدة المتوقعة:** 20-30 دقيقة

**الحقول المطلوبة:**
- وصف الحل المختار
- المبررات والفوائد المتوقعة
- التكلفة المقدرة
- المهام والموارد اللازمة

**نصائح مهمة:**
✓ اختر حلاً واحداً فقط
✓ تأكد من قابلية التنفيذ
✓ قدر التكلفة والوقت بدقة
✓ حدد المسؤوليات بوضوح

### 7. RAID Matrix - إدارة المخاطر
**الهدف:** تحديد المخاطر والتحديات
**المدة المتوقعة:** 15-20 دقيقة

**العناصر الأربعة:**
- المخاطر (Risks): ما قد يحدث وكيفية تجنبه
- الافتراضات (Assumptions): ما نفترضه وكيفية التأكد منه
- المشكلات (Issues): المشاكل الحالية وحلولها
- الاعتماديات (Dependencies): ما ننتظره من الآخرين

## قائمة التحقق النهائية

قبل إرسال المشروع، تأكد من:
□ تم ملء جميع الحقول المطلوبة
□ البيانات دقيقة وموثقة
□ التواريخ واقعية ومتسقة
□ الفريق متفق على المحتوى
□ الموارد متاحة ومحددة
□ المخاطر محددة ولها خطط تخفيف

## نصائح عامة للنجاح

1. **خذ وقتك:** لا تتسرع في التعبئة
2. **اشرك الفريق:** اطلب آراء الزملاء
3. **استخدم البيانات:** اعتمد على الحقائق والأرقام
4. **كن واقعياً:** ضع أهدافاً قابلة للتحقيق
5. **وثق كل شيء:** احتفظ بسجل للقرارات والتغييرات

## جهات الاتصال للدعم

للحصول على مساعدة إضافية:
- فريق إدارة المشاريع: [رقم الهاتف]
- دعم النظام التقني: [رقم الهاتف]
- مكتب الجودة: [رقم الهاتف]

---
تم إنشاء هذا الدليل بواسطة نظام إدارة طلبات المشاريع
التاريخ: ${new Date().toLocaleDateString('ar-SA')}
`
  }

  const downloadGuide = () => {
    const content = generateGuideContent()
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `دليل_مشروع_التحسين_الشامل_${new Date().toISOString().split('T')[0]}.txt`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  const downloadCurrentStepGuide = () => {
    const stepGuides = {
      1: 'المعلومات الأساسية',
      2: 'العثور على المشكلة', 
      3: 'تنظيم المشروع',
      4: 'توضيح العمليات',
      5: 'فهم الأسباب',
      6: 'اختيار الحل',
      7: 'إدارة المخاطر'
    }

    const stepTitle = stepGuides[currentStep as keyof typeof stepGuides] || 'دليل عام'
    
    // محتوى مبسط للمرحلة الحالية
    const stepContent = `
# دليل المرحلة ${currentStep}: ${stepTitle}

## ما تحتاج لإنجازه في هذه المرحلة

[محتوى مخصص للمرحلة الحالية]

## نصائح سريعة
- نصيحة 1
- نصيحة 2
- نصيحة 3

## أمثلة عملية
[أمثلة محددة للمرحلة]

## أخطاء شائعة يجب تجنبها
[قائمة الأخطاء الشائعة]
`

    const blob = new Blob([stepContent], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `دليل_المرحلة_${currentStep}_${stepTitle}.txt`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center gap-3 mb-3">
          <FileText className="w-6 h-6 text-blue-600" />
          <h3 className="font-semibold text-blue-900">تحميل الأدلة المرجعية</h3>
        </div>
        <p className="text-blue-800 text-sm mb-4">
          احصل على نسخة من الدليل لمراجعتها في أي وقت أو مشاركتها مع الفريق
        </p>
        
        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            onClick={downloadGuide}
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
          >
            <Download className="w-4 h-4" />
            تحميل الدليل الكامل
          </Button>
          
          {currentStep && (
            <Button
              onClick={downloadCurrentStepGuide}
              className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
            >
              <Download className="w-4 h-4" />
              دليل المرحلة الحالية
            </Button>
          )}
        </div>
      </div>

      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <CheckCircle className="w-5 h-5 text-amber-600 mt-0.5" />
          <div>
            <h4 className="font-medium text-amber-800 mb-1">نصيحة مهمة</h4>
            <p className="text-amber-700 text-sm">
              احتفظ بنسخة من الدليل على جهازك للرجوع إليها أثناء العمل على المشروع. 
              يمكنك أيضاً مشاركتها مع أعضاء الفريق لضمان التنسيق.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
} 