'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { supabase, auth, database, systemInfo } from '@/lib/supabase'
import { CheckCircle, XCircle, Database, Settings, AlertCircle } from 'lucide-react'

interface DatabaseConnectionProps {
  onConnectionChange?: (isConnected: boolean) => void
}

export function DatabaseConnection({ onConnectionChange }: DatabaseConnectionProps) {
  const [isConnected, setIsConnected] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [connectionError, setConnectionError] = useState<string | null>(null)
  const [showSettings, setShowSettings] = useState(false)
  const [supabaseUrl, setSupabaseUrl] = useState('')
  const [supabaseKey, setSupabaseKey] = useState('')

  // فحص الاتصال بقاعدة البيانات
  const checkConnection = async () => {
    setIsLoading(true)
    setConnectionError(null)

    try {
      if (systemInfo.isDevelopmentMode) {
        // في وضع التطوير، نعتبر الاتصال متاح دائماً
        setIsConnected(true)
        onConnectionChange?.(true)
      } else {
        // فحص الاتصال الحقيقي مع Supabase
        const { data, error } = await supabase
          .from('users')
          .select('id')
          .limit(1)

        if (error) {
          setConnectionError(error.message)
          setIsConnected(false)
          onConnectionChange?.(false)
        } else {
          setIsConnected(true)
          onConnectionChange?.(true)
        }
      }
    } catch (error) {
      setConnectionError('فشل في الاتصال بقاعدة البيانات')
      setIsConnected(false)
      onConnectionChange?.(false)
    } finally {
      setIsLoading(false)
    }
  }

  // تحديث إعدادات الاتصال
  const updateConnectionSettings = () => {
    if (supabaseUrl && supabaseKey) {
      // في بيئة الإنتاج، نحتاج إلى إعادة تحميل الصفحة لتطبيق الإعدادات الجديدة
      localStorage.setItem('supabase_url', supabaseUrl)
      localStorage.setItem('supabase_key', supabaseKey)
      
      alert('تم حفظ الإعدادات. يرجى إعادة تحميل الصفحة لتطبيق التغييرات.')
    }
  }

  useEffect(() => {
    checkConnection()
    
    // تحديث حالة الاتصال كل 30 ثانية
    const interval = setInterval(checkConnection, 30000)
    
    return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    // قراءة الإعدادات المحفوظة محلياً
    const savedUrl = localStorage.getItem('supabase_url')
    const savedKey = localStorage.getItem('supabase_key')
    
    if (savedUrl) setSupabaseUrl(savedUrl)
    if (savedKey) setSupabaseKey(savedKey)
  }, [])

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <Database className="h-6 w-6 text-blue-600" />
          <h3 className="text-lg font-semibold">حالة قاعدة البيانات</h3>
        </div>
        
        <Button
          variant="default"
          size="sm"
          onClick={() => setShowSettings(!showSettings)}
          className="flex items-center gap-2"
        >
          <Settings className="h-4 w-4" />
          الإعدادات
        </Button>
      </div>

      {/* حالة الاتصال */}
      <div className="mb-4">
        {isLoading ? (
          <div className="flex items-center gap-2 text-gray-600">
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
            <span>جاري فحص الاتصال...</span>
          </div>
        ) : isConnected ? (
          <div className="flex items-center gap-2 text-green-600">
            <CheckCircle className="h-5 w-5" />
            <span>متصل بقاعدة البيانات</span>
          </div>
        ) : (
          <div className="flex items-center gap-2 text-red-600">
            <XCircle className="h-5 w-5" />
            <span>غير متصل بقاعدة البيانات</span>
          </div>
        )}
      </div>

      {/* معلومات النظام */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div className="bg-gray-50 p-3 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">الوضع الحالي</div>
          <div className="font-medium">
            {systemInfo.isDevelopmentMode ? 'وضع التطوير' : 'وضع الإنتاج'}
          </div>
        </div>
        
        <div className="bg-gray-50 p-3 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">رابط Supabase</div>
          <div className="font-medium text-xs">
            {systemInfo.supabaseUrl}
          </div>
        </div>
      </div>

      {/* رسالة الخطأ */}
      {connectionError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
          <div className="flex items-center gap-2 text-red-700">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm">{connectionError}</span>
          </div>
        </div>
      )}

      {/* إعدادات الاتصال */}
      {showSettings && (
        <div className="border-t pt-4">
          <h4 className="font-medium mb-3">إعدادات الاتصال</h4>
          
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium mb-1">
                رابط مشروع Supabase
              </label>
              <Input
                type="url"
                value={supabaseUrl}
                onChange={(e) => setSupabaseUrl(e.target.value)}
                placeholder="https://your-project.supabase.co"
                className="w-full"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">
                مفتاح Supabase العام
              </label>
              <Input
                type="password"
                value={supabaseKey}
                onChange={(e) => setSupabaseKey(e.target.value)}
                placeholder="your-anon-key"
                className="w-full"
              />
            </div>
            
            <div className="flex gap-2">
              <Button
                onClick={updateConnectionSettings}
                disabled={!supabaseUrl || !supabaseKey}
                className="flex-1"
              >
                حفظ الإعدادات
              </Button>
              
                             <Button
                 variant="default"
                 onClick={checkConnection}
                 disabled={isLoading}
               >
                فحص الاتصال
              </Button>
            </div>
          </div>
          
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <div className="text-sm text-blue-700">
              <strong>ملاحظة:</strong> لتطبيق الإعدادات الجديدة، يجب إنشاء ملف .env.local 
              في جذر المشروع مع المتغيرات التالية:
              <br />
              <code className="text-xs bg-blue-100 px-1 rounded mt-1 block">
                NEXT_PUBLIC_SUPABASE_URL=your-url<br />
                NEXT_PUBLIC_SUPABASE_ANON_KEY=your-key
              </code>
            </div>
          </div>
        </div>
      )}
    </Card>
  )
} 