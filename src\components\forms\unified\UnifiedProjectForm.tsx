'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { ChevronRight, ChevronLeft, Save, Send, HelpCircle, BookOpen } from 'lucide-react'
import { EnhancedHelpSystem } from '@/components/ui/EnhancedHelpSystem'
import { StepByStepGuide } from '@/components/ui/StepByStepGuide'


// استيراد المكونات الموحدة
import { AdaptiveBasicStep } from './unified-steps/AdaptiveBasicStep'

import { AdaptiveFindStep } from './unified-steps/AdaptiveFindStep'
import { AdaptiveOrganizeStep } from './unified-steps/AdaptiveOrganizeStep'
import { AdaptiveClarifyStep } from './unified-steps/AdaptiveClarifyStep'
import { AdaptiveUnderstandStep } from './unified-steps/AdaptiveUnderstandStep'
import { AdaptiveSelectStep } from './unified-steps/AdaptiveSelectStep'
import { AdaptiveProjectPlanningStep } from './unified-steps/AdaptiveProjectPlanningStep'
import { AdaptiveRiskManagementStep } from './unified-steps/AdaptiveRiskManagementStep'
import { AdaptiveQuickWinStep } from './unified-steps/AdaptiveQuickWinStep'

import { AdaptiveReviewStep } from './unified-steps/AdaptiveReviewStep'


// أنواع النماذج المدعومة
export type FormType = 'enhanced_improvement' | 'suggestion' | 'quick_win'

// واجهة البيانات الأساسية المشتركة
interface BaseFormData {
  // Find - مشترك 100%
  problemDescription: string
  indicatorName: string
  currentValue: number
  targetValue: number
  improvementDirection: 'increase' | 'decrease' | ''
  unit: string
  dataSource: string
  measurementMethod: string
  calculatedGap: number
  isValidDirection?: boolean
  attachments: File[]
  
  // Organize - مشترك مع مرونة
  responsibleDepartment: string
  teamLeader: {
    name: string
    phone: string
    email: string
  }
  teamMembers: Array<{
    name: string
    role: string
    phone: string
    department: string
  }>
  participatingDepartments: string[]
  
  // Clarify - مشترك 100%
  processDescription: string
  processMap: File | null
  problemScope: string
  affectedOutputs: string[]
  
  // Understand - مشترك 100%
  analysisMethod: 'five_whys' | 'fishbone' | 'root_cause_analysis'
  rootCause: string
  fiveWhysSteps: string[]
  fishboneFactors: {
    people: string[]
    process: string[]
    equipment: string[]
    environment: string[]
    materials: string[]
    measurement: string[]
  }
}

// واجهة للتحسين الشامل
export interface EnhancedImprovementData extends BaseFormData {
  // Basic Info - خاص بالتحسين الشامل
  projectName: string
  projectDescription: string
  startDate: string
  endDate: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  
  // Select - حل واحد للتحسين الشامل
  selectedSolution: {
    description: string
    justification: string
    expectedBenefits: string
    estimatedCost: number
    implementationTime: string
  }
  
  // Project Planning - تخطيط المشروع (مرحلة جديدة)
  projectTasks: Array<{
    title: string
    description: string
    assignee: string
    startDate: string
    endDate: string
    status: 'pending' | 'in_progress' | 'completed'
  }>
  requiredResources: Array<{
    type: 'human' | 'financial' | 'equipment' | 'software' | 'other'
    description: string
    quantity: number
    cost: number
    unit: string
  }>
  
  // Risk Management - إدارة المخاطر المبسطة
  risks?: Array<{
    id: string
    description: string
    probability: 'low' | 'medium' | 'high'
    impact: 'low' | 'medium' | 'high'
    mitigation?: string
  }>
}

// واجهة للمقترحات
export interface SuggestionData extends BaseFormData {
  // Select - حلول متعددة للمقترحات
  suggestedSolutions: Array<{
    id: string
    title: string
    description: string
    justification: string
    expectedBenefits: string
    feasibilityScore: number
    impactScore: number
    priority: 'low' | 'medium' | 'high'
  }>
}

// واجهة لكويك وين
export interface QuickWinData extends BaseFormData {
  // Basic Info - معلومات مبسطة لكويك وين
  projectTitle: string
  section: string
  projectExecutor: {
    name: string
    phone: string
    email: string
    startDate?: string
  }
  
  // Select - حل مبسط لكويك وين
  solution: {
    description: string
    tasks: string[]
    implementationWeeks: number // حد أقصى 4 أسابيع
    estimatedCost: number
  }
  
  // مؤشرات متقدمة لكويك وين
  advancedIndicators: Array<{
    name: string
    currentValue: number
    targetValue: number
    direction: 'increase' | 'decrease'
    unit: string
    dataSource: string
    measurementMethod: string
    calculatedGap: number
  }>
  
  // مخاطر مبسطة لكويك وين
  risks?: Array<{
    id: string
    description: string
    probability: 'low' | 'medium' | 'high'
    impact: 'low' | 'medium' | 'high'
    mitigation?: string
  }>
}

// نوع البيانات الموحد
export type UnifiedFormData = EnhancedImprovementData | SuggestionData | QuickWinData

interface UnifiedProjectFormProps {
  formType: FormType
  onSubmit: (data: UnifiedFormData) => void
  onSaveDraft: (data: UnifiedFormData) => void
  initialData?: Partial<UnifiedFormData>
  isLoading?: boolean
}

export function UnifiedProjectForm({ 
  formType,
  onSubmit, 
  onSaveDraft, 
  initialData, 
  isLoading = false 
}: UnifiedProjectFormProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [showHelpSystem, setShowHelpSystem] = useState(false)
  const [showStepGuide, setShowStepGuide] = useState(false)
  const [formData, setFormData] = useState<UnifiedFormData>(() => {
    // إنشاء البيانات الافتراضية حسب نوع النموذج
    const baseData: BaseFormData = {
      // Find
      problemDescription: '',
      indicatorName: '',
      currentValue: 0,
      targetValue: 0,
      improvementDirection: '',
      unit: '',
      dataSource: '',
      measurementMethod: '',
      calculatedGap: 0,
      attachments: [],
      
      // Organize
      responsibleDepartment: '',
      teamLeader: { name: '', phone: '', email: '' },
      teamMembers: [],
      participatingDepartments: [],
      
      // Clarify
      processDescription: '',
      processMap: null,
      problemScope: '',
      affectedOutputs: [],
      
      // Understand
      analysisMethod: 'five_whys',
      rootCause: '',
      fiveWhysSteps: ['', '', '', '', ''],
      fishboneFactors: {
        people: [],
        process: [],
        equipment: [],
        environment: [],
        materials: [],
        measurement: []
      }
    }

    switch (formType) {
      case 'enhanced_improvement':
        return {
          ...baseData,
          // Basic Info
          projectName: '',
          projectDescription: '',
          startDate: '',
          endDate: '',
          priority: 'medium',
          
          // Select
          selectedSolution: {
            description: '',
            justification: '',
            expectedBenefits: '',
            estimatedCost: 0,
            implementationTime: ''
          },
          
          // Project Planning
          projectTasks: [],
          requiredResources: [],
          
          // Risk Management
          risks: []
        } as EnhancedImprovementData

      case 'suggestion':
        return {
          ...baseData,
          // Select
          suggestedSolutions: [],
          
          // Finalize
          recommendations: '',
          nextSteps: []
        } as SuggestionData

      case 'quick_win':
        return {
          ...baseData,
          // Basic Info
          projectTitle: '',
          section: '',
          projectExecutor: { name: '', phone: '', email: '' },
          
          // Select
          solution: {
            description: '',
            tasks: [],
            implementationWeeks: 1,
            estimatedCost: 0
          },
          
          // Advanced Indicators
          advancedIndicators: [],
          
          // مخاطر مبسطة
          risks: []
        } as QuickWinData

      default:
        return baseData as UnifiedFormData
    }
  })

  // تحديث البيانات من initialData
  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({ ...prev, ...initialData }))
    }
  }, [initialData])

  // حساب الفجوة تلقائياً
  useEffect(() => {
    if ('currentValue' in formData && 'targetValue' in formData) {
      const gap = Math.abs(formData.targetValue - formData.currentValue)
      if (formData.calculatedGap !== gap) {
        setFormData(prev => ({ ...prev, calculatedGap: gap }))
      }
    }
  }, [formData.currentValue, formData.targetValue])

  // تحديد المراحل حسب نوع النموذج
  const getSteps = () => {
    if (formType === 'enhanced_improvement') {
      // التحسين الشامل - 9 مراحل
      return [
        { number: 1, title: 'Basic', description: 'المعلومات الأساسية' },
        { number: 2, title: 'Find', description: 'العثور على المشكلة' },
        { number: 3, title: 'Organize', description: 'تنظيم الفريق' },
        { number: 4, title: 'Clarify', description: 'توضيح العمليات' },
        { number: 5, title: 'Understand', description: 'فهم الأسباب' },
        { number: 6, title: 'Select', description: 'اختيار الحل' },
        { number: 7, title: 'Planning', description: 'تخطيط المشروع' },
        { number: 8, title: 'RiskManagement', description: 'إدارة المخاطر' },
        { number: 9, title: 'Review', description: 'المراجعة والإرسال' }
      ]
    } else if (formType === 'suggestion') {
      // المقترحات - 6 مراحل
      return [
        { number: 1, title: 'Find', description: 'العثور على المشكلة' },
        { number: 2, title: 'Organize', description: 'تنظيم الفريق' },
        { number: 3, title: 'Clarify', description: 'توضيح العمليات' },
        { number: 4, title: 'Understand', description: 'فهم الأسباب' },
        { number: 5, title: 'Select', description: 'اقتراح الحلول' },
        { number: 6, title: 'Review', description: 'المراجعة والإرسال' }
      ]
    } else {
      // كويك وين - مرحلتين فقط
      return [
        { number: 1, title: 'QuickWin', description: 'النموذج الموحد' },
        { number: 2, title: 'Review', description: 'المراجعة والإرسال' }
      ]
    }
  }

  const steps = getSteps()
  const totalSteps = steps.length

  const updateFormData = (field: string, value: unknown) => {
    // التعامل مع المسارات المتداخلة مثل projectExecutor.name
    if (field.includes('.')) {
      const parts = field.split('.')
      const mainField = parts[0]
      const nestedField = parts[1]
      
      setFormData(prev => ({
        ...prev,
        [mainField]: {
          ...prev[mainField as keyof typeof prev],
          [nestedField]: value
        }
      }))
    } else {
      // التعامل مع الحقول العادية
      setFormData(prev => ({ ...prev, [field]: value }))
    }
  }

  const validateCurrentStep = (): boolean => {
    const stepTitle = steps[currentStep - 1]?.title

    switch (stepTitle) {
      case 'Basic':
        if (formType === 'enhanced_improvement') {
          const data = formData as EnhancedImprovementData
          return !!(data.projectName && data.projectDescription)
        } else if (formType === 'quick_win') {
          const data = formData as QuickWinData
          return !!(data.projectTitle && data.section)
        }
        return true

      case 'QuickWin':
        if (formType === 'quick_win') {
          const data = formData as QuickWinData
          return !!(
            data.projectTitle && data.projectTitle.trim().length > 5 &&
            data.section &&
            data.solution && data.solution.description && data.solution.description.trim().length > 10 &&
            data.indicatorName && data.indicatorName.trim().length > 3 &&
            data.currentValue !== undefined && data.targetValue !== undefined &&
            data.problemDescription && data.problemDescription.trim().length > 10
          )
        }
        return true

      case 'Find':
        return !!(
          formData.problemDescription && formData.problemDescription.trim().length > 20 &&
          formData.indicatorName && formData.indicatorName.trim().length > 3 &&
          formData.currentValue !== undefined && formData.targetValue !== undefined &&
          formData.unit && formData.improvementDirection
        )

      case 'Organize':
        return !!(
          formData.responsibleDepartment &&
          formData.teamLeader.name && formData.teamLeader.name.trim().length > 2 &&
          formData.teamLeader.phone && formData.teamLeader.email
        )

      case 'Clarify':
        return !!(
          formData.processDescription && formData.processDescription.trim().length > 20 &&
          formData.problemScope && formData.problemScope.trim().length > 10
        )

      case 'Understand':
        return !!(formData.rootCause && formData.rootCause.trim().length > 15)

      case 'Select':
        if (formType === 'enhanced_improvement') {
          const data = formData as EnhancedImprovementData
          return !!(data.selectedSolution.description)
        } else if (formType === 'suggestion') {
          const data = formData as SuggestionData
          return data.suggestedSolutions.length > 0
        } else if (formType === 'quick_win') {
          const data = formData as QuickWinData
          return !!(data.solution.description)
        }
        return true

      case 'Planning':
        if (formType === 'enhanced_improvement') {
          const data = formData as EnhancedImprovementData
          return data.projectTasks.length > 0
        }
        return true

      case 'RiskManagement':
        return true // مرحلة اختيارية

      case 'Review':
        return true // مرحلة المراجعة النهائية

      default:
        return true
    }
  }

  const handleNext = () => {
    if (validateCurrentStep() && currentStep < totalSteps) {
      setCurrentStep(currentStep + 1)
    } else if (!validateCurrentStep()) {
      // عرض رسالة خطأ تفصيلية
      const stepTitle = steps[currentStep - 1]?.title
      let errorMessage = 'يرجى إكمال جميع الحقول المطلوبة في هذه المرحلة'
      
      if (stepTitle === 'Find') {
        errorMessage = 'يرجى إكمال وصف المشكلة (20 حرف على الأقل)، اسم المؤشر، القيم الحالية والمستهدفة، الوحدة، واتجاه التحسن'
      } else if (stepTitle === 'Organize') {
        errorMessage = 'يرجى تحديد القسم المسؤول وإدخال بيانات قائد الفريق كاملة (الاسم، الجوال، الإيميل)'
      } else if (stepTitle === 'Clarify') {
        errorMessage = 'يرجى كتابة وصف العملية (20 حرف على الأقل) ونطاق المشكلة (10 أحرف على الأقل)'
      } else if (stepTitle === 'Understand') {
        errorMessage = 'يرجى تحديد السبب الجذري للمشكلة (15 حرف على الأقل)'
      } else if (stepTitle === 'QuickWin') {
        errorMessage = 'يرجى إكمال عنوان المشروع، القسم، وصف المشكلة والحل، اسم المؤشر والقيم الحالية والمستهدفة'
      }
      
      alert(errorMessage)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = () => {
    if (validateCurrentStep()) {
      onSubmit(formData)
    }
  }

  const handleSaveDraft = () => {
    onSaveDraft(formData)
  }

  const renderStepContent = () => {
    const stepTitle = steps[currentStep - 1]?.title

    switch (stepTitle) {
      case 'Basic':
        return (
          <AdaptiveBasicStep
            formType={formType}
            data={formData}
            updateData={updateFormData}
            errors={{}}
          />
        )

      case 'QuickWin':
        return (
          <AdaptiveQuickWinStep
            data={formData as QuickWinData}
            updateData={updateFormData}
            errors={{}}
            formType={formType}
          />
        )

      case 'Find':
        return (
          <AdaptiveFindStep
            formType={formType}
            data={formData}
            updateData={updateFormData}
            errors={{}}
          />
        )

      case 'Organize':
        return (
          <AdaptiveOrganizeStep
            formType={formType}
            data={formData}
            updateData={updateFormData}
            errors={{}}
          />
        )

      case 'Clarify':
        return (
          <AdaptiveClarifyStep
            formType={formType}
            data={formData}
            updateData={updateFormData}
            errors={{}}
          />
        )

      case 'Understand':
        return (
          <AdaptiveUnderstandStep
            formType={formType}
            data={formData}
            updateData={updateFormData}
            errors={{}}
          />
        )

      case 'Select':
        return (
          <AdaptiveSelectStep
            formType={formType}
            data={formData}
            updateData={updateFormData}
            errors={{}}
          />
        )

      case 'Planning':
        return (
          <AdaptiveProjectPlanningStep
            formType={formType}
            data={formData}
            updateData={updateFormData}
            errors={{}}
          />
        )

      case 'RiskManagement':
        return (
          <AdaptiveRiskManagementStep
            formType={formType}
            data={formData}
            updateData={updateFormData}
            errors={{}}
          />
        )

      case 'Review':
        return (
          <AdaptiveReviewStep
            formType={formType}
            data={formData}
            updateData={updateFormData}
            errors={{}}
          />
        )

      case 'QuickWin':
        // لم يعد مطلوباً - تم استبداله بالنظام متعدد المراحل
        return <div>مرحلة غير مستخدمة</div>

      default:
        return <div>مرحلة غير معروفة</div>
    }
  }

  const getFormTypeTitle = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'مشروع التحسين الشامل'
      case 'suggestion':
        return 'مقترح تحسيني'
      case 'quick_win':
        return 'كويك وين'
      default:
        return 'نموذج مشروع'
    }
  }

  return (
    <div className="max-w-5xl mx-auto p-4 md:p-6">
      <Card className="bg-white shadow-lg border-0 md:border">
        {/* Header */}
        <div className="border-b border-gray-200 p-4 md:p-6">
          <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
            <div className="flex-1">
              <h1 className="text-xl md:text-2xl font-bold text-gray-900">
                {getFormTypeTitle()} - نموذج موحد
              </h1>
              <p className="text-gray-600 mt-1 text-sm md:text-base">
                المرحلة {currentStep} من {totalSteps}: {steps[currentStep - 1]?.description}
              </p>
            </div>
            <div className="flex gap-2 flex-wrap">
              <Button
                onClick={() => setShowStepGuide(true)}
                variant="secondary"
                size="sm"
                className="flex-1 md:flex-none"
              >
                <BookOpen className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">دليل المراحل</span>
                <span className="sm:hidden">دليل</span>
              </Button>
              <Button
                onClick={() => setShowHelpSystem(true)}
                variant="secondary"
                size="sm"
                className="flex-1 md:flex-none"
              >
                <HelpCircle className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">مساعدة</span>
                <span className="sm:hidden">مساعدة</span>
              </Button>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mt-4">
            <div className="flex justify-between text-xs text-gray-500 mb-2">
              <span>المرحلة {currentStep}</span>
              <span>{Math.round((currentStep / totalSteps) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(currentStep / totalSteps) * 100}%` }}
              />
            </div>
          </div>

          {/* Steps Navigation */}
          <div className="mt-4 flex flex-wrap gap-1 md:gap-2">
            {steps.map((step, index) => (
              <button
                key={step.number}
                onClick={() => setCurrentStep(index + 1)}
                className={`px-2 md:px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                  currentStep === index + 1
                    ? 'bg-blue-600 text-white'
                    : currentStep > index + 1
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-600'
                }`}
                title={step.description}
              >
                <span className="hidden sm:inline">{step.title}</span>
                <span className="sm:hidden">{step.number}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-4 md:p-6">
          {renderStepContent()}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-4 md:p-6">
          <div className="flex flex-col md:flex-row md:justify-between gap-4">
            <Button
              onClick={handlePrevious}
              disabled={currentStep === 1}
              variant="secondary"
              className="order-2 md:order-1"
            >
              <ChevronLeft className="w-4 h-4 mr-2" />
              السابق
            </Button>

            <div className="flex gap-3 order-1 md:order-2">
              <Button
                onClick={handleSaveDraft}
                variant="secondary"
                disabled={isLoading}
              >
                <Save className="w-4 h-4 mr-2" />
                حفظ مسودة
              </Button>

              {currentStep < totalSteps ? (
                <Button
                  onClick={handleNext}
                  disabled={!validateCurrentStep() || isLoading}
                  className={!validateCurrentStep() ? 'opacity-50 cursor-not-allowed' : ''}
                >
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      جاري المعالجة...
                    </>
                  ) : (
                    <>
                      التالي
                      <ChevronRight className="w-4 h-4 ml-2" />
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  onClick={handleSubmit}
                  disabled={!validateCurrentStep() || isLoading}
                  className={`bg-green-600 hover:bg-green-700 ${!validateCurrentStep() || isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      جاري الإرسال...
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4 mr-2" />
                      إرسال الطلب
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Help Systems */}
      {showHelpSystem && (
        <EnhancedHelpSystem 
          onClose={() => setShowHelpSystem(false)} 
          currentStep={currentStep}
        />
      )}
      
      {showStepGuide && (
        <StepByStepGuide 
          currentStep={currentStep}
          onClose={() => setShowStepGuide(false)}
        />
      )}
    </div>
  )
} 