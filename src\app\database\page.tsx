'use client'

import { useState } from 'react'
import { DatabaseConnection } from '@/components/database/DatabaseConnection'
import { MigrationManager } from '@/components/database/MigrationManager'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Typography } from '@/components/ui/Typography'
import { 
  Database, 
  Table, 
  Users, 
  FileText, 
  Settings, 
  RefreshCw,
  Download,
  Upload
} from 'lucide-react'

export default function DatabasePage() {
  const [isConnected, setIsConnected] = useState(false)
  const [activeTab, setActiveTab] = useState('connection')

  const tabs = [
    { id: 'connection', label: 'الاتصال', icon: Database },
    { id: 'tables', label: 'الجداول', icon: Table },
    { id: 'users', label: 'المستخدمين', icon: Users },
    { id: 'migrations', label: 'الهجرة', icon: FileText },
    { id: 'settings', label: 'الإعدادات', icon: Settings }
  ]

  const handleConnectionChange = (connected: boolean) => {
    setIsConnected(connected)
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-6">
        <Typography variant="main" className="mb-2">
          إدارة قاعدة البيانات
        </Typography>
        <Typography variant="body" className="text-gray-600">
          إدارة الاتصال بقاعدة البيانات والجداول والمستخدمين
        </Typography>
      </div>

      {/* شريط التبويب */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  {tab.label}
                </button>
              )
            })}
          </nav>
        </div>
      </div>

      {/* محتوى التبويب */}
      <div className="space-y-6">
        {activeTab === 'connection' && (
          <DatabaseConnection onConnectionChange={handleConnectionChange} />
        )}

        {activeTab === 'tables' && (
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">جداول قاعدة البيانات</h3>
              <Button
                variant="primary"
                size="sm"
                disabled={!isConnected}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                تحديث
              </Button>
            </div>
            
            {!isConnected ? (
              <div className="text-center py-8 text-gray-500">
                يجب الاتصال بقاعدة البيانات أولاً لعرض الجداول
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  { name: 'users', description: 'بيانات المستخدمين', count: '3' },
                  { name: 'project_requests', description: 'طلبات المشاريع', count: '0' },
                  { name: 'projects', description: 'المشاريع', count: '0' },
                  { name: 'departments', description: 'الأقسام', count: '7' },
                  { name: 'roles', description: 'الأدوار', count: '6' },
                  { name: 'approvals', description: 'الموافقات', count: '0' }
                ].map((table) => (
                  <div key={table.name} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{table.name}</h4>
                      <span className="text-sm text-gray-500">{table.count} سجل</span>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{table.description}</p>
                    <Button variant="ghost" size="sm" className="w-full">
                      عرض البيانات
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </Card>
        )}

        {activeTab === 'users' && (
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">إدارة المستخدمين</h3>
              <div className="flex gap-2">
                <Button
                  variant="primary"
                  size="sm"
                  disabled={!isConnected}
                  className="flex items-center gap-2"
                >
                  <Users className="h-4 w-4" />
                  إضافة مستخدم
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  disabled={!isConnected}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  تحديث
                </Button>
              </div>
            </div>
            
            {!isConnected ? (
              <div className="text-center py-8 text-gray-500">
                يجب الاتصال بقاعدة البيانات أولاً لإدارة المستخدمين
              </div>
            ) : (
              <div className="space-y-4">
                {[
                  { id: '1', name: 'مدير النظام', email: '<EMAIL>', role: 'admin', status: 'active' },
                  { id: '2', name: 'مدير مكتب إدارة المشاريع', email: '<EMAIL>', role: 'pmo_manager', status: 'active' },
                  { id: '3', name: 'موظف', email: '<EMAIL>', role: 'employee', status: 'active' }
                ].map((user) => (
                  <div key={user.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{user.name}</h4>
                        <p className="text-sm text-gray-600">{user.email}</p>
                        <span className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full mt-1">
                          {user.role}
                        </span>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm">
                          تعديل
                        </Button>
                        <Button variant="danger" size="sm">
                          حذف
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </Card>
        )}

        {activeTab === 'migrations' && (
          <MigrationManager />
        )}

        {activeTab === 'settings' && (
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">إعدادات قاعدة البيانات</h3>
            
            <div className="space-y-6">
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">النسخ الاحتياطي</h4>
                <p className="text-sm text-gray-600 mb-3">
                  إنشاء نسخة احتياطية من قاعدة البيانات
                </p>
                <Button variant="primary" size="sm" disabled={!isConnected}>
                  إنشاء نسخة احتياطية
                </Button>
              </div>
              
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">استعادة البيانات</h4>
                <p className="text-sm text-gray-600 mb-3">
                  استعادة البيانات من نسخة احتياطية
                </p>
                <Button variant="warning" size="sm" disabled={!isConnected}>
                  استعادة البيانات
                </Button>
              </div>
              
              <div className="border rounded-lg p-4">
                <h4 className="font-medium mb-2">إعادة تعيين قاعدة البيانات</h4>
                <p className="text-sm text-gray-600 mb-3">
                  حذف جميع البيانات وإعادة إنشاء الجداول
                </p>
                <Button variant="danger" size="sm" disabled={!isConnected}>
                  إعادة تعيين
                </Button>
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  )
} 