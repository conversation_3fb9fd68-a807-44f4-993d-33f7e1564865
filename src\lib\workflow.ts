import { supabase } from './supabase'
import { Database } from '@/types/database.types'

type ProjectRequest = Database['public']['Tables']['project_requests']['Row']
type ProjectRequestInsert = Database['public']['Tables']['project_requests']['Insert']

export interface WorkflowStep {
  level: number
  role: string
  roleName: string
  status: 'pending' | 'approved' | 'rejected'
  approvedAt?: string
  notes?: string
}

export class WorkflowManager {
  // إنشاء طلب جديد
  static async createRequest(requestData: ProjectRequestInsert): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('project_requests')
        .insert({
          ...requestData,
          status: 'draft',
          approval_level: 0
        })
        .select()
        .single()

      if (error) throw error

      return { success: true, data }
    } catch (error: any) {
      console.error('Error creating request:', error)
      return { success: false, error: error.message }
    }
  }

  // إرسال الطلب للمراجعة
  static async submitRequest(requestId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // تحديث حالة الطلب إلى submitted
      const { error: updateError } = await supabase
        .from('project_requests')
        .update({ 
          status: 'submitted',
          approval_level: 1
        })
        .eq('id', requestId)

      if (updateError) throw updateError

      // إنشاء موافقة للمستوى الأول (PMO Manager)
      const { error: approvalError } = await supabase
        .from('approvals')
        .insert({
          request_id: requestId,
          approver_id: await this.getPMOManagerId(),
          status: 'pending'
        })

      if (approvalError) throw approvalError

      return { success: true }
    } catch (error: any) {
      console.error('Error submitting request:', error)
      return { success: false, error: error.message }
    }
  }

  // اعتماد أو رفض الطلب
  static async processApproval(
    requestId: string, 
    approverId: string, 
    decision: 'approved' | 'rejected',
    notes?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // الحصول على تفاصيل الطلب
      const { data: request, error: requestError } = await supabase
        .from('project_requests')
        .select('*')
        .eq('id', requestId)
        .single()

      if (requestError) throw requestError

      // تحديث الموافقة الحالية
      const { error: approvalError } = await supabase
        .from('approvals')
        .update({
          status: decision,
          notes,
          approved_at: new Date().toISOString()
        })
        .eq('request_id', requestId)
        .eq('approver_id', approverId)

      if (approvalError) throw approvalError

      if (decision === 'rejected') {
        // رفض الطلب
        await supabase
          .from('project_requests')
          .update({ status: 'rejected' })
          .eq('id', requestId)
      } else {
        // الموافقة - تحديد الخطوة التالية
        const requiredLevels = this.getRequiredApprovalLevels(request.main_type, request.sub_type)
        const nextLevel = (request.approval_level || 0) + 1

        if (nextLevel > requiredLevels) {
          // اعتماد نهائي
          await supabase
            .from('project_requests')
            .update({ status: 'approved' })
            .eq('id', requestId)
        } else {
          // إنشاء موافقة للمستوى التالي
          const nextApproverId = await this.getApproverByLevel(nextLevel)
          
          await supabase
            .from('project_requests')
            .update({ 
              status: 'under_review',
              approval_level: nextLevel
            })
            .eq('id', requestId)

          await supabase
            .from('approvals')
            .insert({
              request_id: requestId,
              approver_id: nextApproverId,
              status: 'pending'
            })
        }
      }

      return { success: true }
    } catch (error: any) {
      console.error('Error processing approval:', error)
      return { success: false, error: error.message }
    }
  }

  // الحصول على خطوات سير العمل للطلب
  static async getWorkflowSteps(requestId: string): Promise<WorkflowStep[]> {
    try {
      const { data: request } = await supabase
        .from('project_requests')
        .select('main_type, sub_type, approval_level')
        .eq('id', requestId)
        .single()

      if (!request) return []

      const { data: approvals } = await supabase
        .from('approvals')
        .select(`
          *,
          approver:users(name, role:roles(display_name))
        `)
        .eq('request_id', requestId)
        .order('created_at')

      const requiredLevels = this.getRequiredApprovalLevels(request.main_type, request.sub_type)
      const steps: WorkflowStep[] = []

      for (let level = 1; level <= requiredLevels; level++) {
        const approval = approvals?.find(a => 
          this.getApproverLevelByRole(a.approver.role.display_name) === level
        )

        steps.push({
          level,
          role: this.getRoleByLevel(level),
          roleName: this.getRoleNameByLevel(level),
          status: approval ? (approval.status as 'pending' | 'approved' | 'rejected') : 'pending',
          approvedAt: approval?.approved_at || undefined,
          notes: approval?.notes || undefined
        })
      }

      return steps
    } catch (error) {
      console.error('Error getting workflow steps:', error)
      return []
    }
  }

  // دوال مساعدة
  private static getRequiredApprovalLevels(mainType: string, subType: string | null): number {
    if (mainType === 'general_project') return 3
    
    if (mainType === 'improvement_project') {
      switch (subType) {
        case 'quick_win': return 1
        case 'suggestion': return 2
        case 'improvement_full': return 3
        default: return 1
      }
    }
    
    return 1
  }

  private static getRoleByLevel(level: number): string {
    switch (level) {
      case 1: return 'pmo_manager'
      case 2: return 'planning_manager'
      case 3: return 'executive_manager'
      default: return 'unknown'
    }
  }

  private static getRoleNameByLevel(level: number): string {
    switch (level) {
      case 1: return 'مدير مكتب المشاريع'
      case 2: return 'مدير إدارة التخطيط'
      case 3: return 'المدير التنفيذي'
      default: return 'غير محدد'
    }
  }

  private static getApproverLevelByRole(roleName: string): number {
    switch (roleName) {
      case 'مدير مكتب المشاريع': return 1
      case 'مدير إدارة التخطيط': return 2
      case 'المدير التنفيذي': return 3
      default: return 0
    }
  }

  private static async getPMOManagerId(): Promise<string> {
    // في التطبيق الحقيقي، نحصل على ID من قاعدة البيانات
    // هنا نستخدم ID وهمي للتطوير
    return 'pmo-manager-id'
  }

  private static async getApproverByLevel(level: number): Promise<string> {
    // في التطبيق الحقيقي، نحصل على ID من قاعدة البيانات
    switch (level) {
      case 1: return 'pmo-manager-id'
      case 2: return 'planning-manager-id'
      case 3: return 'executive-manager-id'
      default: return 'unknown-id'
    }
  }
}