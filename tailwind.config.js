/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        'sans': ['Readex Pro', 'sans-serif'],
        'arabic': ['Readex Pro', 'sans-serif'],
        'readex': ['Readex Pro', 'sans-serif'],
        'cairo': ['Cairo', 'sans-serif'],
      },
      fontSize: {
        'xs': ['9px', { lineHeight: '13px' }],      // أصغر حجم - للتفاصيل الدقيقة - مصغر بنسبة 10%
        'sm': ['11px', { lineHeight: '15px' }],     // صغير - للنصوص الثانوية والتسميات - مصغر بنسبة 10%
        'base': ['13px', { lineHeight: '18px' }],   // الحجم الأساسي - للنصوص العادية - مصغر بنسبة 10%
        'lg': ['14px', { lineHeight: '22px' }],     // كبير - للعناوين الفرعية - مصغر بنسبة 10%
        'xl': ['16px', { lineHeight: '25px' }],     // أكبر - للعناوين الرئيسية - مصغر بنسبة 10%
        // أحجام محسنة للداشبورد - مصغرة بنسبة 10%
      },
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        secondary: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        },
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        danger: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      borderRadius: {
        'xl': '1rem',
        '2xl': '1.5rem',
      },
    },
  },
  plugins: [],
} 