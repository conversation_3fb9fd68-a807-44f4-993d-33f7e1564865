import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'

// GET - جلب تفاصيل طلب واحد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { data, error } = await supabaseAdmin
      .from('project_requests')
      .select(`
        *,
        requester:users!requester_id(
          id,
          name,
          email
        ),
        department:departments!department_id(
          id,
          name,
          manager_name
        )
      `)
      .eq('id', params.id)
      .single()

    if (error) {
      console.error('Error fetching request:', error)
      return NextResponse.json(
        { success: false, error: 'فشل في جلب الطلب' },
        { status: 500 }
      )
    }

    if (!data) {
      return NextResponse.json(
        { success: false, error: 'الطلب غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data
    })

  } catch (error) {
    console.error('Error in GET /api/requests/[id]:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// PUT - تحديث تفاصيل الطلب
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    
    const { data, error } = await supabaseAdmin
      .from('project_requests')
      .update({
        title: body.title,
        description: body.description,
        priority: body.priority,
        expected_start_date: body.expected_start_date,
        expected_end_date: body.expected_end_date,
        estimated_budget: body.estimated_budget,
        business_case: body.business_case,
        expected_benefits: body.expected_benefits,
        risks_and_challenges: body.risks_and_challenges,
        form_data: body.form_data,
        attachments: body.attachments,
        updated_at: new Date().toISOString()
      })
      .eq('id', params.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating request:', error)
      return NextResponse.json(
        { success: false, error: 'فشل في تحديث الطلب' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data
    })

  } catch (error) {
    console.error('Error in PUT /api/requests/[id]:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// DELETE - حذف الطلب
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // التحقق من وجود الطلب أولاً
    const { data: existingRequest, error: fetchError } = await supabaseAdmin
      .from('project_requests')
      .select('id, status')
      .eq('id', params.id)
      .single()

    if (fetchError || !existingRequest) {
      return NextResponse.json(
        { success: false, error: 'الطلب غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من أن الطلب يمكن حذفه (لم يتم اعتماده بعد)
    if (existingRequest.status === 'approved' || existingRequest.status === 'converted_to_project') {
      return NextResponse.json(
        { success: false, error: 'لا يمكن حذف طلب معتمد أو محول لمشروع' },
        { status: 400 }
      )
    }

    // حذف الموافقات المرتبطة أولاً
    await supabaseAdmin
      .from('approvals')
      .delete()
      .eq('request_id', params.id)

    // حذف الطلب
    const { error: deleteError } = await supabaseAdmin
      .from('project_requests')
      .delete()
      .eq('id', params.id)

    if (deleteError) {
      console.error('Error deleting request:', deleteError)
      return NextResponse.json(
        { success: false, error: 'فشل في حذف الطلب' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الطلب بنجاح'
    })

  } catch (error) {
    console.error('Error in DELETE /api/requests/[id]:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
} 