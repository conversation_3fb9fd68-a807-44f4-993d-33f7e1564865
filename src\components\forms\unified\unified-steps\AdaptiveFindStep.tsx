'use client'

import React from 'react'
import { Textarea } from '@/components/ui/Input'
import { FieldHelp } from '@/components/ui/HelpSystem'
import { Search } from 'lucide-react'
import { FormType, UnifiedFormData } from '../UnifiedProjectForm'
import { StepHeader } from '../../shared/StepHeader'
import { IndicatorSection } from '../../shared/IndicatorSection'

interface AdaptiveFindStepProps {
  formType: FormType
  data: UnifiedFormData
  updateData: (field: string, value: any) => void
  errors: Record<string, string>
}

export function AdaptiveFindStep({ formType, data, updateData, errors }: AdaptiveFindStepProps) {
  
  const getStepTitle = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'Find - العثور على المشكلة'
      case 'suggestion':
        return 'Find - العثور على المشكلة/الفرصة'
      case 'quick_win':
        return 'Find - تحديد الفرصة السريعة'
      default:
        return 'Find - العثور على المشكلة'
    }
  }

  const getStepDescription = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'حدد المشكلة بدقة من خلال وصف تفصيلي وقياس كمي واضح للوضع الحالي والمستهدف'
      case 'suggestion':
        return 'حدد المشكلة أو الفرصة التحسينية التي تريد اقتراح حلول لها'
      case 'quick_win':
        return 'حدد الفرصة السريعة التي يمكن تحسينها في 4 أسابيع أو أقل'
      default:
        return 'حدد المشكلة بدقة'
    }
  }

  // إعداد بيانات المؤشر للمكون المشترك
  const indicatorData = {
    indicatorName: data.indicatorName || '',
    improvementDirection: data.improvementDirection || '',
    currentValue: data.currentValue || 0,
    targetValue: data.targetValue || 0,
    unit: data.unit || '',
    calculatedGap: data.calculatedGap,
    isValidDirection: data.isValidDirection
  }

  return (
    <div className="space-y-6">
      <StepHeader
        icon={Search}
        title={getStepTitle()}
        description={getStepDescription()}
        formType={formType}
        stepNumber={2}
      />

      <div className="space-y-6">
        {/* وصف المشكلة */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <label className="block text-sm font-medium text-gray-700">
              وصف المشكلة بالتفصيل *
            </label>
            <FieldHelp 
              content={formType === 'quick_win' 
                ? "اكتب وصفاً مختصراً للمشكلة أو الفرصة التي يمكن حلها سريعاً"
                : "اكتب وصفاً دقيقاً وشاملاً للمشكلة، متى تحدث، وما تأثيرها على العمل"
              }
              field="problemDescription"
              step={2}
            />
          </div>
          <Textarea
            value={data.problemDescription}
            onChange={(e) => updateData('problemDescription', e.target.value)}
            placeholder={
              formType === 'quick_win'
                ? "مثال: بطء في الرد على استفسارات العملاء عبر الهاتف..."
                : "مثال: تأخير في معالجة طلبات العملاء يؤدي إلى انخفاض رضا العملاء وزيادة الشكاوى..."
            }
            rows={formType === 'quick_win' ? 3 : 4}
            error={errors.problemDescription}
            maxLength={formType === 'quick_win' ? 500 : 1000}
          />
          <div className="text-xs text-gray-500 mt-1">
            {data.problemDescription.length}/{formType === 'quick_win' ? 500 : 1000} حرف
          </div>
        </div>

        {/* قسم المؤشرات باستخدام المكون المشترك */}
        <IndicatorSection
          data={indicatorData}
          onUpdate={updateData}
          errors={errors}
          formType={formType}
        />
      </div>
    </div>
  )
} 