'use client'

import React from 'react'
import { Select } from '@/components/ui/Input'
import { FieldHelp } from '@/components/ui/HelpSystem'
import { Users, Building } from 'lucide-react'
import { FormType, UnifiedFormData } from '../UnifiedProjectForm'
import { StepHeader } from '../../shared/StepHeader'
import { TeamManagement, TeamLeader } from '../../shared/TeamManagement'

interface AdaptiveOrganizeStepProps {
  formType: FormType
  data: UnifiedFormData
  updateData: (field: string, value: any) => void
  errors: Record<string, string>
}

export function AdaptiveOrganizeStep({ formType, data, updateData, errors }: AdaptiveOrganizeStepProps) {
  const departments = [
    'إدارة الجودة', 'الشؤون الطبية', 'التمريض', 'المختبرات',
    'الأشعة', 'الصيدلة', 'خدمة العملاء', 'الموارد البشرية',
    'تقنية المعلومات', 'الشؤون المالية', 'الخدمات اللوجستية'
  ]

  // إدارة أعضاء الفريق
  const addTeamMember = () => {
    const newMember = { name: '', role: '', phone: '', department: '' }
    updateData('teamMembers', [...data.teamMembers, newMember])
  }

  const removeTeamMember = (index: number) => {
    const updated = data.teamMembers.filter((_, i) => i !== index)
    updateData('teamMembers', updated)
  }

  const updateTeamMember = (index: number, field: string, value: string) => {
    const updated = data.teamMembers.map((member, i) => 
      i === index ? { ...member, [field]: value } : member
    )
    updateData('teamMembers', updated)
  }

  // تحديث قائد الفريق
  const updateTeamLeader = (field: string, value: string) => {
    updateData('teamLeader', { 
      ...data.teamLeader, 
      [field]: value 
    })
  }

  const getStepTitle = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'Organize - تنظيم الفريق'
      case 'suggestion':
        return 'Organize - تنظيم الفريق'
      case 'quick_win':
        return 'Organize - تنظيم الفريق'
      default:
        return 'Organize - تنظيم الفريق'
    }
  }

  const getStepDescription = () => {
    switch (formType) {
      case 'enhanced_improvement':
        return 'حدد الفريق المسؤول عن المشروع - التركيز على تنظيم الفريق فقط، أما المهام والموارد فستكون في مرحلة التخطيط'
      case 'suggestion':
        return 'حدد الفريق الذي سيقوم بدراسة وتقييم المقترحات'
      case 'quick_win':
        return 'حدد الفريق المسؤول عن تنفيذ الحل السريع'
      default:
        return 'حدد الفريق المسؤول'
    }
  }

  return (
    <div className="space-y-6">
      <StepHeader
        icon={Users}
        title={getStepTitle()}
        description={getStepDescription()}
        formType={formType}
        stepNumber={3}
      />

      <div className="space-y-6">
        {/* القسم المسؤول */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Building className="w-4 h-4 text-gray-500" />
            <label className="block text-sm font-medium text-gray-700">
              القسم المسؤول *
            </label>
            <FieldHelp 
              content="القسم الذي ستتم فيه عملية التحسين"
              field="responsibleDepartment"
              step={3}
            />
          </div>
          <Select
            value={data.responsibleDepartment}
            onChange={(e) => updateData('responsibleDepartment', e.target.value)}
            className="w-full"
            error={errors.responsibleDepartment}
          >
            <option value="">اختر القسم</option>
            {departments.map(dept => (
              <option key={dept} value={dept}>{dept}</option>
            ))}
          </Select>
        </div>

        {/* قائد الفريق */}
        <TeamLeader
          teamLeader={{
            name: data.teamLeader.name,
            phone: data.teamLeader.phone,
            email: data.teamLeader.email,
            department: data.responsibleDepartment // استخدام القسم المسؤول
          }}
          onUpdate={(field, value) => {
            if (field === 'department') {
              updateData('responsibleDepartment', value)
            } else {
              updateTeamLeader(field, value)
            }
          }}
          errors={errors}
          departments={departments}
        />

        {/* أعضاء الفريق */}
        <TeamManagement
          teamMembers={data.teamMembers}
          onAddMember={addTeamMember}
          onRemoveMember={removeTeamMember}
          onUpdateMember={updateTeamMember}
          errors={errors}
          departments={departments}
        />

        {/* الأقسام المشاركة */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Building className="w-4 h-4 text-gray-500" />
            <label className="block text-sm font-medium text-gray-700">
              الأقسام المشاركة (اختياري)
            </label>
            <FieldHelp 
              content="الأقسام الأخرى التي ستشارك في المشروع أو تتأثر به"
              field="participatingDepartments"
              step={3}
            />
          </div>
          <Select
            value=""
            onChange={(e) => {
              if (e.target.value && !data.participatingDepartments.includes(e.target.value)) {
                updateData('participatingDepartments', [...data.participatingDepartments, e.target.value])
              }
            }}
            className="w-full"
          >
            <option value="">اختر قسم لإضافته</option>
            {departments.filter(dept => 
              dept !== data.responsibleDepartment && 
              !data.participatingDepartments.includes(dept)
            ).map(dept => (
              <option key={dept} value={dept}>{dept}</option>
            ))}
          </Select>
          
          {data.participatingDepartments.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-2">
              {data.participatingDepartments.map((dept, index) => (
                <span
                  key={index}
                  className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                >
                  {dept}
                  <button
                    type="button"
                    onClick={() => {
                      const updated = data.participatingDepartments.filter((_, i) => i !== index)
                      updateData('participatingDepartments', updated)
                    }}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
} 