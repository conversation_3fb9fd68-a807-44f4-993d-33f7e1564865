'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth, usePermissions } from '@/lib/auth'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
  Lightbulb,
  Target,
  Briefcase,
  Settings,
  Zap
} from 'lucide-react'
import { Pagination, usePagination } from '@/components/ui/Pagination'

export default function RequestsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [typeFilter, setTypeFilter] = useState('all')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [submittedRequests, setSubmittedRequests] = useState<any[]>([])
  
  const { } = useAuth()
  const permissions = usePermissions()
  const router = useRouter()

  // تحميل الطلبات من API
  useEffect(() => {
    const loadRequests = async () => {
      try {
        const response = await fetch('/api/requests')
        if (response.ok) {
          const result = await response.json()
          setSubmittedRequests(result.data || [])
        } else {
          console.error('Error loading requests:', response.statusText)
          // fallback to localStorage for now
          const requests = JSON.parse(localStorage.getItem('submitted_requests') || '[]')
          setSubmittedRequests(requests)
        }
      } catch (error) {
        console.error('Error loading requests:', error)
        // fallback to localStorage for now
        const requests = JSON.parse(localStorage.getItem('submitted_requests') || '[]')
        setSubmittedRequests(requests)
      }
    }
    
    loadRequests()
  }, [])



  // تحويل الطلبات المرسلة إلى التنسيق المطلوب
  const convertedSubmittedRequests = submittedRequests.map((req, index) => {
    // إذا كانت البيانات من API (تحتوي على id و title)
    if (req.id && req.title) {
      return {
        id: req.id,
        title: req.title,
        description: req.description || 'لا يوجد وصف',
        mainType: req.main_type || 'improvement_project',
        subType: req.sub_type,
        status: req.status,
        priority: req.priority || 'medium',
        requester: req.requester?.name || 'المستخدم الحالي',
        department: req.department?.name || 'غير محدد',
        createdAt: new Date(req.created_at).toLocaleDateString('ar-SA'),
        estimatedBudget: req.estimated_budget || 0,
        approvalLevel: req.approval_level || 0,
        requestNumber: req.title.includes('REQ-') ? req.title.split(' ')[0] : `REQ-${Date.now()}`
      }
    }
    
    // إذا كانت البيانات من localStorage (التنسيق القديم)
    return {
      id: `submitted_${index}`,
      title: req.form_data?.projectName || req.form_data?.projectTitle || 'مقترح تحسين',
      description: req.form_data?.problemDescription || req.form_data?.projectDescription || 'لا يوجد وصف',
      mainType: req.form_type === 'suggestion' ? 'improvement_project' : 'improvement_project',
      subType: req.form_type,
      status: req.status,
      priority: req.form_data?.priority || 'medium',
      requester: req.form_data?.teamLeader?.name || 'المستخدم الحالي',
      department: req.form_data?.responsibleDepartment || 'غير محدد',
      createdAt: new Date(req.created_at).toLocaleDateString('ar-SA'),
      estimatedBudget: req.form_data?.estimatedCost || req.form_data?.selectedSolution?.estimatedCost || 0,
      approvalLevel: 0,
      requestNumber: req.request_number
    }
  })

  // استخدام البيانات الحقيقية فقط
  const allRequests = [...convertedSubmittedRequests]

  const statusOptions = [
    { value: 'all', label: 'جميع الحالات' },
    { value: 'draft', label: 'مسودة' },
    { value: 'submitted', label: 'مرسل' },
    { value: 'under_review', label: 'قيد المراجعة' },
    { value: 'approved', label: 'معتمد' },
    { value: 'rejected', label: 'مرفوض' },
    { value: 'completed', label: 'مكتمل' }
  ]

  const categoryOptions = [
    { value: 'all', label: 'جميع الفئات' },
    { value: 'general_project', label: 'مشاريع عامة' },
    { value: 'improvement_project', label: 'مشاريع تحسين' }
  ]

  const typeOptions = [
    { value: 'all', label: 'جميع الأنواع' },
    { value: 'quick_win', label: 'كويك وين سريع' },
    { value: 'improvement_full', label: 'تحسين شامل' },
    { value: 'suggestion', label: 'مقترح تحسين' }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <Edit className="w-4 h-4 text-gray-500" />
      case 'submitted':
      case 'under_review':
        return <Clock className="w-4 h-4 text-yellow-500" />
      case 'approved':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'rejected':
        return <XCircle className="w-4 h-4 text-red-500" />
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-blue-500" />
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    const option = statusOptions.find(opt => opt.value === status)
    return option?.label || status
  }

  const getTypeIcon = (mainType: string, subType?: string | null) => {
    if (mainType === 'general_project') {
      return <Briefcase className="w-4 h-4 text-purple-500" />
    }
    
    if (mainType === 'improvement_project') {
      switch (subType) {
        case 'quick_win':
          return <Zap className="w-4 h-4 text-orange-500" />
        case 'improvement_full':
          return <Settings className="w-4 h-4 text-green-500" />
        case 'suggestion':
          return <Lightbulb className="w-4 h-4 text-yellow-500" />
        default:
          return <Target className="w-4 h-4 text-blue-500" />
      }
    }
    
    return <FileText className="w-4 h-4 text-gray-500" />
  }

  const getTypeText = (mainType: string, subType?: string | null) => {
    if (mainType === 'general_project') {
      return 'مشروع عام'
    }
    
    if (mainType === 'improvement_project') {
      switch (subType) {
        case 'quick_win':
          return 'كويك وين سريع'
        case 'improvement_full':
          return 'تحسين شامل'
        case 'suggestion':
          return 'مقترح تحسين'
        default:
          return 'مشروع تحسين'
      }
    }
    
    return 'غير محدد'
  }

  const getTypeColor = (mainType: string, subType?: string | null) => {
    if (mainType === 'general_project') {
      return 'bg-purple-100 text-purple-800'
    }
    
    if (mainType === 'improvement_project') {
      switch (subType) {
        case 'quick_win':
          return 'bg-orange-100 text-orange-800'
        case 'improvement_full':
          return 'bg-green-100 text-green-800'
        case 'suggestion':
          return 'bg-yellow-100 text-yellow-800'
        default:
          return 'bg-blue-100 text-blue-800'
      }
    }
    
    return 'bg-gray-100 text-gray-800'
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'bg-gray-100 text-gray-800'
      case 'medium':
        return 'bg-blue-100 text-blue-800'
      case 'high':
        return 'bg-orange-100 text-orange-800'
      case 'urgent':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredRequests = allRequests.filter(request => {
    const matchesSearch = request.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || request.status === statusFilter
    const matchesCategory = categoryFilter === 'all' || request.mainType === categoryFilter
    const matchesType = typeFilter === 'all' || request.subType === typeFilter
    
    return matchesSearch && matchesStatus && matchesCategory && matchesType
  })

  // استخدام نظام الترقيم
  const {
    paginatedData: paginatedRequests,
    currentPage,
    totalPages,
    showingFrom,
    showingTo,
    totalItems,
    goToPage
  } = usePagination(filteredRequests, 5) // 5 طلبات في كل صفحة

  return (
    <ProtectedLayout>
      <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">طلبات المشاريع</h1>
              <p className="text-gray-600 mt-1">إدارة ومتابعة جميع طلبات المشاريع</p>
            </div>
            
            {permissions.canCreateRequests() && (
              <Button
                variant="primary"
                onClick={() => router.push('/requests/new')}
                icon={<Plus className="w-4 h-4" />}
              >
                طلب جديد
              </Button>
            )}
          </div>

          {/* Filters */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                {/* Search */}
                <div className="lg:col-span-2">
                  <div className="relative">
                    <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="البحث في الطلبات..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                {/* Status Filter */}
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {statusOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>

                {/* Category Filter */}
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {categoryOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>

                {/* Type Filter */}
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {typeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </CardContent>
          </Card>

          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                    <p className="text-2xl font-bold text-gray-900">{allRequests.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <Clock className="w-6 h-6 text-yellow-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">قيد المراجعة</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {allRequests.filter(r => ['submitted', 'under_review'].includes(r.status)).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <CheckCircle className="w-6 h-6 text-green-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">معتمدة</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {allRequests.filter(r => r.status === 'approved').length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Target className="w-6 h-6 text-purple-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">مشاريع تحسين</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {allRequests.filter(r => r.mainType === 'improvement_project').length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Requests List */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-900">
                  قائمة الطلبات ({filteredRequests.length})
                </h3>
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm" icon={<Filter className="w-4 h-4" />}>
                    فلترة متقدمة
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              {paginatedRequests.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد طلبات</h3>
                  <p className="text-gray-600 mb-4">لم يتم العثور على طلبات تطابق معايير البحث</p>
                  {permissions.canCreateRequests() && (
                    <Button
                      variant="primary"
                      onClick={() => router.push('/requests/new')}
                      icon={<Plus className="w-4 h-4" />}
                    >
                      إنشاء طلب جديد
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {paginatedRequests.map((request) => (
                    <div
                      key={request.id}
                      className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer"
                      onClick={() => router.push(`/requests/${request.id}`)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            {getTypeIcon(request.mainType, request.subType || undefined)}
                            <h4 className="text-lg font-semibold text-gray-900">
                              {request.title}
                            </h4>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(request.mainType, request.subType || undefined)}`}>
                              {getTypeText(request.mainType, request.subType || undefined)}
                            </span>
                          </div>
                          
                          <p className="text-gray-600 mb-3 line-clamp-2">
                            {request.description}
                          </p>
                          
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            {request.requestNumber && (
                              <span className="font-medium text-blue-600">رقم الطلب: {request.requestNumber}</span>
                            )}
                            <span>بواسطة: {request.requester}</span>
                            <span>القسم: {request.department}</span>
                            <span>التاريخ: {request.createdAt}</span>
                            {request.estimatedBudget && (
                              <span>الميزانية: {request.estimatedBudget.toLocaleString()} ر.س</span>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(request.status)}
                            <span className="text-sm font-medium">
                              {getStatusText(request.status)}
                            </span>
                          </div>
                          
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(request.priority)}`}>
                            {request.priority === 'low' && 'منخفضة'}
                            {request.priority === 'medium' && 'متوسطة'}
                            {request.priority === 'high' && 'عالية'}
                            {request.priority === 'urgent' && 'عاجلة'}
                          </span>
                          
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              icon={<Eye className="w-4 h-4" />}
                              onClick={(e) => {
                                e.stopPropagation()
                                router.push(`/requests/${request.id}`)
                              }}
                            />
                            {permissions.canCreateRequests() && (
                              <Button
                                variant="ghost"
                                size="sm"
                                icon={<Edit className="w-4 h-4" />}
                                onClick={(e) => {
                                  e.stopPropagation()
                                  router.push(`/requests/${request.id}/edit`)
                                }}
                              />
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-6">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={goToPage}
                    showingFrom={showingFrom}
                    showingTo={showingTo}
                    totalItems={totalItems}
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </div>
    </ProtectedLayout>
  )
} 