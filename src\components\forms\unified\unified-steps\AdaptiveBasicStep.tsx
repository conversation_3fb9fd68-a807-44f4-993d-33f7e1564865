'use client'

import React from 'react'
import { Input, Textarea, Select } from '@/components/ui/Input'
import { InteractiveTooltip, QuickTip, ExampleBox } from '@/components/ui/InteractiveTooltip'
import { Calendar, FileText, Flag, User, Building } from 'lucide-react'
import { FormType, UnifiedFormData, EnhancedImprovementData, QuickWinData } from '../UnifiedProjectForm'

interface AdaptiveBasicStepProps {
  formType: FormType
  data: UnifiedFormData
  updateData: (field: string, value: any) => void
  errors: Record<string, string>
}

export function AdaptiveBasicStep({ formType, data, updateData, errors }: AdaptiveBasicStepProps) {
  const priorityOptions = [
    { value: 'low', label: 'منخفضة', color: 'text-green-600' },
    { value: 'medium', label: 'متوسطة', color: 'text-yellow-600' },
    { value: 'high', label: 'عالية', color: 'text-orange-600' },
    { value: 'urgent', label: 'عاجلة', color: 'text-red-600' }
  ]

  // التحقق من صحة التواريخ للتحسين الشامل
  const validateDates = (startDate: string, endDate: string) => {
    if (startDate && endDate) {
      return new Date(startDate) < new Date(endDate)
    }
    return true
  }

  const renderEnhancedImprovementBasic = () => {
    const enhancedData = data as EnhancedImprovementData
    const isValidDateRange = validateDates(enhancedData.startDate, enhancedData.endDate)

    return (
      <div className="space-y-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-2 mb-2">
            <FileText className="w-5 h-5 text-blue-600" />
            <h3 className="font-semibold text-blue-900">معلومات أساسية عن المشروع</h3>
          </div>
          <p className="text-blue-800 text-sm">
            قم بتعبئة المعلومات الأساسية للمشروع التي ستساعد في تحديد هوية المشروع وإطاره الزمني
          </p>
        </div>

        <div className="grid grid-cols-1 gap-6">
          {/* اسم المشروع */}
          <InteractiveTooltip step={1} field="projectName">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اسم المشروع *
              </label>
              <Input
                value={enhancedData.projectName}
                onChange={(e) => updateData('projectName', e.target.value)}
                placeholder="مثال: تحسين كفاءة عملية معالجة الطلبات"
                error={errors.projectName}
                maxLength={100}
              />
              <div className="text-xs text-gray-500 mt-1">
                {enhancedData.projectName.length}/100 حرف
              </div>
            </div>
          </InteractiveTooltip>

          {/* وصف المشروع */}
          <InteractiveTooltip step={1} field="projectDescription">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                وصف المشروع *
              </label>
              <Textarea
                value={enhancedData.projectDescription}
                onChange={(e) => updateData('projectDescription', e.target.value)}
                placeholder="اكتب وصفاً تفصيلياً للمشروع، أهدافه، والفوائد المتوقعة..."
                rows={4}
                error={errors.projectDescription}
                maxLength={500}
              />
              <div className="text-xs text-gray-500 mt-1">
                {enhancedData.projectDescription.length}/500 حرف
              </div>
            </div>
          </InteractiveTooltip>

          {/* التواريخ */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <InteractiveTooltip step={1} field="startDate">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <label className="block text-sm font-medium text-gray-700">
                    تاريخ بداية المشروع *
                  </label>
                </div>
                <Input
                  type="date"
                  value={enhancedData.startDate}
                  onChange={(e) => updateData('startDate', e.target.value)}
                  error={errors.startDate}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>
            </InteractiveTooltip>

            <InteractiveTooltip step={1} field="endDate">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <label className="block text-sm font-medium text-gray-700">
                    تاريخ انتهاء المشروع *
                  </label>
                </div>
                <Input
                  type="date"
                  value={enhancedData.endDate}
                  onChange={(e) => updateData('endDate', e.target.value)}
                  error={errors.endDate}
                  min={enhancedData.startDate || new Date().toISOString().split('T')[0]}
                />
              </div>
            </InteractiveTooltip>
          </div>

          {/* تحذير التواريخ */}
          {!isValidDateRange && enhancedData.startDate && enhancedData.endDate && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-center gap-2 text-red-800">
                <Calendar className="w-4 h-4" />
                <span className="text-sm font-medium">
                  تاريخ النهاية يجب أن يكون بعد تاريخ البداية
                </span>
              </div>
            </div>
          )}

          {/* مدة المشروع */}
          {isValidDateRange && enhancedData.startDate && enhancedData.endDate && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <div className="flex items-center gap-2 text-green-800">
                <Calendar className="w-4 h-4" />
                <span className="text-sm font-medium">
                  مدة المشروع: {Math.ceil((new Date(enhancedData.endDate).getTime() - new Date(enhancedData.startDate).getTime()) / (1000 * 60 * 60 * 24))} يوم
                </span>
              </div>
            </div>
          )}

          {/* أولوية المشروع */}
          <InteractiveTooltip step={1} field="priority">
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Flag className="w-4 h-4 text-gray-500" />
                <label className="block text-sm font-medium text-gray-700">
                  أولوية المشروع
                </label>
              </div>
              <Select
                value={enhancedData.priority}
                onChange={(e) => updateData('priority', e.target.value)}
                className="w-full"
              >
                {priorityOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </Select>
              
              {/* عرض وصف الأولوية */}
              <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                <div className={`text-sm font-medium ${priorityOptions.find(p => p.value === enhancedData.priority)?.color}`}>
                  {enhancedData.priority === 'low' && 'منخفضة: يمكن تأجيل المشروع لفترة دون تأثير كبير'}
                  {enhancedData.priority === 'medium' && 'متوسطة: مشروع مهم يحتاج لتنفيذ في الوقت المحدد'}
                  {enhancedData.priority === 'high' && 'عالية: مشروع مهم جداً يؤثر على أداء المؤسسة'}
                  {enhancedData.priority === 'urgent' && 'عاجلة: مشروع حرج يحتاج لتنفيذ فوري'}
                </div>
              </div>
            </div>
          </InteractiveTooltip>
        </div>
      </div>
    )
  }

  const renderQuickWinBasic = () => {
    const quickWinData = data as QuickWinData

    return (
      <div className="space-y-6">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-2 mb-2">
            <FileText className="w-5 h-5 text-yellow-600" />
            <h3 className="font-semibold text-yellow-900">معلومات أساسية - كويك وين</h3>
          </div>
          <p className="text-yellow-800 text-sm">
            معلومات مبسطة للحلول السريعة والفعالة (حد أقصى 4 أسابيع)
          </p>
        </div>

        <div className="grid grid-cols-1 gap-6">
          {/* عنوان المشروع */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              عنوان المشروع *
            </label>
            <Input
              value={quickWinData.projectTitle}
              onChange={(e) => updateData('projectTitle', e.target.value)}
              placeholder="مثال: تحسين سرعة الاستجابة في خدمة العملاء"
              error={errors.projectTitle}
              maxLength={100}
            />
          </div>

          {/* القسم */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Building className="w-4 h-4 text-gray-500" />
              <label className="block text-sm font-medium text-gray-700">
                القسم *
              </label>
            </div>
            <Input
              value={quickWinData.section}
              onChange={(e) => updateData('section', e.target.value)}
              placeholder="مثال: خدمة العملاء"
              error={errors.section}
            />
          </div>

          {/* منفذ المشروع */}
          <div className="bg-gray-50 border rounded-lg p-4">
            <div className="flex items-center gap-2 mb-4">
              <User className="w-5 h-5 text-gray-600" />
              <h4 className="font-semibold text-gray-900">منفذ المشروع</h4>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الاسم *
                </label>
                <Input
                  value={quickWinData.projectExecutor.name}
                  onChange={(e) => updateData('projectExecutor', { 
                    ...quickWinData.projectExecutor, 
                    name: e.target.value 
                  })}
                  placeholder="اسم منفذ المشروع"
                  error={errors['projectExecutor.name']}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  رقم الجوال *
                </label>
                <Input
                  value={quickWinData.projectExecutor.phone}
                  onChange={(e) => updateData('projectExecutor', { 
                    ...quickWinData.projectExecutor, 
                    phone: e.target.value 
                  })}
                  placeholder="05xxxxxxxx"
                  error={errors['projectExecutor.phone']}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  البريد الإلكتروني *
                </label>
                <Input
                  type="email"
                  value={quickWinData.projectExecutor.email}
                  onChange={(e) => updateData('projectExecutor', { 
                    ...quickWinData.projectExecutor, 
                    email: e.target.value 
                  })}
                  placeholder="<EMAIL>"
                  error={errors['projectExecutor.email']}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // عرض المحتوى حسب نوع النموذج
  if (formType === 'enhanced_improvement') {
    return renderEnhancedImprovementBasic()
  } else if (formType === 'quick_win') {
    return renderQuickWinBasic()
  }

  // المقترحات لا تحتاج Basic Step
  return (
    <div className="text-center py-8 text-gray-500">
      <p>المقترحات التحسينية لا تحتاج لمعلومات أساسية</p>
      <p className="text-sm">انتقل للمرحلة التالية للبدء</p>
    </div>
  )
} 