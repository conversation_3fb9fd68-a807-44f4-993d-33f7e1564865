'use client'

import Link from 'next/link'
import { ArrowLeft, Send, CheckCircle, AlertCircle, Clock } from 'lucide-react'
import { useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

export default function SubmissionTestPage() {
  const [testResults, setTestResults] = useState<{[key: string]: any}>({})
  const [loading, setLoading] = useState(false)

  const runSubmissionTest = async (testType: string) => {
    setLoading(true)
    try {
      // محاكاة اختبار الإرسال
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      setTestResults(prev => ({
        ...prev,
        [testType]: {
          success: Math.random() > 0.3, // 70% نجاح
          timestamp: new Date().toISOString(),
          responseTime: Math.floor(Math.random() * 1000) + 500
        }
      }))
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [testType]: { success: false, error: 'فشل الاختبار' }
      }))
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (testType: string) => {
    const result = testResults[testType]
    if (!result) return <Clock className="w-5 h-5 text-gray-400" />
    return result.success 
      ? <CheckCircle className="w-5 h-5 text-green-500" />
      : <AlertCircle className="w-5 h-5 text-red-500" />
  }

  const submissionTests = [
    {
      id: 'form_validation',
      title: 'اختبار التحقق من النموذج',
      description: 'اختبار التحقق من صحة البيانات قبل الإرسال'
    },
    {
      id: 'api_submission',
      title: 'اختبار إرسال API',
      description: 'اختبار إرسال البيانات إلى الخادم'
    },
    {
      id: 'file_upload',
      title: 'اختبار رفع الملفات',
      description: 'اختبار رفع الملفات المرفقة'
    },
    {
      id: 'error_handling',
      title: 'اختبار معالجة الأخطاء',
      description: 'اختبار التعامل مع الأخطاء والاستثناءات'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Navigation */}
        <div className="mb-6">
          <Link 
            href="/testing/forms" 
            className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            العودة إلى اختبارات النماذج
          </Link>
        </div>

        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Send className="w-8 h-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">
              اختبار إرسال النماذج
            </h1>
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            اختبار شامل لعمليات إرسال النماذج والتحقق من البيانات
          </p>
        </div>

        {/* Test Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {submissionTests.map((test) => (
            <Card key={test.id} className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  {test.title}
                </h3>
                {getStatusIcon(test.id)}
              </div>
              
              <p className="text-gray-600 mb-4">
                {test.description}
              </p>

              <Button
                onClick={() => runSubmissionTest(test.id)}
                disabled={loading}
                className="w-full"
              >
                {loading ? 'جاري الاختبار...' : 'تشغيل الاختبار'}
              </Button>

              {testResults[test.id] && (
                <div className={`mt-3 p-3 rounded-lg ${
                  testResults[test.id].success 
                    ? 'bg-green-50 border border-green-200' 
                    : 'bg-red-50 border border-red-200'
                }`}>
                  <p className={`font-medium ${
                    testResults[test.id].success ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {testResults[test.id].success ? 'نجح الاختبار' : 'فشل الاختبار'}
                  </p>
                  {testResults[test.id].responseTime && (
                    <p className="text-sm text-gray-600">
                      وقت الاستجابة: {testResults[test.id].responseTime}ms
                    </p>
                  )}
                </div>
              )}
            </Card>
          ))}
        </div>

        {/* Summary */}
        <Card className="mt-8 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            ملخص النتائج
          </h3>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-green-600">
                {Object.values(testResults).filter((r: any) => r.success).length}
              </p>
              <p className="text-sm text-gray-600">اختبارات ناجحة</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-red-600">
                {Object.values(testResults).filter((r: any) => !r.success).length}
              </p>
              <p className="text-sm text-gray-600">اختبارات فاشلة</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-600">
                {submissionTests.length - Object.keys(testResults).length}
              </p>
              <p className="text-sm text-gray-600">لم تُختبر بعد</p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}
