import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database.types'

// تحديد وضع التطوير
const isDevelopmentMode = process.env.DEVELOPMENT_MODE === 'true' || true // تعيين true مؤقتاً للتطوير

// إنشاء عميل Supabase مع service role key لتجاوز سياسات RLS تمامًا
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo-project.supabase.co'
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo-service-key'

// إنشاء عميل Supabase مع service role key لتجاوز سياسات RLS تمامًا
const supabaseBypassRLS = createClient<Database>(
  supabaseUrl,
  supabaseServiceKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    },
    global: {
      headers: {
        'x-client-info': 'supabase-admin-api',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'x-supabase-bypass-rls': 'true'
      }
    }
  }
)

// تأكيد أن العميل يستخدم مفتاح الخدمة
console.log('Using service role key for Supabase client:', !!supabaseServiceKey)

// واجهة البيانات المرسلة من النموذج
interface RequestSubmission {
  request_number: string
  form_type: 'enhanced_improvement' | 'suggestion' | 'quick_win'
  form_data: any
  status: string
  created_by: string
  created_at: string
}

// POST - إنشاء طلب جديد
export async function POST(request: NextRequest) {
  try {
    const body: RequestSubmission = await request.json()
    
    // التحقق من صحة البيانات الأساسية
    if (!body.form_type || !body.form_data || !body.request_number) {
      return NextResponse.json(
        { error: 'البيانات المطلوبة غير مكتملة' },
        { status: 400 }
      )
    }

    // التحقق من وجود معرف المستخدم
    if (!body.created_by || body.created_by === 'current_user_id') {
      return NextResponse.json(
        { error: 'معرف المستخدم مطلوب. يرجى تسجيل الدخول أولاً.' },
        { status: 401 }
      )
    }

    // تحضير البيانات للحفظ في قاعدة البيانات
    const projectRequestData = {
      title: extractTitle(body.form_data, body.form_type),
      description: extractDescription(body.form_data, body.form_type),
      main_type: body.form_type === 'suggestion' ? 'improvement_project' : 'improvement_project',
      sub_type: body.form_type,
      status: body.status || 'submitted',
      priority: 'medium',
      requester_id: body.created_by, // TODO: الحصول من session
      department_id: extractDepartmentId(body.form_data),
      expected_start_date: extractStartDate(body.form_data),
      expected_end_date: extractEndDate(body.form_data),
      estimated_budget: extractBudget(body.form_data),
      business_case: extractBusinessCase(body.form_data, body.form_type),
      expected_benefits: extractBenefits(body.form_data),
      risks_and_challenges: extractRisks(body.form_data),
      form_data: body.form_data,
      attachments: body.form_data.attachments || [],
      approval_level: 0
    }

    // طباعة البيانات للتشخيص
    console.log('Project request data to insert:', JSON.stringify(projectRequestData, null, 2))

    // حفظ في قاعدة البيانات
    let data, error;
    
    try {
      console.log('Inserting project request with bypass RLS client')
      console.log('Project request data:', JSON.stringify(projectRequestData, null, 2))
      
      // استخدام SQL مباشر لتجاوز سياسات RLS تمامًا
      const { data: insertData, error: insertError } = await supabaseBypassRLS.rpc(
        'admin_insert_project_request',
        {
          project_data: projectRequestData
        }
      )
      
      console.log('Insert response:', insertData ? 'Success' : 'Failed', insertError ? insertError.message : 'No error')
      
      // إذا لم ينجح الإدخال باستخدام RPC، نجرب الطريقة المباشرة
      if (insertError) {
        console.log('Trying direct insert with service role as fallback...')
        const { data: directData, error: directError } = await supabaseBypassRLS
          .from('project_requests')
          .insert([projectRequestData])
          .select()
          .single()
        
        data = directData
        error = directError
      } else {
        data = insertData
        error = insertError
      }
    } catch (e) {
      console.error('Unexpected error during insert:', e)
      error = {
        message: e instanceof Error ? e.message : 'Unexpected error during database operation',
        details: JSON.stringify(e)
      }
    }

    if (error) {
      console.error('Database error:', error)
      console.error('Error details:', JSON.stringify(error, null, 2))
      return NextResponse.json(
        { error: `حدث خطأ في حفظ البيانات: ${error.message}` },
        { status: 500 }
      )
    }

    if (!data) {
      console.error('No data returned after insert')
      return NextResponse.json(
        { error: 'حدث خطأ في حفظ البيانات: لم يتم الحصول على بيانات الإدخال' },
        { status: 500 }
      )
    }

    // إرسال الإشعارات للمعنيين وبدء سير العمل
    await sendNotifications(data.id, body.form_type, projectRequestData)
    await initiateApprovalWorkflow(data.id, body.form_type)

    return NextResponse.json({
      success: true,
      message: 'تم إرسال الطلب بنجاح',
      request_id: data.id,
      request_number: body.request_number
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// GET - استرجاع الطلبات
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    const status = searchParams.get('status')
    const type = searchParams.get('type')

    // التحقق من معرف المستخدم إذا تم تمريره
    if (userId && (userId === 'undefined' || userId === 'null' || !userId.trim())) {
      return NextResponse.json(
        { error: 'معرف المستخدم غير صحيح' },
        { status: 400 }
      )
    }

    let query = supabaseAdmin
      .from('project_requests')
      .select(`
        *,
        requester:users!requester_id(name, email),
        department:departments!department_id(name)
      `)

    // تطبيق الفلاتر
    if (userId) {
      query = query.eq('requester_id', userId)
    }
    if (status) {
      query = query.eq('status', status)
    }
    if (type) {
      query = query.eq('sub_type', type)
    }

    const { data, error } = await query.order('created_at', { ascending: false })

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في استرجاع البيانات' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: data || []
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// دوال مساعدة لاستخراج البيانات من النماذج المختلفة
function extractTitle(formData: any, formType: string): string {
  switch (formType) {
    case 'quick_win':
      return formData.projectTitle || formData.projectName || 'مشروع كويك وين'
    case 'suggestion':
      return formData.projectName || formData.projectTitle || 'مقترح تحسين'
    case 'enhanced_improvement':
      return formData.projectName || formData.projectTitle || 'مشروع تحسين شامل'
    default:
      return 'طلب مشروع'
  }
}

function extractDescription(formData: any, formType: string): string {
  return formData.problemDescription || formData.projectDescription || 'وصف المشروع'
}

function extractDepartmentId(formData: any): string {
  // استخدام معرف قسم موجود في قاعدة البيانات
  // يمكن تحسين هذا لاحقاً بالبحث عن القسم بالاسم
  return 'f7e8c6be-8ef0-4c4c-b6e2-be71bd2d22b5' // معرف قسم مكتب إدارة المشاريع
}

function extractStartDate(formData: any): string | null {
  return formData.startDate || null
}

function extractEndDate(formData: any): string | null {
  return formData.endDate || null
}

function extractBudget(formData: any): number | null {
  return formData.estimatedCost || null
}

function extractBusinessCase(formData: any, formType: string): string {
  switch (formType) {
    case 'quick_win':
      return `المشكلة: ${formData.problemDescription}\nالحل: ${formData.solution?.description}`
    case 'suggestion':
      return `المشكلة: ${formData.problemDescription}\nالحلول المقترحة: ${formData.suggestedSolutions?.map((s: any) => s.title).join(', ')}`
    case 'enhanced_improvement':
      return `المشكلة: ${formData.problemDescription}\nالحل: ${formData.selectedSolution?.title}`
    default:
      return formData.problemDescription || ''
  }
}

function extractBenefits(formData: any): string {
  return formData.expectedBenefits || `تحسين المؤشر: ${formData.indicatorName} من ${formData.currentValue} إلى ${formData.targetValue}`
}

function extractRisks(formData: any): string {
  if (formData.risks && formData.risks.length > 0) {
    return formData.risks.map((risk: any) => `${risk.description} (${risk.probability}/${risk.impact})`).join('\n')
  }
  return 'لا توجد مخاطر محددة'
}

// إرسال الإشعارات
async function sendNotifications(requestId: string, formType: string, requestData: any) {
  try {
    // TODO: تحديد المستقبلين حسب نوع النموذج ومستوى الموافقة
    const notifications: any[] = []
    
    // إشعار لمكتب المشاريع (تعطيل مؤقت حتى يتم إصلاح معرف المستخدم)
    // notifications.push({
    //   user_id: 'pmo-manager-id', // TODO: الحصول من قاعدة البيانات
    //   title: `طلب مشروع جديد: ${requestData.title}`,
    //   message: `تم استلام طلب مشروع جديد من نوع ${getFormTypeDisplayName(formType)}`,
    //   type: 'info',
    //   action_url: `/requests/${requestId}`
    // })

    if (notifications.length > 0) {
      await supabaseAdmin.from('notifications').insert(notifications)
    }
  } catch (error) {
    console.error('Error sending notifications:', error)
  }
}

function getFormTypeDisplayName(formType: string): string {
  switch (formType) {
    case 'quick_win':
      return 'كويك وين'
    case 'suggestion':
      return 'مقترح تحسين'
    case 'enhanced_improvement':
      return 'تحسين شامل'
    default:
      return 'مشروع'
  }
}

// بدء سير العمل للموافقات
async function initiateApprovalWorkflow(requestId: string, formType: string) {
  try {
    // تحديد المستوى الأول من الموافقة
    const firstApproverId = await getApproverByLevel(1)
    
    if (!firstApproverId) {
      console.error('Cannot find first level approver')
      return
    }

    // تحديث مستوى الموافقة في الطلب
    await supabaseAdmin
      .from('project_requests')
      .update({ 
        approval_level: 1,
        status: 'under_review'
      })
      .eq('id', requestId)

    // إنشاء موافقة للمستوى الأول
    await supabaseAdmin
      .from('approvals')
      .insert({
        request_id: requestId,
        approver_id: firstApproverId,
        status: 'pending'
      })

    // إرسال إشعار للمعتمد الأول
    await sendApprovalNotification(
      firstApproverId,
      requestId,
      'طلب موافقة جديد',
      `يتطلب طلب مشروع جديد موافقتك`
    )

  } catch (error) {
    console.error('Error initiating approval workflow:', error)
  }
}

// الحصول على المعتمد حسب المستوى
async function getApproverByLevel(level: number): Promise<string | null> {
  try {
    let roleName: string
    
    switch (level) {
      case 1:
        roleName = 'pmo_manager'
        break
      case 2:
        roleName = 'planning_manager'
        break
      case 3:
        roleName = 'executive_manager'
        break
      default:
        return null
    }

    const roleId = await getRoleId(roleName)
    if (!roleId) return null

    const { data: user, error } = await supabaseAdmin
      .from('users')
      .select('id')
      .eq('role_id', roleId)
      .single()

    if (error || !user) {
      console.error('Error getting approver by level:', error)
      return null
    }

    return user.id
  } catch (error) {
    console.error('Error getting approver by level:', error)
    return null
  }
}

// الحصول على معرف الدور
async function getRoleId(roleName: string): Promise<string | null> {
  try {
    const { data: role, error } = await supabaseAdmin
      .from('roles')
      .select('id')
      .eq('name', roleName)
      .single()

    if (error || !role) {
      console.error('Error getting role ID:', error)
      return null
    }

    return role.id
  } catch (error) {
    console.error('Error getting role ID:', error)
    return null
  }
}

// إرسال إشعار الموافقة
async function sendApprovalNotification(
  userId: string,
  requestId: string,
  title: string,
  message: string
): Promise<void> {
  try {
    await supabaseAdmin
      .from('notifications')
      .insert({
        user_id: userId,
        title,
        message,
        type: 'info',
        action_url: `/approvals/${requestId}`
      })
  } catch (error) {
    console.error('Error sending approval notification:', error)
  }
} 