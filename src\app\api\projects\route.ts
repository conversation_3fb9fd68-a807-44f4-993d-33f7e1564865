import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase-admin'

// واجهة بيانات المشروع
interface ProjectData {
  id: string
  title: string
  description: string
  type: 'suggestion' | 'project' | 'quickwin'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  assignee: string
  dueDate: string
  createdDate: string
  tags: string[]
  progress: number
  pdcaPhase: 'plan' | 'do' | 'check' | 'act'
  estimatedHours?: number
  actualHours?: number
  attachments?: number
  comments?: number
  requestId?: string
  status: 'active' | 'completed' | 'on_hold' | 'cancelled'
}

// GET - جلب جميع المشاريع
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const type = searchParams.get('type')
    const assignee = searchParams.get('assignee')
    const phase = searchParams.get('phase')

    // جلب المشاريع من قاعدة البيانات
    let query = supabaseAdmin
      .from('projects')
      .select(`
        *,
        assignee_user:users!assignee_id(name, email),
        original_request:project_requests!request_id(
          id,
          title,
          description,
          main_type,
          sub_type,
          priority,
          requester:users!requester_id(name)
        )
      `)

    // تطبيق الفلاتر
    if (status) {
      query = query.eq('status', status)
    }
    if (type) {
      query = query.eq('type', type)
    }
    if (assignee) {
      query = query.eq('assignee_id', assignee)
    }
    if (phase) {
      query = query.eq('pdca_phase', phase)
    }

    const { data: projects, error } = await query.order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching projects:', error)
      return NextResponse.json(
        { success: false, error: 'فشل في جلب المشاريع' },
        { status: 500 }
      )
    }

    // تحويل البيانات إلى التنسيق المطلوب للوحة Kanban
    const formattedProjects = projects?.map(project => {
      const projectData = project as any
      const assigneeData = projectData.assignee_user as any
      
      return {
        id: projectData.id,
        title: projectData.title,
        description: projectData.description,
        type: projectData.type || 'project',
        priority: projectData.priority || 'medium',
        assignee: assigneeData?.name || 'غير محدد',
        dueDate: projectData.due_date || projectData.end_date,
        createdDate: projectData.created_at,
        tags: Array.isArray(projectData.tags) ? projectData.tags : [],
        progress: projectData.progress || projectData.progress_percentage || 0,
        pdcaPhase: projectData.pdca_phase || 'plan',
        estimatedHours: projectData.estimated_hours || 0,
        actualHours: projectData.actual_hours || 0,
        attachments: Array.isArray(projectData.attachments) ? projectData.attachments.length : 0,
        comments: projectData.comments || 0,
        requestId: projectData.request_id,
        status: projectData.status || 'active'
      }
    }) || []

    return NextResponse.json({
      success: true,
      data: formattedProjects
    })

  } catch (error) {
    console.error('Error in GET /api/projects:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// POST - إنشاء مشروع جديد أو تحويل طلب معتمد إلى مشروع
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // التحقق من البيانات المطلوبة
    if (!body.title || !body.assignee_id) {
      return NextResponse.json(
        { success: false, error: 'البيانات المطلوبة غير مكتملة' },
        { status: 400 }
      )
    }

    // إذا كان هناك request_id، تحويل الطلب إلى مشروع
    if (body.request_id) {
      // التحقق من أن الطلب معتمد
      const { data: request, error: requestError } = await supabaseAdmin
        .from('project_requests')
        .select('*')
        .eq('id', body.request_id)
        .eq('status', 'approved')
        .single()

      if (requestError || !request) {
        return NextResponse.json(
          { success: false, error: 'الطلب غير موجود أو غير معتمد' },
          { status: 400 }
        )
      }

      // التحقق من عدم وجود مشروع مسبق لهذا الطلب
      const { data: existingProject, error: existingError } = await supabaseAdmin
        .from('projects')
        .select('id')
        .eq('request_id', body.request_id)
        .maybeSingle()

      if (existingProject) {
        return NextResponse.json(
          { success: false, error: 'تم تحويل هذا الطلب إلى مشروع مسبقاً' },
          { status: 400 }
        )
      }

      // تحديد نوع المشروع حسب نوع الطلب
      let projectType = 'project'
      if (request.main_type === 'improvement_project') {
        if (request.sub_type === 'quick_win') projectType = 'quickwin'
        else if (request.sub_type === 'suggestion') projectType = 'suggestion'
      }

      // إنشاء المشروع
      const { data: project, error: projectError } = await supabaseAdmin
        .from('projects')
        .insert({
          title: request.title,
          description: request.description,
          project_manager_id: body.assignee_id,
          request_id: body.request_id,
          end_date: request.expected_end_date,
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        } as any)
        .select()
        .single()

      if (projectError) {
        console.error('Error creating project:', projectError)
        return NextResponse.json(
          { success: false, error: 'فشل في إنشاء المشروع' },
          { status: 500 }
        )
      }

      // تحديث حالة الطلب إلى "محول إلى مشروع"
      await supabaseAdmin
        .from('project_requests')
        .update({ 
          status: 'converted_to_project',
          updated_at: new Date().toISOString()
        })
        .eq('id', body.request_id)

      // إرسال إشعار للمعين
      await sendNotification(
        body.assignee_id,
        'تم تعيين مشروع جديد',
        `تم تعيينك كمسؤول عن مشروع: ${request.title}`,
        'info',
        `/projects/${project.id}`
      )

      return NextResponse.json({
        success: true,
        data: project,
        message: 'تم تحويل الطلب إلى مشروع بنجاح'
      })
    } else {
      // إنشاء مشروع جديد مباشرة
      const { data: project, error: projectError } = await supabaseAdmin
        .from('projects')
        .insert({
          title: body.title,
          description: body.description,
          project_manager_id: body.assignee_id,
          request_id: body.request_id || '',
          end_date: body.due_date,
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        } as any)
        .select()
        .single()

      if (projectError) {
        console.error('Error creating project:', projectError)
        return NextResponse.json(
          { success: false, error: 'فشل في إنشاء المشروع' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        data: project,
        message: 'تم إنشاء المشروع بنجاح'
      })
    }

  } catch (error) {
    console.error('Error in POST /api/projects:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// PUT - تحديث مشروع
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    if (!body.id) {
      return NextResponse.json(
        { success: false, error: 'معرف المشروع مطلوب' },
        { status: 400 }
      )
    }

    const { data: project, error } = await supabaseAdmin
      .from('projects')
      .update({
        title: body.title,
        description: body.description,
        type: body.type,
        priority: body.priority,
        assignee_id: body.assignee_id,
        due_date: body.due_date,
        estimated_hours: body.estimated_hours,
        actual_hours: body.actual_hours,
        pdca_phase: body.pdca_phase,
        progress: body.progress,
        status: body.status,
        tags: body.tags,
        updated_at: new Date().toISOString()
      })
      .eq('id', body.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating project:', error)
      return NextResponse.json(
        { success: false, error: 'فشل في تحديث المشروع' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: project,
      message: 'تم تحديث المشروع بنجاح'
    })

  } catch (error) {
    console.error('Error in PUT /api/projects:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// DELETE - حذف مشروع
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('id')
    
    if (!projectId) {
      return NextResponse.json(
        { success: false, error: 'معرف المشروع مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود المشروع
    const { data: project, error: fetchError } = await supabaseAdmin
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single()

    if (fetchError || !project) {
      return NextResponse.json(
        { success: false, error: 'المشروع غير موجود' },
        { status: 404 }
      )
    }

    // حذف المشروع
    const { error: deleteError } = await supabaseAdmin
      .from('projects')
      .delete()
      .eq('id', projectId)

    if (deleteError) {
      console.error('Error deleting project:', deleteError)
      return NextResponse.json(
        { success: false, error: 'فشل في حذف المشروع' },
        { status: 500 }
      )
    }

    // إذا كان المشروع مرتبط بطلب، إعادة حالة الطلب إلى معتمد
    if (project.request_id) {
      await supabaseAdmin
        .from('project_requests')
        .update({ 
          status: 'approved',
          updated_at: new Date().toISOString()
        })
        .eq('id', project.request_id)
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف المشروع بنجاح'
    })

  } catch (error) {
    console.error('Error in DELETE /api/projects:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// دالة إرسال الإشعارات
async function sendNotification(
  userId: string,
  title: string,
  message: string,
  type: 'info' | 'success' | 'warning' | 'error',
  actionUrl?: string
): Promise<void> {
  try {
    await supabaseAdmin
      .from('notifications')
      .insert({
        user_id: userId,
        title,
        message,
        type,
        action_url: actionUrl,
        is_read: false,
        created_at: new Date().toISOString()
      })
  } catch (error) {
    console.error('Error sending notification:', error)
  }
} 