'use client'

import React, { ReactNode } from 'react'
import { NotificationProvider } from './NotificationSystem'
import { NotificationSettings } from './NotificationTypes'

// مزود الإشعارات الأساسي
export function BasicNotificationProvider({ children }: { children: ReactNode }) {
  return (
    <NotificationProvider>
      {children}
    </NotificationProvider>
  )
}

// مزود الإشعارات المتقدم
export function AdvancedNotificationProvider({ 
  children,
  settings 
}: { 
  children: ReactNode
  settings?: Partial<NotificationSettings>
}) {
  const defaultSettings: Partial<NotificationSettings> = {
    maxVisible: 10,
    defaultDuration: 4000,
    autoRefresh: true,
    refreshInterval: 15000,
    showFilters: true,
    position: 'bottom-right',
    enableSound: true,
    enableVibration: false,
    ...settings
  }

  return (
    <NotificationProvider defaultSettings={defaultSettings}>
      {children}
    </NotificationProvider>
  )
}

// مزود للمشاريع
export function ProjectNotificationProvider({ children }: { children: ReactNode }) {
  const projectSettings: Partial<NotificationSettings> = {
    maxVisible: 8,
    defaultDuration: 5000,
    autoRefresh: true,
    refreshInterval: 30000,
    showFilters: true,
    position: 'bottom-left',
    enableSound: true,
    enableVibration: true
  }

  return (
    <NotificationProvider defaultSettings={projectSettings}>
      {children}
    </NotificationProvider>
  )
}

// مزود للإدارة
export function AdminNotificationProvider({ children }: { children: ReactNode }) {
  const adminSettings: Partial<NotificationSettings> = {
    maxVisible: 15,
    defaultDuration: 6000,
    autoRefresh: true,
    refreshInterval: 10000,
    showFilters: true,
    position: 'top-right',
    enableSound: true,
    enableVibration: false
  }

  return (
    <NotificationProvider defaultSettings={adminSettings}>
      {children}
    </NotificationProvider>
  )
} 