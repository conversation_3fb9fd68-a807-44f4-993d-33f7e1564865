'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { 
  Shield, 
  Upload, 
  Download, 
  Eye, 
  Trash2, 
  Share2, 
  Search,
  AlertTriangle,
  Clock,
  User
} from 'lucide-react'

interface AuditLog {
  id: string
  action: 'upload' | 'download' | 'view' | 'delete' | 'share'
  fileName: string
  fileId: string
  userId: string
  userName: string
  timestamp: string
  ipAddress: string
  userAgent: string
  details?: string
}

interface AuditLoggerProps {
  fileId?: string
  maxLogs?: number
}

export function AuditLogger({ fileId, maxLogs = 100 }: AuditLoggerProps) {
  const [logs, setLogs] = useState<AuditLog[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedAction, setSelectedAction] = useState('all')
  const [selectedUser, setSelectedUser] = useState('all')

  useEffect(() => {
    loadAuditLogs()
  }, [fileId])

  const loadAuditLogs = async () => {
    setIsLoading(true)
    
    // محاكاة تحميل البيانات من API
    const mockLogs: AuditLog[] = [
      {
        id: '1',
        action: 'upload',
        fileName: 'تقرير_المشروع_الجديد.pdf',
        fileId: 'file_001',
        userId: 'user_001',
        userName: 'أحمد محمد',
        timestamp: new Date().toISOString(),
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0...',
        details: 'تم رفع الملف بنجاح'
      },
      {
        id: '2',
        action: 'download',
        fileName: 'عرض_تقديمي.pptx',
        fileId: 'file_002',
        userId: 'user_002',
        userName: 'سارة أحمد',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0...',
        details: 'تم تحميل الملف'
      }
    ]

    await new Promise(resolve => setTimeout(resolve, 1000))
    setLogs(mockLogs.slice(0, maxLogs))
    setIsLoading(false)
  }

  const filteredLogs = logs.filter(log => {
    const matchesSearch = log.fileName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.userName.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesAction = selectedAction === 'all' || log.action === selectedAction
    const matchesUser = selectedUser === 'all' || log.userId === selectedUser
    const matchesFile = !fileId || log.fileId === fileId

    return matchesSearch && matchesAction && matchesUser && matchesFile
  })

  const getActionIcon = (action: AuditLog['action']) => {
    switch (action) {
      case 'upload':
        return <Upload className="w-4 h-4 text-blue-600" />
      case 'download':
        return <Download className="w-4 h-4 text-green-600" />
      case 'view':
        return <Eye className="w-4 h-4 text-gray-600" />
      case 'delete':
        return <Trash2 className="w-4 h-4 text-red-600" />
      case 'share':
        return <Share2 className="w-4 h-4 text-purple-600" />
      default:
        return <Shield className="w-4 h-4 text-gray-600" />
    }
  }

  const getActionText = (action: AuditLog['action']) => {
    switch (action) {
      case 'upload':
        return 'رفع ملف'
      case 'download':
        return 'تحميل ملف'
      case 'view':
        return 'عرض ملف'
      case 'delete':
        return 'حذف ملف'
      case 'share':
        return 'مشاركة ملف'
      default:
        return 'عملية غير معروفة'
    }
  }

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getUniqueUsers = () => {
    const users = Array.from(new Set(logs.map(log => log.userId)))
    return users.map(userId => {
      const log = logs.find(l => l.userId === userId)
      return {
        id: userId,
        name: log?.userName || userId
      }
    })
  }

  const getActionCounts = () => {
    const counts = logs.reduce((acc, log) => {
      acc[log.action] = (acc[log.action] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return counts
  }

  const actionCounts = getActionCounts()
  const uniqueUsers = getUniqueUsers()

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="mr-3 text-gray-600">جاري تحميل سجلات التدقيق...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Statistics */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <Shield className="w-6 h-6 text-blue-600" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">سجل التدقيق والأمان</h3>
              <p className="text-sm text-gray-600">
                مراقبة وتتبع جميع العمليات على الملفات
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{logs.length}</div>
              <div className="text-sm text-gray-600">إجمالي العمليات</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{actionCounts.upload || 0}</div>
              <div className="text-sm text-gray-600">رفع ملفات</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{actionCounts.download || 0}</div>
              <div className="text-sm text-gray-600">تحميل ملفات</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{actionCounts.view || 0}</div>
              <div className="text-sm text-gray-600">عرض ملفات</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{actionCounts.delete || 0}</div>
              <div className="text-sm text-gray-600">حذف ملفات</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="بحث في السجلات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-3 pr-10"
              />
            </div>

            {/* Action Filter */}
            <select
              value={selectedAction}
              onChange={(e) => setSelectedAction(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">جميع العمليات</option>
              <option value="upload">رفع ملف</option>
              <option value="download">تحميل ملف</option>
              <option value="view">عرض ملف</option>
              <option value="delete">حذف ملف</option>
              <option value="share">مشاركة ملف</option>
            </select>

            {/* User Filter */}
            <select
              value={selectedUser}
              onChange={(e) => setSelectedUser(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">جميع المستخدمين</option>
              {uniqueUsers.map(user => (
                <option key={user.id} value={user.id}>{user.name}</option>
              ))}
            </select>

            {/* Refresh Button */}
            <Button
              variant="ghost"
              onClick={loadAuditLogs}
              disabled={isLoading}
            >
              تحديث
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Logs List */}
      <Card>
        <CardContent className="p-0">
          {filteredLogs.length === 0 ? (
            <div className="text-center py-12">
              <Shield className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد سجلات</h3>
              <p className="text-gray-600">
                {searchTerm || selectedAction !== 'all' || selectedUser !== 'all'
                  ? 'لم يتم العثور على سجلات تطابق الفلاتر المحددة'
                  : 'لم يتم تسجيل أي عمليات بعد'
                }
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredLogs.map(log => (
                <div key={log.id} className="p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start gap-4">
                    {/* Action Icon */}
                    <div className="flex-shrink-0 mt-1">
                      {getActionIcon(log.action)}
                    </div>

                    {/* Log Details */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-gray-900">{getActionText(log.action)}</span>
                        <span className="text-gray-500">•</span>
                        <span className="text-gray-700 truncate">{log.fileName}</span>
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <User className="w-3 h-3" />
                          <span>{log.userName}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          <span>{formatDate(log.timestamp)}</span>
                        </div>
                        <span>IP: {log.ipAddress}</span>
                      </div>
                      
                      {log.details && (
                        <div className="mt-2 text-sm text-gray-600 bg-gray-50 p-2 rounded">
                          {log.details}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 