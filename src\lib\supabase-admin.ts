import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database.types'

// إعدادات Supabase للإدارة (تجاوز RLS)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo-project.supabase.co'
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo-service-key'

// إنشاء عميل Supabase للإدارة (يتجاوز RLS)
export const supabaseAdmin = createClient<Database>(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// دالة مساعدة للتحقق من وجود service role key
export const hasServiceRoleKey = () => {
  return process.env.SUPABASE_SERVICE_ROLE_KEY !== undefined
}

// دالة للحصول على العميل المناسب
export const getSupabaseClient = () => {
  // في وضع التطوير أو إذا لم يكن لدينا service role key، نستخدم العميل العادي
  if (!hasServiceRoleKey()) {
    console.warn('Using anonymous client - some operations may fail due to RLS')
    return supabaseAdmin // سيستخدم anon key
  }
  
  return supabaseAdmin
} 