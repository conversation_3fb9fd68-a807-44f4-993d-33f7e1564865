-- Migration: إدراج قوالب المؤشرات الشائعة
-- التاريخ: 2024-01-26
-- الوصف: إضافة قوالب المؤشرات الأكثر استخداماً في المشاريع

-- =============================================
-- قوالب مؤشرات الوقت
-- =============================================

-- قالب: وقت انتظار المريض
INSERT INTO kpi_templates (kpi_type_id, name, description, category, template_data, measurement_method, typical_baseline, typical_target)
SELECT 
  kt.id,
  'وقت انتظار المريض في الطوارئ',
  'قياس متوسط وقت انتظار المريض من الوصول حتى بدء الفحص',
  'healthcare',
  '{"measurement_frequency": "daily", "data_collection": "automatic", "benchmark": "20_minutes"}',
  'حساب متوسط الوقت من تسجيل الوصول إلى بدء الفحص',
  45.0,
  20.0
FROM kpi_types kt WHERE kt.name = 'time';

-- قالب: مدة معالجة الطلب
INSERT INTO kpi_templates (kpi_type_id, name, description, category, template_data, measurement_method, typical_baseline, typical_target)
SELECT 
  kt.id,
  'مدة معالجة طلب الشراء',
  'قياس الوقت اللازم لمعالجة طلب الشراء من التقديم حتى الموافقة',
  'operations',
  '{"measurement_frequency": "per_request", "data_collection": "system", "benchmark": "5_days"}',
  'حساب الفرق بين تاريخ التقديم وتاريخ الموافقة النهائية',
  10.0,
  5.0
FROM kpi_types kt WHERE kt.name = 'time';

-- قالب: وقت الاستجابة للصيانة
INSERT INTO kpi_templates (kpi_type_id, name, description, category, template_data, measurement_method, typical_baseline, typical_target)
SELECT 
  kt.id,
  'وقت الاستجابة لطلبات الصيانة',
  'قياس الوقت من تقديم طلب الصيانة حتى بدء العمل',
  'operations',
  '{"measurement_frequency": "per_request", "data_collection": "manual", "benchmark": "2_hours"}',
  'حساب الفرق بين وقت الطلب ووقت بدء العمل',
  4.0,
  2.0
FROM kpi_types kt WHERE kt.name = 'time';

-- =============================================
-- قوالب مؤشرات العدد
-- =============================================

-- قالب: عدد المرضى المعالجين
INSERT INTO kpi_templates (kpi_type_id, name, description, category, template_data, measurement_method, typical_baseline, typical_target)
SELECT 
  kt.id,
  'عدد المرضى المعالجين يومياً',
  'قياس عدد المرضى الذين تم علاجهم بنجاح يومياً',
  'healthcare',
  '{"measurement_frequency": "daily", "data_collection": "automatic", "benchmark": "150_patients"}',
  'عدد المرضى الذين أكملوا العلاج خلال اليوم',
  120.0,
  150.0
FROM kpi_types kt WHERE kt.name = 'count';

-- قالب: عدد الأخطاء في الفواتير
INSERT INTO kpi_templates (kpi_type_id, name, description, category, template_data, measurement_method, typical_baseline, typical_target)
SELECT 
  kt.id,
  'عدد الأخطاء في الفواتير شهرياً',
  'قياس عدد الأخطاء المكتشفة في الفواتير الشهرية',
  'finance',
  '{"measurement_frequency": "monthly", "data_collection": "manual", "benchmark": "less_than_5"}',
  'عدد الفواتير التي تحتوي على أخطاء خلال الشهر',
  25.0,
  5.0
FROM kpi_types kt WHERE kt.name = 'count';

-- قالب: عدد خطوات الموافقة
INSERT INTO kpi_templates (kpi_type_id, name, description, category, template_data, measurement_method, typical_baseline, typical_target)
SELECT 
  kt.id,
  'عدد خطوات الموافقة على الطلب',
  'قياس عدد الخطوات المطلوبة لاعتماد الطلب',
  'operations',
  '{"measurement_frequency": "per_process", "data_collection": "system", "benchmark": "4_steps"}',
  'عدد مستويات الموافقة المطلوبة',
  8.0,
  4.0
FROM kpi_types kt WHERE kt.name = 'count';

-- =============================================
-- قوالب مؤشرات النسبة المئوية
-- =============================================

-- قالب: نسبة رضا المرضى
INSERT INTO kpi_templates (kpi_type_id, name, description, category, template_data, measurement_method, typical_baseline, typical_target)
SELECT 
  kt.id,
  'نسبة رضا المرضى',
  'قياس نسبة رضا المرضى عن الخدمة المقدمة',
  'healthcare',
  '{"measurement_frequency": "monthly", "data_collection": "survey", "benchmark": "90_percent"}',
  'استبيان رضا المرضى - نسبة الراضين من إجمالي المستجيبين',
  70.0,
  90.0
FROM kpi_types kt WHERE kt.name = 'percentage';

-- قالب: نسبة إنجاز المهام في الوقت المحدد
INSERT INTO kpi_templates (kpi_type_id, name, description, category, template_data, measurement_method, typical_baseline, typical_target)
SELECT 
  kt.id,
  'نسبة إنجاز المهام في الوقت المحدد',
  'قياس نسبة المهام المكتملة في الموعد المحدد',
  'operations',
  '{"measurement_frequency": "monthly", "data_collection": "system", "benchmark": "85_percent"}',
  'عدد المهام المكتملة في الوقت / إجمالي المهام × 100',
  60.0,
  85.0
FROM kpi_types kt WHERE kt.name = 'percentage';

-- قالب: معدل حضور الموظفين
INSERT INTO kpi_templates (kpi_type_id, name, description, category, template_data, measurement_method, typical_baseline, typical_target)
SELECT 
  kt.id,
  'معدل حضور الموظفين',
  'قياس نسبة حضور الموظفين من إجمالي أيام العمل',
  'hr',
  '{"measurement_frequency": "monthly", "data_collection": "system", "benchmark": "95_percent"}',
  'أيام الحضور الفعلية / إجمالي أيام العمل × 100',
  88.0,
  95.0
FROM kpi_types kt WHERE kt.name = 'percentage';

-- =============================================
-- قوالب مؤشرات التكلفة
-- =============================================

-- قالب: تكلفة معالجة الطلب الواحد
INSERT INTO kpi_templates (kpi_type_id, name, description, category, template_data, measurement_method, typical_baseline, typical_target)
SELECT 
  kt.id,
  'تكلفة معالجة الطلب الواحد',
  'قياس التكلفة الإجمالية لمعالجة طلب واحد',
  'finance',
  '{"measurement_frequency": "monthly", "data_collection": "calculation", "benchmark": "100_sar"}',
  'إجمالي تكاليف المعالجة / عدد الطلبات المعالجة',
  150.0,
  100.0
FROM kpi_types kt WHERE kt.name = 'cost';

-- قالب: التوفير الشهري في الكهرباء
INSERT INTO kpi_templates (kpi_type_id, name, description, category, template_data, measurement_method, typical_baseline, typical_target)
SELECT 
  kt.id,
  'التوفير الشهري في استهلاك الكهرباء',
  'قياس مقدار التوفير في فاتورة الكهرباء شهرياً',
  'operations',
  '{"measurement_frequency": "monthly", "data_collection": "bills", "benchmark": "15000_sar"}',
  'الفرق بين الفاتورة السابقة والحالية',
  50000.0,
  35000.0
FROM kpi_types kt WHERE kt.name = 'cost';

-- قالب: تكلفة صيانة الأجهزة
INSERT INTO kpi_templates (kpi_type_id, name, description, category, template_data, measurement_method, typical_baseline, typical_target)
SELECT 
  kt.id,
  'تكلفة صيانة الأجهزة سنوياً',
  'قياس إجمالي تكلفة صيانة الأجهزة خلال السنة',
  'operations',
  '{"measurement_frequency": "yearly", "data_collection": "invoices", "benchmark": "150000_sar"}',
  'مجموع فواتير الصيانة والقطع خلال السنة',
  200000.0,
  150000.0
FROM kpi_types kt WHERE kt.name = 'cost';

-- =============================================
-- قوالب مؤشرات الجودة
-- =============================================

-- قالب: مستوى جودة الخدمة
INSERT INTO kpi_templates (kpi_type_id, name, description, category, template_data, measurement_method, typical_baseline, typical_target)
SELECT 
  kt.id,
  'مستوى جودة الخدمة',
  'قياس مستوى جودة الخدمة المقدمة للعملاء',
  'quality',
  '{"measurement_frequency": "monthly", "data_collection": "assessment", "benchmark": "4_out_of_5"}',
  'تقييم شامل لجودة الخدمة من 1 إلى 5',
  3.0,
  4.5
FROM kpi_types kt WHERE kt.name = 'quality';

-- قالب: معدل الأخطاء في العمليات
INSERT INTO kpi_templates (kpi_type_id, name, description, category, template_data, measurement_method, typical_baseline, typical_target)
SELECT 
  kt.id,
  'معدل الأخطاء في العمليات',
  'قياس نسبة الأخطاء في العمليات اليومية',
  'quality',
  '{"measurement_frequency": "daily", "data_collection": "monitoring", "benchmark": "less_than_2_percent"}',
  'عدد الأخطاء / إجمالي العمليات × 100',
  5.0,
  2.0
FROM kpi_types kt WHERE kt.name = 'quality';

-- =============================================
-- قوالب مؤشرات الكفاءة
-- =============================================

-- قالب: معدل الإنتاجية
INSERT INTO kpi_templates (kpi_type_id, name, description, category, template_data, measurement_method, typical_baseline, typical_target)
SELECT 
  kt.id,
  'معدل الإنتاجية للموظف',
  'قياس معدل إنتاجية الموظف الواحد',
  'efficiency',
  '{"measurement_frequency": "monthly", "data_collection": "tracking", "benchmark": "120_percent"}',
  'الإنتاج الفعلي / الإنتاج المستهدف × 100',
  85.0,
  120.0
FROM kpi_types kt WHERE kt.name = 'efficiency';

-- قالب: معدل استخدام الموارد
INSERT INTO kpi_templates (kpi_type_id, name, description, category, template_data, measurement_method, typical_baseline, typical_target)
SELECT 
  kt.id,
  'معدل استخدام الموارد',
  'قياس مدى الاستفادة من الموارد المتاحة',
  'efficiency',
  '{"measurement_frequency": "monthly", "data_collection": "monitoring", "benchmark": "80_percent"}',
  'الاستخدام الفعلي / الطاقة المتاحة × 100',
  60.0,
  80.0
FROM kpi_types kt WHERE kt.name = 'efficiency';

-- =============================================
-- تحديث عدد الاستخدام للقوالب
-- =============================================
UPDATE kpi_templates SET usage_count = 0 WHERE usage_count IS NULL; 