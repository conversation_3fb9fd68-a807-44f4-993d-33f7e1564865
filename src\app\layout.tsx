import type { Metada<PERSON> } from "next";
import "./globals.css";
import { ToastProvider } from "@/components/ui/Toast";

export const metadata: Metadata = {
  title: "نظام إدارة طلبات المشاريع",
  description: "نظام شامل لإدارة طلبات المشاريع واعتمادها مع التركيز على التحسين المستمر",
  keywords: "إدارة المشاريع, طلبات المشاريع, اعتماد المشاريع, تحسين العمليات",
  authors: [{ name: "فريق تطوير النظام" }],
  robots: "index, follow",
  openGraph: {
    title: "نظام إدارة طلبات المشاريع",
    description: "نظام شامل لإدارة طلبات المشاريع واعتمادها",
    type: "website",
    locale: "ar_SA",
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" className="scroll-smooth" style={{ colorScheme: 'light' }}>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Readex+Pro:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
        <meta name="theme-color" content="#3b82f6" />
        <meta name="color-scheme" content="light" />
        <style dangerouslySetInnerHTML={{
          __html: `
            * { color-scheme: light !important; }
            html { color-scheme: light !important; }
            body { background: #ffffff !important; color: #1f2937 !important; }
          `
        }} />
      </head>
      <body className="antialiased bg-white text-gray-900">
        <ToastProvider>
          <div id="root">
            <main>
              {children}
            </main>
          </div>
        </ToastProvider>
      </body>
    </html>
  );
}
